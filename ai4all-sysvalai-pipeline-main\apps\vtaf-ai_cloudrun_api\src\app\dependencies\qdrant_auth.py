"""
qdrant.py

Provides functionality to manage and cache QdrantProcessor instances per collection.
The `get_qdrant_processor` function ensures a single QdrantProcessor instance exists
per collection name to avoid redundant instantiation.
"""

import asyncio
import logging

from datetime import datetime, timedelta, timezone
from app.services.qdrant.processor import QdrantProcessor

# Internal cache to store QdrantProcessor instances keyed by collection name
_vector_store_cache = {}
_vector_store_lock = asyncio.Lock()
logger = logging.getLogger("vtaf")


async def get_qdrant_processor(collection_name: str, force: bool = False) -> QdrantProcessor:
    """
    Retrieves a cached QdrantProcessor instance for the specified collection name.
    If not present in cache, or if `force` is True, a new instance is created and stored.

    Args:
        collection_name (str): The name of the Qdrant collection.
        force (bool): If True, forces creation of a new processor instance.

    Returns:
        QdrantProcessor: The processor instance associated with the collection.
    """
    global _vector_store_cache
    now = datetime.now(timezone.utc)


    if (
        not force
        and collection_name in _vector_store_cache
        and 'processor' in _vector_store_cache[collection_name]
        and 'expiry' in _vector_store_cache[collection_name]
        and now < _vector_store_cache[collection_name]['expiry']
    ):
        return _vector_store_cache[collection_name]['processor']
    
    async with _vector_store_lock:
        if (
            not force
            and collection_name in _vector_store_cache
            and 'processor' in _vector_store_cache[collection_name]
            and 'expiry' in _vector_store_cache[collection_name]
            and datetime.now(timezone.utc) < _vector_store_cache[collection_name]['expiry']
        ):
            return _vector_store_cache[collection_name]['processor']


        processor = await asyncio.to_thread(QdrantProcessor, collection_name=collection_name)
        expiry = datetime.now(timezone.utc) + timedelta(minutes=50)
        _vector_store_cache[collection_name] = {
            'processor': processor,
            'expiry': expiry,
        }

        logger.info(f"Created and cached QdrantProcessor for collection: {collection_name}")
        return processor

