<template>
  <div class="row q-ma-md q-pa-md bg-blue-1 rounded-borders">
    <q-select
      v-model="selected_project"
      @update:model-value="handleChangeProject"
      :options="projects_list"
      label="Customer"
      class="col-sm-3 custom-select"
      outlined
      dense
      :loading="project_loading"
    />
    <q-select
      v-model="selected_variant"
      @update:model-value="handleChangeVariant"
      :options="variants_list"
      label="Project"
      class="col-sm-4 custom-select"
      outlined
      dense
      :loading="variant_loading"
    />
    <q-select
      v-model="selected_euf_feature"
      :options="euf_features_list"
      label="EUF_OR_FEATURE"
      class="col-sm-5 custom-select"
      @update:model-value="searchButtonClicked"
      outlined
      dense
      :loading="euf_feature_loading"
    />
  </div>
  <div>
    <q-list bordered class="q-pa-md custom-list">
      <!-- Show a spinner inside the list while loading -->

      <q-expansion-item
        v-for="item in sensors_and_algorithms"
        :key="item.uuid"
        expand-separator
        group="projects"
        class="bg-grey-2 rounded-borders shadow-2"
      >
        <template v-slot:header>
          <q-item-section avatar>
            <q-avatar text-color="primary">
              <q-icon name="hub" />
            </q-avatar>
          </q-item-section>
          <q-item-section>
            <q-item-label class="text-primary text-weight-bold text-lg">
              Sensors and Algorithms Configuration
            </q-item-label>
          </q-item-section>
        </template>

        <q-card class="q-ma-md shadow-3 rounded-borders">
          <q-card-section class="bg-blue-1 text-white">
            <div class="text-h6 text-bold text-blue-10">
              <q-icon name="sensors" color="blue-10" size="sm" /> Sensors
            </div>
          </q-card-section>

          <q-list v-if="Object.keys(item.sensors).length" class="q-pa-md">
            <q-item
              v-for="(locations, sensorType) in item.sensors"
              :key="sensorType"
            >
              <q-item-section avatar>
                <q-icon name="podcasts" color="blue-9" />
              </q-item-section>
              <q-item-section>
                <q-item-label class="text-bold text-blue-9 text-lg">{{
                  sensorType
                }}</q-item-label>
                <q-item-label
                  caption
                  class="text-md text-grey-8"
                  v-for="location in locations"
                  :key="location"
                >
                  - {{ location }}
                </q-item-label>
              </q-item-section>
            </q-item>
          </q-list>

          <q-card-section class="bg-green-1 text-white">
            <div class="text-h6 text-bold text-green-10">
              <q-icon name="memory" color="green-10" size="sm" /> Algorithms
            </div>
          </q-card-section>

          <q-list v-if="Object.keys(item.algorithms).length" class="q-pa-md">
            <q-item
              v-for="(algorithms, algoType) in item.algorithms"
              :key="algoType"
            >
              <q-item-section avatar>
                <q-icon name="schema" color="green-9" />
              </q-item-section>
              <q-item-section>
                <q-item-label class="text-bold text-green-9 text-lg">{{
                  algoType
                }}</q-item-label>
                <q-item-label
                  caption
                  class="text-md text-grey-8"
                  v-for="algo in algorithms"
                  :key="algo"
                >
                  - {{ algo }}
                </q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card>
      </q-expansion-item>
    </q-list>
  </div>
  <q-card class="q-pa-md card-container">
    <div class="yaml_button">
      <q-btn
        label="CHANGE AGENT CONFIGURATION"
        color="blue-10"
        @click="showAgentDialog = true"
        :disable="
          !selected_project || !selected_variant || !selected_euf_feature
        "
      />
    </div>
  </q-card>
  <div>
    <q-card class="q-pa-md card-container">
      <!-- File Upload -->
      <q-file
        v-model="selected_file"
        label="Upload CSV or XLSX"
        accept=".csv, .xlsx"
        @update:model-value="handleFileUpload"
        max-file-size="10485760"
        class="q-mb-md file-input"
        filled
        standout="bg-primary text-white"
      >
        <template v-slot:prepend>
          <q-icon name="attach_file" />
        </template>
      </q-file>

      <q-btn
        label="GENERATE TESTCASE"
        color="blue-10"
        :disable="
          !selected_file ||
          !selected_project ||
          !selected_variant ||
          !selected_euf_feature ||
          table_data.length === 0 ||
          selected_rows.length === 0
        "
        @click="file_upload_button"
        class="q-mb-md generate-btn"
      />

      <!-- Data Table -->
      <q-table
        v-if="table_data.length"
        :rows="table_data"
        :columns="columns"
        row-key="id"
        flat
        bordered
        hide-pagination
        :rows-per-page-options="[0]"
        class="styled-table"
      >
        <!-- Header Checkbox -->
        <template v-slot:header="props">
          <q-tr :props="props" class="header-row">
            <q-th>
              <q-checkbox
                v-model="selectAll"
                @update:model-value="toggleSelectAll"
              />
            </q-th>
            <q-th
              v-for="col in props.cols"
              :key="col.name"
              class="table-header"
            >
              {{ col.label }}
            </q-th>
          </q-tr>
        </template>

        <!-- Row Checkboxes -->
        <template v-slot:body="props">
          <q-tr :props="props" class="table-row">
            <q-td>
              <q-checkbox
                :model-value="selected_rows.includes(props.rowIndex)"
                @update:model-value="toggleRowSelection(props.rowIndex)"
              />
            </q-td>
            <q-td v-for="col in props.cols" :key="col.name" class="table-cell">
              {{ props.row[col.field] }}
            </q-td>
          </q-tr>
        </template>
      </q-table>
    </q-card>
  </div>
  <q-dialog v-model="showAgentDialog" persistent>
    <q-card class="q-pa-md" style="min-width: 700px; max-width: 1000px">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">Edit Agent Configuration</div>
        <q-space />
        <q-btn icon="close" flat round dense @click="closeAgentDialog" />
      </q-card-section>

      <q-separator />

      <q-card-section class="q-pa-none">
        <q-tabs
          v-model="activeTab"
          dense
          class="bg-grey-2 text-primary"
          active-color="primary"
          indicator-color="primary"
        >
          <q-tab
            v-for="(agent, key) in localAgentYaml"
            :key="key"
            :name="key"
            :label="key"
          />
        </q-tabs>

        <q-tab-panels v-model="activeTab" animated>
          <q-tab-panel
            v-for="(agent, key) in localAgentYaml"
            :key="key"
            :name="key"
          >
            <!-- Agent Inputs -->
            <div class="q-gutter-md q-mt-md">
              <q-input v-model="agent.role" label="Role" outlined readonly />
              <q-input
                v-model="agent.goal"
                label="Goal"
                outlined
                @update:model-value="markEdited"
              />
              <q-input
                v-model="agent.backstory"
                label="Backstory"
                type="textarea"
                outlined
                @update:model-value="markEdited"
              />
            </div>

            <!-- Associated Tasks -->
            <div class="q-mt-lg">
              <div class="text-subtitle1 q-mb-sm">Tasks for {{ key }}</div>
              <q-separator class="q-mb-md" />

              <div
                v-for="(task, taskKey) in getTasksByAgent(key)"
                :key="taskKey"
                class="q-pa-md q-mb-md bg-grey-1 rounded-borders"
              >
                <div class="text-subtitle2">{{ taskKey }}</div>

                <q-input
                  v-model="localTaskYaml[taskKey].description"
                  label="Description"
                  type="textarea"
                  outlined
                  @update:model-value="markEdited"
                  class="q-mt-sm"
                />
                <q-input
                  v-model="localTaskYaml[taskKey].expected_output"
                  label="Expected Output"
                  type="textarea"
                  outlined
                  @update:model-value="markEdited"
                  class="q-mt-sm"
                />
              </div>
            </div>
          </q-tab-panel>
        </q-tab-panels>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right">
        <q-btn
          flat
          label="Save"
          color="blue-10"
          @click="saveAgentChanges"
          :disable="!is_yaml_edited"
        />
        <q-btn flat label="Close" color="blue-10" @click="closeAgentDialog" />
        <q-btn flat label="RESET" color="blue-10" @click="resetYamlData" />
      </q-card-actions>
    </q-card>
  </q-dialog>
  <q-inner-loading :showing="sensors_and_algorithms_loading">
    <q-spinner-ball size="100px" color="blue-10" />
    <div class="q-mt-md text-blue-10 text-center text-h6 text-weight-bold">
      Getting Project Configuration<br />
      Please Wait...<br />
      <q-icon size="50px" name="mood" color="yellow-8"></q-icon>
    </div>
  </q-inner-loading>
  <q-inner-loading :showing="upload_file_loading">
    <q-spinner-hourglass size="75px" color="blue-10" />
    <div class="q-mt-md text-blue-10 text-h6 text-weight-bold">
      Please wait while creating job...
    </div>
  </q-inner-loading>
</template>

<script setup>
import { onMounted, computed, ref, reactive, watch } from "vue";
import { storeToRefs } from "pinia";
import { useQuasar } from "quasar";
import * as XLSX from "xlsx";
import Papa from "papaparse";
import { useRunKbTcGenerationStore } from "src/stores/ai_testcase/run-kb-tc-generation-store";
import { useRouter } from "vue-router";

const router = useRouter();
const $q = useQuasar();

const run_knowledgebase_config = useRunKbTcGenerationStore();
const {
  projects_list,
  variants_list,
  euf_features_list,
  selected_project,
  selected_variant,
  selected_euf_feature,
  selected_file,
  sensors_and_algorithms,
  table_data,
  selected_rows,
  columns,
  current_execution_id,
  project_loading,
  variant_loading,
  euf_feature_loading,
  sensors_and_algorithms_loading,
  upload_file_loading,
  agent_yaml_content,
  task_yaml_content,
  is_yaml_edited,
  is_yaml_saved,
} = storeToRefs(run_knowledgebase_config);
const {
  get_user_email,
  get_profile_projects,
  get_variant_details,
  get_euf_features_details,
  get_knowledgebase_profile,
  upload_file_with_knowledgebase,
  get_yaml_contents,
} = run_knowledgebase_config;
// onmount
onMounted(async () => {
  selected_project.value = "";
  selected_variant.value = "";
  selected_euf_feature.value = "";
  current_execution_id.value = "";
  sensors_and_algorithms.value = [];
  selected_file.value = null;
  table_data.value = [];
  selected_rows.value = [];
  agent_yaml_content.value = null;
  task_yaml_content.value = null;
  columns.value = [];
  await get_user_email();
  get_profile_projects();
});

// Dialog and tab state
const showAgentDialog = ref(false);
const activeTab = ref("");
const localAgentYaml = reactive({});
const localTaskYaml = reactive({});

function closeAgentDialog() {
  is_yaml_edited.value = false;
  showAgentDialog.value = false;
}

function markEdited() {
  is_yaml_edited.value = true;
}

async function resetYamlData() {
  try {
    await get_yaml_contents(); // Re-fetch from API
    initializeLocalYaml(); // Re-populate editable copies
    is_yaml_saved.value = false;
    $q.notify({
      type: "info",
      message: "Configuration reset to original.",
    });
  } catch (error) {
    $q.notify({
      type: "negative",
      message: "Failed to reset configuration.",
    });
  }
}

watch(showAgentDialog, (val) => {
  if (val) {
    is_yaml_edited.value = false;
    initializeLocalYaml();
  }
});

function initializeLocalYaml() {
  // Clear and re-populate agent data
  Object.keys(localAgentYaml).forEach((key) => delete localAgentYaml[key]);
  for (const key in agent_yaml_content.value) {
    localAgentYaml[key] = {
      role: agent_yaml_content.value[key].role,
      goal: agent_yaml_content.value[key].goal,
      backstory: agent_yaml_content.value[key].backstory,
    };
  }

  // Clear and re-populate task data
  Object.keys(localTaskYaml).forEach((key) => delete localTaskYaml[key]);
  for (const key in task_yaml_content.value) {
    localTaskYaml[key] = {
      description: task_yaml_content.value[key].description,
      expected_output: task_yaml_content.value[key].expected_output,
      agent: task_yaml_content.value[key].agent,
    };
  }

  // Set the active tab to first agent
  activeTab.value = Object.keys(localAgentYaml)[0];
}

function getTasksByAgent(agentKey) {
  const filtered = {};
  for (const taskKey in localTaskYaml) {
    if (localTaskYaml[taskKey].agent === agentKey) {
      filtered[taskKey] = localTaskYaml[taskKey];
    }
  }
  return filtered;
}

function saveAgentChanges() {
  for (const key in localAgentYaml) {
    agent_yaml_content.value[key] = {
      ...localAgentYaml[key], // copy updated role, goal, backstory
    };
  }
  for (const key in localTaskYaml) {
    task_yaml_content.value[key] = {
      ...localTaskYaml[key],
    };
  }
  is_yaml_edited.value = false;
  is_yaml_saved.value = true;
  $q.notify({
    type: "positive",
    message: "Agent configuration saved successfully.",
  });
  showAgentDialog.value = false;
}

const handleChangeProject = () => {
  selected_variant.value = "";
  selected_euf_feature.value = "";
  current_execution_id.value = "";
  sensors_and_algorithms.value = [];
  selected_file.value = null;
  table_data.value = [];
  selected_rows.value = [];
  columns.value = [];
  get_variant_details();
};

const handleChangeVariant = () => {
  selected_euf_feature.value = "";
  current_execution_id.value = "";
  sensors_and_algorithms.value = [];
  selected_file.value = null;
  table_data.value = [];
  selected_rows.value = [];
  columns.value = [];
  get_euf_features_details();
};

const searchButtonClicked = async () => {
  current_execution_id.value = "";
  sensors_and_algorithms.value = [];
  selected_file.value = null;
  table_data.value = [];
  selected_rows.value = [];
  columns.value = [];
  await get_knowledgebase_profile();
  get_yaml_contents();
};

const selectAll = computed({
  get: () =>
    selected_rows.value.length === table_data.value.length &&
    table_data.value.length > 0,
  set: (val) => toggleSelectAll(val),
});

const handleFileUpload = (file) => {
  selected_rows.value = [];
  if (!file) return;
  const reader = new FileReader();
  reader.onload = (event) => {
    const data = event.target.result;
    file.name.endsWith(".csv") ? parseCSV(data) : parseXLSX(data);
  };

  if (file.size > 10485760) {
    $q.notify({ type: "negative", message: "File size exceeds 10MB limit" });
    selected_file.value = null;
  } else {
    reader.readAsBinaryString(file);
  }
};

const parseCSV = (data) => {
  Papa.parse(data, {
    header: true,
    skipEmptyLines: true,
    complete: (result) => {
      table_data.value = result.data;
      if (validateTableData()) {
        updateColumns(Object.keys(result.data[0]));
      }
    },
  });
};

const parseXLSX = (data) => {
  const workbook = XLSX.read(data, { type: "binary" });
  const sheetData = XLSX.utils.sheet_to_json(
    workbook.Sheets[workbook.SheetNames[0]]
  );
  table_data.value = sheetData;
  if (validateTableData()) {
    updateColumns(Object.keys(sheetData[0]));
  }
};

const validateTableData = () => {
  const hasEmptyRow = table_data.value.some((row) =>
    Object.values(row).every(
      (cell) => cell === null || cell === undefined || cell === ""
    )
  );

  if (hasEmptyRow) {
    $q.notify({
      type: "negative",
      message:
        "The uploaded file contains empty rows. Please clean the file and try again.",
    });
    selected_file.value = null;
    table_data.value = [];
    columns.value = [];
    return false;
  }
  return true;
};

const updateColumns = (keys) => {
  columns.value = keys.map((key) => ({
    name: key,
    label: key,
    field: key,
    align: "left",
  }));
};

const toggleRowSelection = (index) => {
  selected_rows.value.includes(index)
    ? (selected_rows.value = selected_rows.value.filter((i) => i !== index))
    : selected_rows.value.push(index);
};

const toggleSelectAll = (val) => {
  selected_rows.value = val ? table_data.value.map((_, index) => index) : [];
};

const file_upload_button = async () => {
  await upload_file_with_knowledgebase();
  selected_project.value = "";
  selected_variant.value = "";
  selected_euf_feature.value = "";
  selected_file.value = null;
  table_data.value = [];
  columns.value = [];
  selected_rows.value = [];

  router.push(`/ai_testcase/job_status/${current_execution_id.value}`);
};
</script>

<style scoped>
.q-expansion-item {
  border-radius: 10px;
  overflow: hidden;
}
.q-card {
  border-radius: 10px;
}

/* Bigger Font for Sensors & Algorithms */
.text-lg {
  font-size: 1.2rem;
}

.text-md {
  font-size: 1rem;
}
.custom-select {
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rounded-borders {
  border-radius: 10px;
}

.shadow-2 {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.shadow-3 {
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

.custom-list {
  background: #f5f5f5;
  border-radius: 10px;
  padding: 15px;
}
</style>
