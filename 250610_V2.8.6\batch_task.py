from PyQt5.QtCore import QThread, pyqtSignal

from spread_sheet import *
from vertexai_gemini import get_chat_response
# from response_parser import parse_response
from DataProcessing import parse_test_cases, fields as case_fields


#todo will be deleted
import configparser
import os
import win32api
from datetime import datetime


CASE_HEADER_INDEX = ["Object Heading", "oReference_Sys", "Object Text", "aTestCondition", "aTestAction", "aTestExpectation", "TC_Designer"]

USER_NAME = None

class BatchTask(QThread):

    message_signal = pyqtSignal(str)

    def __init__(self, **kwargs):
        super().__init__()
        self.kwargs = kwargs
        self.prompt_data_source = None
        self.case_count = 0
        self.errorResponseCase = 0

    def run(self):
        """
        batch task step
        1. initialize()
        2. build data_source
        3. data_source read_all_prompts
        4. send request
        5. handle response
        """
        try:
            self.init_data_source()
            self.read_prompts()
            for prompt in self.prompt_data_source.all_prompt():
                self.handle_request(prompt["prompt"])
                response = get_chat_response(prompt["prompt"])
                self.handle_response(prompt, response)
        except Exception as e:
            print(f"An error occurred when doing batch task:{e}")
            print(e.__traceback__.tb_lineno)
            print(e.__traceback__.tb_frame.f_code.co_filename)
            print(e.__traceback__.tb_frame.f_locals)
            self.message_signal.emit("error:"+str(e))
        self._end()

    def init_data_source(self):
        readErrorResult = self.get_sheet_config()
        if readErrorResult:
            raise Exception("configError:"+str(readErrorResult))
        if self.kwargs.get("task_type") == "single":
            if self.prompt_sheet_name is None:
                raise Exception("configError:" + str(readErrorResult))
            prompt_sheet_name = self.prompt_sheet_name.replace("TestSpec_", "Prompt_")
            self.prompt_data_source = GoogleSheetSinglePromptWithoutSavingDataSource(prompt_sheet_url=self.prompt_sheet,
                                                                                     test_spec_sheet_url=self.test_spec_sheet,
                                                                                     prompt_sheet_name=prompt_sheet_name)
        else:
            self.prompt_data_source = GoogleSheetBatchPromptDataSource(prompt_sheet_url=self.prompt_sheet,
                                                                       test_spec_sheet_url=self.test_spec_sheet)
        
    def get_sheet_config(self):
        case_SpreadsheetId_file = r"./config.ini"
        if os.path.exists(case_SpreadsheetId_file):
            config = configparser.ConfigParser()
            config.read(case_SpreadsheetId_file)
            try:
                self.prompt_sheet = config.get('SheetConfig', 'prompt_sheet')
                self.test_spec_sheet = config.get('TestSpec_SheetConfig', 'test_spec_sheet')
                self.prompt_sheet_name = config.get('SheetConfig', 'prompt_sheet_name')
                # self.TS_Dataset_sheet_name = config.get('TestSpec_SheetConfig', 'TS_Dataset_sheet_name')
                # self.TC_prompt_sheet_name  = config.get('TestSpec_SheetConfig', 'TS_prompt_sheet_name')
                if not check_config_spread_sheet_permission(self.prompt_sheet):
                    return "prompt_sheet does not have write permission!!!"
                if not check_config_spread_sheet_permission(self.test_spec_sheet):
                    return "test_spec_sheet does not have write permission!!!"
                return False
            except Exception as e:
                return "config.ini is not valid!\n"+str(e)
        else:
            return "config.ini not found"

    def read_prompts(self):
        """
        prompt is a dict at least contains key=prompt
        prompt format {"prompt": "xxxxx", "":""}
        Returns:
        """
        pass
    def get_datasource(self):
        return self.prompt_data_source

    def handle_request(self, prompt):
        self.message_signal.emit("send:"+prompt)

    def handle_response(self, prompt, response):
        #todo Exception check
        self.message_signal.emit("recv:"+response)
        self.case_count += self.prompt_data_source.save_result(prompt, response)
        if response.startswith("The session"):
            self.errorResponseCase += 1

    def get_case_count(self):
        current_case_count = self.case_count
        self.case_count = 0
        return current_case_count

    def _end(self):
        # send back case count and end signal
        self.message_signal.emit("case_count:"+str(self.case_count))
        self.message_signal.emit("end")
        self.message_signal.emit("response_error:"+str(self.errorResponseCase))



class GoogleSheetBatchPromptDataSource(object):

    """
    the datasource class
    responsible for read prompt from the google sheet and saving result
    """
    def __init__(self, prompt_sheet_url, test_spec_sheet_url, **kwargs):
        self.prompt_sheet_url = prompt_sheet_url
        self.test_spec_sheet_url = test_spec_sheet_url

        self.prompts = []
        self.prompt_sheet: SpreadSheet = SpreadSheet(prompt_sheet_url)
        self.test_spec_sheet: SpreadSheet = SpreadSheet(test_spec_sheet_url)

        self.sheet_header_cache = dict()
        self.read_prompt()

    def read_prompt(self):
        sheet_names = self.prompt_sheet.get_sheet_names()
        for sheet_name in sheet_names:
            if not sheet_name.startswith("Prompt"):
                continue
            data = self.prompt_sheet.read_by_sheet_name(sheet_name)
            # filter data status == "ready"
            for i, row in enumerate(data):
                if row.get("Status") == "ready" and row.get("Prompt_Design", "").strip() != "":
                    self.prompts.append({"prompt": row.get("Prompt_Design"), "row": i+2, "category": sheet_name})

    def all_prompt(self):
        return self.prompts

    def save_result(self, prompt, response:str):
        """

        Args:
            prompt:
            response:

        Returns: case count

        """
        row = prompt["row"]
        category = prompt["category"]
        if response.startswith("The session"):
            return 0
        try:
            # case_list = parse_test_cases(response)
            case_list_all = parse_test_cases(response)
            case_list = case_list_all.get("Test Cases", [])
        except Exception as e:
            print(f"parse case error: {e}")
            self._update_prompt_status(category, row, status="genFailed")
            return 0

        if self._save_prompt_object_heading(category, row, case_list, response):
            self._update_prompt_status(category, row)
        else:
            return 0
        ValidcaseNumber = self._add_test_spec_record(category, case_list)
        # print(ValidcaseNumber)
        # return len(case_list)
        return ValidcaseNumber

    def _save_prompt_object_heading(self, category, row, case_list, response):
        object_headings = []
        for case in case_list:
            if len(case) != 6:
                continue
            object_headings.append(str(case[0]).strip())
        save_text = "\n".join(object_headings).strip()
        date_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        if len(object_headings) == 0:
            self._update_prompt_status(category, row, status="genFailed")
            # errorInfo = "Test Case attributes are not well defined, please init dataset for standard format output!"
            errorInfo = "Each test case must have below elements: Test Case ID, Covered requirement id, Test objectives, Test Condition, Test Action, Test expectation."
            self.prompt_sheet.update_cell(category, row, 7, errorInfo)
            return False
        else:
            self.prompt_sheet.update_cell(category, row, 5, save_text)
            self._update_prompt_status(category, row)
            self.prompt_sheet.update_cell(category, row, 7, len(object_headings))
            self.prompt_sheet.update_cell(category, row, 8, response)
            self.prompt_sheet.update_cell(category, row, 9, date_now)
            return True

    def _update_prompt_status(self, category, row, status="generated"):
        self.prompt_sheet.update_cell(category, row, 6, status)

    def _get_sheet_header(self, sheet_name: str):
        if sheet_name in self.sheet_header_cache:
            return self.sheet_header_cache[sheet_name]
        if sheet_name.startswith("Prompt_"):
            headers = self.prompt_sheet.get_sheet_header(sheet_name)
        elif sheet_name.startswith("TestSpec_"):
            headers = self.test_spec_sheet.get_sheet_header(sheet_name)
        else:
            headers = []
        self.sheet_header_cache[sheet_name] = headers
        return headers

    def _add_test_spec_record(self, category, case_list):
        case_count = 0
        test_spec_sheet_name = category.replace("Prompt_", "TestSpec_")
        if test_spec_sheet_name not in self.test_spec_sheet.get_sheet_names():
            if not self._add_test_sheet(test_spec_sheet_name):
                return case_count
        sheet_headers = self._get_sheet_header(test_spec_sheet_name)
        for case in case_list:
            if len(case) != 6:
                # return case_count
                continue
            # add user name
            case.append(get_display_name())
            row = []
            for col in sheet_headers:
                if col in CASE_HEADER_INDEX:
                    row.append(case[CASE_HEADER_INDEX.index(col)])
                else:
                    row.append("")
            self.test_spec_sheet.add_row(test_spec_sheet_name, row, table_range="A3")
            case_count += 1
        return case_count

    def _add_test_sheet(self, sheet_name):
        try:
            headers = ["Object Heading", "oReference", "aBug_ID", "aFailed_Req", "oTestLevel", "oReference_Sys",
                       "aBug_ID_Sys", "aFailed_Req_Sys", "aTicket_ID", "TC_Designer", "aObjectType", "Object Short Text",
                       "Object Text", "aTestCondition", "aTestAction", "aTestExpectation", "aTestReaction", "aTestPriority",
                       "adASIL", "aTestEnvironment", "aTestMethod", "aTestType", "sdPlannedFor", "sVariants", "aObjectStatus",
                       "aReviewComments_Test", "aScriptComments", "sCarVariants", "aTestDesignMethod", "aReviewStatus_Test",
                       "aReviewStatus_FO", "aTestExecutionOrder", "aNegativeTest", "aTestMachine", "aTestDate", "aTester",
                       "aVersion", "aTestResult", "aTestResultComment", "aTestLog", "CAPL_Case", "CAPL_Para", "ReqCount",
                       "aTestResultComment_Sys", "AttrChkResult", "notUsed4", "Req", "Case", "CaseCount", "CaseResult",
                       "ReqResult", "APR", "notUsed8", "APR_ID", "APR_Sts"]
            self.test_spec_sheet.create_new_sheet(sheet_name)
            self.test_spec_sheet.add_row(sheet_name, headers)
            return True
        except Exception as e:
            print(f"Create Sheet error: {e}")
            return False


class GoogleSheetSinglePromptDataSource(GoogleSheetBatchPromptDataSource):

    def __init__(self, prompt_sheet_url, test_spec_sheet_url, prompt_sheet_name, **kwargs):
        self.prompt_sheet_name = prompt_sheet_name
        super(GoogleSheetSinglePromptDataSource, self).__init__(prompt_sheet_url, test_spec_sheet_url, **kwargs)

    def read_prompt(self):
        sheet_names = self.prompt_sheet.get_sheet_names()
        if self.prompt_sheet_name in sheet_names:
            data = self.prompt_sheet.read_by_sheet_name(self.prompt_sheet_name)
            # filter data status == "ready"
            for i, row in enumerate(data):
                if row.get("Status") == "ready" and row.get("Prompt_Design", "").strip() != "":
                    self.prompts.append(
                        {"prompt": row.get("Prompt_Design"), "row": i + 2, "category": self.prompt_sheet_name})
                    break


class GoogleSheetSinglePromptWithoutSavingDataSource(GoogleSheetBatchPromptDataSource):

    """
    read first prompt but not auto save
    need to be cached at the sending step
    then call the manual_save_result() function to save the result
    """

    def __init__(self, prompt_sheet_url, test_spec_sheet_url, prompt_sheet_name, **kwargs):
        self.prompt_sheet_name = prompt_sheet_name
        super(GoogleSheetSinglePromptWithoutSavingDataSource, self).__init__(prompt_sheet_url, test_spec_sheet_url, **kwargs)
        self.last_prompt = None

    def read_prompt(self):
        sheet_names = self.prompt_sheet.get_sheet_names()
        if self.prompt_sheet_name in sheet_names:
            data = self.prompt_sheet.read_by_sheet_name(self.prompt_sheet_name)
            # filter data status == "ready"
            for i, row in enumerate(data):
                if row.get("Status") == "ready" and row.get("Prompt_Design", "").strip() != "":
                    self.prompts.append(
                        {"prompt": row.get("Prompt_Design"), "row": i + 2, "category": self.prompt_sheet_name})
                    break

    # def save_result(self, prompt, response:str):
    #     """
    #     put the prompt to global
    #     Args:
    #         prompt:
    #         response:

    #     Returns:

    #     """
    #     self.last_prompt = prompt
    #     row = prompt["row"]
    #     category = prompt["category"]
    #     date_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    #     if response.startswith("The session"):
    #         return 0
    #     self.prompt_sheet.update_cell(category, row, 8, response)
    #     self.prompt_sheet.update_cell(category, row, 9, date_now)
    #     return 0

    # def manual_save_result(self, response: str):
    #     if self.last_prompt is not None:
    #         return super().save_result(self.last_prompt, response)


def get_display_name():
    """获取当前用户的显示名称（全名）。"""
    global USER_NAME
    if USER_NAME is not None:
        return USER_NAME
    try:
        name_format = win32api.NameDisplay
        user_name = win32api.GetUserNameEx(name_format)
        email_address = win32api.GetUserNameEx(win32api.NameUserPrincipal)
        USER_NAME = user_name
        return user_name
    except Exception as e:
        # print(f"获取用户全名失败: {e}")
        user_name = os.getlogin()
        USER_NAME = user_name
        return user_name


if __name__ == '__main__':
    prompt_sheet_url = "11RMCa4B9YqKdGh_wIW0rce0q8ccKB4tPmw8QLSwM2ek"
    test_spec_sheet_url = "1SBwYx-uAAHhaH5tCNgYeXA4-wVX2av63CKmgur6pjKA"
    data_source = GoogleSheetBatchPromptDataSource(prompt_sheet_url, test_spec_sheet_url)
    data_source.read_prompt()

