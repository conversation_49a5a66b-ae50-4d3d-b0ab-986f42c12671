"""
logging_config.py

Sets up the logging configuration for the application. Logs messages to the console 
and optionally to a file. The logger is configured to output logs with a timestamp, 
log level, and message.
"""

import logging
import sys


def setup_logging() -> logging.Logger:
    """
    Sets up logging configuration for the application.
    Logs are displayed in the console and can be configured to log to a file.

    Returns:
        logging.Logger: Configured logger instance for the application.
    """
    # Configure logging format and level
    logging.basicConfig(
        level=logging.INFO,  # Set the default log level to INFO
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)  # Log to console
        ]
    )

    logging.getLogger("LiteLLM").setLevel(logging.WARNING)

    # Create a logger instance for the "vtaf" application
    logger = logging.getLogger("vtaf")

    return logger
