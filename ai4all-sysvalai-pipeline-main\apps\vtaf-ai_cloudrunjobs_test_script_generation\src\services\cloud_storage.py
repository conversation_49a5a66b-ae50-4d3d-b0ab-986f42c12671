from pathlib import Path

from google.cloud import storage

from configs.env import settings  # use env loader instead of os.environ directly
from utils.logger import get_logger

logger = get_logger(__name__)


def update_gcs_blob_content_type(blob_name: str) -> None:
    try:
        client = storage.Client(project=settings.PROJECT_ID)
        bucket = client.bucket(settings.BUCKET_NAME)
        blob = bucket.blob(blob_name)
        filepath = Path(blob_name)
        if filepath.suffix == '.xlsx':
            blob.content_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        else:
            blob.content_type = "application/octet-stream"
        blob.patch()
        logger.info(f"Updated {blob_name} content type to {blob.content_type} in bucket {settings.BUCKET_NAME}")
    except Exception as e:
        logger.error(f"Failed to update metadata for {blob_name} in bucket {settings.BUCKET_NAME}: {e}")
        raise
