[DEFAULT]
# 测试配置文件 - 用于默认按钮功能测试

[GOOGLE_SHEETS]
# 批量处理测试配置 (send_batch_reqs)
prompt_sheet_url = https://docs.google.com/spreadsheets/d/1test_batch_prompts_sheet_id/edit
test_spec_sheet_url = https://docs.google.com/spreadsheets/d/1test_batch_specs_sheet_id/edit
prompt_sheet_name = Prompt_Batch
test_spec_sheet_name = TestSpec_Batch

# 单个请求测试配置 (send_single_req)
single_prompt_sheet_url = https://docs.google.com/spreadsheets/d/1test_single_prompt_sheet_id/edit
single_test_spec_sheet_url = https://docs.google.com/spreadsheets/d/1test_single_spec_sheet_id/edit

[AI_CONFIG]
# AI模型配置
default_model = gemini-1.5-flash
temperature = 0.7
max_output_tokens = 8192

[BATCH_PROCESSING]
# 批量处理特定配置
batch_size = 10
max_retries = 3
timeout_seconds = 30

[SINGLE_TASK]
# 单个任务特定配置
prompt_text = This is a test prompt for single request functionality. Please respond with a simple acknowledgment.
output_format = json
save_to_file = true

[TESTING]
# 测试模式配置
mock_mode = true
simulation_mode = true
log_level = INFO
test_data_enabled = true
