from langfuse import <PERSON><PERSON>
from app.core.constants import LANGFUSE_HOST, LANGFUSE_PUBLIC_KEY, LANGFUSE_SECRET_KEY
import logging

logger = logging.getLogger("vtaf")

_langfuse = None

def get_langfuse():
    global _langfuse
    if _langfuse is None:
        try:
            _langfuse = Langfuse(
                secret_key=LANGFUSE_SECRET_KEY,
                public_key=LANGFUSE_PUBLIC_KEY,
                host=LANGFUSE_HOST,
            )
            logger.info(f"Langfuse health: {_langfuse.client.health.health()}")
        except Exception as e:
            logger.exception("Failed to initialize Langfuse client")
            raise RuntimeError("Langfuse client initialization failed") from e
    return _langfuse
