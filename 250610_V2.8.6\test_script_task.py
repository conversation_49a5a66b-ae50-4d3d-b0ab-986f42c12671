import shutil

from PyQt5.QtCore import QThread, pyqtSignal

# from batch_task import GoogleSheetBatchPromptDataSource
from spread_sheet import *
from vertexai_gemini import get_chat_response, load_file_to_Part, get_used_model
# from response_parser import parse_response
from DataProcessing import  parse_test_script

# todo will be deleted
import configparser
import os, time
import win32api
from datetime import datetime

CASE_HEADER_INDEX = ["Object Heading", "oReference_Sys", "Object Text", "aTestCondition", "aTestAction",
                     "aTestExpectation", "TC_Designer"]

USER_NAME = None

last_module = None

global_number = 0

def get_TS_save_number():
    return global_number

def set_TS_save_number():
    global global_number
    global_number += 1
print(get_used_model())

class TestScriptTask(QThread):
    message_signal = pyqtSignal(str)

    def __init__(self, head_file=None, head_file_prompt=None, **kwargs):
        super().__init__()
        self.head_file = head_file
        self.head_file_prompt = head_file_prompt
        self.kwargs = kwargs
        self.prompt_data_source = None
        self.case_count = 0
        self.errorResponseCase = 0
        self.CAPL_File = os.getcwd()

    def run(self):
        """
        batch task step
        1. initialize()
        2. build data_source
        3. data_source read_all_prompts
        4. send request
        5. handle response
        """
        global last_module
        try:
            self.init_data_source()
            self.read_prompts()
            capl_contents = {}
            for prompt in self.prompt_data_source.all_prompt():
                attach_file_prompt = []
                module = prompt["category"].split("_")[1]
                print(get_used_model())
                if module != last_module:
                    last_module = module
                    #todo 调用zou wen方法 - 需确认是调一次还是跟随这prompt一起发送
                    attach_file = self.head_file.get(module, None)
                    if attach_file is not None:
                        if get_used_model().startswith("gemini"):
                            attach_file_prompt = load_file_to_Part(attach_file, "text/plain")
                            attach_file_prompt.append(self.head_file_prompt)
                            self.handle_request(str(attach_file)+"\n"+self.head_file_prompt)
                            response = get_chat_response(attach_file_prompt)
                            if response.startswith('The session'):
                                last_module = None
                        if module not in capl_contents:
                            capl_contents[module] = []  # 创建空列表
                        capl_contents[module].append(self.handle_response(prompt, response))
                        time.sleep(1)

                    else:
                        self.handle_request("New Module not found headfile")
                self.handle_request(prompt["prompt"])
                response = get_chat_response(prompt["prompt"])

                if module not in capl_contents:
                    capl_contents[module] = []  # 创建空列表
                capl_contents[module].append(self.handle_response(prompt, response))

            for module_name, content in capl_contents.items():  # 遍历字典的键值对
                # print(module_name)
                # print(content)
                self.save_capl_file(content, module_name, self.CAPL_File)

        except Exception as e:
            print(f"An error occurred when doing batch task:{e}")
            print(e.__traceback__.tb_lineno)
            print(e.__traceback__.tb_frame.f_code.co_filename)
            print(e.__traceback__.tb_frame.f_locals)
            print("error:" + str(e))
            self.message_signal.emit("error:" + "Please check that the contents of the form are correct" + str(e))
        self._end()

    def init_data_source(self):
        readErrorResult = self.get_sheet_config()
        if readErrorResult:
            raise Exception("configError:" + str(readErrorResult))
        if self.kwargs.get("task_type") == "single":
            if self.prompt_sheet_name is None:
                raise Exception("configError:" + str(readErrorResult))
            # prompt_sheet_name = self.prompt_sheet_name.replace("TestSpec_", "Prompt_")
            self.prompt_data_source = TestScriptSinglePromptWithoutSavingDataSource(
                prompt_sheet_url=self.test_spec_sheet,
                test_spec_sheet_url=self.test_spec_sheet,
                prompt_sheet_name=self.TS_prompt_sheet_name)
        else:
            self.prompt_data_source = TestScriptPromptDataSource(prompt_sheet_url=self.test_spec_sheet,
                                                                 test_spec_sheet_url=self.test_spec_sheet)

    def get_sheet_config(self):
        case_SpreadsheetId_file = r"./config.ini"
        if os.path.exists(case_SpreadsheetId_file):
            config = configparser.ConfigParser()
            config.read(case_SpreadsheetId_file)
            try:
                self.prompt_sheet = config.get('SheetConfig', 'prompt_sheet')
                self.prompt_sheet_name = config.get('SheetConfig', 'prompt_sheet_name')
                self.test_spec_sheet = config.get('TestSpec_SheetConfig', 'test_spec_sheet')
                self.TS_Dataset_sheet_name = config.get('TestSpec_SheetConfig', 'TS_Dataset_sheet_name')
                self.TS_prompt_sheet_name = config.get('TestSpec_SheetConfig', 'TS_prompt_sheet_name')
                # self.CAPL_File = config.get('TestSpec_SheetConfig', 'CAPL_File', )
                # print(self.CAPL_File)
                if not check_config_spread_sheet_permission(self.prompt_sheet):
                    return "prompt_sheet does not have write permission!!!"
                if not check_config_spread_sheet_permission(self.test_spec_sheet):
                    return "test_spec_sheet does not have write permission!!!"
                return False
            except Exception as e:
                return "config.ini is not valid!\n" + str(e)
        else:
            return "config.ini not found"

    def read_prompts(self):
        """
        prompt is a dict at least contains key=prompt
        prompt format {"prompt": "xxxxx", "":""}
        Returns:
        """
        pass

    def get_datasource(self):
        return self.prompt_data_source

    def handle_request(self, prompt):
        self.message_signal.emit("send:" + prompt)

    def handle_response(self, prompt, response):
        # todo Exception check
        self.message_signal.emit("recv:" + response)
        # self.case_count += self.prompt_data_source.save_result(prompt, response)
        capl_content = self.prompt_data_source.save_result(prompt, response)
        # print("handle_response,capl_content:",capl_content)

        if response.startswith("The session"):
            self.errorResponseCase += 1
        return capl_content

    def save_capl_file(self, file_content, module_name, target_path=None):  # 参数顺序调整
        """
        将文件内容保存到 CAPL 文件夹下的模块文件夹中，并处理历史文件。

        Args:
            file_content: 要保存的文件内容（字符串）。
            module_name: 模块名称（字符串），用作文件名（不包含扩展名）。
            target_path: 目标路径（字符串）。如果为空，则保存到当前软件目录下的 CAPL 文件夹；
                         否则，保存到指定路径下的 CAPL 文件夹。


        Returns:
            True: 保存成功。
            False: 保存失败。
        """
        if target_path:
            if os.path.exists(target_path):
                return self.save_to_capl(file_content, os.path.join(target_path, "CAPL"), module_name)
            else:
                self.message_signal.emit(f"info:目标路径不存在: {target_path}，已将文件保存在软件同路径下")
                current_dir = os.getcwd()
                return self.save_to_capl(file_content, os.path.join(current_dir, "CAPL"), module_name)
        else:
            current_dir = os.getcwd()
        return self.save_to_capl(file_content, os.path.join(current_dir, "CAPL"), module_name)

    # Yuting:保存为.can文件
    def save_to_capl(self, content, folder, module):
        """将文件保存到指定的 CAPL 文件夹下的模块文件夹中，并处理历史文件。"""
        module_folder = os.path.join(folder, module)  # 模块文件夹路径

        if not os.path.exists(module_folder):
            try:
                os.makedirs(module_folder)
                self.message_signal.emit(f"info:已创建模块文件夹: {module_folder}")
            except OSError as e:
                self.message_signal.emit(f"error:创建模块文件夹失败: {e}")
                return False
        else:
            if os.listdir(module_folder):  # 检查模块文件夹是否为空
                history_folder = os.path.join(module_folder, "history")
                try:
                    os.makedirs(history_folder, exist_ok=True)
                    for existing_filename in os.listdir(module_folder):
                        if existing_filename != "history":
                            source_file = os.path.join(module_folder, existing_filename)
                            timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
                            name, ext = os.path.splitext(existing_filename)
                            new_filename = f"{name}_{timestamp}{ext}"
                            destination_file = os.path.join(history_folder, new_filename)
                            shutil.move(source_file, destination_file)
                    self.message_signal.emit(f"info:已将现有文件移动到 history 文件夹: {history_folder}")
                except OSError as e:
                    self.message_signal.emit(f"error:创建 history 文件夹或移动文件失败: {e}")
                    return False

        #  使用模块名作为文件名，并添加 .can 扩展名
        file_path = os.path.join(module_folder, f"{module}.can")

        try:
            with open(file_path, "w", encoding="utf-8") as f:
                 # 确保所有元素都是字符串
                content = [str(item) if item is not None else '' for item in content]
                f.write("\n".join(content))  # 使用换行符连接列表元素
            # self.message_signal.emit(f"info:文件已保存到: {file_path}")
            return True
        except Exception as e:
            # self.message_signal.emit(f"error:保存文件失败: {e}")
            print(content)
            print(e)
            return False

    def get_case_count(self):
        current_case_count = self.case_count
        self.case_count = 0
        return current_case_count

    def _end(self):
        # send back case count and end signal
        self.message_signal.emit("case_count:" + str(self.case_count))
        self.message_signal.emit("end")
        self.message_signal.emit("response_error:" + str(self.errorResponseCase))


class TestScriptPromptDataSource(object):
    message_signal = pyqtSignal(str)

    """
    the datasource class
    responsible for read prompt from the google sheet and saving result
    """

    def __init__(self, prompt_sheet_url, test_spec_sheet_url, **kwargs):
        self.prompt_sheet_url = prompt_sheet_url
        self.test_spec_sheet_url = test_spec_sheet_url

        self.prompts = []
        self.prompt_sheet: SpreadSheet = SpreadSheet(prompt_sheet_url)

        self.sheet_header_cache = dict()
        self.read_prompt()

    def read_prompt(self):
        sheet_names = self.prompt_sheet.get_sheet_names()
        for sheet_name in sheet_names:
            if not sheet_name.startswith("TestSpec_"):
                continue
            try:
                data = self.prompt_sheet.read_by_sheet_name(sheet_name)
                # filter data status == "ready"
                for i, row in enumerate(data):
                    if row.get("Status") == "ready" and row.get("Prompt Design", "").strip() != "":
                        self.prompts.append({"prompt": row.get("Prompt Design"), "row": i + 2, "category": sheet_name})
            except Exception as e:
                print(f"read {sheet_name} prompt error: {e}")
                raise (f"read {sheet_name} prompt error: {e}")

    def all_prompt(self):
        # print("self.prompts", self.prompts)
        return self.prompts

    def save_result(self, prompt, response: str):
        """

        Args:
            prompt:
            response:

        Returns: case count

        """
        row = prompt["row"]
        category = prompt["category"]
        headers = self.prompt_sheet.get_sheet_header(category)
        if response.startswith("The session"):
            return 0
        try:
            # Yuting:parse_test_script提取提取Confidence Score以及Capl脚本
            results = parse_test_script(response)
            # print("response", response)
            # print("results[Confidence Score]",results["Confidence Score"])
            if isinstance(results["Confidence Score"], int) and results["Confidence Score"] < 90:
                # Yuting:直接指定具体列数不适用于所有表格，建议修改为headers.index("***")+1得到索引列
                self.prompt_sheet.update_cell(category, row, headers.index("Status") + 1, "promptReply")
                self.prompt_sheet.update_cell(category, row, headers.index("Prompt Log") + 1, response)
                return 0
            elif isinstance(results["Confidence Score"], int) and results["Confidence Score"] >= 90:
                set_TS_save_number()
                self.prompt_sheet.update_cell(category, row, headers.index("Status") + 1, "generated")
                self.prompt_sheet.update_cell(category, row, headers.index("Prompt Log") + 1, response)
                return results["Capl"]
            else:
                return results["Capl"]
            # print("save_result,results:",results)
        except Exception as e:
            print(f"parse case error: {e}")
            self.prompt_sheet.update_cell(category, row, headers.index("Status") + 1, "genFailed")
            self.prompt_sheet.update_cell(category, row, headers.index("Prompt Log") + 1, response)
            return 0


    def _get_sheet_header(self, sheet_name: str):
        if sheet_name in self.sheet_header_cache:
            return self.sheet_header_cache[sheet_name]
        if sheet_name.startswith("Prompt_"):
            headers = self.prompt_sheet.get_sheet_header(sheet_name)
        elif sheet_name.startswith("TestSpec_"):
            headers = self.test_spec_sheet.get_sheet_header(sheet_name)
        else:
            headers = []
        self.sheet_header_cache[sheet_name] = headers
        return headers

    def _add_test_sheet(self, sheet_name):
        try:
            headers = ["Object Heading", "oReference", "aBug_ID", "aFailed_Req", "oTestLevel", "oReference_Sys",
                       "aBug_ID_Sys", "aFailed_Req_Sys", "aTicket_ID", "TC_Designer", "aObjectType",
                       "Object Short Text",
                       "Object Text", "aTestCondition", "aTestAction", "aTestExpectation", "aTestReaction",
                       "aTestPriority",
                       "adASIL", "aTestEnvironment", "aTestMethod", "aTestType", "sdPlannedFor", "sVariants",
                       "aObjectStatus",
                       "aReviewComments_Test", "aScriptComments", "sCarVariants", "aTestDesignMethod",
                       "aReviewStatus_Test",
                       "aReviewStatus_FO", "aTestExecutionOrder", "aNegativeTest", "aTestMachine", "aTestDate",
                       "aTester",
                       "aVersion", "aTestResult", "aTestResultComment", "aTestLog", "CAPL_Case", "CAPL_Para",
                       "ReqCount",
                       "aTestResultComment_Sys", "AttrChkResult", "notUsed4", "Req", "Case", "CaseCount", "CaseResult",
                       "ReqResult", "APR", "notUsed8", "APR_ID", "APR_Sts"]
            self.test_spec_sheet.create_new_sheet(sheet_name)
            self.test_spec_sheet.add_row(sheet_name, headers)
            return True
        except Exception as e:
            print(f"Create Sheet error: {e}")
            return False


class TestScriptSinglePromptWithoutSavingDataSource(TestScriptPromptDataSource):
    """
    read first prompt but not auto save
    need to be cached at the sending step
    then call the manual_save_result() function to save the result
    """

    def __init__(self, prompt_sheet_url, test_spec_sheet_url, prompt_sheet_name, **kwargs):
        self.prompt_sheet_name = prompt_sheet_name
        super(TestScriptSinglePromptWithoutSavingDataSource, self).__init__(prompt_sheet_url, test_spec_sheet_url,
                                                                            **kwargs)
        self.last_prompt = None

    def read_prompt(self):
        sheet_names = self.prompt_sheet.get_sheet_names()
        if self.prompt_sheet_name in sheet_names:
            data = self.prompt_sheet.read_by_sheet_name(self.prompt_sheet_name)
            # filter data status == "ready"
            for i, row in enumerate(data):
                if row.get("Status") == "ready" and row.get("Prompt Design", "").strip() != "":
                    self.prompts.append(
                        {"prompt": row.get("Prompt Design"), "row": i + 2, "category": self.prompt_sheet_name})
                    break

    def save_result(self, prompt, response: str):
        """
        put the prompt to global
        Args:
            prompt:
            response:

        Returns:

        """
        self.last_prompt = prompt
        row = prompt["row"]
        category = prompt["category"]
        headers = self.prompt_sheet.get_sheet_header(category)
        date_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        if response.startswith("The session"):
            return 0
        try:
            # Yuting:parse_test_script提取提取Confidence Score以及Capl脚本
            results = parse_test_script(response)
            # print("response", response)
            # print("results[Confidence Score]", results["Confidence Score"])
            if isinstance(results["Confidence Score"], int) and results["Confidence Score"] < 90:
                # Yuting:直接指定具体列数不适用于所有表格，建议修改为headers.index("***")+1得到索引列
                self.prompt_sheet.update_cell(category, row, headers.index("Status") + 1, "promptReply")
                self.prompt_sheet.update_cell(category, row, headers.index("Prompt Log") + 1, response)
                return 0
            elif isinstance(results["Confidence Score"], int) and results["Confidence Score"] >= 90:
                set_TS_save_number()
                self.prompt_sheet.update_cell(category, row, headers.index("Status") + 1, "generated")
                self.prompt_sheet.update_cell(category, row, headers.index("Prompt Log") + 1, response)
                return results["Capl"]
            else:
                return results["Capl"]
            # print("save_result,results:",results)
        except Exception as e:
            print(f"parse case error: {e}")
            self.prompt_sheet.update_cell(category, row, headers.index("Status") + 1, "genFailed")
            self.prompt_sheet.update_cell(category, row, headers.index("Prompt Log") + 1, response)

    def manual_save_result(self, response: str):
        if self.last_prompt is not None:
            return super().save_result(self.last_prompt, response)


def get_display_name():
    """获取当前用户的显示名称（全名）。"""
    global USER_NAME
    if USER_NAME is not None:
        return USER_NAME
    try:
        name_format = win32api.NameDisplay
        user_name = win32api.GetUserNameEx(name_format)
        email_address = win32api.GetUserNameEx(win32api.NameUserPrincipal)
        USER_NAME = user_name
        return user_name
    except Exception as e:
        # print(f"获取用户全名失败: {e}")
        user_name = os.getlogin()
        USER_NAME = user_name
        return user_name


if __name__ == '__main__':
    prompt_sheet_url = "1Q_q0rZ_Fk0fvobdRTn7jQ7y_kosNvdr1BXcstKPYwrU"
    test_spec_sheet_url = "1Q_q0rZ_Fk0fvobdRTn7jQ7y_kosNvdr1BXcstKPYwrU"
    TS_HEADFile = {'Diag': ['C:\\AI\\Diag\\API\\autoTest_CamError.txt', 'C:\\AI\\Diag\\API\\autoTest_Diag_API.txt',
                            'C:\\AI\\Diag\\API\\autoTest_Diag_FaultInject.txt',
                            'C:\\AI\\Diag\\API\\autoTest_UPA_API.txt'],
                   'EH': ['C:\\AI\\EH\\API\\autoTest_CamError.txt', 'C:\\AI\\EH\\API\\autoTest_Diag_API.txt',
                          'C:\\AI\\EH\\API\\autoTest_Diag_FaultInject.txt', 'C:\\AI\\EH\\API\\autoTest_UPA_API.txt']}
    TS_HEADFile_Prompt = "Please read the contents of these files and use the file names as the generated CAPL code header files. The file contents can be used as the function interface called by the generated code."
    tc_batch_task = TestScriptTask(TS_HEADFile, TS_HEADFile_Prompt, task_type="single")
    tc_batch_task.run()

