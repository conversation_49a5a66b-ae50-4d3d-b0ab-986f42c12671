module "gcs_buckets" {
  source         = "git::https://github-ri.vnet.valeo.com/GROUP-AI4ALL/ai4all-infra-terraform-modules//modules/storage/bucket?ref=main"
  project_id     = var.project_id
  buckets        = var.buckets
  buckets_config = var.buckets_config
}


# This Folder Used To Store Knowledge Based Cloud Build Logs
resource "google_storage_bucket_object" "vtaf_ai_cloudrunjobs_knowledgebase_source_folder" {
  name    = "${var.vtaf_ai_service_name}-knowledgebase-source/"
  bucket  = "${var.project_id}-vtaf-ai-cloudbuild"
  content = " "

}



# This Folder Used To Store Test Case Generation Cloud Build Logs
resource "google_storage_bucket_object" "vtaf_ai_testcase_generation_source_folder" {
  name    = "${var.vtaf_ai_service_name}-testcase-generation-source/"
  bucket  = "${var.project_id}-vtaf-ai-cloudbuild"
  content = " "

}

resource "google_storage_bucket_object" "vtaf_ai_testcase_generation_job_history_folder" {
  name    = "${var.vtaf_ai_service_name}/job_history/"
  bucket  = "${var.project_id}-vtaf-ai-agents"
  content = " "
}

resource "google_storage_bucket_object" "vtaf_ai_knowledgebase_folder" {
  name    = "${var.vtaf_ai_service_name}/knowledgebase/"
  bucket  = "${var.project_id}-vtaf-ai-agents"
  content = " "
}

resource "google_storage_bucket_object" "vtaf_ai_testcase_generation_user_input_folder" {
  name    = "${var.vtaf_ai_service_name}/user_input/"
  bucket  = "${var.project_id}-vtaf-ai-agents"
  content = " "
}

resource "google_storage_bucket_object" "vtaf_ai_testcase_generation_user_uploads_folder" {
  name    = "${var.vtaf_ai_service_name}/user_uploads/"
  bucket  = "${var.project_id}-vtaf-ai-agents"
  content = " "
}