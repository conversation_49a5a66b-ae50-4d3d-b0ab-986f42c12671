# GCP 对话功能测试指南

## 🎯 概述

这个测试版本专门用于在 Google Cloud 上验证 AI 对话功能，包含详细的测试日志输出。

## 📁 新增文件

1. **`src/main_with_test.py`** - 带对话测试的主程序
2. **`deploy/cloud-run-job-test.yaml`** - 测试版本的 Cloud Run Job 配置
3. **`deploy/deploy-test.sh`** - 测试版本部署脚本

## 🚀 快速开始

### 步骤 1: 部署测试版本

```bash
cd deploy
chmod +x deploy-test.sh
./deploy-test.sh
```

### 步骤 2: 运行对话测试

```bash
# 执行对话测试作业
gcloud run jobs execute vtaf-ai-multiprompt-test --region=us-central1
```

### 步骤 3: 查看测试结果

```bash
# 查看作业状态
gcloud run jobs describe vtaf-ai-multiprompt-test --region=us-central1

# 查看详细日志
gcloud logging read 'resource.type="cloud_run_job" AND resource.labels.job_name="vtaf-ai-multiprompt-test"' --limit=100

# 实时查看日志
gcloud logging tail 'resource.type="cloud_run_job" AND resource.labels.job_name="vtaf-ai-multiprompt-test"'
```

## 🧪 测试内容

### 对话测试项目

1. **基础问候测试**
   - 提示词: "你好！请回复'GCP对话测试成功'"
   - 验证: 基本对话功能

2. **测试用例生成测试**
   - 提示词: "请生成一个汽车功能测试用例，包含以下字段：Test Case ID, Test Objective, Test Condition, Test Action, Test Expectation"
   - 验证: 复杂内容生成能力

3. **简单计算测试**
   - 提示词: "请计算 2+3 等于多少，并简短回答"
   - 验证: 逻辑推理能力

4. **技术问题测试**
   - 提示词: "请用一句话解释什么是 Vertex AI"
   - 验证: 技术知识回答能力

### 测试指标

- ✅ **成功率**: 测试通过的百分比
- ⏱️ **响应时间**: 每个对话的响应时间
- 📊 **响应质量**: 响应内容的长度和相关性
- 🔢 **Token 计数**: 提示词的 Token 使用量

## 📊 预期输出

### 成功的测试输出示例

```
🧪 开始对话功能测试
============================================================
📝 初始化 AI 服务...
✅ AI 服务初始化成功
🔄 开始执行 4 个对话测试...

--- 对话测试 1/4: 基础问候测试 ---
📤 提示词: 你好！请回复'GCP对话测试成功'
✅ 响应成功 (耗时: 2.34秒)
📥 响应长度: 25 字符
📄 响应内容: GCP对话测试成功！很高兴为您服务。
🔢 提示词 Token 数: 12

--- 对话测试 2/4: 测试用例生成测试 ---
📤 提示词: 请生成一个汽车功能测试用例...
✅ 响应成功 (耗时: 4.56秒)
📥 响应长度: 245 字符
📄 响应内容: **Test Case ID**: TC_AUTO_001
**Test Objective**: 验证汽车启动功能
**Test Condition**: 车辆处于停车状态，钥匙在手
**Test Action**: 插入钥匙并转动到启动位置
**Test Expectation**: 发动机成功启动，仪表盘显示正常
🔢 提示词 Token 数: 45

============================================================
📊 对话功能测试结果统计
============================================================
✅ 成功测试: 4/4
📈 成功率: 100.0%
⏱️  平均响应时间: 3.21秒
⏱️  总测试时间: 12.84秒

🎉 对话功能测试通过! (成功率: 100.0%)
```

## 🔧 配置选项

### 环境变量

| 变量名 | 值 | 说明 |
|--------|-----|------|
| `TEST_CONVERSATION_ONLY` | `true` | 仅运行对话测试，不执行批处理 |
| `PROJECT_ID` | `valeo-cp2673-dev` | Google Cloud 项目 ID |
| `REGION` | `us-central1` | 运行区域 |
| `AI_MODEL` | `gemini-1.5-pro` | 使用的 AI 模型 |

### 测试模式

1. **仅对话测试模式** (`TEST_CONVERSATION_ONLY=true`)
   - 只运行对话测试
   - 测试完成后程序退出
   - 适合快速验证对话功能

2. **完整测试模式** (`TEST_CONVERSATION_ONLY=false`)
   - 先运行对话测试
   - 然后执行完整的批处理任务
   - 适合生产环境验证

## 🛠️ 故障排除

### 常见问题

1. **认证失败**
   ```
   Error: Your default credentials were not found
   ```
   **解决方案**: 确保在 GCP 环境中运行，会自动使用默认认证

2. **模型访问失败**
   ```
   Error: Permission denied for Vertex AI
   ```
   **解决方案**: 检查服务账号是否有 Vertex AI 使用权限

3. **超时错误**
   ```
   Error: Request timeout
   ```
   **解决方案**: 增加 `taskTimeout` 值或检查网络连接

### 调试技巧

1. **查看实时日志**:
   ```bash
   gcloud logging tail 'resource.type="cloud_run_job"'
   ```

2. **过滤特定日志**:
   ```bash
   gcloud logging read 'resource.type="cloud_run_job" AND textPayload:"对话测试"'
   ```

3. **检查作业状态**:
   ```bash
   gcloud run jobs executions list --job=vtaf-ai-multiprompt-test --region=us-central1
   ```

## 🎯 成功标准

测试被认为成功当：
- ✅ 成功率 ≥ 75%
- ⏱️ 平均响应时间 < 10秒
- 📄 所有响应都包含有意义的内容
- 🔢 Token 计数功能正常工作

## 📈 下一步

测试成功后，你可以：
1. 使用原始的 `main.py` 运行完整的批处理任务
2. 根据测试结果调整模型参数
3. 部署到生产环境

---

**🎊 现在你可以在 GCP 上直接看到详细的对话测试结果了！**
