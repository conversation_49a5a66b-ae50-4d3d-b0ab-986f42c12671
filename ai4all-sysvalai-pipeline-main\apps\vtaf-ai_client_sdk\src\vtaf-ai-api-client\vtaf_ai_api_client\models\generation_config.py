from collections.abc import Mapping
from typing import Any, TypeVar, cast

from attrs import define as _attrs_define
from attrs import field as _attrs_field

T = TypeVar("T", bound="GenerationConfig")


@_attrs_define
class GenerationConfig:
    """
    Attributes:
        temperature (float):
        top_k (int):
        top_p (float):
        maximum_output_tokens (int):
        candidates_count (int):
        stop_sequences (list[str]):
    """

    temperature: float
    top_k: int
    top_p: float
    maximum_output_tokens: int
    candidates_count: int
    stop_sequences: list[str]
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        temperature = self.temperature

        top_k = self.top_k

        top_p = self.top_p

        maximum_output_tokens = self.maximum_output_tokens

        candidates_count = self.candidates_count

        stop_sequences = self.stop_sequences

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "temperature": temperature,
                "top_k": top_k,
                "top_p": top_p,
                "maximum_output_tokens": maximum_output_tokens,
                "candidates_count": candidates_count,
                "stop_sequences": stop_sequences,
            }
        )

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        d = dict(src_dict)
        temperature = d.pop("temperature")

        top_k = d.pop("top_k")

        top_p = d.pop("top_p")

        maximum_output_tokens = d.pop("maximum_output_tokens")

        candidates_count = d.pop("candidates_count")

        stop_sequences = cast(list[str], d.pop("stop_sequences"))

        generation_config = cls(
            temperature=temperature,
            top_k=top_k,
            top_p=top_p,
            maximum_output_tokens=maximum_output_tokens,
            candidates_count=candidates_count,
            stop_sequences=stop_sequences,
        )

        generation_config.additional_properties = d
        return generation_config

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
