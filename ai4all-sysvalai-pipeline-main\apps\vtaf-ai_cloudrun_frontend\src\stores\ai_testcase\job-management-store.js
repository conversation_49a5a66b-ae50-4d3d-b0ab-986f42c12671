import { ref } from "vue";
import { defineStore } from "pinia";
import { Notify, Loading } from "quasar";
import { ai_testcaseApi } from "src/boot/axios";

export const useJobManagementStore = defineStore("job_management_store", () => {
  const user_email = ref("");

  const current_job_execution_id = ref("");
  const current_job_execution_id_status = ref(null);

  const all_history = ref([]);
  const current_history = ref([]);
  const job_status_loading = ref(false);
  const current_history_table_loading = ref(false);
  const tc_history_table_loading = ref(false);

  async function get_user_email() {
    try {
      const response = await ai_testcaseApi.get(`/user/get_user`);
      if (response.status === 200) {
        // Success
        const data = response.data.data;
        user_email.value = data;
        console.log(data);
      } else {
        handleError(response);
      }
    } catch (err) {
      console.log(err);
    }
  }

  async function get_job_status() {
    try {
      job_status_loading.value = true;
      const response = await ai_testcaseApi.get(
        `/job/get_job_status/${current_job_execution_id.value}`
      );
      if (response.status == 200) {
        current_job_execution_id_status.value = response.data;
      } else {
        handleError(response);
      }
    } catch (err) {
      console.log(err);
    } finally {
      job_status_loading.value = false;
    }
  }

  async function get_all_job_history() {
    try {
      tc_history_table_loading.value = true;
      console.log("email:", user_email.value);

      const response = await ai_testcaseApi.get(
        `/job/get_all_job_history/${user_email.value}`
      );
      if (response.status == 200) {
        console.log(response.data);
        all_history.value = response.data;
      } else {
        handleError(response);
      }
    } catch (err) {
      console.log(err);
    } finally {
      tc_history_table_loading.value = false;
    }
  }

  async function get_current_jobs() {
    try {
      current_history_table_loading.value = true;
      console.log("email:", user_email.value);

      const response = await ai_testcaseApi.get(
        `/job/get_current_jobs/${user_email.value}`
      );
      if (response.status == 200) {
        console.log(response.data);
        current_history.value = response.data;
      } else {
        handleError(response);
      }
    } catch (err) {
      console.log(err);
    } finally {
      current_history_table_loading.value = false;
    }
  }

  return {
    user_email,
    current_job_execution_id,
    current_job_execution_id_status,
    all_history,
    current_history,
    job_status_loading,
    current_history_table_loading,
    tc_history_table_loading,
    get_user_email,
    get_job_status,
    get_all_job_history,
    get_current_jobs,
  };
});
