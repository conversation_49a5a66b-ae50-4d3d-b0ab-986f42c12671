system_understanding_task:
  description: >
    Conduct a thorough analysis of the complete {topic} {feature},
    including hardware components, subsystems, and overall architecture. 
    Identify potential areas for {feature} testing.
  expected_output: >
    A comprehensive document outlining the {feature} system's architecture, 
    {sensors}, {algorithms} and the integration points between hardware and software.
  agent: system_test_engineer

requirements_review_task:
  description: >
    Review the {feature} requirements documentation to identify testable conditions for {requirement}. 
    Clarify ambiguous or incomplete requirements with the Requirement Engineer.
  expected_output: >
    A list of identified requirements with their testable conditions. Any unclear or ambiguous requirements should be flagged for clarification.
  agent: system_test_engineer

test_case_draft_task:
  description: >
    Develop detailed test cases based on the analyzed requirement {requirement} to ensure comprehensive coverage of the {feature} system. The test cases must cover all aspects of {feature}, including {sensors} and {algorithms} (hardware, software, integration). Include positive, negative, and edge case scenarios for System Test Bench validation.
  expected_output: >
    Test cases that cover all possible scenarios in maximum of 3 testcases per requirement.
    Ensure that Precondition, Test Procedure, Reset in the test action, and Test Expectation
    are numbered sequentially starting from 1.
  agent: system_test_engineer

test_case_writing_task:
  description: >
    Create comprehensive test cases based on the analyzed requirement {requirement} to ensure full coverage of the {feature} system. The test cases should address all components of {feature}, including {sensors} and {algorithms} (hardware, software, integration). Ensure the inclusion of positive, negative, and edge case scenarios for System Test Bench validation.
  expected_output: >
    Test cases that cover all possible scenarios in maximum of 3 testcases per requirement.
    Ensure that Precondition, Test Procedure, Reset in the test action, and Test Expectation
    are numbered sequentially starting from 1.
  agent: system_test_engineer

requirement_clarification_task:
  description: >
    Resolve queries raised by the System Test Engineer regarding unclear or ambiguous requirements about {feature}.
    Provide detailed explanations or request clarifications from stakeholders as needed.
  expected_output: >
    A document outlining the clarified requirements or additional information provided to resolve the queries.
  agent: requirement_engineer

quality_assurance_task:
  description: >
    Ensure all {feature} system components meet the required quality standards by performing thorough testing. 
    Review the testcase coverage for the requirement {requirement} & quality of the testase.
  expected_output: >
    Review the testcase coverage for the requirement {requirement} & quality of the testase and provide feedback for {feature} system.
  agent: quality_assurance_engineer