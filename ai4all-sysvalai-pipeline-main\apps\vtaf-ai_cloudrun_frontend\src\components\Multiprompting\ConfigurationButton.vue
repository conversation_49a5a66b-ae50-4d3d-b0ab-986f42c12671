<template>
  <div>
    <q-btn label="Change Configuration" class="q-ma-md bg-white" style="text-transform: none;" size="sm" @click="openDialog" />

        <q-dialog v-model="showDialog" persistent>
      <q-card class="q-pa-md" style="min-width: 500px; max-width: 90vw; border-radius: 16px;">
        <q-card-section>
          <div class="text-h6 text-primary">Edit Configuration</div>
        </q-card-section>

        <q-separator />

        <q-card-section class="q-ma-sm">
          <div class="q-gutter-md column">
            <div v-for="(value, key) in form" :key="key" class="row items-center q-col-gutter-md q-mt-md">
              <div class="col-5 text-right text-weight-medium text-capitalize">
                {{ key.replace(/_/g, ' ') }}:
              </div>
              <div class="col-7">
                <q-input dense outlined v-model="form[key]" />
              </div>
            </div>
          </div>
        </q-card-section>

        <q-separator />

        <q-card-actions align="right" class="q-gutter-sm">
          <q-btn flat label="Cancel" color="negative" v-close-popup />
          <q-btn flat label="Save" color="primary" @click="saveConfiguration" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const showDialog = ref(false)

const configurations = ref({
  prompt_sheet: '',
  dataset_sheet_name: '',
  prompt_sheet_name: '',
  test_spec_sheet: '',
  ts_dataset_sheet_name: '',
  ts_prompt_sheet_name: ''
})

const form = ref({ ...configurations.value })

function openDialog() {
  form.value = { ...configurations.value }
  showDialog.value = true
}

function saveConfiguration() {
  configurations.value = { ...form.value }
  showDialog.value = false
  console.log('Saved config:', configurations.value)
}
</script>
