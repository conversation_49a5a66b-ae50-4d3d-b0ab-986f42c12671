#!/bin/bash
# Google Cloud 部署脚本

set -e

# 配置变量
PROJECT_ID="valeo-cp2673-dev"
REGION="us-central1"
SERVICE_NAME="vtaf-ai-multiprompt"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

echo "🚀 开始部署 VTAF AI Multiprompt 到 Google Cloud"
echo "项目: ${PROJECT_ID}"
echo "区域: ${REGION}"
echo "镜像: ${IMAGE_NAME}"

# 检查是否已登录 Google Cloud
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "❌ 请先登录 Google Cloud: gcloud auth login"
    exit 1
fi

# 设置项目
echo "📝 设置 Google Cloud 项目..."
gcloud config set project ${PROJECT_ID}

# 启用必要的 API
echo "🔧 启用必要的 Google Cloud API..."
gcloud services enable \
    cloudbuild.googleapis.com \
    run.googleapis.com \
    aiplatform.googleapis.com \
    sheets.googleapis.com \
    drive.googleapis.com

# 构建 Docker 镜像
echo "🔨 构建 Docker 镜像..."
cd ..
gcloud builds submit --tag ${IMAGE_NAME} .

# 部署到 Cloud Run Jobs
echo "🚀 部署到 Cloud Run Jobs..."
gcloud run jobs replace deploy/cloud-run-job.yaml \
    --region=${REGION}

echo "✅ 部署完成!"
echo ""
echo "📋 后续步骤:"
echo "1. 更新 deploy/cloud-run-job.yaml 中的环境变量"
echo "2. 设置 Google Sheets URL 和其他配置"
echo "3. 运行作业:"
echo "   gcloud run jobs execute ${SERVICE_NAME}-job --region=${REGION}"
echo ""
echo "📊 查看作业状态:"
echo "   gcloud run jobs describe ${SERVICE_NAME}-job --region=${REGION}"
echo ""
echo "📝 查看日志:"
echo "   gcloud logging read 'resource.type=\"cloud_run_job\"' --limit=50"
