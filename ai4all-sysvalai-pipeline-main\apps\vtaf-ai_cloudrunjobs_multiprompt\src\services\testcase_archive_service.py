#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Case Archive Service - 测试用例归档服务
对应原始ai_generate.py中的do_tc_archive()方法
处理测试用例的归档操作
"""

import os
from typing import Dict, Any, List
from itertools import zip_longest
from utils.logger import get_logger
from services.sheet_manager import SheetManager
from services.config_service import ConfigurationService
from utils.data_processing import parse_test_cases

logger = get_logger(__name__)

# 目标表格的表头
TARGET_HEADERS = ["Object Heading", "oReference_Sys", "Object Text", "aTestCondition", "aTestAction", 
                  "aTestExpectation", "TC_Designer", "aTestResultComment_Sys", "AttrChkResult", "notUsed4", 
                  "Req", "Case", "CaseCount", "CaseResult", "ReqResult", "APR", "notUsed8", "APR_ID", "APR_Sts"]

# 源字段列表，与 case 列表中的数据顺序对应
SOURCE_FIELDS = ["Object Heading", "oReference_Sys", "Object Text", "aTestCondition", "aTestAction", 
                 "aTestExpectation", "TC_Designer"]

class TestCaseArchiveService:
    """测试用例归档服务"""
    
    def __init__(self):
        self.sheet_manager = SheetManager()
        self.config_service = ConfigurationService()
        self.case_count = 0
        
    def execute(self) -> int:
        """执行测试用例归档任务"""
        try:
            logger.info("🚀 Starting test case archive job")
            
            # 获取配置
            config = self._get_config()
            self._validate_config(config)
            
            # 连接到源表格
            if not self.sheet_manager.connect_sheet(config['prompt_sheet']):
                raise Exception("Failed to connect to prompt sheet")
            
            # 连接到目标表格
            target_sheet = SheetManager()
            if not target_sheet.connect_sheet(config['case_sheet']):
                raise Exception("Failed to connect to case sheet")
            
            # 处理归档
            self._process_archive(config, target_sheet)
            
            logger.info(f"✅ Test case archive completed. Archived {self.case_count} test cases")
            return 0
            
        except Exception as e:
            logger.exception(f"Test case archive job failed: {e}")
            return 1
    
    def _get_config(self) -> Dict[str, Any]:
        """获取配置"""
        config = {}
        
        # 从环境变量获取
        config['prompt_sheet'] = os.getenv('PROMPT_SHEET_URL')
        config['case_sheet'] = os.getenv('CASE_SHEET_URL')
        config['prompt_sheet_name'] = os.getenv('PROMPT_SHEET_NAME', 'Prompt_Archive')
        config['case_sheet_name'] = os.getenv('CASE_SHEET_NAME', 'TestCases')
        
        # 如果环境变量中没有配置，尝试从配置文件读取
        if not config['prompt_sheet'] or not config['case_sheet']:
            try:
                if self.config_service.config_exists():
                    sheet_config = self.config_service.get_sheet_config_for_batch_task()
                    if 'error' not in sheet_config:
                        config['prompt_sheet'] = sheet_config.get('prompt_sheet')
                        config['case_sheet'] = sheet_config.get('test_spec_sheet')  # 使用test_spec_sheet作为case_sheet
                        logger.info("Configuration loaded from config.ini")
            except Exception as e:
                logger.error(f"Failed to read config file: {e}")
        
        return config
    
    def _validate_config(self, config: Dict[str, Any]):
        """验证配置"""
        required_fields = ['prompt_sheet', 'case_sheet', 'prompt_sheet_name', 'case_sheet_name']
        missing_fields = []
        
        for field in required_fields:
            if not config.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            raise ValueError(f"Missing required configuration for archive: {missing_fields}")
        
        logger.info("Archive configuration validation passed")
    
    def _process_archive(self, config: Dict[str, Any], target_sheet: SheetManager):
        """处理归档操作"""
        prompt_sheet_name = config['prompt_sheet_name']
        case_sheet_name = config['case_sheet_name']
        
        # 读取源数据
        data = self.sheet_manager.read_by_sheet_name(prompt_sheet_name)
        
        row_index = 1  # 行索引，从1开始，因为第一行是表头
        for row in data:
            status = row.get("Status")  # 获取Status列
            if status == "ToArchive":
                prompt_log = row.get("Prompt Log")
                if prompt_log:
                    self._handle_prompt_log(prompt_log, target_sheet, case_sheet_name)
                # 更新状态为 "generated"
                self.sheet_manager.update_cell(prompt_sheet_name, row_index + 1, 6, "generated")
            row_index += 1
    
    def _handle_prompt_log(self, prompt_log: str, target_sheet: SheetManager, case_sheet_name: str):
        """处理提示词日志，解析并保存测试用例"""
        try:
            results = parse_test_cases(prompt_log)
            cases = results.get("Test Cases", [])
            
            for case in cases:
                # 添加TC_Designer
                case.append(self._get_user_name())
                
                # 创建目标行数据
                row_to_add = [""] * len(TARGET_HEADERS)
                
                for source_header, value in zip_longest(SOURCE_FIELDS, case, fillvalue=""):
                    try:
                        target_index = TARGET_HEADERS.index(source_header)
                        row_to_add[target_index] = value
                    except ValueError:
                        logger.warning(f"Source header '{source_header}' not found in TARGET_HEADERS")
                
                # 添加到目标表格
                target_sheet.add_row(case_sheet_name, row_to_add)
                self.case_count += 1
                
        except Exception as e:
            logger.error(f"Failed to handle prompt log: {e}")
    
    def _get_user_name(self) -> str:
        """获取用户名"""
        # 尝试从环境变量获取
        user_name = os.getenv('USER_NAME')
        if user_name:
            return user_name
        
        # 尝试从系统获取
        try:
            import getpass
            return getpass.getuser()
        except:
            return "CloudRunUser"
