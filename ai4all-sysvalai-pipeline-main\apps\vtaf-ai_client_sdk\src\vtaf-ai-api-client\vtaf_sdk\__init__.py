import httpx

from vtaf_ai_api_client.client import Client
from .agents import AgentsAPI
from .chunking import ChunkingAPI
from .generations import GenerationsAPI
from .google_auth import GoogleIDTokenAuth
from .parsing import ParsingAPI
from .vector_db import VectorDBAPI


class VTAFClient:
    def __init__(self, base_url: str, audience: str, timeout: int = 60):
        client = Client(base_url=base_url)
        auth = GoogleIDTokenAuth(audience=audience)
        httpx_client = httpx.Client(base_url=base_url, auth=auth, timeout=httpx.Timeout(timeout))
        client._client = httpx_client

        self.client = client
        self.parsing = ParsingAPI(self.client)
        self.chunking = ChunkingAPI(self.client)
        self.generations = GenerationsAPI(self.client)
        self.agents = AgentsAPI(self.client)
        self.vector_db = VectorDBAPI(self.client)
