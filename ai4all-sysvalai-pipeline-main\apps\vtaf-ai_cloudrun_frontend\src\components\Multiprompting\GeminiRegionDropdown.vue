<template>
  <div class="q-mt-md q-ml-md q-mr-md">
    <div class="row items-center q-gutter-sm">
      <div style="min-width: 60px;">Region</div>
    <q-select
      v-model="selectedRegion"
      :options="regionOptions"
      outlined
      dense
      emit-value
      map-options
      style="flex: 1;"
      bg-color="white"
    />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const selectedRegion = ref('Iowa (us-central1)');
const regionOptions = [
  'Iowa (us-central1)',
  'Belgium (europe-west1)',
  'Singapore (asia-southeast1)',
  // Add more regions as needed
];
</script>
