import { ref } from "vue";
import { defineStore } from "pinia";
import { Notify, Loading } from "quasar";
import { chat_with_kb } from "src/boot/axios";

export const useChatWithKnowledgebaseStore = defineStore("use_chat_with_knowledgebase_store", () => {
  const user_email = ref("");
  const user_name = ref("");
  const new_chat = ref(true);
  const recentChats = ref([])
  const olderChats = ref([])
  const recent_chats = ref({});
  const delete_session_id = ref("");
  const current_session_id = ref("");
  const current_session_details = ref([]);
  const current_session_title = ref("");
  const projects_variants_dropdown = ref([]);
  const projects_dropdown_loading = ref(false);
  const selected_project_variant = ref("");
  const selected_project_variant_uuid = ref("");
  const prompt_input = ref("");
  const prompt_response = ref("");

  const isConversationLoading = ref(false);
  const isChatLoading = ref(false);
  const errorDialogMessage = ref("");
  const errorDialogVisible = ref(false);


  async function get_user_email() {
    try {
      Loading.show();
      console.log("starting");

      const response = await chat_with_kb.get(`/user/get_user`);
      if (response.status === 200) {
        // Success
        const data = response.data.data;
        user_email.value = data;
        let splitted_user_email = user_email.value.split(".");
        let first_name = splitted_user_email[0].charAt(0).toUpperCase()+splitted_user_email[0].slice(1);
        user_name.value = first_name + " " +splitted_user_email[1].toUpperCase()
        console.log(data);
        Loading.hide();
      } else {
        Loading.hide();
      }
    } catch (err) {
      console.log(err);
      Loading.hide();
    }
  }

  async function jwt_token(){
    try {
      const response = await chat_with_kb.get(`/with_knowledgebase/jwt_token`);
      if (response.status === 200) {
        // Success
        console.log(response.data)
      } else {

      }
    } catch (err) {
      console.log(err);
    }
  }

  async function get_projects() {
    try {
      projects_dropdown_loading.value = true;
      const response = await chat_with_kb.get(`/with_knowledgebase/projects`);
      if (response.status === 200) {
        // Success
        const data = response.data;
        console.log(data);
        projects_variants_dropdown.value = data;
        projects_dropdown_loading.value = false;
      } else {
        projects_dropdown_loading.value = false;
      }
    } catch (err) {
      console.log(err);
      projects_dropdown_loading.value = false;
    }
  }

  async function get_chats(){
    try {
      const formData =  new FormData();
      formData.append("email", user_email.value);
      const response = await chat_with_kb.post(`/with_knowledgebase/get_chats`, formData);
      if (response.status === 200) {
        // Success
        recent_chats.value = response.data;
      } else {

      }
    } catch (err) {
      console.log(err);
    }
  }

  async function delete_chat(){
    try {
      const formData =  new FormData();
      formData.append("email", user_email.value);
      formData.append("session_id", delete_session_id.value);
      const response = await chat_with_kb.post(`/with_knowledgebase/delete_session`, formData);
      if (response.status === 200) {
        // Success
        console.log("Session Deleted : ", response.data.message);
        Notify.create({
          color: "positive",
          position: "top",
          message: `${response.data.message}`,
          icon: "check_circle",
        });
      }
    } catch (err) {
      console.log(err);
    }
  }

  async function get_session_conversation(){
    try {
      const formData =  new FormData();
      formData.append("email", user_email.value);
      formData.append("session_id", current_session_id.value)
      const response = await chat_with_kb.post(`/with_knowledgebase/get_session`, formData);
      if (response.data.status === "success") {
        // Success
        console.log(response);

        current_session_details.value = response.data.data;
        selected_project_variant_uuid.value = response.data.variant_id;
        selected_project_variant.value = response.data.project;
      } else {
        handleError(response.data);
      }
    } catch (err) {
      console.log(err);
    }
  }

    async function create_session(){
    try {
      const formData =  new FormData();
      formData.append("email", user_email.value);
      formData.append("title", selected_project_variant.value);
      formData.append("variant_id", selected_project_variant_uuid.value);
      const response = await chat_with_kb.post(`/with_knowledgebase/create_session`, formData);
      if (response.status === 200) {
        // Success
        console.log(response);

        current_session_id.value = response.data.session_id;
      } else {

      }
    } catch (err) {
      console.log(err);
    }
  }

  async function get_chat_response(query){
    try {
      const formData =  new FormData();
      formData.append("email", user_email.value);
      formData.append("new_chat", new_chat.value);
      formData.append("session_id", current_session_id.value);
      formData.append("query", query);
      formData.append("projects_to_search", selected_project_variant_uuid.value)
      const response = await chat_with_kb.post(`/with_knowledgebase/conversation`, formData);
      if (response.data.status === "success") {
        // Success
        console.log(response);
        prompt_response.value = response.data.ai_response;
        current_session_title.value = response.data.title;
      } else {
        console.log("error");

        handleError(response.data)
      }
    } catch (err) {
      console.log(err);
    }
  }

  function handleError(error) {
    console.log(error)
    errorDialogMessage.value = `Error:\n${error.detail}`;
    errorDialogVisible.value = true;
  }


  return {
    user_email,
    user_name,
    new_chat,
    recentChats,
    olderChats,
    recent_chats,
    delete_session_id,
    current_session_id,
    current_session_details,
    current_session_title,
    projects_variants_dropdown,
    projects_dropdown_loading,
    selected_project_variant,
    selected_project_variant_uuid,
    prompt_input,
    prompt_response,
    isConversationLoading,
    isChatLoading,
    errorDialogMessage,
    errorDialogVisible,
    get_user_email,
    jwt_token,
    get_projects,
    get_chats,
    delete_chat,
    create_session,
    get_session_conversation,
    get_chat_response
  };
});
