import re

# 修改后的字段列表
fields = [
    "Test Case ID", "Covered Requirement ID", "Test Objective", "Test Condition", "Test Action",
    "Test Expectation"
]

def parse_test_cases(text):
    """
    """
    results = {}  # 使用字典存储结果

    # 1. 首先提取 Confidence Score
    confidence_matches = re.findall(r"Confidence Score:\s*(?:\*+)?\s*(\d+)%", text)  # 添加(?:\*+)?
    results["Confidence Score"] = confidence_matches[0] if confidence_matches else ""

    # 2. 使用 Test Case ID 分割文本 (这部分代码保持不变)
    case_texts = re.findall(
        r"(?i)(?:\*+|#+|:+|：+)*(?:\s?)(?:\*+|#+|:+|：+)*\s*Test Case ID\s?:.*?(?=(?:\*+|#+|:+|：+)*(?:\s?)(?:\*+|#+|:+|：+)*\s*Test Case ID\s?:|\Z)",
        text, re.DOTALL)

    # 3. 解析每个测试用例
    cases = []
    for case_text in case_texts:
        case_data = parse_single_case(case_text)  # 解析其他字段
        # case_data_list = [case_data.get(field, '') for field in fields[:-1]] # 排除 Confidence Score
        case_data_list = [case_data.get(field, '') for field in fields[:]] 
        cases.append(case_data_list)
    results["Test Cases"] = cases  # 将测试用例列表存储到 results 字典中


    return results # 返回字典

# Yuting:提取Confidence Score以及Capl脚本
def parse_test_script(text):
    results = {}  # 初始化一个空字典来存储结果

    try:
        # 1. 提取置信度分数
        # 使用正则表达式查找 "Confidence Score:" 后面的数字百分比
        # (?:\*+)? 用于匹配可选的星号
        # confidence_matches = re.findall(r"Confidence Score:\s*(?:\*+)?\s*(\d+)%", text)
        confidence_matches1 = re.search(r"confidence level is\s*\**(\d+)%", text)  # 改进正则表达式
        confidence_matches2 = re.findall(r"Confidence Score:\s*(?:\*+)?\s*(\d+)%", text)  # 添加(?:\*+)?
        if confidence_matches1:
            results["Confidence Score"] = confidence_matches1[0] if confidence_matches1 else ""
        else:
            # 如果找到匹配项，则将第一个匹配项转换为整数并存储在字典中;否则，存储 None
            results["Confidence Score"] = int(confidence_matches2[0]) if confidence_matches2 else ""

        # 2. 提取 CAPL 脚本
        # 使用正则表达式查找以 "```capl" 开头和 "```" 结尾的 CAPL 脚本
        # re.DOTALL 允许 "." 匹配换行符
        capl_script_match = re.search(r"```capl\n(.*?)```", text, re.DOTALL)

        # 如果找到匹配项，则提取脚本内容并存储在字典中;否则，存储 None
        results["Capl"] = capl_script_match.group(1) if capl_script_match else None

    except (IndexError, AttributeError) as e:  # 处理潜在的错误，例如找不到匹配项
        print(f"解析测试脚本时出错: {e}")  # 打印错误消息
        return {}  # 出错时返回空字典

    return results  # 返回包含结果的字典


def parse_single_case(text):
    """
    解析单条测试用例文本，将其转换为一个字典。

    Args:
        text (str): 单条测试用例文本。

    Returns:
        dict: 解析后的测试用例数据，以字段名为键。
    """
    case_data = {}
    current_field = None

    for line in text.splitlines():
        line = line.strip()
        if not line:
            if current_field is not None:
                case_data[current_field] += "\n"
            continue

        match = None
        for field in fields:
            # 使用正则表达式进行模糊匹配，忽略大小写，并可选匹配冒号
            match = re.match(rf"(?i)(?:\*+|#+|:+|：+)*(?:\s?)(?:\*+|#+|:+|：+)*\s*{field}s?:?\s*(.*)", line)
            if match:
                # 如果遇到新的字段，并且当前字段不是 Test Expectation，则保存当前字段内容
                if current_field is not None and current_field != "Test Expectation":
                    case_data[current_field] = clean_field_value(case_data[current_field])

                current_field = field
                case_data[current_field] = match.group(1).strip().replace('*', '')
                break
        else:
            if current_field is not None:
                case_data[current_field] += " " + line.replace('*', '')

    # 处理最后一个字段
    if current_field is not None:
        case_data[current_field] = clean_field_value(case_data[current_field])

    # 逐个处理 Test Condition/Test Action/Test Expectation，在序号前加换行
    for field in ("Test Condition", "Test Action", "Test Expectation"):
        if field in case_data:
            case_data[field] = re.sub(r'(?<!\n)(\d+\.)', r'\n\1', case_data[field])
        # 处理 Covered Requirement ID，将逗号替换为换行符
    if "Covered Requirement ID" in case_data:
        case_data["Covered Requirement ID"] = case_data["Covered Requirement ID"].replace(",", "\n").replace("，", "\n")

    # 处理 Test Expectation 字段的无效内容
    if "Test Expectation" in case_data:
        test_expectation = case_data["Test Expectation"]
        test_expectation = re.sub(r"\n\s*(?!(\d+\.|$))[^$]*", "", test_expectation, flags=re.MULTILINE)
        case_data["Test Expectation"] = test_expectation  # 更新字典

    # 不再需要为缺失的字段填充空字符串，因为已经在构建 case_data_list 时处理了

    return case_data

def clean_field_value(value):
    """
    清理字段值，包括：
    - 移除开头和结尾的空白字符
    - 将连续的空白字符替换为单个空格
    - 将逗号和顿号替换为换行符
    """
    value = value.strip()
    # value = re.sub(r'\s+', ' ', value)
    # value = value.replace(",", "\n").replace("，", "\n")

    # if any(field in value for field in ("Test Condition", "Test Action", "Test Expectation")):
    #     value = re.sub(r'(?<!\n)(\d+\.)', r'\n\1', value)
    #
    # if "Test Expectation" in value:
    #     value = re.sub(r"\n\s*(?!\d+\.)(.*)", "", value)

    return value

def print_test_cases(results):
    print(f"Confidence Score: {results.get('Confidence Score', '')}") # 打印Confidence Score
    print("-" * 20)
    for case_data in results.get("Test Cases", []):
        for i, field_value in enumerate(case_data):
            print(f"{fields[i]}: {field_value}")
        print()

# 测试代码
if __name__ == "__main__":
    with open("test_cases.txt", "r", encoding="utf-8") as f:
        text = f.read()
    results = parse_test_script(text)
    # print(cases)
    # print_test_cases(results)
    print(results['Capl'])
    print(results['Confidence Score'])