# Use the official lightweight Node.js 12 image.
# https://hub.docker.com/_/node
FROM node:16 as buildenv

# Create and change to the app directory.
WORKDIR /app
# Copy application dependency manifests to the container image.
# A wildcard is used to ensure both package.json AND package-lock.json are copied.
# Copying this separately prevents re-running npm install on every code change.
COPY package*.json ./

# Install production dependencies.
COPY . ./
RUN npm install
RUN npm install -g @quasar/cli
RUN quasar build

FROM nginx as production-stage
RUN mkdir /app
COPY --from=buildenv /app/dist/spa /usr/share/nginx/html
RUN mkdir -p /usr/share/nginx/html/src
COPY --from=buildenv /app/src /usr/share/nginx/html/src
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
# Run the web service on container startup.
#CMD [ "quasar", "dev" "-p" "8080"]
