#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <AUTHOR> <PERSON>
# @Mail   : <EMAIL>
# @Time   : 2025/03/11 16:42:43
# @Version: $Id$

# Please install OpenAI SDK first: `pip3 install openai`

import os,base64
from openai import OpenAI,AuthenticationError,APIError
device_name = os.environ['COMPUTERNAME']
deepseek_model_list = []
if device_name.startswith("WUH17"):
    # print(device_name)
    try:
        client = OpenAI(api_key="sk-501a1af9eaf247b09958c332bb753926", base_url="http://*************:3000/api")

        allmodel = client.models.list()
        # print(len(allmodel.data))
        for model in allmodel.data:
            # print(model.id)
            deepseek_model_list.append(model.id)
            # if not model.name.startswith("deepseek"):
            #     print(model.info)
    except AuthenticationError as e:
        print("Authentication Error"+str(e))
    except APIError:
        print("API Error")

#===========================Chat==================================
ALL_messages=[
        # {"role": "system", "content": "You are a helpful assistant"},
        # {"role": "user", "content": "Hello"},
    ]

def ds_update_system_instruction(system_instruction):
    ALL_messages.append({"role": "system", "content": system_instruction})

def get_response(model, prompt):
    ALL_messages.append({"role": "user", "content": prompt})
    response = client.chat.completions.create(
        model=model,
        messages=ALL_messages,
        stream=False
    )
    ALL_messages.append(response.choices[0].message)
    # print(response)
    return response.choices[0].message.content+"\n"+str(response.usage.approximate_total)

def ds_chat(model, prompt):
    return get_response(model, prompt)

def load_file(file_path,file_type,prompt):
    with open(file_path, "rb") as file:
        file_param = (file_path.split('\\')[-1],file.read(),"text/plain")
        upload_file_resp = client.files.create(file=file_param,purpose='assistants')
        # print(upload_file_resp)
        # print(client.files.retrieve(file_id=upload_file_resp.id))
        # print(client.files.content(file_id=upload_file_resp.id))
        # print(client.files.retrieve_content(file_id=upload_file_resp.id))
        # print(client.files.delete(file_id=upload_file_resp.id))
        file_id = upload_file_resp.id
        print(f"File uploaded successfully. File ID: {file_id}")

    # content = []
    # encoded_file = base64.b64encode(
    #                 open(file_path, "rb").read()).decode("utf-8")
    # content.append({"text":prompt,"type":file_type})
    # # content.append({"image_url":{"url":encoded_file},"type":"image_url"})
    # content.append({"text_url":{"url":encoded_file},"type":"text_url"})
    # # print(content)
    # return content

# Round 1
# ALL_messages.append({"role": "user", "content": "Hello"})
# response = get_response("deepseek-r1:70b","法雷奥是做什么的")
# print(f"Messages Round 1: {response}")

# # Round 2
# ALL_messages.append({"role": "user", "content": "What is the second?"})
# response = get_response("deepseek-r1:70b",ALL_messages)
# print(f"Messages Round 2: {response}")

# Round 3
local_file = r"C:\AI\Diag\API\autoTest_CamError.txt"
load_file1 = r"C:\AI\Diag\API\autoTest_Diag_FaultInject.txt"
load_file2 = r"C:\AI\Diag\API\autoTest_UPA_API.txt"

# ALL_messages.append({"role": "user", "content": load_file(local_file,"text/plain","这个文件中的代码有什么功能?")})
# response = get_response("deepseek-r1:70b",ALL_messages)
# print(f"Messages Round 3: {response}")

#===========================Request==================================

import requests

def upload_file(token, file_path):
    url = 'http://*************:3000/api/v1/files/'
    headers = {
        'Authorization': f'Bearer {token}',
        'Accept': 'application/json'
    }
    files = {'file': open(file_path, 'rb')}
    response = requests.post(url, headers=headers, files=files)
    return response.json()

def chat_with_file(token, model, query, file_id):
    url = 'http://*************:3000/api/chat/completions'
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    payload = {
        'model': model,
        # 'messages': [{'role': 'user', 'content': query}],
        'messages': query,
        'files': [{'type': 'file', 'id': file_id}]
    }
    response = requests.post(url, headers=headers, json=payload)
    return response.json()


def get_response_with_file(model, prompt, local_file):
    key = "sk-501a1af9eaf247b09958c332bb753926"
    resp = upload_file(token=key, file_path=local_file)
    # print(resp)
    ALL_messages.append({"role": "user", "content": prompt})
    result = chat_with_file(key, model, ALL_messages, resp["id"])
    # print(result)
    ALL_messages.append(result["choices"][0]["message"])
    # print(result["choices"][0]["message"]["content"])
    # print(result["usage"]["approximate_total"])
    return result["choices"][0]["message"]["content"]+'\n'+str(result["usage"]["approximate_total"])



# url = "http://*************:3000/api/chat/completions"
# headers = {
#     "Authorization": "Bearer sk-501a1af9eaf247b09958c332bb753926",
#     "Content-Type": "application/json"
# }
# data = {
#     "model": "deepseek-r1:70b",
#     "messages": [
#         {
#             "role": "user",
#             "content": "Why is the sky blue?"
#         }
#     ]
# }

# response = requests.post(url, headers=headers, json=data)
# print(response.json())
