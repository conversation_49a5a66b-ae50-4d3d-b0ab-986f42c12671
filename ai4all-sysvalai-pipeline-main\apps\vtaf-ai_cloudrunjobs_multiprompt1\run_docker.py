#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Docker 运行脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def check_docker():
    """检查 Docker 是否可用"""
    try:
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Docker 可用: {result.stdout.strip()}")
            return True
        else:
            print("❌ Docker 不可用")
            return False
    except FileNotFoundError:
        print("❌ Docker 未安装")
        return False

def check_docker_compose():
    """检查 Docker Compose 是否可用"""
    try:
        result = subprocess.run(['docker-compose', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Docker Compose 可用: {result.stdout.strip()}")
            return True
        else:
            # 尝试 docker compose (新版本)
            result = subprocess.run(['docker', 'compose', 'version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Docker Compose 可用: {result.stdout.strip()}")
                return True
            else:
                print("❌ Docker Compose 不可用")
                return False
    except FileNotFoundError:
        print("❌ Docker Compose 未安装")
        return False

def check_files():
    """检查必要文件"""
    required_files = [
        'Dockerfile',
        'docker-compose.yml',
        '.env.docker',
        'src/requirements.txt'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
        else:
            print(f"✅ 找到文件: {file}")
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    return True

def setup_environment():
    """设置环境"""
    print("📝 设置 Docker 环境...")
    
    # 检查 .env.docker 文件
    env_file = Path('.env.docker')
    if env_file.exists():
        print("⚠️  请编辑 .env.docker 文件，填入正确的配置信息:")
        print("   - PROJECT_ID: 你的 Google Cloud 项目 ID")
        print("   - PROJECT_NUMBER: 你的项目编号")
        print("   - DEEPSEEK_API_KEY: DeepSeek API 密钥")
        print("   - 其他必要配置...")
    
    # 检查认证文件
    auth_files = ['src/client_secret.json', 'src/service-account.json']
    auth_found = False
    
    for auth_file in auth_files:
        if Path(auth_file).exists():
            print(f"✅ 找到认证文件: {auth_file}")
            auth_found = True
            break
    
    if not auth_found:
        print("⚠️  请将 Google Cloud 认证文件放入 src/ 目录:")
        print("   - client_secret.json (OAuth 认证)")
        print("   - service-account.json (服务账号认证)")
    
    # 检查配置文件
    config_file = Path('src/config.ini')
    if config_file.exists():
        print(f"✅ 找到配置文件: {config_file}")
    else:
        print("⚠️  建议创建 src/config.ini 文件配置 Google Sheets 信息")

def build_image():
    """构建 Docker 镜像"""
    print("🔨 构建 Docker 镜像...")
    
    try:
        result = subprocess.run([
            'docker', 'build', '-t', 'vtaf-ai-multiprompt', '.'
        ], check=True)
        
        print("✅ Docker 镜像构建成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Docker 镜像构建失败: {e}")
        return False

def run_test_mode():
    """运行测试模式"""
    print("🧪 启动测试模式...")
    
    try:
        subprocess.run([
            'docker-compose', '--profile', 'test', 'up', 'vtaf-ai-multiprompt-test'
        ], check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 测试模式运行失败: {e}")

def run_batch_mode():
    """运行批处理模式"""
    print("🚀 启动批处理模式...")
    
    try:
        subprocess.run([
            'docker-compose', 'up', 'vtaf-ai-multiprompt'
        ], check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 批处理模式运行失败: {e}")

def show_logs():
    """显示日志"""
    print("📋 显示容器日志...")
    
    try:
        subprocess.run([
            'docker-compose', 'logs', '-f'
        ], check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 显示日志失败: {e}")

def stop_containers():
    """停止容器"""
    print("🛑 停止容器...")
    
    try:
        subprocess.run([
            'docker-compose', 'down'
        ], check=True)
        
        print("✅ 容器已停止")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 停止容器失败: {e}")

def main():
    """主函数"""
    print("🐳 VTAF AI Multiprompt Docker 运行工具")
    print("=" * 50)
    
    # 检查环境
    if not check_docker() or not check_docker_compose():
        print("请先安装 Docker 和 Docker Compose")
        return
    
    if not check_files():
        print("请确保所有必要文件存在")
        return
    
    setup_environment()
    
    while True:
        print("\n请选择操作:")
        print("1. 构建 Docker 镜像")
        print("2. 运行测试模式")
        print("3. 运行批处理模式")
        print("4. 显示日志")
        print("5. 停止容器")
        print("6. 退出")
        
        choice = input("请输入选择 (1-6): ").strip()
        
        if choice == '1':
            build_image()
            
        elif choice == '2':
            run_test_mode()
            
        elif choice == '3':
            run_batch_mode()
            
        elif choice == '4':
            show_logs()
            
        elif choice == '5':
            stop_containers()
            
        elif choice == '6':
            print("再见!")
            break
            
        else:
            print("无效选择，请重新输入")

if __name__ == '__main__':
    main()
