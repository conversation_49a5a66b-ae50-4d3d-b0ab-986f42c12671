#!/bin/bash
# 部署对话测试版本到 Google Cloud

set -e

PROJECT_ID="valeo-cp2673-dev"
REGION="us-central1"
SERVICE_NAME="vtaf-ai-multiprompt"
TEST_JOB_NAME="vtaf-ai-multiprompt-test"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

echo "🧪 部署 VTAF AI Multiprompt 对话测试版本"
echo "项目: ${PROJECT_ID}"
echo "区域: ${REGION}"
echo "测试作业: ${TEST_JOB_NAME}"

# 检查是否已登录 Google Cloud
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "❌ 请先登录 Google Cloud: gcloud auth login"
    exit 1
fi

# 设置项目
echo "📝 设置 Google Cloud 项目..."
gcloud config set project ${PROJECT_ID}

# 构建 Docker 镜像（包含测试版本）
echo "🔨 构建包含对话测试的镜像..."
cd ..
gcloud builds submit --tag ${IMAGE_NAME}:latest .

# 部署测试版本的 Cloud Run Job
echo "🚀 部署对话测试版本..."
gcloud run jobs replace deploy/cloud-run-job-test.yaml \
    --region=${REGION}

echo "✅ 对话测试版本部署完成!"
echo ""
echo "🧪 运行对话测试:"
echo "gcloud run jobs execute ${TEST_JOB_NAME} --region=${REGION}"
echo ""
echo "📊 查看测试结果:"
echo "gcloud run jobs describe ${TEST_JOB_NAME} --region=${REGION}"
echo ""
echo "📝 查看详细日志:"
echo "gcloud logging read 'resource.type=\"cloud_run_job\" AND resource.labels.job_name=\"${TEST_JOB_NAME}\"' --limit=100"
echo ""
echo "📝 实时查看日志:"
echo "gcloud logging tail 'resource.type=\"cloud_run_job\" AND resource.labels.job_name=\"${TEST_JOB_NAME}\"'"
