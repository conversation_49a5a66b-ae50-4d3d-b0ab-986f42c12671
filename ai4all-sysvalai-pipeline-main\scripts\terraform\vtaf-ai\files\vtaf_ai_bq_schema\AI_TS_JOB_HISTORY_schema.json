[{"mode": "NULLABLE", "name": "user_email", "type": "STRING", "description": "user_email"}, {"mode": "NULLABLE", "name": "timestamp", "type": "TIMESTAMP", "description": "timestamp"}, {"mode": "NULLABLE", "name": "job_start_time", "type": "TIMESTAMP", "description": "job_start_time"}, {"mode": "NULLABLE", "name": "job_end_time", "type": "TIMESTAMP", "description": "job_end_time"}, {"mode": "NULLABLE", "name": "job_duration", "type": "STRING", "description": "job_duration"}, {"mode": "NULLABLE", "name": "status", "type": "STRING", "description": "status"}, {"mode": "NULLABLE", "name": "execution_status", "type": "STRING", "description": "execution_status"}, {"mode": "NULLABLE", "name": "user_upload", "type": "STRING", "description": "user_upload"}, {"mode": "NULLABLE", "name": "result_file_id", "type": "STRING", "description": "result_file_id"}, {"mode": "NULLABLE", "name": "total_token", "type": "INT64", "description": "total_token"}, {"mode": "NULLABLE", "name": "prompt_token", "type": "INT64", "description": "prompt_token"}, {"mode": "NULLABLE", "name": "cached_prompt_token", "type": "INT64", "description": "cached_prompt_token"}, {"mode": "NULLABLE", "name": "completion_token", "type": "INT64", "description": "completion_token"}, {"mode": "NULLABLE", "name": "successfull_agent_request", "type": "INT64", "description": "successfull_agent_request"}, {"mode": "NULLABLE", "name": "execution_id", "type": "STRING", "description": "execution_id"}, {"mode": "NULLABLE", "name": "llm_model", "type": "STRING", "description": "llm_model"}, {"mode": "NULLABLE", "name": "llm_location", "type": "STRING", "description": "llm_location"}]