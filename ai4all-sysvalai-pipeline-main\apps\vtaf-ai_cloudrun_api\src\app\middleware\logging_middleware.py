import time
from google.cloud import logging as gcp_logging
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request

# Set up Google Cloud Logging client
logging_client = gcp_logging.Client()
logger = logging_client.logger('ai4all-logs-proto-v1')

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)

    def get_iap_email(self, request: Request):
      
        email = request.headers.get('x-goog-authenticated-user-email', 'unknown')
        return email.split(':')[-1] if email != 'unknown' else 'unknown'

    async def dispatch(self, request: Request, call_next):
      
        start_time = time.time()       
        api_url = str(request.url)
        view_name = request.url.path or 'unknown'          
        referer_url = request.headers.get('Referer', 'unknown')
        email = self.get_iap_email(request)       
        response = await call_next(request)      
        end_time = time.time()
        duration = end_time - start_time

        # Log the data
        log_entry = {
            'email': email,
            'referer_url': referer_url,
            'api_url': api_url, 
            'start_time': time.strftime('%Y-%m-%d %H:%M:%S', time.gmtime(start_time)),
            'end_time': time.strftime('%Y-%m-%d %H:%M:%S', time.gmtime(end_time)),
            'duration_seconds': duration,
            'response_status': response.status_code
        }

        # Write to Google Cloud Logging
        logger.log_struct(log_entry, severity='INFO')
        # print(log_entry)

        return response