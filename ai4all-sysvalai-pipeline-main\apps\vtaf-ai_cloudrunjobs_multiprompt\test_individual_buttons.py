#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试三个独立的日志按钮环境变量功能
EXECUTE_SAVE_LOG, EXECUTE_LOAD_LOG, EXECUTE_CLEAR_LOGS
"""

import os
import sys
import json
import time
from datetime import datetime
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

def test_save_log_button():
    """测试Save Log按钮环境变量"""
    print("🧪 测试EXECUTE_SAVE_LOG环境变量")
    print("=" * 60)
    
    try:
        # 设置环境变量
        os.environ['EXECUTE_SAVE_LOG'] = 'true'
        os.environ['SKIP_CONVERSATION_TEST'] = 'true'
        os.environ['EXECUTE_DEFAULT_BUTTONS'] = 'false'
        
        # 可选：设置自定义日志数据
        custom_log_data = {
            "test_type": "save_log_button_test",
            "conversation_id": f"test_save_{int(time.time())}",
            "session_start": datetime.now().isoformat(),
            "conversations": [
                {
                    "user": "测试Save Log按钮功能",
                    "assistant": "这是通过EXECUTE_SAVE_LOG环境变量触发的保存操作",
                    "timestamp": datetime.now().isoformat()
                }
            ],
            "metadata": {
                "source": "test_individual_buttons.py",
                "button": "Save Log",
                "test_mode": True
            }
        }
        os.environ['LOG_DATA'] = json.dumps(custom_log_data, ensure_ascii=False, indent=2)
        
        # 导入并执行main函数
        from main import main
        result = main()
        
        if result == 0:
            print("✅ Save Log按钮测试成功！")
            return True
        else:
            print("❌ Save Log按钮测试失败！")
            return False
            
    except Exception as e:
        print(f"❌ Save Log按钮测试异常: {e}")
        return False
    finally:
        # 清理环境变量
        for key in ['EXECUTE_SAVE_LOG', 'LOG_DATA', 'SKIP_CONVERSATION_TEST', 'EXECUTE_DEFAULT_BUTTONS']:
            if key in os.environ:
                del os.environ[key]

def test_load_log_button():
    """测试Load Log按钮环境变量"""
    print("\n🧪 测试EXECUTE_LOAD_LOG环境变量")
    print("=" * 60)
    
    try:
        # 设置环境变量
        os.environ['EXECUTE_LOAD_LOG'] = 'true'
        os.environ['SKIP_CONVERSATION_TEST'] = 'true'
        os.environ['EXECUTE_DEFAULT_BUTTONS'] = 'false'
        
        # 可选：设置特定的日志文件名
        # os.environ['LOG_FILENAME'] = 'specific_log_file.json'
        
        # 导入并执行main函数
        from main import main
        result = main()
        
        if result == 0:
            print("✅ Load Log按钮测试成功！")
            return True
        else:
            print("❌ Load Log按钮测试失败！")
            return False
            
    except Exception as e:
        print(f"❌ Load Log按钮测试异常: {e}")
        return False
    finally:
        # 清理环境变量
        for key in ['EXECUTE_LOAD_LOG', 'LOG_FILENAME', 'SKIP_CONVERSATION_TEST', 'EXECUTE_DEFAULT_BUTTONS']:
            if key in os.environ:
                del os.environ[key]

def test_clear_logs_button():
    """测试Clear Logs按钮环境变量"""
    print("\n🧪 测试EXECUTE_CLEAR_LOGS环境变量")
    print("=" * 60)
    
    # 先确认是否要清除日志
    try:
        from services.log_service import LogService
        log_service = LogService()
        
        if os.path.exists(log_service.log_dir):
            log_files = [f for f in os.listdir(log_service.log_dir) if f.endswith('.json')]
            if log_files:
                print(f"⚠️ 发现 {len(log_files)} 个日志文件将被清除")
                confirm = input("确定要继续测试Clear Logs功能吗？(y/n): ").strip().lower()
                if confirm not in ['y', 'yes', '是']:
                    print("❌ 用户取消Clear Logs测试")
                    return False
            else:
                print("📭 没有日志文件需要清除")
        else:
            print("📁 日志目录不存在")
    except Exception as e:
        print(f"⚠️ 检查日志文件时出错: {e}")
    
    try:
        # 设置环境变量
        os.environ['EXECUTE_CLEAR_LOGS'] = 'true'
        os.environ['SKIP_CONVERSATION_TEST'] = 'true'
        os.environ['EXECUTE_DEFAULT_BUTTONS'] = 'false'
        
        # 导入并执行main函数
        from main import main
        result = main()
        
        if result == 0:
            print("✅ Clear Logs按钮测试成功！")
            return True
        else:
            print("❌ Clear Logs按钮测试失败！")
            return False
            
    except Exception as e:
        print(f"❌ Clear Logs按钮测试异常: {e}")
        return False
    finally:
        # 清理环境变量
        for key in ['EXECUTE_CLEAR_LOGS', 'SKIP_CONVERSATION_TEST', 'EXECUTE_DEFAULT_BUTTONS']:
            if key in os.environ:
                del os.environ[key]

def test_multiple_buttons():
    """测试同时设置多个按钮环境变量（应该按优先级执行）"""
    print("\n🧪 测试同时设置多个按钮环境变量")
    print("=" * 60)
    print("注意：按照代码逻辑，优先级为 Save Log > Load Log > Clear Logs")
    
    try:
        # 同时设置多个环境变量
        os.environ['EXECUTE_SAVE_LOG'] = 'true'
        os.environ['EXECUTE_LOAD_LOG'] = 'true'
        os.environ['EXECUTE_CLEAR_LOGS'] = 'true'
        os.environ['SKIP_CONVERSATION_TEST'] = 'true'
        os.environ['EXECUTE_DEFAULT_BUTTONS'] = 'false'
        
        # 设置Save Log的数据
        multi_test_data = {
            "test_type": "multiple_buttons_test",
            "message": "测试同时设置多个按钮环境变量",
            "expected_behavior": "应该只执行Save Log功能",
            "timestamp": datetime.now().isoformat()
        }
        os.environ['LOG_DATA'] = json.dumps(multi_test_data, ensure_ascii=False)
        
        # 导入并执行main函数
        from main import main
        result = main()
        
        if result == 0:
            print("✅ 多按钮测试成功！（应该只执行了Save Log功能）")
            return True
        else:
            print("❌ 多按钮测试失败！")
            return False
            
    except Exception as e:
        print(f"❌ 多按钮测试异常: {e}")
        return False
    finally:
        # 清理环境变量
        for key in ['EXECUTE_SAVE_LOG', 'EXECUTE_LOAD_LOG', 'EXECUTE_CLEAR_LOGS', 
                   'LOG_DATA', 'SKIP_CONVERSATION_TEST', 'EXECUTE_DEFAULT_BUTTONS']:
            if key in os.environ:
                del os.environ[key]

def interactive_menu():
    """交互式菜单"""
    print("\n" + "="*80)
    print("🎯 独立日志按钮环境变量测试菜单")
    print("="*80)
    print("1. 🔧 测试 EXECUTE_SAVE_LOG 环境变量")
    print("2. 📖 测试 EXECUTE_LOAD_LOG 环境变量")
    print("3. 🗑️ 测试 EXECUTE_CLEAR_LOGS 环境变量")
    print("4. 🔄 测试同时设置多个环境变量")
    print("5. 🧪 运行所有测试")
    print("0. 🚪 退出")
    print("="*80)
    
    while True:
        try:
            choice = input("\n请选择操作 (0-5): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                test_save_log_button()
            elif choice == '2':
                test_load_log_button()
            elif choice == '3':
                test_clear_logs_button()
            elif choice == '4':
                test_multiple_buttons()
            elif choice == '5':
                run_all_tests()
            else:
                print("❌ 无效选择，请输入 0-5")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 操作异常: {e}")

def run_all_tests():
    """运行所有测试"""
    print("\n🚀 运行所有独立按钮测试")
    print("=" * 80)
    
    tests = [
        ("Save Log按钮", test_save_log_button),
        ("Load Log按钮", test_load_log_button),
        ("多按钮优先级", test_multiple_buttons),
        # Clear Logs测试需要用户确认，所以不包含在自动测试中
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔧 执行测试: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    # 统计结果
    print("\n" + "="*80)
    print("📊 测试结果汇总")
    print("="*80)
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            success_count += 1
    
    total_tests = len(results)
    success_rate = (success_count / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"\n总计: {success_count}/{total_tests} 测试通过")
    print(f"成功率: {success_rate:.1f}%")

if __name__ == '__main__':
    print("🚀 独立日志按钮环境变量功能测试")
    print("=" * 80)
    print("这个脚本测试三个独立的环境变量：")
    print("• EXECUTE_SAVE_LOG - 触发Save Log按钮功能")
    print("• EXECUTE_LOAD_LOG - 触发Load Log按钮功能")
    print("• EXECUTE_CLEAR_LOGS - 触发Clear Logs按钮功能")
    print("=" * 80)
    
    # 启动交互式菜单
    interactive_menu()
