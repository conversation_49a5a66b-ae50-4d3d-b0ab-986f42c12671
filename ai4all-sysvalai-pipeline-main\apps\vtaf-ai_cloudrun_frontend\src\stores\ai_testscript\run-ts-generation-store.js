import { ref } from "vue";
import { defineStore } from "pinia";
import { Notify, Loading } from "quasar";
import { ai_testcaseApi } from "src/boot/axios";

export const useRunTsGenerationStore = defineStore("run_ts_geneation", () => {
  const user_email = ref("");
  const spreadsheetLink = ref('');
  const accessCheckResult = ref(null);
  const extractedSheetId = ref('');
  const current_history = ref([]);
  const current_history_table_loading = ref(false);
  const showSuccessBanner = ref(false)
  const upload_file_loading = ref(false)

  const errorDialogMessage = ref("");
  const errorDialogVisible = ref(false);

  async function get_user_email() {
    try {
      const response = await ai_testcaseApi.get(`/user/get_user`);
      if (response.status === 200) {
        // Success
        const data = response.data.data;
        user_email.value = data;
        console.log(data);
      } else {
        handleError(response);
      }
    } catch (err) {
      console.log(err);
    }
  }

  async function get_current_jobs() {
    try {
      current_history_table_loading.value = true;
      console.log("email:", user_email.value);

      const response = await ai_testcaseApi.get(
        `/run_ts/get_current_jobs/${user_email.value}`
      );
      if (response.status == 200) {
        console.log(response.data);
        current_history.value = response.data;
      } else {
        handleError(response);
      }
    } catch (err) {
      console.log(err);
    } finally {
      current_history_table_loading.value = false;
    }
  }

  async function send_sheet() {
    upload_file_loading.value = true;
     try {
      const formData = new FormData();
      formData.append("user_email", user_email.value);
      formData.append("sheet_link", spreadsheetLink.value);
      const response = await ai_testcaseApi.post("/run_ts/generate_ts", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      if (response.status === 200 && response.data.message === "success") {
        showSuccessBanner.value = true
        setTimeout(() => {
          showSuccessBanner.value = false
        }, 10000) // Hide banner after 4 seconds
        spreadsheetLink.value = "";
      } else {
        handleError(response)
      }
    } catch (error) {
      Notify.create({
        color: "negative",
        position: "bottom",
        message: error.message,
        icon: "report_problem",
      });
    } finally {
      upload_file_loading.value = false;
    }
  }

  return {
    user_email,
    spreadsheetLink,
    accessCheckResult,
    extractedSheetId,
    current_history,
    current_history_table_loading,
    showSuccessBanner,
    upload_file_loading,
    errorDialogMessage,
    errorDialogVisible,
    get_user_email,
    send_sheet,
    get_current_jobs
  };
});
