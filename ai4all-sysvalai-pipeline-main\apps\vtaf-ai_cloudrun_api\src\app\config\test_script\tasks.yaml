read_keyword_definitions:
  description: >
    Read the VTAF keyword list from the provided Excel file and extract definitions.
    Make the keywords available for subsequent automation test case generation tasks.
  expected_output: >
    A structured list of keyword definitions including Keyword Syntax, Parameters, and Descriptions.
  agent: automation_test_engineer

generate_precondition_steps:
  description: >
    Analyze the {TestCases} Test Steps and generate the 'Precondition' section for the automation test case using the VTAF keywords.
    Use the format {Keyword(Param=Value)} for each keyword.
    Each step should begin with PC_X. (e.g., PC_1., PC_2., etc.) and be placed on a new line.
    Inline comments using '//' can be included as needed.
    Example:
      PC_1. {Vosstrex_Interface_LoadScene(Scene="Circular_Road")} // Load Scene
  expected_output: >
    A newline-separated string of precondition steps formatted as PC_1., PC_2., etc.
  agent: automation_test_engineer

generate_test_procedure_steps:
  description: >
    Analyze the {TestCases} Test Steps and generate the 'TestProcedure' section using VTAF keywords.
    Only include core execution steps.
    Use the format {Keyword(Param=Value)} for each keyword.
    Each step should begin with TP_X. (e.g., TP_1., TP_2., etc.) and be placed on a new line.
    Example:
      TP_1. {Vosstrex_Interface_StartScene()} // Start Scene
  expected_output: >
    A newline-separated string of test procedure steps formatted as TP_1., TP_2., etc.
  agent: automation_test_engineer

generate_reset_steps:
  description: >
    Analyze the {TestCases} Test Steps and generate the 'Reset' section using VTAF keywords.
    These steps should revert the system to its original or default state.
    Use the format {Keyword(Param=Value)} for each keyword.
    Each step should begin with RST_X. (e.g., RST_1., RST_2., etc.) and be placed on a new line.
    Example:
      RST_1. {Vosstrex_Interface_StopScene()} // Stop Scene
  expected_output: >
    A newline-separated string of reset steps formatted as RST_1., RST_2., etc.
  agent: automation_test_engineer

generate_test_expectations:
  description: >
    Analyze the {TestCases} Test Steps and generate the 'sAutomationExpectation' section using VTAF keywords.
    Define expected outcomes for each test procedure step (TP_X).
    Use the format {Keyword(Param=Value)}.
    Each expectation should begin with eTP_X. (e.g., eTP_1., eTP_2., etc.) and be placed on a new line.
    Do not repeat the corresponding TP steps.
    Example:
      eTP_3. {Check_CANoe_SysVar(Name="Gear", Condition="X==1")} // Check Gear is in Drive
  expected_output: >
    A newline-separated string of expectation steps formatted as eTP_1., eTP_2., etc.
  agent: automation_test_engineer

generate_test_notes:
  description: >
    Provide relevant notes or considerations related to the test case.
    These may include assumptions, setup requirements, or other important context.
  expected_output: >
    A sentence or paragraph summarizing key notes for test execution.
  agent: automation_test_engineer

compile_final_testcase_json:
  description: >
    Combine the previously generated Precondition, TestProcedure, Reset, sAutomationExpectation, and Notes into a complete JSON object.
    
    Your response must be a valid JSON object directly parsable with `json.loads()` in Python.
    Follow these rules strictly:
    - Use double quotes for all keys and string values.
    - Escape all inner quotes (e.g., \") inside strings.
    - Ensure all parentheses (), brackets {}, and quotes are balanced.
    - The output must be directly parseable using `json.loads()` in Python.
    
    The output will be parsed programmatically. Malformed JSON will cause failure.
  expected_output: >
    A valid JSON object with the structure below:
       {
         "testcase": {
           "sAutomationSteps": {
             "Precondition": "PC_1. ...\\nPC_2. ...",
             "TestProcedure": "TP_1. ...\\nTP_2. ...",
             "Reset": "RST_1. ...\\nRST_2. ..."
           },
           "sAutomationExpectation": "eTP_1. ...\\neTP_2. ...",
           "TestType": "Automated",
           "Notes": "Additional execution-related notes."
         }
       }
  agent: automation_test_engineer
