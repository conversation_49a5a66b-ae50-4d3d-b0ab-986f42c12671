import { ref } from "vue";
import { defineStore } from "pinia";
import { Notify, Loading } from "quasar";
import { chatA<PERSON> } from "src/boot/axios";
import MarkdownIt from "markdown-it";

const md = new MarkdownIt({
  html: true, // Allow HTML inside Markdown
  linkify: true, // Convert links to clickable
  typographer: true, // Smart punctuation
});

export const useChatStore = defineStore("chat_store", () => {
  const user_email = ref("dev.valeo.com");
  const gemini_version = ref("gemini-2.0-flash-001");
  const backup_prompt = ref("");
  const prompt = ref("");
  const gemini_version_loading = ref(false);
  const prompt_response = ref("");
  const uploaded_file = ref(null);
  const file_name = ref("");
  const file_type = ref("");
  const prompt_loading = ref(false);
  const prompt_banner_loading = ref(false);
  const temperature = ref(1);
  const output_tokens = ref(8192);
  const selectedRegion = ref("us-central1");
  const chat_type = ref("single");
  const chat_hisory_clear = ref(true);
  const chat_history = ref([]);
  const regions = ref([
    {
      label: "Americas",
      regions: [
        { label: "us-central1 (lowa)", value: "us-central1" },
        { label: "us-east1 (south Carolina)", value: "us-east1" },
        { label: "us-east4 (Norther Virginia)", value: "us-east4" },
        { label: "us-east5 (Columbus)", value: "us-east5" },
        { label: "us-south1 (Dallas)", value: "us-south1" },
        { label: "us-west1 (Oregon)", value: "us-west1" },
        { label: "us-west4 (Las Vegas)", value: "us-west4" },
      ],
    },
    {
      label: "Asia Pacific",
      regions: [
        { label: "asia-east1 (Taiwan)", value: "asia-east1" },
        { label: "asia-east2 (Hong Knog)", value: "asia-east2" },
        { label: "asia-south1 (Mumbai)", value: "asia-south1" },
      ],
    },
    {
      label: "Europe",
      regions: [
        { label: "europe-central2 (Warsaw)", value: "europe-central2" },
        { label: "europe-north1 (Finland)", value: "europe-north1" },
        { label: "europe-southwest1 (Madrid)", value: "europe-southwest1" },
        { label: "europe-west1 (Belgium)", value: "europe-west1" },
        { label: "europe-west2 (London)", value: "europe-west2" },
        { label: "europe-west3 (Frankfurt)", value: "europe-west3" },
        { label: "europe-west4 (Netherlands)", value: "europe-west4" },
        { label: "europe-west6 (Zurich)", value: "europe-west6" },
        { label: "europe-west8 (Milan)", value: "europe-west8" },
        { label: "europe-west9 (Paris)", value: "europe-west9" },
      ],
    },
    {
      label: "Middle-East",
      regions: [
        { label: "me-central1 (Doha)", value: "me-central1" },
        { label: "me-central2 (Dammam)", value: "me-central2" },
        { label: "me-west1 (Tel Aviv)", value: "me-west1" },
      ],
    },
  ]);

  async function get_user_email() {
    try {
      Loading.show();
      const response = await chatApi.get(`/get_user`);
      const data = response.data.data;
      user_email.value = data;
      console.log(data);
    } catch (err) {
      handleFetchError(err);
    } finally {
      Loading.hide();
    }
  }

  // function to get response for the prompt
  async function getPromptResponse() {
    try {
      // Create a new FormData object
      const formData = new FormData();

      // Append the fields to the FormData object
      formData.append("gemini_version", gemini_version.value);
      formData.append("user_prompt", backup_prompt.value);
      formData.append("temperature", temperature.value);
      formData.append("output_tokens", output_tokens.value);
      formData.append("region", selectedRegion.value);

      // Ensure 'uploaded_file.value' is a File object
      if (uploaded_file.value) {
        formData.append("file_input", uploaded_file.value); // Add the file input
        formData.append("file_name", file_name.value);
        formData.append("file_type", file_type.value);
      }

      const response = await chatApi.post("/query", formData, {
        headers: {
          "Content-Type": "multipart/form-data", // Set content type for FormData
        },
      });
      console.log(response.data.response);
      prompt_response.value = md.render(response.data.response);
      console.log(prompt_response.value);
      prompt_banner_loading.value = false;
      prompt_loading.value = false;
    } catch (err) {
      // Handle the error here
      console.error("Error Getting Response:", err);
      Notify.create({
        type: "negative",
        message: "Failed to change model!",
      });
    } finally {
      prompt_banner_loading.value = false;
      prompt_loading.value = false;
      console.log("finished");
    }
  }

  async function getPromptResponseStream() {
    try {
      // Create a new FormData object
      const formData = new FormData();

      // Append the fields to the FormData object
      formData.append("gemini_version", gemini_version.value);
      formData.append("user_prompt", backup_prompt.value);

      // Ensure 'uploaded_file.value' is a File object
      if (uploaded_file.value) {
        formData.append("file_input", uploaded_file.value); // Add the file input
        formData.append("file_name", file_name.value);
        formData.append("file_type", file_type.value);
      }

      const response = await fetch(
        process.env.CHAT_API_URL+"/stream",
        // "http://127.0.0.1:3000/chat/stream",
        {
          method: "POST",
          body: formData,
        }
      );
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      // Process the stream as it's received
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        // Decode and append new content to the response box
        const chunk = decoder.decode(value, { stream: true });
        console.log(chunk);
        if (!chunk) {
          return;
        }
        prompt_response.value += md.render(chunk);
      }
      // prompt_response.value = response.data.response;
      console.log(prompt_response.value);
      prompt_banner_loading.value = false;
      prompt_loading.value = false;
    } catch (err) {
      // Handle the error here
      console.error("Error Getting Response:", err);
      Notify.create({
        type: "negative",
        message: "Failed to change model!",
      });
    } finally {
      prompt_banner_loading.value = false;
      prompt_loading.value = false;
      console.log("finished");
    }
  }
  async function getMultiChatResponse() {
    try {
      // Create a new FormData object
      const formData = new FormData();
      // Append the fields to the FormData object
      formData.append("gemini_version", gemini_version.value);
      formData.append("user_prompt", backup_prompt.value);
      formData.append("temperature", temperature.value);
      formData.append("output_tokens", output_tokens.value);
      formData.append("region", selectedRegion.value);
      formData.append("clear", chat_hisory_clear.value);
      prompt.value = "";
      const response = await fetch(
        process.env.CHAT_API_URL+"/multichat1",
        // "http://127.0.0.1:3000/chat/multichat1",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            gemini_version: gemini_version.value,
            user_prompt: backup_prompt.value,
            temperature: temperature.value,
            output_tokens: output_tokens.value,
            region: selectedRegion.value,
            clear: chat_hisory_clear.value,
          }),
        }
      );

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      const { done, value } = await reader.read();
      const response_object = decoder.decode(value);
      console.log(response_object);

      const parsed_data = JSON.parse(response_object);
      console.log(parsed_data);

      prompt_response.value = md.render(parsed_data.response);
      console.log(prompt_response.value);
      chat_hisory_clear.value = false;
      prompt_banner_loading.value = false;
      prompt_loading.value = false;
      // prompt_response.value = response.data.response;
    } catch (err) {
      // Handle the error here
      console.error("Error Getting Response:", err);
      Notify.create({
        type: "negative",
        message: "Failed to change model!",
      });
    } finally {
      console.log("finished");
    }
  }

  function handleFetchError(err) {
    if (err.response) {
      const errorMessage = err.response
        ? err.response.data.detail
        : err.message;
      const errorStatus = err.response.status;
      Notify.create({
        color: "negative",
        position: "bottom",
        message: `${errorStatus} : ${errorMessage}`,
        icon: "report_problem",
      });
    } else {
      Notify.create({
        color: "negative",
        position: "bottom",
        message: err.message,
        icon: "report_problem",
      });
    }
  }
  return {
    user_email,
    gemini_version,
    backup_prompt,
    prompt,
    gemini_version_loading,
    prompt_response,
    uploaded_file,
    file_name,
    file_type,
    prompt_loading,
    prompt_banner_loading,
    temperature,
    output_tokens,
    selectedRegion,
    chat_type,
    chat_hisory_clear,
    regions,
    chat_history,
    get_user_email,
    getPromptResponse,
    getPromptResponseStream,
    getMultiChatResponse,
  };
});
