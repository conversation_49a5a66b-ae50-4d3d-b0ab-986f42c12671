import os
import configparser
from typing import List, Dict, Any
from datetime import datetime
from services.ai_service import AIService
from services.sheet_manager import SheetManager
from services.config_service import ConfigurationService
from utils.data_processing import parse_test_cases
from utils.logger import get_logger

logger = get_logger(__name__)

# 测试用例头部索引
CASE_HEADER_INDEX = ["Object Heading", "oReference_Sys", "Object Text", "aTestCondition", "aTestAction", "aTestExpectation", "TC_Designer"]

def get_display_name():
    """获取当前用户显示名称"""
    return os.getenv('USER', os.getenv('USERNAME', 'Unknown'))

def check_config_spread_sheet_permission(sheet_url: str) -> bool:
    """检查Google Sheet权限"""
    try:
        # 这里应该实现实际的权限检查逻辑
        # 暂时返回True，实际实现时需要连接到Google Sheets API
        return True
    except Exception:
        return False

class BatchProcessor:
    def __init__(self, ai_service: AIService):
        self.ai_service = ai_service
        self.sheet_manager = SheetManager()
        self.case_count = 0
        self.error_count = 0
        self.error_response_count = 0

    def run(self, config: Dict[str, Any]):
        """执行批处理任务"""
        try:
            logger.info("Starting batch processing...")

            # 验证配置
            self._validate_config(config)

            # 初始化数据源
            data_source = self._init_data_source(config)

            # 读取提示词
            prompts = data_source.read_prompts()
            logger.info(f"Found {len(prompts)} prompts to process")

            if not prompts:
                logger.warning("No prompts found to process")
                return

            # 处理每个提示词
            for i, prompt in enumerate(prompts):
                logger.info(f"Processing prompt {i+1}/{len(prompts)}: {prompt.get('category', 'Unknown')}")
                self._process_prompt(prompt, data_source)

            logger.info(f"Batch processing completed. Cases: {self.case_count}, Errors: {self.error_count}, Response Errors: {self.error_response_count}")

        except Exception as e:
            logger.exception(f"Batch processing failed: {e}")
            raise

    def _validate_config(self, config: Dict[str, Any]):
        """验证配置参数"""
        # 如果配置中没有表格信息，尝试从配置文件读取
        if not config.get('prompt_sheet') or not config.get('test_spec_sheet'):
            config_service = ConfigurationService()
            sheet_config = config_service.get_sheet_config_for_batch_task()

            if 'error' in sheet_config:
                raise ValueError(sheet_config['error'])

            # 合并配置
            config.update(sheet_config)

        required_fields = ['prompt_sheet', 'test_spec_sheet']
        for field in required_fields:
            if not config.get(field):
                raise ValueError(f"Missing required configuration: {field}")

        # 检查权限
        if not check_config_spread_sheet_permission(config['prompt_sheet']):
            raise ValueError("prompt_sheet does not have write permission")

        if not check_config_spread_sheet_permission(config['test_spec_sheet']):
            raise ValueError("test_spec_sheet does not have write permission")
            
    def _init_data_source(self, config: Dict[str, Any]):
        """初始化数据源"""
        task_type = config.get('task_type', 'batch')
        
        if task_type == 'single':
            return SinglePromptDataSource(
                prompt_sheet_url=config['prompt_sheet'],
                test_spec_sheet_url=config['test_spec_sheet'],
                prompt_sheet_name=config['prompt_sheet_name']
            )
        else:
            return BatchPromptDataSource(
                prompt_sheet_url=config['prompt_sheet'],
                test_spec_sheet_url=config['test_spec_sheet']
            )
            
    def _process_prompt(self, prompt: Dict[str, Any], data_source):
        """处理单个提示词"""
        try:
            # 发送请求
            logger.debug(f"Sending request for prompt: {prompt.get('prompt', '')[:100]}...")
            response = self.ai_service.generate_response(prompt['prompt'])

            # 检查响应是否为错误响应
            if response.startswith("The session"):
                self.error_response_count += 1
                logger.warning(f"Received error response: {response[:100]}...")
                return

            # 保存结果
            case_count = data_source.save_result(prompt, response)
            self.case_count += case_count

            logger.debug(f"Processed prompt, generated {case_count} cases")

        except Exception as e:
            self.error_count += 1
            logger.error(f"Failed to process prompt: {e}")
            # 更新状态为失败
            try:
                data_source._update_status(prompt, "error")
            except Exception as update_error:
                logger.error(f"Failed to update error status: {update_error}")


class BatchPromptDataSource:
    """批量提示词数据源"""
    
    def __init__(self, prompt_sheet_url: str, test_spec_sheet_url: str):
        self.prompt_sheet_url = prompt_sheet_url
        self.test_spec_sheet_url = test_spec_sheet_url
        self.sheet_manager = SheetManager()
        self.prompts = []
        
    def read_prompts(self) -> List[Dict[str, Any]]:
        """读取所有提示词"""
        self.sheet_manager.connect_sheet(self.prompt_sheet_url)
        sheet_names = self.sheet_manager.get_sheet_names()
        
        for sheet_name in sheet_names:
            if sheet_name.startswith("Prompt_"):
                data = self.sheet_manager.read_by_sheet_name(sheet_name)
                for i, row in enumerate(data):
                    if row.get("Status") == "ready" and row.get("Prompt_Design", "").strip():
                        self.prompts.append({
                            "prompt": row.get("Prompt_Design"),
                            "row": i + 2,
                            "category": sheet_name
                        })
        return self.prompts
        
    def save_result(self, prompt: Dict[str, Any], response: str) -> int:
        """保存处理结果"""
        row = prompt["row"]
        category = prompt["category"]

        # 检查是否为错误响应
        if response.startswith("The session"):
            return 0

        try:
            # 解析测试用例
            case_list_all = parse_test_cases(response)
            case_list = case_list_all.get("Test Cases", [])

            if not case_list:
                self._update_status(prompt, "genFailed")
                error_info = "Each test case must have below elements: Test Case ID, Covered requirement id, Test objectives, Test Condition, Test Action, Test expectation."
                self.sheet_manager.update_cell(category, row, 7, error_info)
                return 0

            # 保存Object Heading到提示词表
            if self._save_prompt_object_heading(category, row, case_list, response):
                self._update_status(prompt, "generated")
                # 保存到测试规格表
                valid_case_count = self._save_to_test_spec(prompt, case_list)
                return valid_case_count
            else:
                return 0

        except Exception as e:
            logger.error(f"Failed to save result: {e}")
            self._update_status(prompt, "genFailed")
            return 0
            
    def _update_status(self, prompt: Dict[str, Any], status: str):
        """更新提示词状态"""
        self.sheet_manager.update_cell(
            prompt['category'],
            prompt['row'],
            6,  # Status column (column F)
            status
        )

    def _save_prompt_object_heading(self, category: str, row: int, case_list: List, response: str) -> bool:
        """保存Object Heading到提示词表"""
        object_headings = []
        for case in case_list:
            if len(case) != 6:
                continue
            object_headings.append(str(case[0]).strip())

        save_text = "\n".join(object_headings).strip()
        date_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if len(object_headings) == 0:
            error_info = "Each test case must have below elements: Test Case ID, Covered requirement id, Test objectives, Test Condition, Test Action, Test expectation."
            self.sheet_manager.update_cell(category, row, 7, error_info)
            return False
        else:
            # 保存Object Heading (column E)
            self.sheet_manager.update_cell(category, row, 5, save_text)
            # 保存用例数量 (column G)
            self.sheet_manager.update_cell(category, row, 7, len(object_headings))
            # 保存完整响应 (column H)
            self.sheet_manager.update_cell(category, row, 8, response)
            # 保存时间戳 (column I)
            self.sheet_manager.update_cell(category, row, 9, date_now)
            return True

    def _save_to_test_spec(self, prompt: Dict[str, Any], case_list: List) -> int:
        """保存到测试规格表"""
        case_count = 0
        category = prompt["category"]
        test_spec_sheet_name = category.replace("Prompt_", "TestSpec_")

        # 连接测试规格表
        self.sheet_manager.connect_sheet(self.test_spec_sheet_url)

        # 检查是否存在测试规格表，不存在则创建
        sheet_names = self.sheet_manager.get_sheet_names()
        if test_spec_sheet_name not in sheet_names:
            if not self._add_test_sheet(test_spec_sheet_name):
                return case_count

        # 获取表头
        sheet_headers = self.sheet_manager.get_sheet_header(test_spec_sheet_name)

        # 保存每个测试用例
        for case in case_list:
            if len(case) != 6:
                continue

            # 添加用户名
            case.append(get_display_name())

            # 构建行数据
            row = []
            for col in sheet_headers:
                if col in CASE_HEADER_INDEX:
                    row.append(case[CASE_HEADER_INDEX.index(col)])
                else:
                    row.append("")

            # 添加行到表格
            self.sheet_manager.add_row(test_spec_sheet_name, row, table_range="A3")
            case_count += 1

        return case_count

    def _add_test_sheet(self, sheet_name: str) -> bool:
        """创建新的测试规格表"""
        try:
            headers = [
                "Object Heading", "oReference", "aBug_ID", "aFailed_Req", "oTestLevel", "oReference_Sys",
                "aBug_ID_Sys", "aFailed_Req_Sys", "aTicket_ID", "TC_Designer", "aObjectType", "Object Short Text",
                "Object Text", "aTestCondition", "aTestAction", "aTestExpectation", "aTestResult", "aTestComment",
                "aTestStatus", "aTestDate", "aTestTime", "aTestDuration", "aTestEnvironment", "aTestData",
                "aTestPriority", "aTestSeverity", "aTestType", "aTestCategory", "aTestSubCategory", "aTestTag"
            ]

            # 创建表格并添加头部
            self.sheet_manager.create_sheet(sheet_name)
            self.sheet_manager.add_row(sheet_name, headers, table_range="A1")

            # 添加第二行（可能是格式行）
            format_row = [""] * len(headers)
            self.sheet_manager.add_row(sheet_name, format_row, table_range="A2")

            logger.info(f"Created new test spec sheet: {sheet_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to create test sheet {sheet_name}: {e}")
            return False


class SinglePromptDataSource(BatchPromptDataSource):
    """单个提示词数据源"""

    def __init__(self, prompt_sheet_url: str, test_spec_sheet_url: str, prompt_sheet_name: str):
        super().__init__(prompt_sheet_url, test_spec_sheet_url)
        self.prompt_sheet_name = prompt_sheet_name

    def read_prompts(self) -> List[Dict[str, Any]]:
        """读取单个提示词"""
        self.sheet_manager.connect_sheet(self.prompt_sheet_url)

        # 如果prompt_sheet_name以TestSpec_开头，转换为Prompt_
        if self.prompt_sheet_name.startswith("TestSpec_"):
            prompt_sheet_name = self.prompt_sheet_name.replace("TestSpec_", "Prompt_")
        else:
            prompt_sheet_name = self.prompt_sheet_name

        data = self.sheet_manager.read_by_sheet_name(prompt_sheet_name)

        for i, row in enumerate(data):
            if row.get("Status") == "ready" and row.get("Prompt_Design", "").strip():
                self.prompts.append({
                    "prompt": row.get("Prompt_Design"),
                    "row": i + 2,
                    "category": prompt_sheet_name
                })
                break  # 只处理第一个ready状态的提示词

        return self.prompts

    def manual_save_result(self, response: str) -> int:
        """手动保存结果（用于单个任务的保存操作）"""
        if not self.prompts:
            return 0

        prompt = self.prompts[0]  # 获取第一个提示词
        return self.save_result(prompt, response)
