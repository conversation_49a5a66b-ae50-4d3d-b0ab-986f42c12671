###################################################################################################
# 
# USAGE : DO NOT CHANGE ANYTHING.
# 
###################################################################################################
name: Static Code Analysis
on:
  workflow_dispatch:

jobs:
  StaticCodeScanning:
    runs-on: Linux
    steps:
  
    - uses: actions/checkout@v3
      with:
        # Disabling shallow clones is recommended for improving the relevancy of reporting
        fetch-depth: 0
  
    - name: SonarQube Default Scan
      run: sonar-scanner -Dsonar.login=${{ secrets.SONARQUBE_TOKEN }} -Dsonar.verbose=true -Dsonar.sources=. -Dsonar.host.url=${{ vars.SONARQUBE_URL }} -Dsonar.projectKey=${{ github.repository_owner }}-${{ github.event.repository.name }}

# Triggering SonarQube analysis as results of it are required by Quality Gate check.
    - name: SonarQube Quality-gate
      uses: digit-actions/sonarqube-quality-gate-action@master
      env:
        SONAR_TOKEN: ${{ secrets.SONARQUBE_TOKEN }}
        SONAR_HOST_URL: ${{ vars.SONARQUBE_URL }}

    # Check the Quality Gate status.
    - name: SonarQube Quality Gate check
      id: sonarqube-quality-gate-check
      uses:  digit-actions/sonarqube-quality-gate-action@master
      # Force to fail step after specific time.
      timeout-minutes: 5
      env:
       SONAR_TOKEN: ${{ secrets.SONARQUBE_TOKEN }}
       SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }} #OPTIONAL

    # Optionally you can use the output from the Quality Gate in another step.
    # The possible outputs of the `quality-gate-status` variable are `PASSED`, `WARN` or `FAILED`.
    - name: "SonarQube Quality Gate Status value"
      run: echo "The Quality Gate status is ${{ steps.sonarqube-quality-gate-check.outputs.quality-gate-status }}"
