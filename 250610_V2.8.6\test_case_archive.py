from PyQt5.QtCore import QThread, pyqtSignal
from spread_sheet import SpreadSheet
from DataProcessing import parse_test_cases
from batch_task import get_display_name
import configparser
import os
from itertools import zip_longest

# 目标工作表的表头
TARGET_HEADERS = ["Object Heading", "oReference", "aBug_ID", "aFailed_Req", "oTestLevel", "oReference_Sys",
                   "aBug_ID_Sys", "aFailed_Req_Sys", "aTicket_ID", "TC_Designer", "aObjectType", "Object Short Text",
                   "Object Text", "aTestCondition", "aTestAction", "aTestExpectation", "aTestReaction", "aTestPriority",
                   "adASIL", "aTestEnvironment", "aTestMethod", "aTestType", "sdPlannedFor", "sVariants", "aObjectStatus",
                   "aReviewComments_Test", "aScriptComments", "sCarVariants", "aTestDesignMethod", "aReviewStatus_Test",
                   "aReviewStatus_FO", "aTestExecutionOrder", "aNegativeTest", "aTestMachine", "aTestDate", "aTester",
                   "aVersion", "aTestResult", "aTestResultComment", "aTestLog", "CAPL_Case", "CAPL_Para", "ReqCount",
                   "aTestResultComment_Sys", "AttrChkResult", "notUsed4", "Req", "Case", "CaseCount", "CaseResult",
                   "ReqResult", "APR", "notUsed8", "APR_ID", "APR_Sts"]

# 源字段列表，与 case 列表中的数据顺序对应
SOURCE_FIELDS = ["Object Heading", "oReference_Sys", "Object Text", "aTestCondition", "aTestAction", "aTestExpectation", "TC_Designer"]  # 添加其他必要的字段


class TestCaseArchiveTask(QThread):
    message_signal = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.case_count = 0

    def run(self):
        try:
            if not self.get_config():
                raise Exception("Failed to get configuration.")

            source_sheet = SpreadSheet(self.prompt_sheet)
            target_sheet = SpreadSheet(self.test_spec_sheet)

            if self.case_sheet_name not in target_sheet.get_sheet_names():
                target_sheet.create_new_sheet(self.case_sheet_name)
                target_sheet.add_row(self.case_sheet_name, TARGET_HEADERS) # 添加表头

            data = source_sheet.read_by_sheet_name(self.prompt_sheet_name)

            row_index = 1 # 行索引，从1开始，因为第一行是表头
            for row in data:
                status = row.get("Status") # 获取F列的Status
                if status == "ToArchive":
                    prompt_log = row.get("Prompt Log")
                    if prompt_log:
                        self.handle_prompt_log(prompt_log, target_sheet)
                    # 更新状态为 "Generated"
                    source_sheet.update_cell(self.prompt_sheet_name, row_index + 1, 6, "generated") # row_index + 1 因为read_by_sheet_name返回的数据不包含表头
                row_index += 1
                
            #source_sheet.save() # 保存修改后的源Excel文件
            
            self.message_signal.emit(f"info:Archived {self.case_count} test cases.")
            self.message_signal.emit("end")

        except Exception as e:
            self.message_signal.emit(f"error:Archiving test cases failed: {e}")
            print(f"Archiving test cases failed: {e}")
            self.message_signal.emit("end")

    def handle_prompt_log(self, prompt_log, target_sheet):
        results = parse_test_cases(prompt_log)
        cases = results.get("Test Cases", [])
        # print("打印1:",cases)

        for case in cases:
            case.append(get_display_name())  # 添加 TC_Designer
            # print("打印2:",case)
            row_to_add = [""] * len(TARGET_HEADERS)

            for source_header, value in zip_longest(SOURCE_FIELDS, case, fillvalue=""):
                try:
                    target_index = TARGET_HEADERS.index(source_header)
                    row_to_add[target_index] = value
                except ValueError:
                    print(f"Warning: Source header '{source_header}' not found in TARGET_HEADERS")

            target_sheet.add_row(self.case_sheet_name, row_to_add)
            self.case_count += 1


    def get_config(self):
        config_file = r"./config.ini"
        if os.path.exists(config_file):
            config = configparser.ConfigParser()
            config.read(config_file)
            try:
                self.prompt_sheet = config.get('SheetConfig', 'prompt_sheet')
                self.test_spec_sheet = config.get('TestSpec_SheetConfig', 'test_spec_sheet')
                self.prompt_sheet_name = config.get('SheetConfig', 'prompt_sheet_name')
                self.case_sheet_name = config.get('SheetConfig', 'prompt_sheet_name').replace("Prompt_", "TestSpec_")
                # ... other config items if needed ...
                return True
            except Exception as e:
                print(f"Error reading config.ini: {e}")  # 打印错误信息
                return False
        else:
            print("config.ini not found")  # 打印错误信息
            return False


# # MyMainForm 中的 do_tc_archive 函数
#     def do_tc_archive(self):
#         self.pushButton.setEnabled(False)
#         self.pushButton.setText("Generating...")
#         self.button_disable()

#         self.tc_archive_task = TestCaseArchiveTask()
#         self.tc_archive_task.message_signal.connect(self.handle_batch_task_signal)
#         self.tc_archive_task.start()