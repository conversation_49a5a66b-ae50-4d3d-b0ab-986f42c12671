from pydantic_settings import BaseSettings


class AppSettings(BaseSettings):
    SERVICES_PROJECT_ID: str
    KNOWLEDGEBASE_CREATION_JOB	: str
    SERVICES_BUCKET_NAME: str
    SERVICES_DATASET_ID: str
    TESTCASE_GENERATION_JOB: str
    SERVICES_ACCOUNT_EMAIL: str
    TESTSCRIPT_GENERATION_JOB: str
    class Config:
        env_file = "app\configs\.env"
        case_sensitive = True


settings = AppSettings()


