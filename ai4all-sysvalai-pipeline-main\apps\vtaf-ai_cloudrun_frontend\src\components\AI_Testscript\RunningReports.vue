<template>
    <q-table
    flat
    bordered
    title="Running Reports"
    :rows="current_history"
    :columns="columns"
    row-key="timestamp"
    class="table_style"
    color="blue-10"
    :loading="current_history_table_loading"
  >
    <template v-slot:top>
      <q-icon size="50px" color="blue-10" name="task"></q-icon>
      <span class="text-blue-10 text-h5 q-ml-md">Running Reports</span>
    </template>

    <template v-slot:body-cell-report="props">
      <q-td :props="props">
        <q-btn
          color="primary"
          label="Report"
          @click="goToReport(props.row.execution_id)"
          dense
          size="sm"
        />
      </q-td>
    </template>

    <template v-slot:no-data>
      <div class="full-width row flex-center q-pa-md">
        <q-icon name="task_alt" size="md" color="green-7" class="q-mr-sm" />
        <span class="text-white-7">No Reports are running currently</span>
      </div>
    </template>
  </q-table>
</template>


<script setup>
import { ref, onMounted } from "vue";
import { storeToRefs } from "pinia";
import ErrorDialog from "./ErrorDialog.vue";
import { useRunTsGenerationStore } from 'src/stores/ai_testscript/run-ts-generation-store';

const ai_testscript_config = useRunTsGenerationStore();
const { user_email, spreadsheetLink, current_history, current_history_table_loading} = storeToRefs(ai_testscript_config);
const { get_user_email, check_access, send_sheet } = ai_testscript_config;


const columns = ref([
  {
    name: "timestamp",
    label: "Job Created",
    field: (row) => row.timestamp,
    align: "left",
    sortable: true,
  },
  {
    name: "execution_id",
    label: "Job ID",
    field: (row) => row.execution_id,
    align: "left",
    sortable: true,
  },
  {
    name: "execution_status",
    label: "Execution Status",
    field: (row) => row.execution_status,
    align: "left",
    sortable: true,
  },
  { name: "report", label: "Report", field: "report", align: "center" }, // Column for the button
]);

const goToReport = (executionId) => {
  current_job_execution_id.value = executionId;
  router.push(`/ai_testcase/job_status/${current_job_execution_id.value}`);
};

onMounted(async () => {
  await get_user_email();
  get_current_jobs();
});

</script>
