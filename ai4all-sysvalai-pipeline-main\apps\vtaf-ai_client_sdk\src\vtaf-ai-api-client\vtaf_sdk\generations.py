from vtaf_ai_api_client.api.generations_v_1 import (
    generator_health_check,
    get_supported_models_v1_generations_supported_models_get,
    rag_v1_generations_rag_post,
)
from vtaf_ai_api_client.models import GenerationRequest


class GenerationsAPI:
    def __init__(self, client):
        self.client = client

    def health_check(self):
        return generator_health_check.sync(client=self.client).additional_properties

    def supported_models(self, force: bool = False):
        return get_supported_models_v1_generations_supported_models_get.sync(client=self.client, force=force)

    def rag(self, payload: GenerationRequest, force: bool = False):
        return rag_v1_generations_rag_post.sync(client=self.client, body=payload, force=force)
