version: '3.8'

services:
  vtaf-ai-multiprompt:
    build: .
    container_name: vtaf-ai-multiprompt
    env_file:
      - .env.docker
    environment:
      - RUN_MODE=${RUN_MODE:-batch}
      - TASK_TYPE=${TASK_TYPE:-batch}
      - PROMPT_SHEET_URL=${PROMPT_SHEET_URL}
      - TEST_SPEC_SHEET_URL=${TEST_SPEC_SHEET_URL}
      - PROMPT_SHEET_NAME=${PROMPT_SHEET_NAME}
      - AI_MODEL=${AI_MODEL:-gemini-1.5-pro}
    volumes:
      # 配置文件（如果存在）
      - ./src/config.ini:/app/src/config.ini:ro
      # 认证文件（如果存在）
      - ./src/client_secret.json:/app/src/client_secret.json:ro
      - ./src/service-account.json:/app/src/service-account.json:ro
      # 日志目录
      - ./logs:/app/logs
    ports:
      - "8080:8080"
    restart: unless-stopped

  # 测试模式服务
  vtaf-ai-multiprompt-test:
    build: .
    container_name: vtaf-ai-multiprompt-test
    env_file:
      - .env.docker
    environment:
      - RUN_MODE=test
    volumes:
      - ./src/config.ini:/app/src/config.ini:ro
      - ./src/client_secret.json:/app/src/client_secret.json:ro
      - ./src/service-account.json:/app/src/service-account.json:ro
      - ./logs:/app/logs
    profiles:
      - test
