site_name: VTAF Keywords
repo_url: https://github-ri.vnet.valeo.com/GROUP-AI4ALL/ai4all-sysvalai-pipeline
repo_name: GROUP-AI4ALL/ai4all-sysvalai-pipeline

theme:
  name: material
  palette:
    - scheme: default
      type: light
      primary: teal
      accent: orange
      toggle:
        icon: material/toggle-switch-off-outline
        name: switch to dark mode
    - scheme: slate
      type: light
      primary: teal
      accent: lime
      toggle:
        icon: material/toggle-switch
        name: switch to light mode
  logo: icons/vtaf.png
  features:
    - search.suggest
    - search.highlight
    - navigation.tabs
    - toc.integrate
    - navigation.tabs

extra:
  version:
      provider: mike
      default: stable
        
plugins:
  - search
  - mike
  - swagger-ui-tag
  - monorepo 


nav:
  - Home: index.md
  - API Reference:  '!include ./apps/vtaf-ai_cloudrun_api/mkdocs.yml'
  # - keyword Groups:
  #     - AI: keywords/AI.md
  #     - ApplicationControl: keywords/App_Ctl.md
  #     - Camera: keywords/Camera.md
  #     - CAPL: keywords/CAPL.md
  #     - Chamber-Controls: keywords/Chamber_Controls.md
  #     - Communication: keywords/Communication.md
  #     - Debugging Tool: Keywords/Debugging_Tools.md
  #     - Flow_Controls: Keywords/Flow_Controls.md
  #     - Network_Protocol: Keywords/Network_Protocol.md
  #     - Other: Keywords/Other.md
  #     - Power: Keywords/Power.md
  #     - RobustnessTestes: Keywords/RobustnessTests.md
  #     - Training Group: Keywords/Training_Group.md
  #     - Vision Analyzer: Keywords/VisionAnalyzer.md
  #     - VTAF Framework: Keywords/VTAF_Framwork_UseOnly.md
  


