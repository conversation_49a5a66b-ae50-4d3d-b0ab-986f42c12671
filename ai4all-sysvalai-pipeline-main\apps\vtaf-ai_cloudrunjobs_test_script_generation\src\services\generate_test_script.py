from services.vtaf_api_clients import VtafApiClients

from utils.logger import get_logger

logger = get_logger(__name__)


def generate_test_script(testcases, user_email):
    token_usage = {
        "total_tokens": 0,
        "prompt_tokens": 0,
        "cached_prompt_tokens": 0,
        "completion_tokens": 0,
        "successful_requests": 0
    }

    try:

        test_scripts = []

        vtaf_apis = VtafApiClients()

        unhealthy_services = vtaf_apis.check_health("agents")
        if not unhealthy_services:
            logger.error(f"Health check failed for: {', '.join(unhealthy_services)}. Job aborted.")
            return f"Health check failed for: {', '.join(unhealthy_services)}. Job aborted.", False

        for testcase in testcases:
            test_script = vtaf_apis.generate_test_script(testcase, user_email)
            automation_steps = []
            test_action = test_script.get("json_dict", {}).get("testcase", {}).get("sAutomationSteps", {})
            if "Precondition" in test_action:
                automation_steps.append(f"Precondition:\n {str(test_action['Precondition'])}\n")
            if "TestProcedure" in test_action:
                automation_steps.append(f"Test Procedure:\n {str(test_action['TestProcedure'])}\n")
            if "Reset" in test_action:
                automation_steps.append(f"Reset:\n {str(test_action['Reset'])}")
            testcase.update({"sAutomationSteps": "\n".join(automation_steps).strip()})
            testcase.update({"sAutomationExpectation": str(
                test_script.get("json_dict", "").get("testcase", "").get("sAutomationExpectation", "")).strip()})
            testcase.update(
                {"Notes": str(test_script.get("json_dict", "").get("testcase", "").get("Notes", "")).strip()})

            token_usage = test_script.get("token_usage", "")
            test_scripts.append(testcase)
            # Update total token usage
            token_usage["total_tokens"] += int(token_usage.get("total_tokens"))
            token_usage["prompt_tokens"] += int(token_usage.get("prompt_tokens"))
            token_usage["cached_prompt_tokens"] += int(token_usage.get("cached_prompt_tokens"))
            token_usage["completion_tokens"] += int(token_usage.get("completion_tokens"))
            token_usage["successful_requests"] += int(token_usage.get("successful_requests"))
        return test_scripts, token_usage, True
    except Exception as e:
        logger.exception(f"Error during service communication: {e}")
        return f"Error during service communication: {e}", token_usage, False
