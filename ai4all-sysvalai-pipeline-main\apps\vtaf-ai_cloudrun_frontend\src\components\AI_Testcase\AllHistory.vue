<template>
  <q-page class="q-pa-md">
    <q-table
      flat
      bordered
      title="Job History"
      :rows="all_history"
      :columns="columns"
      row-key="timestamp"
      class="table_style"
      color="blue-10"
      :pagination="{ rowsPerPage: 10 }"
      :loading="tc_history_table_loading"
    >
      <template v-slot:top>
        <q-icon size="50px" color="blue-10" name="history"></q-icon>
        <span class="text-blue-10 text-h5 q-ml-md">Reports History</span>
      </template>

      <template v-slot:body-cell-status="props">
        <q-td :props="props">
          <span :class="getStatusColor(props.row.status)">
            {{ props.row.status }}
          </span>
        </q-td>
      </template>

      <template v-slot:body-cell-execution_status="props">
        <q-td :props="props">
          <span :class="getStatusColor(props.row.execution_status)">
            {{ props.row.execution_status }}
          </span>
        </q-td>
      </template>

      <template v-slot:body-cell-report="props">
        <q-td :props="props">
          <q-btn
            color="primary"
            label="Report"
            @click="goToReport(props.row.execution_id)"
            dense
            size="sm"
          />
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useJobManagementStore } from "src/stores/ai_testcase/job-management-store";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";

const router = useRouter();

const ai_testcase_config = useJobManagementStore();
const { all_history, current_job_execution_id, tc_history_table_loading } =
  storeToRefs(ai_testcase_config);
const { get_user_email, get_all_job_history } = ai_testcase_config;

const columns = ref([
  {
    name: "timestamp",
    label: "Job Created",
    field: (row) => row.timestamp,
    align: "left",
    sortable: true,
  },
  {
    name: "execution_id",
    label: "Job ID",
    field: (row) => row.execution_id,
    align: "left",
    sortable: true,
  },
  {
    name: "job_duration",
    label: "Duration",
    field: (row) => row.job_duration,
    align: "left",
    sortable: true,
  },
  {
    name: "status",
    label: "Status",
    field: (row) => row.status,
    align: "left",
    sortable: true,
  },
  {
    name: "execution_status",
    label: "Execution Status",
    field: (row) => row.execution_status,
    align: "left",
    sortable: true,
  },
  { name: "report", label: "Report", field: "report", align: "center" }, // Column for the button
]);

const getStatusColor = (status) => {
  if (status === "started") return "text-blue-10";
  if (status === "completed" || status === "passed") return "text-green-9";
  if (status === "failed") return "text-red-8";
  return "text-grey";
};

const goToReport = (executionId) => {
  current_job_execution_id.value = executionId;
  router.push(`/ai_testcase/job_status/${current_job_execution_id.value}`);
};

onMounted(async () => {
  await get_user_email();
  get_all_job_history();
});
</script>

<style scoped>
.table_style {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  color: black;
}
</style>
