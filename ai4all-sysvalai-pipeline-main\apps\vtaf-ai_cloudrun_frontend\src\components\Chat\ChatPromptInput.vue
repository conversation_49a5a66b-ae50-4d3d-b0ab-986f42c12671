<template>
  <!-- <div
    :class="[
      'row items-center q-pa-md bg-grey-3',
      { 'sticky-bottom': isStuckToBottom },
    ]"
  >
    <q-file
      borderless
      v-model="uploaded_file"
      label="Attach File"
      accept=".jpeg, .png, .pdf, .txt"
    />
    <q-input
      bottom-slots
      v-model="prompt"
      label="Enter Your Prompt Here..."
      style="flex: 1"
      @keydown.enter="handleEnter"
    >
      <template v-slot:after>
        <q-btn
          round
          dense
          flat
          icon="send"
          @click="handleClick"
          :disable="prompt_banner_loading"
        />
      </template>
    </q-input>
  </div> -->
  <div class="input_row">
    <q-card style="width: 100%">
      <div v-if="file_name" class="file-name">
        Selected File: {{ file_name }}
      </div>
      <q-card-section style="display: flex; align-items: center">
        <q-btn
          flat
          round
          icon="attach_file"
          @click="selectFile"
          color="positive"
        />
        <q-input
          v-model="prompt"
          type="textarea"
          placeholder="Enter text or file name"
          filled
          @keydown.enter="handleEnter"
          style="flex: 1; margin: 0 8px; overflow-y: auto; max-height: 200px"
          autogrow
        />
        <q-btn flat round icon="north" color="positive" @click="handleClick" />
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { storeToRefs } from "pinia";
import { computed } from "vue";
import { useChatStore } from "src/stores/chat/chat_store";

const chat_store = useChatStore();
const {
  uploaded_file,
  prompt,
  prompt_loading,
  prompt_banner_loading,
  prompt_response,
  file_name,
} = storeToRefs(chat_store);
const { getPromptResponse, getPromptResponseStream } = chat_store;

const selectFile = () => {
  const input = document.createElement("input");
  input.type = "file";
  input.onchange = (event) => {
    uploaded_file.value = event.target.files[0];
    if (uploaded_file) {
      file_name.value = uploaded_file.value.name; // Display the file name in the input box
    }
  };
  input.click(); // Programmatically click the input to open the file dialog
};

const handleClick = () => {
  prompt_loading.value = true;
  prompt_response.value = "";
  prompt_banner_loading.value = true;
  getPromptResponse();
};

const handleEnter = (event) => {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    handleClick();
  }
};
</script>

<style scoped>
.sticky-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: white; /* Adjust as needed */
  z-index: 1000; /* Make sure it's above other elements */
}
.input_row {
  display: flex;
  justify-content: center;
  margin: auto;
  margin-top: 1rem;
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 1000; /* Make sure it's above other elements */
}
.file-name {
  margin-left: 2rem;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-weight: bold;
  color: #4caf50;
}
</style>
