#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VTAF AI Multiprompt API Server - 前端与Cloud Run Jobs的桥梁
提供RESTful API接口供前端调用，触发相应的Cloud Run Jobs
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import FastAPI, HTTPException, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from utils.logger import get_logger
from services.cloud_run_job_trigger import CloudRunJobTrigger

logger = get_logger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="VTAF AI Multiprompt API",
    description="API服务用于触发Cloud Run Jobs",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
job_trigger = None

def get_job_trigger():
    """获取JobTrigger实例，延迟初始化"""
    global job_trigger
    if job_trigger is None:
        try:
            job_trigger = CloudRunJobTrigger()
            logger.info("✅ CloudRunJobTrigger initialized successfully")
        except Exception as e:
            logger.warning(f"⚠️ CloudRunJobTrigger initialization failed: {e}")
            logger.info("🎭 Running in simulation mode")
            job_trigger = CloudRunJobTrigger()  # 会自动进入模拟模式
    return job_trigger

# Pydantic模型
class JobTriggerRequest(BaseModel):
    """任务触发请求模型"""
    button_type: str = Field(..., description="按钮类型")
    config: Dict[str, Any] = Field(default_factory=dict, description="任务配置")
    user_email: Optional[str] = Field(None, description="用户邮箱")
    
class JobStatusRequest(BaseModel):
    """任务状态查询请求模型"""
    operation_name: str = Field(..., description="操作名称")

# API路由
@app.get("/")
async def root():
    """根路径 - 健康检查"""
    return {
        "message": "VTAF AI Multiprompt API Server",
        "status": "running",
        "timestamp": datetime.utcnow().isoformat(),
        "available_endpoints": [
            "/trigger-job",
            "/job-status",
            "/available-jobs",
            "/health"
        ]
    }

@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查Cloud Run Job Trigger是否正常
        trigger = get_job_trigger()
        available_jobs = trigger.get_available_jobs()

        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "available_jobs_count": len(available_jobs),
            "project_id": trigger.project_id,
            "region": trigger.region,
            "simulation_mode": not trigger.google_cloud_available
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@app.get("/available-jobs")
async def get_available_jobs():
    """获取可用的任务类型"""
    try:
        trigger = get_job_trigger()
        jobs = trigger.get_available_jobs()
        
        # 添加任务描述
        job_descriptions = {
            'submit': '基础提交任务 - 单个提示词处理',
            'send_batch_reqs': '批量处理任务 - 批量提示词处理',
            'send_single_req': '单个任务处理',
            'archive_ts': '测试用例归档',
            'send_batch_tcs': '批量测试脚本生成',
            'send_single_tc': '单个测试脚本生成',
            'send_mail': '邮件发送',
            'email_back': '邮件回复',
            'send_training_dataset': '训练数据集加载',
            'send_ts_training_dataset': 'TS训练数据集加载',
            'set_system_prompt': '系统指令设置',
            'insert_media': '文件操作',
        }
        
        detailed_jobs = {}
        for button, job_type in jobs.items():
            detailed_jobs[button] = {
                'job_type': job_type,
                'description': job_descriptions.get(button, '未知任务'),
                'cloud_run_job_name': f'vtaf-ai-{job_type.replace("_", "-")}'
            }
        
        return {
            "status": "success",
            "jobs": detailed_jobs,
            "total_count": len(detailed_jobs)
        }
        
    except Exception as e:
        logger.error(f"Failed to get available jobs: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get available jobs: {str(e)}")

@app.post("/trigger-job")
async def trigger_job(request: JobTriggerRequest, background_tasks: BackgroundTasks, http_request: Request):
    """触发Cloud Run Job"""
    try:
        # 记录请求信息
        client_ip = http_request.client.host
        logger.info(f"🎯 Job trigger request from {client_ip}: {request.button_type}")
        
        # 添加用户信息到配置
        if request.user_email:
            request.config['user_email'] = request.user_email
        
        # 添加客户端信息
        request.config['client_ip'] = client_ip
        request.config['user_agent'] = http_request.headers.get('user-agent', 'Unknown')
        
        # 触发任务
        trigger = get_job_trigger()
        result = await trigger.trigger_job(request.button_type, request.config)
        
        if result['status'] == 'success':
            logger.info(f"✅ Job triggered successfully: {result['job_name']}")
            return JSONResponse(
                status_code=200,
                content={
                    "status": "success",
                    "message": "Job triggered successfully",
                    "data": result
                }
            )
        else:
            logger.error(f"❌ Job trigger failed: {result['message']}")
            return JSONResponse(
                status_code=400,
                content={
                    "status": "error",
                    "message": result['message'],
                    "data": result
                }
            )
            
    except Exception as e:
        error_msg = f"API error: {str(e)}"
        logger.exception(f"Job trigger API error: {e}")
        raise HTTPException(status_code=500, detail=error_msg)

@app.post("/job-status")
async def get_job_status(request: JobStatusRequest):
    """获取任务状态"""
    try:
        logger.info(f"📊 Checking job status: {request.operation_name}")
        
        trigger = get_job_trigger()
        status = await trigger.get_job_status(request.operation_name)
        
        return {
            "status": "success",
            "data": status
        }
        
    except Exception as e:
        logger.error(f"Failed to get job status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get job status: {str(e)}")

# 特定按钮的快捷端点
@app.post("/submit")
async def submit_prompt(
    prompt_text: str,
    model: str = "gemini-1.5-flash",
    temperature: float = 0.7,
    user_email: Optional[str] = None
):
    """Submit按钮快捷端点"""
    config = {
        'prompt_text': prompt_text,
        'model': model,
        'temperature': temperature,
        'run_mode': 'production'
    }
    
    if user_email:
        config['user_email'] = user_email
    
    request = JobTriggerRequest(button_type='submit', config=config, user_email=user_email)
    return await trigger_job(request, BackgroundTasks(), Request(scope={'type': 'http', 'client': ('127.0.0.1', 0), 'headers': []}))

@app.post("/send-batch-reqs")
async def send_batch_requests(
    prompt_sheet: str,
    test_spec_sheet: str,
    model: str = "gemini-1.5-flash",
    user_email: Optional[str] = None
):
    """Send Batch Reqs按钮快捷端点"""
    config = {
        'prompt_sheet': prompt_sheet,
        'test_spec_sheet': test_spec_sheet,
        'model': model,
        'run_mode': 'production'
    }
    
    if user_email:
        config['user_email'] = user_email
    
    request = JobTriggerRequest(button_type='send_batch_reqs', config=config, user_email=user_email)
    return await trigger_job(request, BackgroundTasks(), Request(scope={'type': 'http', 'client': ('127.0.0.1', 0), 'headers': []}))

# 错误处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    logger.error(f"HTTP Exception: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "status": "error",
            "message": exc.detail,
            "timestamp": datetime.utcnow().isoformat()
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.exception(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "status": "error",
            "message": "Internal server error",
            "timestamp": datetime.utcnow().isoformat()
        }
    )

# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("🚀 VTAF AI Multiprompt API Server starting up...")
    try:
        trigger = get_job_trigger()
        logger.info(f"📍 Project ID: {trigger.project_id}")
        logger.info(f"🌍 Region: {trigger.region}")
        logger.info(f"🔧 Available jobs: {len(trigger.get_available_jobs())}")
        logger.info(f"🎭 Simulation mode: {not trigger.google_cloud_available}")
    except Exception as e:
        logger.warning(f"⚠️ Startup warning: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("🛑 VTAF AI Multiprompt API Server shutting down...")

if __name__ == "__main__":
    # 开发模式运行
    port = int(os.getenv('PORT', 8080))
    host = os.getenv('HOST', '0.0.0.0')
    
    logger.info(f"🌐 Starting server on {host}:{port}")
    
    uvicorn.run(
        "api_server:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )
