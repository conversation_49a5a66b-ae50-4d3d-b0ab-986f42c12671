import json
import os

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload


class DriveUploader:
    SHARED_FOLDER_ID = os.getenv("SHARED_FOLDER_ID", "")

    def __init__(self, gcp_secret_manager):
        self.gcp_secret_manager = gcp_secret_manager
        self.drive_service = self._create_service()
        self.credentials = None

    def _create_service(self):
        try:
            vtaf_ai_sheet_oauth_file = "vtaf_ai_sheet_oauth.json"
            vtaf_ai_sheet_token_file = "vtaf_ai_sheet_token.json"
            vtaf_ai_sheet_oauth = json.loads(self.gcp_secret_manager.read_secret("vtaf-ai-sheet-oauth"))
            with open(vtaf_ai_sheet_oauth_file, "w") as temp_file:
                json.dump(vtaf_ai_sheet_oauth, temp_file)

            vtaf_ai_sheet_token = json.loads(self.gcp_secret_manager.read_secret("vtaf-ai-sheet-token"))
            with open(vtaf_ai_sheet_token_file, "w") as temp_file:
                json.dump(vtaf_ai_sheet_token, temp_file)

            if os.path.exists(vtaf_ai_sheet_token_file):
                self.credentials = Credentials.from_authorized_user_file(vtaf_ai_sheet_token_file,
                                                                         scopes=[
                                                                             'https://www.googleapis.com/auth/drive',
                                                                             'https://www.googleapis.com/auth/spreadsheets'
                                                                         ])
            # If there are no (valid) credentials available, let the user log in.
            if not self.credentials or not self.credentials.valid:
                if self.credentials and self.credentials.expired and self.credentials.refresh_token:
                    self.credentials.refresh(Request())
                else:
                    flow = InstalledAppFlow.from_client_secrets_file(
                        vtaf_ai_sheet_oauth_file, [
                            'https://www.googleapis.com/auth/drive',
                            'https://www.googleapis.com/auth/spreadsheets'
                        ]
                    )
                    self.credentials = flow.run_local_server(port=0)
                # Save the credentials for the next run
                with open(vtaf_ai_sheet_token_file, "w") as token:
                    token.write(self.credentials.to_json())
            return build('drive', 'v3', credentials=self.credentials)
        except Exception as e:
            raise RuntimeError("❌ Failed to auth the Google Drive.")

    def upload_excel_as_sheet(self, file_path):
        file_metadata = {
            'name': os.path.basename(file_path),
            'parents': [self.SHARED_FOLDER_ID]
        }
        media = MediaFileUpload(file_path,
                                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                chunksize=1024 * 1024,
                                resumable=True
                                )  # MIME type for Excel files

        try:
            file_metadata['mimeType'] = 'application/vnd.google-apps.spreadsheet'
            uploaded_file = self.drive_service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id'
            ).execute()

            return uploaded_file.get('id'), None
        except Exception as e:
            try:
                del file_metadata['mimeType']  # Remove the Google Doc mimeType
                uploaded_file = self.drive_service.files().create(
                    body=file_metadata,
                    media_body=media,
                    fields='id'
                ).execute()

                return uploaded_file.get('id'), None
            except Exception as final_exception:
                print(
                    f"Failed to Upload file to folder {self.SHARED_FOLDER_ID} after retries: {str(final_exception)}")
                return None, str(final_exception)

    @staticmethod
    def get_drive_file_url(file_id: str) -> str:
        return f"https://docs.google.com/spreadsheets/d/{file_id}"
