<template>
  <div class="row">
    <div class="col-sm-3">
      <q-select
        v-model="selected_project"
        @update:model-value="handleChangeProject"
        :options="tree_projects"
        label="Customer"
        class="q-mt-md"
      />
      <q-select
        v-model="selected_variant"
        @update:model-value="handleChangeVariant"
        :options="tree_variants"
        label="Project"
        class="q-mt-md"
      />
      <q-select
        v-model="selected_euf_features"
        :options="tree_euf_features"
        label="EUF_OR_FEATURE"
        class="q-mt-md"
      />
    </div>
    <div class="col-sm-8 q-ml-xl">
      <q-file
        v-model="selectedFile_knowledgebase"
        label="Click to upload file"
        accept=".csv, .xlsx, .pdf, .dbc"
        @update:model-value="handleFileUpload"
        max-file-size="10485760"
        class="q-mt-md file-input"
        filled
        standout="bg-primary text-white"
      >
        <template v-slot:prepend>
          <q-icon name="attach_file" />
        </template>
      </q-file>
      <q-select
        class="q-mt-md"
        v-model="selected_file_type"
        :options="file_type_knowledgebase_options"
        label="File Type"
        @update:model-value="handleChangeFileType"
        style="width: 300px"
      />
      <q-input
        class="q-mt-md"
        v-if="selected_file_type === 'Others... - pdf'"
        outlined
        v-model="selected_file_type_for_others"
        label="others specify"
      ></q-input>
      <q-btn
        label="CREATE KNOWLEDGE BASE"
        color="blue-10"
        :disable="
          !selectedFile_knowledgebase ||
          !selected_project ||
          !selected_variant ||
          !selected_euf_features ||
          !selected_file_type
        "
        @click="file_upload_button"
        class="q-mt-md generate-btn"
      />
    </div>
  </div>
  <hr class="q-mt-lg" />
  <div class="q-mt-md text-blue-10 text-weight-bold text-h5 text-center">
    Search History
  </div>
  <div class="row q-mt-md q-mb-lg bg-blue-1 q-pa-md rounded-borders shadow-2">
    <q-select
      v-model="selected_search_project"
      :options="search_projects"
      label="Search Project"
      class="col-sm-3 q-mr-sm rounded-borders"
      outlined
      dense
      @update:model-value="handleSearchProject"
    />
    <q-select
      v-model="selected_search_variant"
      :options="search_variants"
      label="Search Variant"
      class="col-sm-3 q-mr-sm rounded-borders"
      outlined
      dense
      @update:model-value="handleSearchVariant"
    />
    <q-select
      v-model="selected_search_euf_feature"
      :options="search_euf_features"
      label="Search Feature"
      class="col-sm-4 text-blue-10 rounded-borders"
      outlined
      dense
    />
    <q-btn
      :disable="
        !selected_search_project ||
        !selected_search_variant ||
        !selected_search_euf_feature
      "
      unelevated
      color="blue-10"
      class="col-sm-1 bg-blue-10 text-white rounded-borders shadow-1 q-ml-sm"
      @click="searchButtonClicked"
      icon="search"
    />
  </div>
  <q-table
    v-if="search_table_data"
    title="Knowledge Base history"
    :rows="search_table_data"
    :columns="columns"
    row-key="execution_id"
    bordered
    flat
    :loading="kb_history_table_loading"
    color="blue-10"
  >
    <!-- Download Button in User Upload Column -->
    <template v-slot:body-cell-user_upload="props">
      <q-td :props="props">
        <q-btn
          flat
          icon="cloud_download"
          color="primary"
          @click="downloadFile(props.row.user_upload)"
          dense
        />
      </q-td>
    </template>

    <!-- Download Button in Results Column -->
    <template v-slot:body-cell-result_file="props">
      <q-td :props="props">
        <template
          v-if="props.row.result_file === 'NA' || props.row.result_file === ''"
        >
          NA
        </template>
        <template v-else>
          <q-btn
            flat
            icon="cloud_download"
            color="secondary"
            @click="downloadFile(props.row.result_file)"
            dense
            :disable="props.row.execution_status !== 'completed'"
          />
        </template>
      </q-td>
    </template>
  </q-table>
</template>

<script setup>
import { onMounted } from "vue";
import { storeToRefs } from "pinia";
import { useKnowledgebaseManagementStore } from "src/stores/ai_testcase/knowledgebase-management-store";
import { useRouter } from "vue-router";
const router = useRouter();

const knowledgebase_testcase_config = useKnowledgebaseManagementStore();
const {
  tree_projects,
  selected_project,
  tree_variants,
  selected_variant,
  tree_euf_features,
  selected_euf_features,
  selectedFile_knowledgebase,
  selected_file_type,
  selected_file_type_for_others,
  file_type_knowledgebase_options,
  search_projects,
  selected_search_project,
  search_variants,
  selected_search_variant,
  search_euf_features,
  selected_search_euf_feature,
  search_table_data,
  valid_user_for_create_knowledge_profile,
  kb_history_table_loading,
} = storeToRefs(knowledgebase_testcase_config);
const {
  get_user_email,
  get_profile_projects,
  get_tree_variant_details,
  get_tree_euf_features_details,
  createKnowledgebase,
  get_search_variant_details,
  get_search_euf_features_details,
  get_search_knowledgebase_profile,
} = knowledgebase_testcase_config;

onMounted(async () => {
  selected_project.value = "";
  selected_variant.value = "";
  selected_euf_features.value = "";
  selectedFile_knowledgebase.value = null;
  selected_file_type.value = "";
  selected_file_type_for_others.value = "";
  selected_search_project.value = "";
  selected_search_variant.value = "";
  selected_search_euf_feature.value = "";
  await get_user_email();
  get_profile_projects();
});

const go_to_ai_testcase = () => {
  router.push("/ai_testcase");
};

// Table Columns Definition
const columns = [
  {
    name: "user_email",
    label: "User Email",
    field: "user_email",
    align: "left",
  },
  {
    name: "execution_id",
    label: "Execution ID",
    field: "execution_id",
    align: "left",
  },
  {
    name: "created_time",
    label: "Created Time",
    field: "created_time",
    align: "left",
  },
  { name: "status", label: "Status", field: "status", align: "left" },
  {
    name: "execution_status",
    label: "Execution Status",
    field: "execution_status",
    align: "left",
  },
  {
    name: "user_upload",
    label: "Input File",
    field: "user_upload",
    align: "center",
  },
  {
    name: "result_file",
    label: "Result",
    field: "result_file",
    align: "center",
  },
];

// Download File Function
const downloadFile = (filePath) => {
  window.open(filePath, "_blank"); // Open file in a new tab
};

const handleChangeProject = (event) => {
  selected_variant.value = "";
  selected_euf_features.value = "";
  selectedFile_knowledgebase.value = null;
  selected_file_type.value = "";
  selected_file_type_for_others.value = "";
  console.log(event);
  get_tree_variant_details();
};

const handleChangeVariant = (event) => {
  selected_euf_features.value = "";
  selectedFile_knowledgebase.value = null;
  selected_file_type.value = "";
  selected_file_type_for_others.value = "";
  console.log(event);
  get_tree_euf_features_details();
};

const handleChangeFileType = (event) => {
  selected_file_type_for_others.value = "";
  console.log(event);
};

const handleSearchProject = (event) => {
  console.log(event);
  selected_search_variant.value = "";
  selected_search_euf_feature.value = "";
  get_search_variant_details();
};

const handleSearchVariant = (event) => {
  console.log(event);
  selected_search_euf_feature.value = "";
  get_search_euf_features_details();
};

const searchButtonClicked = (event) => {
  get_search_knowledgebase_profile();
};

const handleFileUpload = (file) => {
  if (!file) return;
  const reader = new FileReader();
  if (file.size > 10485760) {
    $q.notify({ type: "negative", message: "File size exceeds 10MB limit" });
    selectedFile.value = null;
  } else {
    reader.readAsBinaryString(file);
  }
};

const file_upload_button = async () => {
  await createKnowledgebase();
  selected_project.value = "";
  selected_variant.value = "";
  selected_euf_features.value = "";
  selected_file_type.value = "";
  selected_file_type_for_others.value = "";
  selectedFile_knowledgebase.value = null;
  selected_file_type.value = "";
  // router.push("/ai_testcase/job_status");
};
</script>
