"""
impersonation.py

Handles generation of impersonated credentials and ID tokens using Google Cloud IAM.
Primarily used for accessing services (e.g., Qdrant) that are protected by Identity-Aware Proxy (IAP)
through service account impersonation.

Exposes functions to:
- Generate impersonated credentials
- Obtain ID tokens for specific target audiences
"""

import google.auth
import google.auth.transport.requests as google_requests

from google.auth import impersonated_credentials
from app.core.constants import (
    GOOGLE_CLOUD_SCOPES,
    QDRANT_IAP_CLIENT_ID,
    VTAF_SERVICE_ACCOUNT_EMAIL,
)

def get_impersonated_credentials(
    target_principal: str,
    lifetime: int = 300,
) -> google.auth.credentials.Credentials:
    """
    Generates impersonated credentials for the specified target service account.

    Args:
        target_principal (str): The email address of the target service account to impersonate.
        lifetime (int): Duration in seconds for which the credentials are valid. Default is 300.

    Returns:
        google.auth.credentials.Credentials: The impersonated credentials.
    """
    source_credentials, _ = google.auth.default(scopes=GOOGLE_CLOUD_SCOPES)

    target_credentials = impersonated_credentials.Credentials(
        source_credentials=source_credentials,
        target_principal=target_principal,
        target_scopes=[],  # No additional scopes needed for ID token generation
        lifetime=lifetime,
    )

    return target_credentials


def get_impersonated_id_token(
    target_credentials: google.auth.credentials.Credentials,
    target_audience: str,
) -> str:
    """
    Generates an ID token using impersonated credentials for a specified audience.

    Args:
        target_credentials (google.auth.credentials.Credentials): The impersonated credentials.
        target_audience (str): The audience for which the token is intended (e.g., IAP client ID).

    Returns:
        str: A valid ID token for authenticating with the target audience.
    """
    credentials = impersonated_credentials.IDTokenCredentials(
        target_credentials=target_credentials,
        target_audience=target_audience,
        include_email=True,
    )

    request = google_requests.Request()
    credentials.refresh(request)

    return credentials.token


def get_id_token() -> str:
    """
    Convenience function to generate an ID token for accessing Qdrant through IAP
    using impersonated credentials of the VTAF service account.

    Returns:
        str: A valid ID token for Qdrant IAP authentication.
    """
    qdrant_id_token = get_impersonated_id_token(
        target_credentials=get_impersonated_credentials(VTAF_SERVICE_ACCOUNT_EMAIL),
        target_audience=QDRANT_IAP_CLIENT_ID,
    )
    return qdrant_id_token
