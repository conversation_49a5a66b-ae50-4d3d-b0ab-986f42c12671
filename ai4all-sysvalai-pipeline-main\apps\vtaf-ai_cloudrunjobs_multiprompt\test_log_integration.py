#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日志管理功能集成到JobRouter和main函数的脚本
"""

import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

def test_log_functions_via_job_router():
    """通过JobRouter测试日志管理功能"""
    print("🧪 测试日志管理功能集成")
    print("=" * 60)
    
    from services.job_router import JobRouter, get_available_job_types
    
    # 显示可用的任务类型
    available_jobs = get_available_job_types()
    print("📋 可用的任务类型:")
    for job_type, description in available_jobs.items():
        if job_type.startswith('log_'):
            print(f"  - {job_type}: {description}")
    
    # 测试每个日志功能
    log_job_types = ['log_save', 'log_load', 'log_list', 'log_clear']
    
    for job_type in log_job_types:
        print(f"\n🔧 测试 {job_type}...")
        
        # 设置环境变量
        os.environ['JOB_TYPE'] = job_type
        os.environ['RUN_MODE'] = 'test'  # 使用测试模式
        
        if job_type == 'log_save':
            # 为保存操作设置示例数据
            import json
            sample_data = {
                "test": "log_integration_test",
                "job_type": job_type,
                "message": "Testing log save via JobRouter"
            }
            os.environ['LOG_DATA'] = json.dumps(sample_data)
        
        try:
            router = JobRouter()
            result = router.route_job()
            
            if result == 0:
                print(f"  ✅ {job_type} 测试成功")
            else:
                print(f"  ❌ {job_type} 测试失败")
                
        except Exception as e:
            print(f"  ❌ {job_type} 测试异常: {e}")
        
        # 清理环境变量
        if 'LOG_DATA' in os.environ:
            del os.environ['LOG_DATA']

def test_log_functions_via_main():
    """通过main函数测试日志管理功能"""
    print("\n🧪 测试通过main函数执行日志管理功能")
    print("=" * 60)
    
    # 设置环境变量来触发日志管理功能
    os.environ['RUN_MODE'] = 'test'
    os.environ['EXECUTE_LOG_FUNCTIONS'] = 'true'
    os.environ['SKIP_CONVERSATION_TEST'] = 'true'
    os.environ['EXECUTE_DEFAULT_BUTTONS'] = 'false'
    
    try:
        from main import main
        result = main()
        
        if result == 0:
            print("✅ main函数中的日志管理功能测试成功")
        else:
            print("❌ main函数中的日志管理功能测试失败")
            
    except Exception as e:
        print(f"❌ main函数测试异常: {e}")

def test_individual_log_jobs():
    """测试单个日志任务"""
    print("\n🧪 测试单个日志任务执行")
    print("=" * 60)
    
    # 设置环境变量
    os.environ['RUN_MODE'] = 'production'  # 使用生产模式来实际执行
    os.environ['SKIP_CONVERSATION_TEST'] = 'true'
    os.environ['EXECUTE_DEFAULT_BUTTONS'] = 'false'
    os.environ['EXECUTE_LOG_FUNCTIONS'] = 'false'
    
    # 测试单个日志保存任务
    print("📝 测试单个日志保存任务...")
    os.environ['JOB_TYPE'] = 'log_save'
    
    import json
    sample_data = {
        "test_type": "individual_job_test",
        "message": "Testing individual log save job",
        "timestamp": "2024-01-01T00:00:00"
    }
    os.environ['LOG_DATA'] = json.dumps(sample_data)
    
    try:
        from main import main
        result = main()
        
        if result == 0:
            print("✅ 单个日志保存任务测试成功")
        else:
            print("❌ 单个日志保存任务测试失败")
            
    except Exception as e:
        print(f"❌ 单个日志保存任务测试异常: {e}")

if __name__ == '__main__':
    print("🚀 开始日志管理功能集成测试")
    print("=" * 80)
    
    # 测试1: 通过JobRouter直接测试
    test_log_functions_via_job_router()
    
    # 测试2: 通过main函数测试
    test_log_functions_via_main()
    
    # 测试3: 测试单个日志任务
    test_individual_log_jobs()
    
    print("\n🎉 日志管理功能集成测试完成")
