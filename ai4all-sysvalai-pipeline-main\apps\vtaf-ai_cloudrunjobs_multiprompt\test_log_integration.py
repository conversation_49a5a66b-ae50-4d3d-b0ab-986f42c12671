#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI对话日志管理功能测试脚本
模拟AI对话，保存日志，加载日志并显示，最后清除日志
"""

import os
import sys
import json
import time
import random
from datetime import datetime
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

def generate_mock_conversation():
    """生成模拟AI对话数据"""
    conversations = [
        {
            "user": "你好，请帮我写一个Python函数来计算斐波那契数列",
            "assistant": "好的！我来帮你写一个计算斐波那契数列的Python函数：\n\n```python\ndef fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)\n```\n\n这是一个递归实现。如果你需要更高效的版本，我也可以提供迭代实现。",
            "timestamp": datetime.now().isoformat()
        },
        {
            "user": "能否提供一个迭代版本？性能更好一些",
            "assistant": "当然可以！这里是迭代版本，性能确实更好：\n\n```python\ndef fibonacci_iterative(n):\n    if n <= 1:\n        return n\n    \n    a, b = 0, 1\n    for _ in range(2, n + 1):\n        a, b = b, a + b\n    return b\n```\n\n这个版本的时间复杂度是O(n)，比递归版本的O(2^n)要好很多。",
            "timestamp": datetime.now().isoformat()
        },
        {
            "user": "谢谢！还有其他优化方法吗？",
            "assistant": "是的！还有几种优化方法：\n\n1. **矩阵快速幂法**（O(log n)）\n2. **记忆化递归**\n3. **生成器版本**（节省内存）\n\n如果你需要计算非常大的斐波那契数，矩阵快速幂法是最优的选择。需要我详细解释任何一种方法吗？",
            "timestamp": datetime.now().isoformat()
        }
    ]

    # 随机选择1-3个对话
    num_conversations = random.randint(1, len(conversations))
    selected_conversations = conversations[:num_conversations]

    return {
        "conversation_id": f"conv_{int(time.time())}_{random.randint(1000, 9999)}",
        "session_start": datetime.now().isoformat(),
        "total_exchanges": len(selected_conversations),
        "conversations": selected_conversations,
        "metadata": {
            "model": "claude-3-sonnet",
            "temperature": 0.7,
            "max_tokens": 2000,
            "test_mode": True
        }
    }

def display_conversation(conversation_data):
    """美化显示对话内容"""
    print("\n" + "="*80)
    print(f"📋 对话ID: {conversation_data.get('conversation_id', 'Unknown')}")
    print(f"🕐 开始时间: {conversation_data.get('session_start', 'Unknown')}")
    print(f"💬 对话轮数: {conversation_data.get('total_exchanges', 0)}")
    print("="*80)

    conversations = conversation_data.get('conversations', [])
    for i, conv in enumerate(conversations, 1):
        print(f"\n--- 对话轮次 {i} ---")
        print(f"� 用户: {conv.get('user', '')}")
        print(f"🤖 助手: {conv.get('assistant', '')}")
        print(f"⏰ 时间: {conv.get('timestamp', '')}")

    metadata = conversation_data.get('metadata', {})
    if metadata:
        print(f"\n📊 元数据:")
        for key, value in metadata.items():
            print(f"  {key}: {value}")
    print("="*80)

def simulate_ai_conversation_and_save():
    """模拟AI对话并保存日志"""
    print("🤖 模拟AI对话并保存日志")
    print("=" * 60)

    try:
        from services.job_router import JobRouter

        # 生成模拟对话数据
        print("📝 生成模拟AI对话数据...")
        conversation_data = generate_mock_conversation()

        # 显示生成的对话
        print("🎭 生成的对话内容:")
        display_conversation(conversation_data)

        # 保存对话日志
        print("\n💾 保存对话日志...")
        os.environ['JOB_TYPE'] = 'log_save'
        os.environ['RUN_MODE'] = 'production'  # 使用生产模式实际保存
        os.environ['LOG_DATA'] = json.dumps(conversation_data, ensure_ascii=False, indent=2)

        router = JobRouter()
        result = router.route_job()

        if result == 0:
            print("✅ 对话日志保存成功！")
            return conversation_data['conversation_id']
        else:
            print("❌ 对话日志保存失败！")
            return None

    except Exception as e:
        print(f"❌ 模拟对话保存异常: {e}")
        return None
    finally:
        # 清理环境变量
        for key in ['JOB_TYPE', 'LOG_DATA']:
            if key in os.environ:
                del os.environ[key]

def load_and_display_logs():
    """加载并显示日志"""
    print("\n📖 加载并显示日志")
    print("=" * 60)

    try:
        from services.job_router import JobRouter

        # 首先获取日志列表
        print("📋 获取日志文件列表...")
        os.environ['JOB_TYPE'] = 'log_list'
        os.environ['RUN_MODE'] = 'production'

        router = JobRouter()
        result = router.route_job()

        if result != 0:
            print("❌ 获取日志列表失败！")
            return None

        # 加载最新的日志文件
        print("\n📂 加载最新的日志文件...")
        os.environ['JOB_TYPE'] = 'log_load'
        # 不设置LOG_FILENAME，让它自动加载最新的

        router = JobRouter()
        result = router.route_job()

        if result == 0:
            print("✅ 日志加载成功！")
            return True
        else:
            print("❌ 日志加载失败！")
            return False

    except Exception as e:
        print(f"❌ 加载日志异常: {e}")
        return False
    finally:
        # 清理环境变量
        for key in ['JOB_TYPE', 'LOG_FILENAME']:
            if key in os.environ:
                del os.environ[key]

def clear_all_logs():
    """清除所有日志"""
    print("\n🗑️ 清除所有日志")
    print("=" * 60)

    try:
        from services.job_router import JobRouter

        # 清除日志
        print("🧹 清除所有日志文件...")
        os.environ['JOB_TYPE'] = 'log_clear'
        os.environ['RUN_MODE'] = 'production'

        router = JobRouter()
        result = router.route_job()

        if result == 0:
            print("✅ 日志清除成功！")
            return True
        else:
            print("❌ 日志清除失败！")
            return False

    except Exception as e:
        print(f"❌ 清除日志异常: {e}")
        return False
    finally:
        # 清理环境变量
        if 'JOB_TYPE' in os.environ:
            del os.environ['JOB_TYPE']

def interactive_menu():
    """交互式菜单"""
    print("\n" + "="*80)
    print("🎯 AI对话日志管理测试菜单")
    print("="*80)
    print("1. 🤖 模拟AI对话并保存日志")
    print("2. 📖 加载并显示日志")
    print("3. 📋 查看日志文件列表")
    print("4. 🗑️ 清除所有日志")
    print("5. 🔄 完整流程测试（对话→保存→加载→清除）")
    print("6. 🧪 通过main函数测试所有日志功能")
    print("0. 🚪 退出")
    print("="*80)

    while True:
        try:
            choice = input("\n请选择操作 (0-6): ").strip()

            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                simulate_ai_conversation_and_save()
            elif choice == '2':
                load_and_display_logs()
            elif choice == '3':
                list_log_files()
            elif choice == '4':
                if confirm_action("清除所有日志"):
                    clear_all_logs()
            elif choice == '5':
                full_workflow_test()
            elif choice == '6':
                test_via_main_function()
            else:
                print("❌ 无效选择，请输入 0-6")

        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 操作异常: {e}")

def list_log_files():
    """列出日志文件"""
    print("\n📋 查看日志文件列表")
    print("=" * 60)

    try:
        from services.job_router import JobRouter

        os.environ['JOB_TYPE'] = 'log_list'
        os.environ['RUN_MODE'] = 'production'

        router = JobRouter()
        result = router.route_job()

        if result == 0:
            print("✅ 日志列表获取成功！")
        else:
            print("❌ 日志列表获取失败！")

    except Exception as e:
        print(f"❌ 获取日志列表异常: {e}")
    finally:
        if 'JOB_TYPE' in os.environ:
            del os.environ['JOB_TYPE']

def confirm_action(action_name):
    """确认操作"""
    while True:
        confirm = input(f"⚠️ 确定要{action_name}吗？(y/n): ").strip().lower()
        if confirm in ['y', 'yes', '是']:
            return True
        elif confirm in ['n', 'no', '否']:
            return False
        else:
            print("请输入 y/n")

def full_workflow_test():
    """完整流程测试"""
    print("\n� 完整流程测试：对话→保存→加载→清除")
    print("=" * 80)

    # 步骤1: 模拟对话并保存
    print("步骤 1/4: 模拟AI对话并保存")
    conversation_id = simulate_ai_conversation_and_save()
    if not conversation_id:
        print("❌ 流程测试失败：无法保存对话")
        return

    input("\n按回车键继续到下一步...")

    # 步骤2: 加载并显示
    print("\n步骤 2/4: 加载并显示日志")
    if not load_and_display_logs():
        print("❌ 流程测试失败：无法加载日志")
        return

    input("\n按回车键继续到下一步...")

    # 步骤3: 查看日志列表
    print("\n步骤 3/4: 查看日志文件列表")
    list_log_files()

    input("\n按回车键继续到最后一步...")

    # 步骤4: 清除日志（可选）
    print("\n步骤 4/4: 清除日志（可选）")
    if confirm_action("清除所有日志"):
        clear_all_logs()
        print("✅ 完整流程测试完成！")
    else:
        print("✅ 完整流程测试完成（保留日志文件）！")

def test_via_main_function():
    """通过main函数测试所有日志功能"""
    print("\n🧪 通过main函数测试所有日志功能")
    print("=" * 60)

    # 设置环境变量来触发日志管理功能
    os.environ['RUN_MODE'] = 'production'
    os.environ['EXECUTE_LOG_FUNCTIONS'] = 'true'
    os.environ['SKIP_CONVERSATION_TEST'] = 'true'
    os.environ['EXECUTE_DEFAULT_BUTTONS'] = 'false'

    try:
        from main import main
        result = main()

        if result == 0:
            print("✅ main函数中的日志管理功能测试成功")
        else:
            print("❌ main函数中的日志管理功能测试失败")

    except Exception as e:
        print(f"❌ main函数测试异常: {e}")
    finally:
        # 清理环境变量
        for key in ['RUN_MODE', 'EXECUTE_LOG_FUNCTIONS', 'SKIP_CONVERSATION_TEST', 'EXECUTE_DEFAULT_BUTTONS']:
            if key in os.environ:
                del os.environ[key]

if __name__ == '__main__':
    print("🚀 AI对话日志管理功能测试")
    print("=" * 80)
    print("这个脚本可以帮你测试AI对话日志的完整生命周期：")
    print("• 模拟AI对话数据生成")
    print("• 保存对话日志到文件")
    print("• 加载和显示历史对话")
    print("• 管理和清理日志文件")
    print("=" * 80)

    # 启动交互式菜单
    interactive_menu()
