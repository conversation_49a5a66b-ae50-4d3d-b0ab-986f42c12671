import os
import time

import google.auth.transport.requests as google_requests
import google.oauth2.id_token as id_token
import requests

from configs.env import settings
from utils.logger import get_logger

logger = get_logger(__name__)


class VtafApiClients:
    def __init__(self):
        self.api_base = "https://" + settings.DOMAIN_NAME + "/vtaf-api"
        self.file_path = os.path.join(settings.SOURCE_PATH, settings.FILE_NAME)
        self.timeout = 60

        creds = id_token.fetch_id_token_credentials(request=google_requests.Request(), audience=settings.VTAF_AI_CLIENT_ID)
        self.session = google_requests.AuthorizedSession(creds) 

    # Check health of a specific service endpoint with retries
    def check_health(self, service: str) -> bool:
        url = f"{self.api_base}/v1/{service}/health"
        for attempt in range(1, 4):
            try:
                response = self.session.get(url, timeout=self.timeout)
                response.raise_for_status()
                logger.info(f"{service.capitalize()} is healthy.")
                return True
            except requests.exceptions.RequestException as e:
                logger.warning(f"Attempt {attempt} failed: {e}")
                logger.info("Retrying in 10 seconds...")
                time.sleep(10)

        logger.error(f"{service.capitalize()} health check failed after 3 attempts.")
        return False

    # Summarize a block of text using the summarization endpoint
    def generate_test_script(self, testcase: str, user_email: str) -> str:
        url = f"{self.api_base}/v1/agents/generate_test_script"
        payload = {"TestCases": testcase, "email": user_email, "job_id": settings.CLOUD_RUN_EXECUTION}

        try:
            response = self.session.post(url, json=payload, timeout=self.timeout * 2)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Generation error: {e}")
            return ""


