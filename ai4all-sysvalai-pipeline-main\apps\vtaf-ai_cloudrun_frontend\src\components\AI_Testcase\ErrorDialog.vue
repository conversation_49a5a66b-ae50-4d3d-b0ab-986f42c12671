<template>
  <q-dialog v-model="errorDialogVisible" persistent>
    <q-card
      class="bg-negative text-white"
      style="width: 400px; max-width: 90vw"
    >
      <q-card-section class="row items-center q-pa-md">
        <q-icon name="error" size="md" class="q-mr-sm" />
        <div class="text-h6">Oops! Something went wrong</div>
      </q-card-section>
      <q-card-section
        style="white-space: pre-wrap; max-height: 50vh; overflow-y: auto"
      >
        {{ errorDialogMessage }}
      </q-card-section>
      <q-card-actions align="right" class="q-pa-md">
        <q-btn
          flat
          label="OK"
          color="white"
          @click="errorDialogVisible = false"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useRunTcGenerationStore } from "src/stores/ai_testcase/run-tc-generation-store.js";
import { storeToRefs } from "pinia";

const ai_testcase_config = useRunTcGenerationStore();
const { errorDialogMessage, errorDialogVisible } =
  storeToRefs(ai_testcase_config);
</script>
