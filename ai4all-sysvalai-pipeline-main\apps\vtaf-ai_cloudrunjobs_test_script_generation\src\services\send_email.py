import base64
import json
import os
import re
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ip<PERSON>
from email.mime.text import MIMEText

from google.auth.transport.requests import Request
from google.cloud import secretmanager
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from configs.env import settings

# Define the scope required for sending emails
SCOPES = ['https://www.googleapis.com/auth/gmail.send']

# The token file stores user credentials, created after first authentication
TOKEN_FILE = 'token.json'
CREDENTIALS_FILE = 'OAuthCredentials.json'  # Downloaded OAuth credentials from Google Cloud Console



def authenticate_gmail_api():
    """Authenticate using OAuth2 and return the Gmail service."""
    creds = None
    # Check if token.json exists (previously authorized)
    client_token = get_email_token()
    with open("token.json", "w") as temp_file:
        json.dump(client_token, temp_file)

    if os.path.exists(TOKEN_FILE):
        creds = Credentials.from_authorized_user_file(TOKEN_FILE, SCOPES)

    # If no credentials or credentials are expired, get new ones
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            # Load credentials from Secret Manager
            client_secrets = get_oauth_credentials_from_secret()
            with open("OAuthCredentials.json", "w") as temp_file:
                json.dump(client_secrets, temp_file)
            flow = InstalledAppFlow.from_client_secrets_file(
                "OAuthCredentials.json", SCOPES)
            creds = flow.run_local_server(port=0)

            # Clean up temp file (optional but good practice)
            os.remove("OAuthCredentials.json")

        # Save the credentials for next time
        with open(TOKEN_FILE, 'w') as token:
            token.write(creds.to_json())

    # Build Gmail API service
    return build('gmail', 'v1', credentials=creds)


def get_email_token(secret_name="vtaf-ai-email-token", project_id=settings.SERVICES_PROJECT_NUMBER):
    """Fetch the OAuth2 client secret JSON from Secret Manager."""
    client = secretmanager.SecretManagerServiceClient()
    secret_path = f"projects/{project_id}/secrets/{secret_name}/versions/latest"

    response = client.access_secret_version(request={"name": secret_path})
    secret_payload = response.payload.data.decode("UTF-8")

    return json.loads(secret_payload)


def get_oauth_credentials_from_secret(secret_name="vtaf-ai-email-oauth", project_id=settings.SERVICES_PROJECT_NUMBER):
    """Fetch the OAuth2 client secret JSON from Secret Manager."""
    client = secretmanager.SecretManagerServiceClient()
    secret_path = f"projects/{project_id}/secrets/{secret_name}/versions/latest"

    response = client.access_secret_version(request={"name": secret_path})
    secret_payload = response.payload.data.decode("UTF-8")

    return json.loads(secret_payload)


# Create and send the email with HTML body
def send_email(service, sender, to, subject, html_body):
    """Create and send an HTML email message with a table."""
    message = MIMEMultipart()
    message['to'] = to
    message['from'] = sender
    message['subject'] = subject

    # Create the HTML message
    msg = MIMEText(html_body, 'html')
    message.attach(msg)

    raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()

    try:
        message = service.users().messages().send(userId="me", body={'raw': raw_message}).execute()
        print(f'Message sent to {to}, Message ID: {message["id"]}')
    except HttpError as error:
        print(f'An error occurred: {error}')


def extract_first_name(email):
    # Split by '.' or '-'
    parts = re.split(r'[.-]', email.split('@')[0])  # Split on the local part (before '@')

    # Take the first part and capitalize the first character
    first_name = parts[0].capitalize()

    return first_name


def send_email_toVTAF_User(useremail, information):
    sender_email = "<EMAIL>"  # Your no-reply email address
    recipient_email = useremail  # Recipient email address
    username = extract_first_name(useremail)
    subject = f"[VTAF.AI]Generate Test Script Job Status: {information['execution_id']}"
    html_body = f"""
        <html>
        <body>

        <p>Hi {username},<br></p>

            <p>Below is the requested job execution data:</p>
                    
            <h3><u>Job Information:</u></h3>
            <table border="1">
                <tr>
                    <th>Job Start Time</th>
                    <th>Job End Time</th>
                    <th>Job Duration</th>
                    <th>Business Group</th>
                    <th>Product Group</th>
                    <th>Product Line</th>
                    <th>Job Status</th>
                    <th>Execution Status</th>
                </tr>
                <tr>
                    <td>{information['job_start_time']}</td>
                    <td>{information['job_end_time']}</td>
                    <td>{information['job_duration']}</td>
                    <td>{information['bg']}</td>
                    <td>{information['pg']}</td>
                    <td>{information['pl']}</td>
                    <td>{information['job_status']}</td>
                    <td>{information['execution_status']}</td>
                </tr>
            </table>
            <h3><u>Execution Information:</u></h3>
            <table border="1">
                <tr>
                    <th>VTAF AI Job Execution ID</th>
                </tr>
                <tr>
                    <td>{information['execution_id']}</td>
                </tr>
            </table>
            <h3><u>Additional Information:</u></h3>
            <p>User Input File:</p>
            <table border="1">
                <tr>
                    <th>Input File</th>
                </tr>
                <tr>
                    <td>{information['user_upload']}</td>
                </tr>
            </table>
            <p>Job Output File:</p>
            <table border="1">
                <tr>
                    <th>Output CSV</th>
                </tr>
                <tr>
                    <td>{information['result_file']}</td>
                </tr>
            </table>
            <p>LLM Details:</p>
            <table border="1">
                <tr>
                    <th>Model</th>
                    <th>Location</th>
                </tr>
                <tr>
                    <td>{information['model']}</td>
                    <td>{information['location']}</td>
                </tr>
            </table>            
            <p>Agent Usage details:</p>
            <table border="1">
                <tr>
                    <th>Total Token</th>
                    <th>Prompt Token</th>
                    <th>Cached Prompt Token</th>
                    <th>Completion Token</th>
                    <th>Successful Agent Request</th>
                </tr>
                <tr>
                    <td>{information['total_token']}</td>
                    <td>{information['prompt_token']}</td>
                    <td>{information['cached_prompt_token']}</td>
                    <td>{information['completion_token']}</td>
                    <td>{information['successful_agent_request']}</td>
                </tr>
            </table>            
            
        <p><a href="https://{settings.DOMAIN_NAME}/#/">Visit VTAF.AI Website for detailed view!</a></p>
        
        <p>Note: This is an automated Email.</p>
            <p>Best regards,</p>
            <p>VTAF AI Team</p>
        </body>
        </html>
    """
    try:
        service = authenticate_gmail_api()
        send_email(service, sender_email, recipient_email, subject, html_body)
        print("Email sent successfully!")
    except Exception as e:
        print(f"Error in sending email: {e}")
