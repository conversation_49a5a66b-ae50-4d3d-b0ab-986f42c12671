#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug parsing functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.data_processing import parse_test_cases, parse_single_case

def debug_test_case_parsing():
    """Debug test case parsing"""
    print("=== Debugging Test Case Parsing ===")
    
    test_input = """
Confidence Score: 85%

**Test Case ID**: TC_001
**Covered Requirement ID**: REQ_001
**Test Objective**: Test basic functionality
**Test Condition**: System is ready
**Test Action**: Execute test
**Test Expectation**: Test passes
"""
    
    print("Input text:")
    print(repr(test_input))
    print("\nInput text (formatted):")
    print(test_input)
    
    result = parse_test_cases(test_input)
    
    print(f"\nResult: {result}")
    print(f"Confidence Score: {result.get('Confidence Score', 'NOT FOUND')}")
    print(f"Test Cases: {result.get('Test Cases', [])}")
    print(f"Number of test cases: {len(result.get('Test Cases', []))}")
    
    # Let's also test the regex pattern directly
    import re
    
    case_texts = re.findall(
        r"(?i)(?:\*+|#+|:+|：+)*(?:\s?)(?:\*+|#+|:+|：+)*\s*Test Case ID\s?:.*?(?=(?:\*+|#+|:+|：+)*(?:\s?)(?:\*+|#+|:+|：+)*\s*Test Case ID\s?:|\Z)",
        test_input, re.DOTALL)
    
    print(f"\nRegex matches: {len(case_texts)}")
    for i, match in enumerate(case_texts):
        print(f"Match {i+1}:")
        print(repr(match))
        print("---")
        
        # Test single case parsing
        single_result = parse_single_case(match)
        print(f"Single case result: {single_result}")
        print("=" * 40)

if __name__ == "__main__":
    debug_test_case_parsing()
