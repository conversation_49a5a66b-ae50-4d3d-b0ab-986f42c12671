from services.vtaf_api_clients import VtafApiClients
from configs.env import settings
from utils.logger import get_logger

logger = get_logger(__name__)

def run_job(project_detail) -> str:
    vtaf_apis = VtafApiClients()

    # Perform all required health checks at the beginning
    required_services = ["parsing", "chunking", "vectordb"]
    if settings.ENABLE_SUMMARIZATION:
        required_services.append("agents")

    unhealthy_services = [
        service for service in required_services
        if not vtaf_apis.check_health(service)
    ]

    if unhealthy_services:
        raise RuntimeError(
            f"Health check failed for: {', '.join(unhealthy_services)}. Job aborted."
        )

    parsed_data = vtaf_apis.parse_file()
    if not parsed_data:
        raise RuntimeError("Parsing failed. Job aborted.")

    pages = [entry["page_content"] for entry in parsed_data]

    for idx, page_text in enumerate(pages):
        logger.info(f"Processing page {idx + 1}/{len(pages)}")

        chunks_data = vtaf_apis.chunk_text(page_text)
        if not chunks_data:
            logger.warning(f"No chunks for page {idx}. Skipping.")
            continue

        chunks = [chunk["text"] for chunk in chunks_data]
        chunks = [chunk for chunk in chunks if chunk.strip()] 
        
        if settings.ENABLE_SUMMARIZATION:
            chunks = [vtaf_apis.summarize_text(chunk) for chunk in chunks]
            chunks = [chunk for chunk in chunks if chunk.strip()]

        if chunks:
            vtaf_apis.add_chunks_to_vector_db(chunks, project_detail)

    logger.info("Job completed.")
    return "success"
