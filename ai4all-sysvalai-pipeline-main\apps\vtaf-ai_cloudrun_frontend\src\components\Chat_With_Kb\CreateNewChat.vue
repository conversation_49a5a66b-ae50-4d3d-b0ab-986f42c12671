<template>
  <div>
  <transition name="fade">
    <div v-if="current_session_id===''" class="q-pt-xl flex flex-center column items-center text-center">
      <!-- Greeting Text -->
      <div class="text-h5 text-primary q-mb-lg">Hello, {{ user_name }} 👋</div>
      <div class="text-subtitle1 q-mb-xs">What can I do for you today?</div>
      <div class="text-body2 text-grey-7 q-mb-md">Have a chat with your knowledgebase assistant.</div>

      <!-- Create Chat Button -->
      <q-btn
        label="Create New Chat"
        color="primary"
        icon="add_comment"
        unelevated
        outline
        @click="goToNewChat()"
      />
      <q-inner-loading :showing="isConversationLoading">
        <q-spinner-puff size="50px" color="primary" />
      </q-inner-loading>
    </div>
  </transition>
<q-dialog v-model="showProjectDialog" persistent>
  <q-card style="min-width: 400px">
    <q-card-section class="text-h6">
      Select Project for New Conversation
    </q-card-section>

    <q-card-section>
      <q-select
        filled
        dense
        use-input
        input-debounce="0"
        v-model="dummy_variant_id"
        :options="filteredOptions"
        @filter="filterFn"
        @update:model-value="handleSelect"
        emit-value
        map-options
        clearable
        label="Select Project"
        :loading="projects_dropdown_loading"
      />
    </q-card-section>

    <q-card-actions align="right">
      <q-btn flat label="Cancel" v-close-popup />
      <q-btn
        unelevated
        label="Start Chat"
        color="primary"
        @click="createChatSessionWithProject"
        :disable="!dummy_variant_id"
        :loading="start_chat_button_loading"
      />
    </q-card-actions>
  </q-card>
</q-dialog>
</div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useChatWithKnowledgebaseStore } from 'src/stores/chat_with_kb/chat_with_knowledgebase_router';
import { storeToRefs } from "pinia";
import { useRouter } from 'vue-router';

const router = useRouter();

const showProjectDialog = ref(false);
const start_chat_button_loading = ref(false);

const chat_with_kbStore = useChatWithKnowledgebaseStore();
const { recent_chats, recentChats, olderChats, user_name, current_session_id, current_session_details, isConversationLoading, isChatLoading, selected_project_variant, selected_project_variant_uuid, projects_variants_dropdown } = storeToRefs(chat_with_kbStore);

const { create_session, get_chats } = chat_with_kbStore;

const dummy_varinat = ref("");
const dummy_variant_id = ref("");

const loadChats = async () => {
  isChatLoading.value=true;
  await get_chats();

  recentChats.value = [];
  olderChats.value = [];

  const now = new Date()
  for (const chat of recent_chats.value.sessions) {
    const lastMod = new Date(chat.last_modified)
    const diff = (now - lastMod) / (1000 * 60 * 60 * 24)
    if (diff < 7) {
      recentChats.value.push(chat)
    } else {
      olderChats.value.push(chat)
    }
  }
  isChatLoading.value=false;
}

// const goToNewChat = async () => {
//   isConversationLoading.value = true;
//   current_session_details.value = [];
//   await create_session();
//   console.log('New Session Initiated')
//   isConversationLoading.value = false;
//   await loadChats();
//   // router.push('/chat_with_knowledgebase_trial')
//   router.push(`/chat_with_knowledgebase/${current_session_id.value}`);
// }

const createChatSessionWithProject = async () => {
  if (!dummy_variant_id.value) return;
  selected_project_variant.value = dummy_varinat.value;
  selected_project_variant_uuid.value = dummy_variant_id.value;
  start_chat_button_loading.value = true;
  current_session_details.value = [];
  await create_session(selected_project_variant_uuid.value);
  start_chat_button_loading.value=false;
  showProjectDialog.value = false;
  await loadChats();
  router.push(`/chat_with_knowledgebase/${current_session_id.value}`);
};

const goToNewChat = async () => {
  dummy_varinat.value = "";
  dummy_variant_id.value = "";
  showProjectDialog.value = true;
};

// Turn { key: uuid } into dropdown options
const projectsOptions = computed(() =>
  Object.entries(projects_variants_dropdown.value).map(([label, value]) => ({
    label,
    value,
  }))
);

// Filtering support
const filterQuery = ref("");
const filteredOptions = computed(() =>
  projectsOptions.value.filter((option) =>
    option.label.toLowerCase().includes(filterQuery.value.toLowerCase())
  )
);
const filterFn = (val, update) => {
  filterQuery.value = val;
  update();
};

// Save both UUID and key when selected
const handleSelect = (uuid) => {
  dummy_variant_id.value = uuid;
  const found = Object.entries(projects_variants_dropdown.value).find(
    ([key, val]) => val === uuid
  );
  dummy_varinat.value = found ? found[0] : null;
};
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.6s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
