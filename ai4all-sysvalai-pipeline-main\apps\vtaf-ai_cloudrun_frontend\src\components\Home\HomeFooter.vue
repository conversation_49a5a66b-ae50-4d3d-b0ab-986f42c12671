<template>
  <footer>
    <div class="container">
      <div class="row">
        <div class="col" id="company">
          <img src="src/assets/home/<USER>" alt="" class="logo" />
          <p>
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Dolorem
            labore, quis repudiandae nemo magnam earum ullam necessitatibus
            facilis ipsum quasi.
          </p>
          <!-- <div class="social">
            <a href="#"><i class="fab fa-facebook"></i></a>
            <a href="#"><i class="fab fa-instagram"></i></a>
            <a href="#"><i class="fab fa-youtube"></i></a>
            <a href="#"><i class="fab fa-twitter"></i></a>
            <a href="#"><i class="fab fa-linkedin"></i></a>
          </div> -->
        </div>

        <div class="col" id="services">
          <h3>Services</h3>
          <div class="links">
            <a href="#">Services</a>
            <a href="#">Services</a>
            <a href="#">Services</a>
            <a href="#">Services</a>
          </div>
        </div>

        <div class="col" id="useful-links">
          <h3>Services</h3>
          <div class="links">
            <a href="#">Services</a>
            <a href="#">Services</a>
            <a href="#">Services</a>
            <a href="#">Services</a>
          </div>
        </div>

        <div class="col" id="contact">
          <h3>Contact</h3>
          <div class="contact-details">
            <q-icon name="location_on"></q-icon>
            <p>
              QWERTYUIERT, <br />
              CHENNAI, INDIA
            </p>
          </div>
          <div class="contact-details">
            <q-icon name="phone"></q-icon>
            <p>+02 1111111111</p>
          </div>
        </div>
      </div>

      <!-- <div class="row">
        <div class="form">
          <form action="">
            <input type="text" placeholder="Email here..." />
            <button><i class="fa fa-paper-plane"></i></button>
          </form>
        </div>
      </div> -->
    </div>
  </footer>
</template>

<style scoped>
footer {
  background: linear-gradient(to bottom, #014a88, #000);
  position: relative;
  width: 100%;
  min-height: 350px;
  padding: 3rem 1rem;
}
.container {
  max-width: 1140px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.col {
  min-width: 250px;
  color: #f2f2f2f2;
  font-family: poppins;
  padding: 0 2rem;
}
.col .logo {
  width: 100px;
  margin-bottom: 10px;
}
.col h3 {
  color: #f2f2f2f2;
  margin-bottom: 20px;
  position: relative;
  cursor: pointer;
}
.col h3::after {
  content: "";
  height: 3px;
  width: 0px;
  background-color: #f2f2f2f2;
  position: absolute;
  bottom: 0;
  left: 0;
  transition: 0.3s ease;
}
.col h3:hover::after {
  width: 30px;
}
.col .social a i {
  color: #f2f2f2f2;
  margin-top: 2rem;
  margin-right: 5px;
  transition: 0.3s ease;
}
.col .social a i:hover {
  transform: scale(1.5);
  filter: grayscale(25);
}
.col .links a {
  display: block;
  text-decoration: none;
  color: #f2f2f2;
  margin-bottom: 5px;
  position: relative;
  transition: 0.3s ease;
}
.col .links a::before {
  content: "";
  height: 16px;
  width: 3px;
  position: absolute;
  top: 5px;
  left: -10px;
  background-color: #f2f2f2f2;
  transition: 0.5s ease;
  opacity: 0;
}
.col .links a:hover::before {
  opacity: 1;
}
.col .links a:hover {
  transform: translateX(-8px);
  color: #f2f2f2f2;
}
.col .contact-details {
  display: inline-flex;
  justify-content: space-between;
}
.col .contact-details i {
  margin-right: 15px;
}
.row .form {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}
.row .form input {
  background-color: #1a1c20ff;
  border: 0;
  outline: none;
  padding: 14px 20px;
  border-radius: 6px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.form button {
  padding: 14px 20px;
  border: 0;
  border-radius: 6px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  background-color: #014a88;
}

/********** Responsive Design ********/
@media (max-width: 900px) {
  .row {
    flex-direction: column;
  }
  .col {
    width: 100%;
    text-align: left;
    margin-bottom: 25px;
  }
}
@media (max-width: 768px) {
  .row {
    flex-direction: column;
  }
  .col {
    width: 100%;
    text-align: left;
    margin-bottom: 20px;
  }
}
</style>
