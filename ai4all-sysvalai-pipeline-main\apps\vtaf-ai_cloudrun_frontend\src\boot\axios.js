import { boot } from "quasar/wrappers";
import axios from "axios";

// Be careful when using SSR for cross-request state pollution
// due to creating a Singleton instance here;
// If any client changes this (global) instance, it might be a
// good idea to move this instance creation inside of the
// "export default () => {}" function below (which runs individually
// for each client)
// const api = axios.create({ baseURL: 'https://api.example.com' })
// console.log(process.env.ENV_FILE);

const chat_api_url = process.env.CHAT_API_URL;
const testcase_api_url = process.env.TESTCASE_API_URL;
const chat_kb_api_url = process.env.CHAT_KB_URL;
const chat_bigquery_api_url = process.env.CHAT_BIGQUERY_URL;

const chatURL = process.env.DEV
  ? "http://127.0.0.1:3000/chat"
  :  chat_api_url;

const ai_testcaseURL = process.env.DEV
  ? "http://127.0.0.1:3001/ai_testcase"
  :  testcase_api_url;

const chat_kbURL = process.env.DEV
  ? "http://127.0.0.1:3002/conversation"
  :  chat_kb_api_url;

const chat_bigqueryURL = process.env.DEV
  ? "http://127.0.0.1:3003/vtaf-api"
  :  chat_bigquery_api_url;

// const chatURL = "https://" + process.env.DOMAIN_NAME + "/chat";
// const ai_testcaseURL = "https://" + process.env.DOMAIN_NAME + "/ai_testcase";

const chatApi = axios.create({ baseURL: chatURL });
const ai_testcaseApi = axios.create({ baseURL: ai_testcaseURL });
const chat_with_kb = axios.create({ baseURL: chat_kbURL });
const chat_with_bqApi = axios.create({ baseURL: chat_bigqueryURL });

export default boot(({ app }) => {
  // for use inside Vue files (Options API) through this.$axios and this.$api

  app.config.globalProperties.$axios = axios;
  // ^ ^ ^ this will allow you to use this.$axios (for Vue Options API form)
  //       so you won't necessarily have to import axios in each vue file

  // app.config.globalProperties.$api = api
  // ^ ^ ^ this will allow you to use this.$api (for Vue Options API form)
  //       so you can easily perform requests against your app's API
});

export { chatApi, ai_testcaseApi, chat_with_kb, chat_with_bqApi };
