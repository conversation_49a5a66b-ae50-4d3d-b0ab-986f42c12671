<template>
  <div
    v-if="!prompt_response"
    class="banner q-pa-lg row justify-center items-center"
    ref="scrollTarget"
  >
    <!-- Image on the left side with 3D rotation animation -->
    <img
      src="src/assets/home/<USER>"
      alt="Help Image"
      class="banner-image q-mr-md"
      style="height: 50px; width: 50px"
    />

    <!-- If prompt_loading is true, show spinner -->
    <div v-if="prompt_loading" class="q-ml-md">
      <q-spinner-dots color="primary" size="40px" />
    </div>

    <!-- Text that displays letter by letter -->
    <h1 v-else class="banner-text text-h5 text-bold">{{ displayedText }}</h1>
  </div>
</template>

<script setup>
import { storeToRefs } from "pinia";
import { useChatStore } from "src/stores/chat/chat_store";
import { ref, onMounted } from "vue";
import { QSpinnerDots } from "quasar";

const chat_store = useChatStore();
const { prompt_response, prompt_loading } = storeToRefs(chat_store);

const scrollTarget = ref(null);
const displayedText = ref(""); // Holds the animated text

const fullText = "What can I help with?"; // Complete text to display
const typingSpeed = 100; // Speed of typing effect in milliseconds

// Handle smooth scroll when needed
const smoothScrollToBanner = () => {
  scrollTarget.value.scrollIntoView({ behavior: "smooth" });
};

// Scroll to banner when the component is mounted
onMounted(() => {
  startTypingAnimation(); // Start typing animation on mount
});

// Function to simulate typing effect
const startTypingAnimation = () => {
  let index = 0; // Track the current index of the text

  const typeLetter = () => {
    if (index < fullText.length) {
      displayedText.value += fullText.charAt(index); // Add next letter to displayed text
      index++;
      setTimeout(typeLetter, typingSpeed); // Call the function again after typing speed delay
    }
  };

  typeLetter(); // Start the typing effect
};
</script>

<style scoped>
/* Banner style */
.banner {
  border-radius: 12px;

  display: flex;
  justify-content: center; /* Horizontally centers the content */
  align-items: center; /* Vertically centers the content */
  min-height: 150px; /* Adjust the height if needed */
}

/* Styling for the image */
.banner-image {
  object-fit: cover;
  border-radius: 50%;
  animation: rotateImage 2s ease-in-out forwards; /* Apply 3D rotation animation */
  transform-origin: center;
}

/* 3D rotation keyframes */
@keyframes rotateImage {
  0% {
    transform: rotateY(0deg);
  }
  100% {
    transform: rotateY(360deg);
  }
}

/* Text styling */
.banner-text {
  opacity: 1; /* No opacity animation needed */
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}
</style>
