{"name": "vtaf", "version": "0.0.1", "description": "A Webste for VTAF Application", "productName": "VTAF.AI Web App", "author": "Vigneshwaran SADASIVAM <<EMAIL>>", "private": true, "scripts": {"lint": "eslint --ext .js,.vue ./", "format": "prettier --write \"**/*.{js,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build"}, "dependencies": {"@quasar/extras": "^1.16.12", "@studio-freight/lenis": "^1.0.42", "axios": "^1.2.1", "chroma-js": "^2.4.2", "gsap": "^3.12.5", "highlight.js": "^11.10.0", "leaflet": "^1.9.4", "markdown-it": "^14.1.0", "markdown-it-prism": "^2.3.0", "marked": "^14.1.3", "papaparse": "^5.5.2", "pinia": "^2.0.11", "quasar": "^2.17.1", "quasar-dotenv": "^1.0.5", "vue": "^3.4.18", "vue-router": "^4.0.12", "xlsx": "^0.18.5", "xml2js": "^0.6.2"}, "devDependencies": {"@quasar/app-vite": "^1.10.2", "autoprefixer": "^10.4.2", "eslint": "^8.11.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-vue": "^9.0.0", "postcss": "^8.4.14", "prettier": "^2.5.1", "vite-plugin-checker": "^0.6.4"}, "engines": {"node": "^20 || ^18 || ^16", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}