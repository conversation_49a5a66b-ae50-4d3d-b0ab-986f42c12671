<template>
  <div>
  <div class="text-h4 q-ma-lg text-center text-blue-10">
    VTAF.AI TESTCASE GENERATION
  </div>
  <q-table
    flat
    bordered
    title="Running Reports"
    :rows="current_history"
    :columns="columns"
    row-key="timestamp"
    class="table_style"
    color="blue-10"
    :loading="current_history_table_loading"
  >
    <template v-slot:top>
      <q-icon size="50px" color="blue-10" name="task"></q-icon>
      <span class="text-blue-10 text-h5 q-ml-md">Running Reports</span>
    </template>

    <template v-slot:body-cell-report="props">
      <q-td :props="props">
        <q-btn
          color="primary"
          label="Report"
          @click="goToReport(props.row.execution_id)"
          dense
          size="sm"
        />
      </q-td>
    </template>

    <template v-slot:no-data>
      <div class="full-width row flex-center q-pa-md">
        <q-icon name="task_alt" size="md" color="green-7" class="q-mr-sm" />
        <span class="text-white-7">No Reports are running currently</span>
      </div>
    </template>
  </q-table>
  <div class="button_style">
  <div>
    <q-btn
      color="blue-2"
      text-color="blue-10"
      label="Past Testcase Logs"
      @click="go_to_history()"
      icon="history"
    />
  </div>
  <div>
    <q-btn
      color="blue-10"
      text-color="white"
      label="Multiprompting"
      @click="go_to_multiprompting_page()"
      icon="arrow_forward"
      class="q-mr-sm"
    />
    <q-btn
      color="blue-10"
      text-color="white"
      label="Generate Testcase Without Knowledgebase"
      @click="create_form()"
      icon="note_add"
      class="q-ml-sm"
    />
    <q-btn
      color="blue-10"
      text-color="white"
      label="Generate Testcase With Knowledgebase"
      @click="go_to_project_profile()"
      icon="note_add"
      class="q-ml-sm"
    />
  </div>
</div>
  <error-dialog></error-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useJobManagementStore } from "src/stores/ai_testcase/job-management-store";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";
import ErrorDialog from "./ErrorDialog.vue";

const router = useRouter();

const ai_testcase_config = useJobManagementStore();
const {
  current_history,
  current_job_execution_id,
  current_history_table_loading,
} = storeToRefs(ai_testcase_config);
const { get_user_email, get_current_jobs } = ai_testcase_config;

const columns = ref([
  {
    name: "timestamp",
    label: "Job Created",
    field: (row) => row.timestamp,
    align: "left",
    sortable: true,
  },
  {
    name: "execution_id",
    label: "Job ID",
    field: (row) => row.execution_id,
    align: "left",
    sortable: true,
  },
  {
    name: "execution_status",
    label: "Execution Status",
    field: (row) => row.execution_status,
    align: "left",
    sortable: true,
  },
  { name: "report", label: "Report", field: "report", align: "center" }, // Column for the button
]);

const goToReport = (executionId) => {
  current_job_execution_id.value = executionId;
  router.push(`/ai_testcase/job_status/${current_job_execution_id.value}`);
};

onMounted(async () => {
  await get_user_email();
  get_current_jobs();
});

const go_to_multiprompting_page = () => {
  router.push("/multiprompting");
}

const create_form = () => {
  router.push("/ai_testcase/create_job");
};

const go_to_history = () => {
  router.push("/ai_testcase/history");
};

const go_to_project_profile = () => {
  router.push("/ai_testcase/run_project_profile");
};
</script>

<style scoped>
.table_style {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  color: black;
  margin: 20px;
}
.button_style {
  display: flex;
  justify-content: space-between;
  margin: 20px;
}
</style>
