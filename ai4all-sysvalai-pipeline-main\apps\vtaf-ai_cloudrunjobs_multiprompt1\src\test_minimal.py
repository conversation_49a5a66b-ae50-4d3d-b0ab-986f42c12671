#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化测试脚本 - 用于验证云端部署
"""

import os
import sys
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    """最小化测试主函数"""
    print("🧪 最小化云端部署测试")
    print("=" * 50)
    
    try:
        # 1. 测试基础导入
        print("📦 测试基础模块导入...")
        
        # 设置云端环境标识
        os.environ['GOOGLE_CLOUD_PROJECT'] = 'valeo-cp2673-dev'
        
        from configs.env import settings
        print(f"✅ 配置加载成功: PROJECT_ID={settings.PROJECT_ID}")
        
        from utils.logger import get_logger
        logger = get_logger(__name__)
        logger.info("✅ 日志模块加载成功")
        
        # 2. 测试环境检测
        print("\n🌍 环境检测...")
        cloud_vars = ['K_SERVICE', 'FUNCTION_NAME', 'GAE_SERVICE', 'GOOGLE_CLOUD_PROJECT']
        is_cloud = any(os.getenv(var) for var in cloud_vars)
        print(f"环境类型: {'☁️  Google Cloud' if is_cloud else '💻 本地环境'}")
        
        # 3. 测试配置验证
        print("\n📝 配置验证...")
        required_settings = ['PROJECT_ID', 'REGION', 'DEFAULT_MODEL']
        for setting in required_settings:
            value = getattr(settings, setting, None)
            print(f"  {setting}: {value}")
        
        print("\n✅ 所有基础测试通过!")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
