from typing import Dict, Any, List
from services.ai_service import AIService
from services.sheet_manager import Sheet<PERSON>anager
from utils.data_processing import parse_test_cases
from utils.logger import get_logger

logger = get_logger(__name__)

class TestCaseService:
    """测试用例生成服务"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.sheet_manager = SheetManager()
        
    async def send_ts_training_dataset(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """发送TS训练数据集"""
        try:
            # 对应前端 SendTsTrainingDatasetButton 功能
            logger.info("Processing TS training dataset")
            
            if not self.sheet_manager.connect_sheet(config['test_spec_sheet_url']):
                return {"status": "error", "message": "Failed to connect to test spec sheet"}
                
            # 读取TS数据集
            ts_dataset = self.sheet_manager.read_by_sheet_name(config['ts_dataset_sheet_name'])
            
            return {
                "status": "success",
                "message": f"TS training dataset loaded with {len(ts_dataset)} records",
                "data": ts_dataset
            }
            
        except Exception as e:
            logger.error(f"TS training dataset error: {e}")
            return {"status": "error", "message": str(e)}
            
    async def send_batch_test_cases(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """批量生成测试用例"""
        try:
            # 对应前端 SendBatchTCsButton 功能
            logger.info("Processing batch test cases generation")
            
            # 连接到提示词表格
            if not self.sheet_manager.connect_sheet(config['prompt_sheet_url']):
                return {"status": "error", "message": "Failed to connect to prompt sheet"}
                
            # 读取所有提示词
            sheet_names = self.sheet_manager.get_sheet_names()
            total_cases = 0
            
            for sheet_name in sheet_names:
                if sheet_name.startswith("Prompt_"):
                    cases_generated = await self._process_sheet_prompts(sheet_name, config)
                    total_cases += cases_generated
                    
            return {
                "status": "success",
                "message": f"Batch test case generation completed. Generated {total_cases} test cases",
                "total_cases": total_cases
            }
            
        except Exception as e:
            logger.error(f"Batch test cases error: {e}")
            return {"status": "error", "message": str(e)}
            
    async def send_single_test_case(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """生成单个测试用例"""
        try:
            # 对应前端 SendSingleTCButton 功能
            logger.info("Processing single test case generation")
            
            prompt = config.get('prompt', '')
            model = config.get('model', settings.DEFAULT_MODEL)
            
            if not prompt:
                return {"status": "error", "message": "Prompt is required"}
                
            # 生成测试用例
            response = self.ai_service.generate_response(prompt, model)
            test_cases = parse_test_cases(response)
            
            return {
                "status": "success",
                "message": "Single test case generation completed",
                "test_cases": test_cases,
                "raw_response": response
            }
            
        except Exception as e:
            logger.error(f"Single test case error: {e}")
            return {"status": "error", "message": str(e)}
            
    async def _process_sheet_prompts(self, sheet_name: str, config: Dict[str, Any]) -> int:
        """处理单个工作表的提示词"""
        try:
            data = self.sheet_manager.read_by_sheet_name(sheet_name)
            cases_count = 0
            
            for i, row in enumerate(data):
                if row.get("Status") == "ready" and row.get("Prompt_Design", "").strip():
                    prompt = row.get("Prompt_Design")
                    model = config.get('model', settings.DEFAULT_MODEL)
                    
                    # 生成响应
                    response = self.ai_service.generate_response(prompt, model)
                    test_cases = parse_test_cases(response)
                    
                    # 保存结果
                    if test_cases.get("Test Cases"):
                        cases_count += len(test_cases["Test Cases"])
                        self.sheet_manager.update_cell(sheet_name, i + 2, 3, "completed")
                    else:
                        self.sheet_manager.update_cell(sheet_name, i + 2, 3, "genFailed")
                        
            return cases_count
            
        except Exception as e:
            logger.error(f"Error processing sheet {sheet_name}: {e}")
            return 0