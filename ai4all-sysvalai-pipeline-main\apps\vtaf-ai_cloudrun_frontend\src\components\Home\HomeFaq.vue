<!-- <template>
  <div class="faq flex flex-center">
    <q-card class="my-card">
      <q-card-section>
        <div class="text-h6">Frequently Asked Questions</div>
      </q-card-section>

      <q-separator />

      <q-list>
        <q-expansion-item
          v-for="(item, index) in faqs"
          :key="index"
          expand-separator
          icon="question_answer"
          :label="item.question"
        >
          <q-card>
            <q-card-section>{{ item.answer }}</q-card-section>
          </q-card>
        </q-expansion-item>
      </q-list>
    </q-card>
  </div>
</template>

<script setup>
const faqs = [
  {
    question: "How do I install Quasar?",
    answer:
      "You can install Quasar by running the following command: npm install -g @quasar/cli",
  },
  {
    question: "How do I create a new Quasar project?",
    answer:
      "You can create a new Quasar project by running the following command: quasar create my-quasar-app",
  },
  // Add more FAQ items as needed
];
</script>

<style scoped>
.my-card {
  width: 100%;
  max-width: 800px;
}
.faq {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
</style> -->

<template>
  <div class="row faq">
    <div class="col-5"></div>
    <div class="col-7">
      <div class="text-h6 text-center faq_head">Frequently Asked Questions</div>
      <q-separator class="separator"></q-separator>
      <div class="card">
        <q-card class="my-card">
          <q-list>
            <q-expansion-item
              class="question"
              v-for="(item, index) in faqs"
              :key="index"
              expand-separator
              :icon="item.icon"
              :label="item.question"
              :expanded="expandedIndex === index"
              @click="toggleExpansion(index)"
            >
              <q-card>
                <q-card-section>{{ item.answer }}</q-card-section>
              </q-card>
            </q-expansion-item>
          </q-list>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const expandedIndex = ref(null); // Initialize expandedIndex to null

const faqs = [
  {
    question: "How do I install Quasar?",
    answer:
      "You can install Quasar by running the following command: npm install -g @quasar/cli",
    icon: "navigate_next",
  },
  {
    question: "How do I create a new Quasar project?",
    answer:
      "You can create a new Quasar project by running the following command: quasar create my-quasar-app",
    icon: "question_answer",
  },
  {
    question: "How do I install Quasar?",
    answer:
      "You can install Quasar by running the following command: npm install -g @quasar/cli",
    icon: "navigate_next",
  },
  {
    question: "How do I create a new Quasar project?",
    answer:
      "You can create a new Quasar project by running the following command: quasar create my-quasar-app",
    icon: "question_answer",
  },
  {
    question: "How do I install Quasar?",
    answer:
      "You can install Quasar by running the following command: npm install -g @quasar/cli",
    icon: "navigate_next",
  },
  {
    question: "How do I create a new Quasar project?",
    answer:
      "You can create a new Quasar project by running the following command: quasar create my-quasar-app",
    icon: "question_answer",
  },
  // Add more FAQ items as needed
];

const toggleExpansion = (index) => {
  // Toggle the clicked item
  expandedIndex.value = expandedIndex.value === index ? null : index;
};
</script>

<style scoped>
.my-card {
  width: 90%;
  max-width: 1000px;
}
.faq {
  border: 1px solid;
  border-radius: 1rem;
  box-shadow: 20px 20px 50px grey;
  width: 90%;
  margin: 0 auto;
  height: 500px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.card {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
}
.faq_head {
  margin-bottom: 1rem;
}
.separator {
  width: 50%;
  margin: 0 auto;
}
</style>
