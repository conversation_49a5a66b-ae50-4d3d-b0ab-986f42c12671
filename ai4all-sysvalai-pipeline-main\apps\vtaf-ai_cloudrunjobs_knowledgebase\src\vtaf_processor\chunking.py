import requests
import logging

from config import API_BASE, CHUNKING_STRATEGY

def chunk_text(text: str, headers: dict) -> list:
    url = f"{API_BASE}/v1/chunking/single-string-chunking"
    payload = {
        "string": text,
        "chunking_strategy": CHUNKING_STRATEGY,
        "parameters": {}
    }

    try:
        response = requests.post(url, headers=headers, json=payload)
        if response.status_code == 200:
            logging.info("Chunking successful.")
            return response.json().get("results", [])
    except Exception as e:
        logging.error(f"Chunking error: {e}")
    
    return []
