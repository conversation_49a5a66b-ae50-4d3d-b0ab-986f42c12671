from google.cloud import bigquery
from google.oauth2 import service_account
import os

SERVICES_PROJECT_ID = os.getenv("SERVICES_PROJECT_ID", "")

#Service Account Keys
# BQ_KEY = "valeo-cp2673-acp-key.json"

def bq_update_query(query):
    # credentials = service_account.Credentials.from_service_account_file(BQ_KEY)
    # Initialize a BigQuery client with the service account credentials
    # client = bigquery.Client(project="valeo-cp2673-acp", credentials=credentials)
    client = bigquery.Client(project=SERVICES_PROJECT_ID)
    
    # Execute the query
    result = client.query(query=query)
    print(result)


def bq_select_query(query):
    # credentials = service_account.Credentials.from_service_account_file(BQ_KEY)
    # Initialize a BigQuery client with the service account credentials
    # client = bigquery.Client(project="valeo-cp2673-acp", credentials=credentials)
    client = bigquery.Client(project=SERVICES_PROJECT_ID)

    # Execute the query
    result = client.query(query=query)
    return list(result)