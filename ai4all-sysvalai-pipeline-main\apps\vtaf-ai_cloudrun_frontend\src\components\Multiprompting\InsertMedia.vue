<template>
  <div class="q-mt-md q-ml-md q-mr-md">
    <div class="row items-center q-gutter-sm">
      <!-- Insert Media Button -->
      <q-btn
        label="Insert Media"
        class="bg-white"
        @click="triggerFileInput"
      />

      <!-- Hidden file input -->
      <input
        ref="fileInput"
        type="file"
        @change="handleFileChange"
        class="hidden"
        accept="*/*"
      />

      <!-- File name display -->
      <q-input
        v-model="fileName"
        readonly
        placeholder="The maximum file size cannot exceed 7M"
        dense
        outlined
        class="col"
      />
    </div>

    <!-- Error Notification -->
    <q-notify
      v-if="showError"
      type="negative"
      message="File exceeds 7MB limit!"
      position="top"
      timeout="2500"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useQuasar } from 'quasar';

const fileName = ref('');
const fileInput = ref(null);
const showError = ref(false);
const $q = useQuasar();

const triggerFileInput = () => {
  fileInput.value?.click();
};

const handleFileChange = (e) => {
  const file = e.target.files[0];
  if (file) {
    if (file.size <= 7 * 1024 * 1024) {
      fileName.value = file.name;
      showError.value = false;
    } else {
      fileName.value = '';
      showError.value = true;
      $q.notify({
        type: 'negative',
        message: 'File exceeds 7MB limit!',
      });
    }
  }
};
</script>

<style scoped>
.hidden {
  display: none;
}
</style>
