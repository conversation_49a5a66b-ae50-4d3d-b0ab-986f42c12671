import os
import time
from typing import Optional

import google.auth
import jwt
import requests
from google.auth.transport.requests import Request

from Utilities.secret_manager import GCPSecretManager


SCOPES = ["https://www.googleapis.com/auth/cloud-platform"]


class QdrantTokenManager:
    def __init__(
            self
    ):
        self.service_account_email = os.getenv("SERVICE_ACCOUNT_EMAIL")
        self.qdrant_client_url = os.getenv("QDRANT_CLIENT_URL")
        self.collection_name = os.getenv("COLLECTION_NAME")
        self.qdrant_iap_client_id = os.getenv("QDRANT_IAP_CLIENT_ID")
        self.id_token = None  # Cached ID Token
        self.gcp_secret_manager = GCPSecretManager()

        if not all(
                [self.service_account_email, self.qdrant_client_url, self.collection_name, self.qdrant_iap_client_id]):
            raise ValueError("Missing required environment variables for QdrantTokenManager initialization.")

    def _read_token(self) -> Optional[str]:

        try:
            qdrant_token = self.gcp_secret_manager.read_secret("vtaf-ai-qdrant-token")
            return qdrant_token
        except Exception as e:
            print(f"Failed to read token file: {e}")
            return None

    def _write_token(self, token: str) -> None:
        try:
            self.gcp_secret_manager.add_secret_version("vtaf-ai-qdrant-token", token)
        except Exception as e:
            print(f"Failed to write token file: {e}")
            raise


    def _get_id_token(self) -> str:
        """Generate fresh Google ID token using service account impersonation."""
        try:
            credentials, _ = google.auth.default(scopes=SCOPES)
            credentials.refresh(Request())
            access_token = credentials.token

            body = {"audience": self.qdrant_iap_client_id, "includeEmail": "true"}

            response = requests.post(
                f"https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/{self.service_account_email}:generateIdToken",
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                },
                json=body,
                timeout=10,
            )
            response.raise_for_status()

            id_token = response.json().get("token")
            if not id_token:
                raise RuntimeError("No ID token found in response")

            print("Obtained fresh ID token")
            return id_token
        except Exception as e:
            print(f"Error getting ID token: {e}")
            raise

    def _get_qdrant_jwt(self) -> str:
        """Request Qdrant JWT token from the Qdrant auth endpoint with retries."""
        body = {
            "email": self.service_account_email,
            "collections": [{"collection": self.collection_name, "access": "rw"}],
        }

        url = f"{self.qdrant_client_url.rstrip('/')}/qdrant-auth/request-jwt"
        retries = 3
        for attempt in range(1, retries + 1):
            try:
                response = requests.post(
                    url,
                    headers={
                        "Authorization": f"Bearer {self.id_token}",
                        "Content-Type": "application/json",
                    },
                    json=body,
                    timeout=10,
                )
                if response.status_code == 200:
                    qdrant_jwt = response.json().get("token")
                    if not qdrant_jwt:
                        raise RuntimeError("Qdrant JWT token missing in response")
                    print("Obtained Qdrant JWT token")
                    return qdrant_jwt
                else:
                    print(f"Attempt {attempt} failed with status code {response.status_code}")
            except requests.RequestException as e:
                print(f"Attempt {attempt} failed with exception: {e}")
            time.sleep(2)

        raise RuntimeError("Failed to obtain Qdrant JWT token after retries")

    def get_current_jwt(self) -> str:
        """
        Get a valid cached Qdrant JWT token or refresh if expired or missing.
        """
        qdrant_jwt = self._read_token()
        if qdrant_jwt:
            try:
                decoded = jwt.decode(qdrant_jwt, options={"verify_signature": False})
                exp = decoded.get("exp")
                if exp and exp < time.time():
                    print("Cached Qdrant JWT token expired, refreshing")
                    raise jwt.ExpiredSignatureError()
                print(f"Decoded token: {decoded}")
                return qdrant_jwt
            except jwt.ExpiredSignatureError:
                print("Qdrant JWT token expired")
            except jwt.InvalidTokenError:
                print("Invalid Qdrant JWT token")

        # Token missing, expired or invalid: refresh
        self.id_token = self._get_id_token()
        new_jwt = self._get_qdrant_jwt()
        self._write_token(new_jwt)
        return new_jwt
