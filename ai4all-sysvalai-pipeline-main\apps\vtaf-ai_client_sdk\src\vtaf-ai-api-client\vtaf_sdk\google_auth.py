import time
from typing import Optional

import httpx
from google.auth.transport.requests import Request as GoogleRequest
from google.oauth2 import id_token


class GoogleIDTokenAuth(httpx.Auth):
    requires_request_body = False

    def __init__(self, audience: str):
        self._audience = audience
        self._token: Optional[str] = None
        self._token_expiry: float = 0

    def _get_token(self) -> str:
        now = time.time()
        if not self._token or now >= self._token_expiry:
            print("🔐 Refreshing token")
            self._token = id_token.fetch_id_token(GoogleRequest(), self._audience)
            self._token_expiry = now + 3000  # cache for ~50 mins
        return self._token

    def auth_flow(self, request: httpx.Request):
        token = self._get_token()
        request.headers["Authorization"] = f"Bearer {token}"
        yield request
