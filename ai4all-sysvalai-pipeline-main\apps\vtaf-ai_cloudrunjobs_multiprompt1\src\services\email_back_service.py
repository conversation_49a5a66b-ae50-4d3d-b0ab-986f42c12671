#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Email Back Service - 邮件回复服务
对应原始ai_generate.py中的do_dmailback()方法
处理邮件回复任务
"""

import os
import time
from typing import Dict, Any, Optional
from utils.logger import get_logger
from services.ai_service import AIService
from services.sheet_manager import SheetManager
from services.config_service import ConfigurationService

logger = get_logger(__name__)

class EmailBackService:
    """邮件回复服务"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.sheet_manager = SheetManager()
        self.config_service = ConfigurationService()
        
    def execute(self) -> int:
        """执行邮件回复任务"""
        try:
            logger.info("🚀 Starting email back job")
            
            # 获取配置
            config = self._get_config()
            self._validate_config(config)
            
            # 连接到任务表格
            if not self.sheet_manager.connect_sheet(config['task_sheet']):
                raise Exception("Failed to connect to task sheet")
            
            # 处理邮件回复
            result = self._process_email_back(config)
            
            if result:
                logger.info("✅ Email back job completed successfully")
                return 0
            else:
                logger.error("❌ Email back job failed")
                return 1
            
        except Exception as e:
            logger.exception(f"Email back job failed: {e}")
            return 1
    
    def _get_config(self) -> Dict[str, Any]:
        """获取配置"""
        config = {}
        
        # 从环境变量获取
        config['task_sheet'] = os.getenv('TASK_SHEET_URL')
        config['email_check_interval'] = int(os.getenv('EMAIL_CHECK_INTERVAL', '60'))  # 检查间隔（秒）
        config['max_wait_time'] = int(os.getenv('MAX_WAIT_TIME', '300'))  # 最大等待时间（秒）
        
        # 如果环境变量中没有配置，尝试从配置文件读取
        if not config['task_sheet']:
            try:
                if self.config_service.config_exists():
                    sheet_config = self.config_service.get_sheet_config_for_batch_task()
                    if 'error' not in sheet_config:
                        config['task_sheet'] = sheet_config.get('prompt_sheet')
                        logger.info("Task sheet configuration loaded from config.ini")
            except Exception as e:
                logger.error(f"Failed to read config file: {e}")
        
        return config
    
    def _validate_config(self, config: Dict[str, Any]):
        """验证配置"""
        required_fields = ['task_sheet']
        missing_fields = []
        
        for field in required_fields:
            if not config.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            raise ValueError(f"Missing required configuration for email back: {missing_fields}")
        
        logger.info("Email back configuration validation passed")
    
    def _process_email_back(self, config: Dict[str, Any]) -> bool:
        """处理邮件回复"""
        try:
            # 1. 等待新的邮件内容
            email_content = self._wait_for_new_email(config)
            
            if not email_content:
                logger.error("No new email content found")
                return False
            
            logger.info(f"Found new email content: {email_content[:100]}...")
            
            # 2. 生成回复内容
            reply_prompt = f"Please reply to this email: {email_content}"
            
            # 3. 使用AI生成回复
            response = self.ai_service.generate_response(reply_prompt)
            
            if response.startswith("Error"):
                logger.error(f"Failed to generate email reply: {response}")
                return False
            
            # 4. 更新表格状态
            self._update_reply_status(config, response)
            
            # 5. 记录回复结果
            self._log_reply_result(email_content, response)
            
            return True
            
        except Exception as e:
            logger.error(f"Email back processing failed: {e}")
            return False
    
    def _wait_for_new_email(self, config: Dict[str, Any]) -> Optional[str]:
        """等待新的邮件内容"""
        try:
            check_interval = config.get('email_check_interval', 60)
            max_wait_time = config.get('max_wait_time', 300)
            
            logger.info(f"Waiting for new email content (max {max_wait_time}s, check every {check_interval}s)")
            
            start_time = time.time()
            last_content = None
            
            while time.time() - start_time < max_wait_time:
                try:
                    # 检查任务表格的第3行第4列（D列）是否有新内容
                    current_content = self.sheet_manager.read_cell("Task", 3, 4)
                    
                    if current_content and current_content != last_content:
                        logger.info("Found new email content")
                        return current_content
                    
                    last_content = current_content
                    time.sleep(check_interval)
                    
                except Exception as e:
                    logger.warning(f"Error checking for new email: {e}")
                    time.sleep(check_interval)
            
            logger.warning(f"No new email content found within {max_wait_time} seconds")
            return None
            
        except Exception as e:
            logger.error(f"Failed to wait for new email: {e}")
            return None
    
    def _update_reply_status(self, config: Dict[str, Any], response: str):
        """更新回复状态"""
        try:
            # 更新任务表格，标记回复已完成
            self.sheet_manager.update_cell("Task", 3, 5, "replied")  # E列标记状态
            self.sheet_manager.update_cell("Task", 3, 6, response[:500])  # F列保存回复内容（截断）
            logger.info("Updated reply status in task sheet")
        except Exception as e:
            logger.warning(f"Failed to update reply status: {e}")
    
    def _log_reply_result(self, email_content: str, response: str):
        """记录回复结果"""
        logger.info("=" * 50)
        logger.info("EMAIL BACK RESULT")
        logger.info("=" * 50)
        logger.info(f"Original email: {email_content[:200]}...")
        logger.info(f"Reply length: {len(response)} characters")
        logger.info(f"Reply content: {response[:300]}...")
        logger.info("=" * 50)
