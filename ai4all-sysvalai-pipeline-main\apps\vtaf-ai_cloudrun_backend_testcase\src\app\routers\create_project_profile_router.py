from fastapi import APIRouter, HTTPException, Form, UploadFile, File
from google.cloud import bigquery
from utilities import get_bigquery_client, generate_signed_url, upload_file_to_storage, trigger_run_job, bigquery_insert_query
from configs.env import settings
import datetime
import json
import traceback

router = APIRouter()
client = get_bigquery_client()

services_project_id = settings.SERVICES_PROJECT_ID
services_dataset_id = settings.SERVICES_DATASET_ID
services_bucket_name = settings.SERVICES_BUCKET_NAME
ai_testcase_job = settings.TESTCASE_GENERATION_JOB
knowledgebase_creation_job = settings.KNOWLEDGEBASE_CREATION_JOB

@router.post("/valid_user_knowledge_profile")
def valid_user_knowledge_profile(email : str = Form(...), bg: str = Form(...), pg: str = Form(...), pl: str = Form(...)):
    try:
        fetch_query = f"""SELECT EXISTS (
            SELECT 1 FROM `{services_project_id}.{services_dataset_id}.KEY_USERGROUP` WHERE user_email = '{email}' AND bg = '{bg}' AND pg = '{pg}' AND pl = '{pl}'
        )"""
        query = client.query(query=fetch_query)

        result = False
        tiat_users_list = ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']
        for row in query.result():
            result = row[0]
            if email in tiat_users_list:
                result = True
            return result
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)
    
@router.post("/fetch_champions")
def fetch_champions(bg: str = Form(...), pg: str = Form(...), pl: str = Form(...)):
    try:
        fetch_query = f"SELECT user_email FROM `{services_project_id}.{services_dataset_id}.KEY_USERGROUP` WHERE bg = '{bg}' AND pg = '{pg}' AND pl = '{pl}'"
        query = client.query(query=fetch_query)

        champion = []
        for row in query.result():
            champion.append(row['user_email'])

        if len(champion):
            return champion
        else:
            return ["<EMAIL>"]
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)
    
@router.get("/get_organization_inputs")
def get_organization_inputs():
    try:
        query = f"""
            SELECT 
                bg,
                ARRAY_AGG(STRUCT(pg, pl, org_uuid)) AS details
            FROM `{services_project_id}.{services_dataset_id}.VALEO_ORGANIZATION`
            GROUP BY bg
        """
        query_job = client.query(query=query)
        results = []
        for row in query_job.result():
            results.append({
                "bg": row["bg"],
                "details": row["details"]
            })
        return results
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)

@router.post("/get_functionality_inputs")
def get_functionality_inputs(org_uuid: str = Form(...)):
    try:
        query = f"""
            SELECT 
                category, 
                ARRAY_AGG(STRUCT(value, uuid)) AS details 
            FROM `{services_project_id}.{services_dataset_id}.EUF_FEATURE_CONFIG_TABLE`
            WHERE EXISTS (
                SELECT 1
                FROM UNNEST(SPLIT(relation, ',')) AS rel_uuid
                WHERE rel_uuid = @org_uuid
            )
            GROUP BY category
        """

        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("org_uuid", "STRING", org_uuid)
            ]
        )

        query_job = client.query(query=query, job_config=job_config)
        results = []
        for row in query_job.result():
            results.append({
                "category": row["category"],
                "details": row["details"]
            })
        return results
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)

@router.post("/get_algorithms_and_sensors")
def get_algorithms_and_sensors(org_uuid: str = Form(...)):
    try:
        sensor_query = f"""
            SELECT category, ARRAY_AGG(value) AS details
            FROM `{services_project_id}.{services_dataset_id}.PROJECT_CONFIGURATION`
            WHERE type = "sensor"
            AND relation LIKE '%{org_uuid}%'
            group by category
        """

        algorithm_query = f"""
            SELECT category, ARRAY_AGG(value) AS details
            FROM `{services_project_id}.{services_dataset_id}.PROJECT_CONFIGURATION`
            WHERE type = "algorithm"
            AND relation LIKE '%{org_uuid}%'
            GROUP BY category
        """

        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("org_uuid", "STRING", org_uuid)
            ]
        )
        sensor_query_job = client.query(query=sensor_query, job_config=job_config)
        algorithm_query_job = client.query(query=algorithm_query)

        # Convert to dictionary
        algorithm_dict = {row.category: row.details for row in algorithm_query_job.result()}
        sensor_dict = {row.category: row.details for row in sensor_query_job.result()}

        results = {
            "sensors" : sensor_dict,
            "algorithms" : algorithm_dict
        }
        return results
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)

@router.get("/get_profile_projects")
def get_profile_projects():
    try:
        query = f"""
            SELECT * FROM `{services_project_id}.{services_dataset_id}.PROJECT_TABLE`;
        """
        # Run query
        query_job = client.query(query)
        results = query_job.result()

        projects = []
        for row in results:
            projects.append(row)
        sorted_data = sorted(projects, key=lambda x: x["project"])
        return sorted_data
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)

@router.get("/get_profile_project_variant/{project_uuid}")
def get_profile_project_variant(project_uuid: str):
    try:
        query = f"""
            SELECT * FROM `{services_project_id}.{services_dataset_id}.PROJECT_VARIANT_TABLE` WHERE PROJECT_UUID = '{project_uuid}'         
        """
        # Run query
        query_job = client.query(query)
        results = query_job.result()

        projects = []
        for row in results:
            projects.append(row)
        
        sorted_data = sorted(projects, key=lambda x: x["variant"])
        return sorted_data
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)

@router.post("/create_project_profile")
def create_project_profile(
    user_email: str = Form(...),
    bg: str = Form(...),
    pg: str = Form(...),
    pl: str = Form(...),
    features_or_functionality: str = Form(...),
    features_or_functionality_uuid: str = Form(...),
    selected_profile_project: str = Form(...),
    selected_profile_project_uuid: str = Form(...),
    selected_profile_project_variant: str = Form(...),
    selected_profile_project_variant_uuid: str = Form(...),
    selected_algorithms: str = Form("[]"),  # Default empty list in case it's missing
    selected_inputs: str = Form("[]"),  # Default empty list in case it's missing
):
    try:
        # Convert JSON string fields back to Python objects
        selected_algorithms_json = json.loads(selected_algorithms)
        selected_inputs_json = json.loads(selected_inputs)

        current_timestamp = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ")

        # Define BigQuery Table
        table_id = f"{services_project_id}.{services_dataset_id}.PROJECT_PROFILE_TABLE"

        check_query = f"""SELECT EXISTS (
            SELECT 1 FROM `{table_id}` WHERE project = '{selected_profile_project}' AND variant = '{selected_profile_project_variant}' AND euf_feature = '{features_or_functionality}'
        ) AS record_exists"""

        # Run the query
        query_job = client.query(check_query)
        result = query_job.result()

        # Extract the boolean value
        record_exists = [row["record_exists"] for row in result]

        if record_exists[0]:
            return {"status": "failed", "message": f"Already There is a combination [{selected_profile_project}, {selected_profile_project_variant}, {features_or_functionality}]"}


        # SQL Query to insert data
        execute_query = f"""
        INSERT INTO `{table_id}` (
            user_email, created_timestamp, bg, pg, pl, 
            project, project_uuid, 
            variant, variant_uuid, 
            euf_feature, euf_feature_uuid, 
            sensors, algorithms, uuid
        )
        VALUES (
            "{user_email}", "{current_timestamp}", "{bg}", "{pg}", "{pl}", 
            "{selected_profile_project}", "{selected_profile_project_uuid}", 
            "{selected_profile_project_variant}", "{selected_profile_project_variant_uuid}", 
            "{features_or_functionality}", "{features_or_functionality_uuid}", 
            JSON '{json.dumps(selected_inputs_json)}',
            JSON '{json.dumps(selected_algorithms_json)}',
            GENERATE_UUID()
        )
        """

        # Insert row into BigQuery
        errors = bigquery_insert_query(execute_query=execute_query)

        if errors:
            return {"status": "error", "errors": errors}
        return {"status": "success", "message": "Project profile stored successfully"}
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)
