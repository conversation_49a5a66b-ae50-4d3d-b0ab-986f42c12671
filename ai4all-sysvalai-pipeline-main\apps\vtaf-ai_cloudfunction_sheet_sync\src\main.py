import logging
import os
import functions_framework

from bigquery_service import BigqueryHandler
from config_service import ConfigSync
from google_sheet_service import GoogleSheetHandler

from dotenv import load_dotenv

load_dotenv()

BQ_PROJECT_ID = os.getenv("BQ_PROJECT_ID")
BQ_DATA_SET = os.getenv("BQ_DATA_SET")
LOCATION = os.getenv("LOCATION")
SHEET_ID = os.getenv("SHEET_ID")

SCOPES = ["https://www.googleapis.com/auth/spreadsheets",
          "https://www.googleapis.com/auth/documents",
          "https://www.googleapis.com/auth/drive"]


def setup_logging():
    """
    Set up logging to print to console.
    """
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[logging.StreamHandler()]
    )


setup_logging()
logger = logging.getLogger(__name__)

# if __name__ == '__main__':
#     try:
#         setup_logging()
#         logger = logging.getLogger(__name__)
#         SHEET_NAME = 'LIGHT'
#         ROW_INDEX = 2 - 1
#         COLUMN_LETTER = 'C'
#         for ROW_INDEX in range(1, 9):
#             logger.info(f"SHEET_NAME - {SHEET_NAME}, ROW_INDEX - {ROW_INDEX}, COLUMN_LETTER - {COLUMN_LETTER}")
#             google_sheet = GoogleSheetHandler(SHEET_ID, CREDENTIAL_FILE, SCOPES, logger)
#             bigquery = BigqueryHandler(CREDENTIAL_FILE, BQ_PROJECT_ID, logger)
#             config_sync = ConfigSync(BQ_PROJECT_ID, BQ_DATA_SET, google_sheet, bigquery, logger)
#             if SHEET_NAME == 'Config' and COLUMN_LETTER == 'A':
#                 config_sync.update_functions(SHEET_NAME, ROW_INDEX, COLUMN_LETTER)
#
#             if SHEET_NAME == 'Config' and not COLUMN_LETTER == 'A':
#                 config_sync.project_config(SHEET_NAME, ROW_INDEX, COLUMN_LETTER)
#
#             if (SHEET_NAME == 'BRAIN' or SHEET_NAME == 'POWER' or SHEET_NAME == 'LIGHT') and COLUMN_LETTER == 'B':
#                 config_sync.project_functions_relation(SHEET_NAME, ROW_INDEX, COLUMN_LETTER)
#
#             if (SHEET_NAME == 'BRAIN' or SHEET_NAME == 'POWER' or SHEET_NAME == 'LIGHT') and not COLUMN_LETTER == 'A' and not COLUMN_LETTER == 'B':
#                 config_sync.project_config_relation(SHEET_NAME, ROW_INDEX, COLUMN_LETTER)
#     except Exception as e:
#         logger.error(f"An unexpected error occurred: {str(e)}")

@functions_framework.http
def vtaf_config_sync(request):
    try:
        request_json = request.get_json(silent=True)
        SHEET_NAME = request_json['SHEET_NAME']
        ROW_INDEX = int(request_json['ROW_INDEX']) - 1
        COLUMN_LETTER = str(request_json['COLUMN_LETTER']).upper()

        logger.info(f"SHEET_NAME - {SHEET_NAME}, ROW_INDEX - {ROW_INDEX}, COLUMN_LETTER - {COLUMN_LETTER}")
        google_sheet = GoogleSheetHandler(SHEET_ID, SCOPES, logger)
        bigquery = BigqueryHandler(BQ_PROJECT_ID, logger)
        config_sync = ConfigSync(BQ_PROJECT_ID, BQ_DATA_SET, google_sheet, bigquery, logger)
        if SHEET_NAME == 'Config' and COLUMN_LETTER == 'A':
            config_sync.update_functions(SHEET_NAME, ROW_INDEX, COLUMN_LETTER)

        if SHEET_NAME == 'Config' and not COLUMN_LETTER == 'A':
            config_sync.project_config(SHEET_NAME, ROW_INDEX, COLUMN_LETTER)

        if (SHEET_NAME == 'BRAIN' or SHEET_NAME == 'POWER' or SHEET_NAME == 'LIGHT') and COLUMN_LETTER == 'B':
            config_sync.project_functions_relation(SHEET_NAME, ROW_INDEX, COLUMN_LETTER)

        if (SHEET_NAME == 'BRAIN' or SHEET_NAME == 'POWER' or SHEET_NAME == 'LIGHT') and not COLUMN_LETTER == 'A' and not COLUMN_LETTER == 'B':
            config_sync.project_config_relation(SHEET_NAME, ROW_INDEX, COLUMN_LETTER)
        return 'Success'
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        return f'Failed with error {str(e)}'
