import re,os,win32api
import gspread,random,time
from gspread.exceptions import APIError

# google spread sheet client
gc = gspread.oauth(credentials_filename="./client_secret.json", authorized_user_filename="authorized_user_1.json")

def extract_sheet_id(url):
    """
    extract sheet id from the google spread sheet url
    Args:
        url: google SpreadSheet_url or SpreadSheet_id
    Returns:
        spread_sheet_id
    """
    if len(url) < 45:
        return url
    spread_sheet_pattern = r'/spreadsheets/d/([a-zA-Z0-9_-]+)'

    # match ID from url
    folder_match = re.search(spread_sheet_pattern, url)
    if folder_match:
        return folder_match.group(1)

    # not match
    return None


class SpreadSheet(object):

    def __init__(self, url):
        self.url = url
        self.spread_sheet_id = extract_sheet_id(url)
        try:
            self.spread_sheet = gc.open_by_key(self.spread_sheet_id)
        except Exception as e:
            self.spread_sheet = gc.open_by_key(self.spread_sheet_id)
        self.sheet_names = [worksheet.title for worksheet in self.spread_sheet.worksheets()]


    def reconnect(self):
        try:
            self.spread_sheet.client.login()
            # self.spread_sheet = gc.open_by_key(self.spread_sheet_id)
        except Exception as e:
            print(e)
            self.spread_sheet = gc.open_by_key(self.spread_sheet_id)

    def get_sheet_names(self):
        return self.sheet_names

    def get_sheet_header(self, sheet_name):
        self.reconnect()
        worksheet = self.spread_sheet.worksheet(sheet_name)
        return worksheet.row_values(1)

    def read_by_sheet_name(self, sheet_name):
        retries = 5
        for attempt in range(retries):
            try:
                self.reconnect()
                worksheet = self.spread_sheet.worksheet(sheet_name)
                return worksheet.get_all_records()
            except APIError as error:
                if '429' in str(error):
                    # 如果是 429 错误，等待一段时间后重试
                    wait_time = (2 ** attempt) + random.uniform(0, 1)
                    print(f"Hit rate limit, retrying in {wait_time} seconds...")
                    time.sleep(wait_time)
                else:
                    # 其他类型的错误，直接抛出
                    raise
            except Exception as e:
                # print(f"An error occurred: {e}")
                # return (f"error: read {sheet_name} prompt error: {e}")
                raise e

    def update_cell(self, sheet_name, row, col, value):
        self.reconnect()
        worksheet = self.spread_sheet.worksheet(sheet_name)
        worksheet.update_cell(row, col, value)

    def read_cell(self, sheet_name, row, col):
        """读取指定单元格的值。"""
        try:
            self.reconnect()  # 如果需要，重新连接
            worksheet = self.spread_sheet.worksheet(sheet_name)
            cell_value = worksheet.cell(row, col).value
            return cell_value
        except APIError as e:
            if e.response.status_code == 429:  # 处理 "Too Many Requests" 错误
                wait_time = int(e.response.headers.get('Retry-After', 60))  # 获取重试时间
                print(f"API 请求过快，等待 {wait_time} 秒...")
                time.sleep(wait_time)  # 等待一段时间后重试
                return self.read_cell(sheet_name, row, col)  # 递归调用
            else:
                raise  # 其他 API 错误，重新抛出异常
        except Exception as e:
            print(f"读取单元格 ({sheet_name}, {row}, {col}) 时出错: {e}")
            return None  # 或抛出异常 

    def add_row(self, sheet_name, row_data:list, table_range="A1"):
        self.reconnect()
        worksheet = self.spread_sheet.worksheet(sheet_name)
        worksheet.append_row(row_data, table_range=table_range)

    def clear_sheet(self, sheet_name):
        self.reconnect()
        worksheet = self.spread_sheet.worksheet(sheet_name)
        worksheet.clear()

    def create_new_sheet(self, sheet_name):
        self.reconnect()
        if sheet_name in self.sheet_names:
            return self.sheet_names
        else:
            self.spread_sheet.add_worksheet(sheet_name, rows=1000, cols=20)
            self.sheet_names.append(sheet_name)
            return self.sheet_names

def check_config_spread_sheet_permission(spread_sheet_id):
    """检查配置表权限。"""
    try:
        ls = gc.list_permissions(spread_sheet_id)
        # print(ls)
        user_exists = False
        for item in ls:
            if item["displayName"] == get_display_name():
                user_exists = True
                # print(item['role'])
                if item['role'] == 'writer' or item['role'] == 'owner':
                    return True
                else:
                    return False
        if not user_exists:
            gc.insert_permission(file_id=spread_sheet_id, value=win32api.GetUserNameEx(win32api.NameUserPrincipal), perm_type='user', role='writer',notify=False)
            return True
    except Exception as e:
        print(f"检查配置表权限失败: {e}")
        return False

def get_display_name():
    """获取当前用户的显示名称（全名）。"""
    try:
        name_format = win32api.NameDisplay
        user_name = win32api.GetUserNameEx(name_format)
        # email_address = win32api.GetUserNameEx(win32api.NameUserPrincipal)
        return user_name
    except Exception as e:
        # print(f"获取用户全名失败: {e}")
        user_name = os.getlogin()
        return user_name


if __name__ == '__main__':
    # spread_sheet = SpreadSheet("https://docs.google.com/spreadsheets/d/11RMCa4B9YqKdGh_wIW0rce0q8ccKB4tPmw8QLSwM2ek/edit?gid=339096555#gid=339096555")
    spread_sheet = SpreadSheet("11RMCa4B9YqKdGh_wIW0rce0q8ccKB4tPmw8QLSwM2ek")
    print(get_display_name())
    # print(spread_sheet.get_sheet_names())
    # print(spread_sheet.read_by_sheet_name("Training_Dataset"))
    # spread_sheet.clear_sheet("std")
    # spread_sheet.create_new_sheet("test11")

    # print(check_config_spread_sheet_permission("1abssPSR8u6SLMN92ekM_OxlV4TKHNCkEUFASTvDqE0k"))
    # print(check_config_spread_sheet_permission("1Et1nw9VZ9tafryh1YZGaaE1uZQliRjSkZp2c6NZT_B8"))
    # print(check_config_spread_sheet_permission("1bPXwiEWA5ZQ2Zfntg1AJYKmd5sUqvs0NIbaCqtpiFgY"))
    # print(check_config_spread_sheet_permission("1dlNn40WBJ3177mm98IRe6cvflRAA9BwlpVruD-OKd-c"))
    # print(check_config_spread_sheet_permission("1vyoXcKBzFmK38COEfKykvL0jf1i8tx6Chw7x1u8ljr4"))
    # print(check_config_spread_sheet_permission("1_H3sWq5Ia72OGAhinvkQE1D9q1O-U1eSSi9kDCzH0NY"))

    dataset = spread_sheet.read_by_sheet_name("Training_Dataset")
    print(dataset)
    keys_to_check = ['Components', 'Ttitle', 'Training_Prompt']
    if all(key in dataset[0] for key in keys_to_check):
        print("所有键都存在于字典中")
    else:
        print("存在键不存在于字典中")
    # for item in dataset:
    #     print(item)