import datetime
import os
import time

import pandas as pd

from configs.env import settings
from services.send_email import send_email_toVTAF_User
from utils.logger import get_logger

logger = get_logger(__name__)


class JobManager:
    def __init__(self, bq_handler, start_timestamp):
        self.bq_handler = bq_handler
        self.execution_id = settings.CLOUD_RUN_EXECUTION
        self.start_timestamp = start_timestamp
        self.project = settings.PROJECT_ID
        self.dataset = settings.BQ_DATA_SET
        self.table = settings.BQ_TABLE
        self.source_path = settings.SOURCE_PATH
        self.file_name = settings.FILE_NAME
        self.llm_model = settings.MODEL
        self.llm_location = settings.EMBEDDING_LOCATION

    def get_project_details(self):
        query = f"""
        SELECT project, variant, euf_feature FROM `{self.project}.{self.dataset}.PROJECT_PROFILE_TABLE`
        WHERE uuid = '{settings.PROJECT_PROFILE_UUID}'
        """
        rows = self.bq_handler.bq_select_query(query)
        if rows:
            logger.info("Profile found in BigQuery.")
            project_detail = {
                "project": rows[0].get("project", ""),
                "variant": rows[0].get("variant", ""),
                "euf_feature": rows[0].get("euf_feature", "")
            }
            return project_detail
        return None

    def get_file_type(self):
        file_path = os.path.join(self.source_path, self.file_name)
        file_types = {
            "csv": ["csv"],
            "excel": ["xls", "xlsx", "xlsm", "xlsb"],
            "document": ["doc", "docx", "odt", "rtf", "txt"],
            "pdf": ["pdf"],
            "dbc": ["dbc"]
        }

        ext = os.path.splitext(file_path)[1].lower().lstrip('.')

        for ftype, ext_list in file_types.items():
            if ext in ext_list:
                return ftype
        return "unknown"

    def read_file(self):
        """Read CSV or Excel file and remove empty lines."""
        file_path = os.path.join(self.source_path, self.file_name)
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return None, None

        _, ext = os.path.splitext(self.file_name.lower())
        if ext not in ['.csv', '.xls', '.xlsx']:
            logger.error(f"Unsupported file extension: {ext}")
            return None, None

        try:
            if ext == '.csv':
                df = pd.read_csv(file_path)
                file_type = "CSV"
            else:
                df = pd.read_excel(file_path)
                file_type = "Excel"

            df = df.dropna(how='all').reset_index(drop=True)
            logger.info(f"Successfully read {file_type} file: {file_path}, rows after dropping empty: {len(df)}")
            return df, file_type

        except Exception as e:
            logger.exception(f"Failed to read file {file_path}: {e}")
            return None, None

    def poll_for_job_details(self, max_retries: int = 5, base_delay: int = 5):
        """Poll BigQuery for job row with exponential backoff."""
        query = f"""
        SELECT * FROM `{self.project}.{self.dataset}.{self.table}`
        WHERE execution_id = '{self.execution_id}'
        """

        for attempt in range(max_retries):
            try:
                rows = self.bq_handler.bq_select_query(query)
                if rows:
                    logger.info("Job found in BigQuery.")
                    return True, rows[0]
                else:
                    logger.warning(f"Job not found. Retry {attempt + 1}/{max_retries}...")
            except Exception as e:
                logger.error(f"Error querying BigQuery: {e}", exc_info=True)

            sleep_time = base_delay * (2 ** attempt)
            logger.debug(f"Sleeping for {sleep_time} seconds before next retry.")
            time.sleep(sleep_time)

        logger.error("Max retries reached. Job not found in BigQuery.")
        return False, {}

    def update_failed_status(self, start_time_dt, token_usage: dict, message="Job failed before processing"):
        """Mark job as failed in BigQuery."""
        time.sleep(10)  # wait before updating

        try:
            completed_timestamp = datetime.datetime.now()
            start_time_dt = datetime.datetime.strptime(self.start_timestamp, "%Y-%m-%dT%H:%M:%S.%fZ")
            duration = completed_timestamp - start_time_dt

            days = duration.days
            hours, remainder = divmod(duration.seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            formatted_duration = f"{days}D {hours}H {minutes}M {seconds}S"

            query = f"""
            UPDATE `{self.project}.{self.dataset}.{self.table}`
            SET job_start_time = '{self.start_timestamp}',
                job_end_time = '{completed_timestamp.strftime("%Y-%m-%dT%H:%M:%S.%fZ")}',
                status = 'failed - {message}',
                job_duration = '{formatted_duration}',
                execution_status = 'completed',
                result_file = 'NA',
                total_token = '{token_usage.get("total_tokens", 0)}',
                prompt_token = '{token_usage.get("prompt_tokens", 0)}',
                cached_prompt_token = '{token_usage.get("cached_prompt_tokens", 0)}',
                completion_token = '{token_usage.get("completion_tokens", 0)}',
                successfull_agent_request = '{token_usage.get("successful_requests", 0)}'
            WHERE execution_id = '{self.execution_id}'
            """
            job_record = self.bq_handler.bq_update_query(query)
            logger.info("Failed job status updated in BigQuery.")
            return job_record
        except Exception as e:
            logger.error(f"Failed to update job failed status: {e}", exc_info=True)
            raise

    def update_job_start(self):
        """Mark job as started in BigQuery."""
        try:
            start_query = f"""
            UPDATE `{self.project}.{self.dataset}.{self.table}`
            SET job_start_time = '{self.start_timestamp}',
                execution_status = 'running',
                llm_model = '{self.llm_model}',
                llm_location = '{self.llm_location}'
            WHERE execution_id = '{self.execution_id}';
            """
            self.bq_handler.bq_update_query(start_query)
            logger.info("Job start time updated in BigQuery.")
        except Exception as e:
            logger.error(f"Failed to update job start status: {e}", exc_info=True)
            raise

    def update_job_complete(self, csv_bucket_path: str, token_usage: dict):
        """Mark job as completed successfully in BigQuery."""
        try:
            completed_timestamp = datetime.datetime.now()
            start_time_dt = datetime.datetime.strptime(self.start_timestamp, "%Y-%m-%dT%H:%M:%S.%fZ")
            duration = completed_timestamp - start_time_dt

            days = duration.days
            hours, remainder = divmod(duration.seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            formatted_duration = f"{days}D {hours}H {minutes}M {seconds}S"

            completed_query = f"""
            UPDATE `{self.project}.{self.dataset}.{self.table}`
            SET job_start_time = '{self.start_timestamp}',
                job_end_time = '{completed_timestamp.strftime("%Y-%m-%dT%H:%M:%S.%fZ")}',
                status = 'passed',
                job_duration = '{formatted_duration}',
                execution_status = 'completed',
                total_token = '{token_usage.get("total_tokens", 0)}',
                prompt_token = '{token_usage.get("prompt_tokens", 0)}',
                cached_prompt_token = '{token_usage.get("cached_prompt_tokens", 0)}',
                completion_token = '{token_usage.get("completion_tokens", 0)}',
                successfull_agent_request = '{token_usage.get("successful_requests", 0)}',
                result_file = '{csv_bucket_path}'
            WHERE execution_id = '{self.execution_id}';
            """
            job_record = self.bq_handler.bq_update_query(completed_query)
            logger.info("Job completion status updated in BigQuery.")
            return job_record
        except Exception as e:
            logger.error(f"Failed to update job completion status: {e}", exc_info=True)
            raise

    def send_email(self, job_record: dict, token_usage: dict):
        try:

            information = {
                "job_start_time": job_record.get("job_start_time", ""),
                "job_end_time": job_record.get("job_end_time", ""),
                "job_duration": job_record.get("job_duration", ""),
                "bg": job_record.get("bg", ""),
                "pg": job_record.get("pg", ""),
                "pl": job_record.get("pl", ""),
                "job_status": job_record.get("status", ""),
                "execution_status": job_record.get("execution_status", ""),
                "result_file": job_record.get("result_file", ""),
                "user_upload": job_record.get("user_upload", ""),
                "total_token": f"{token_usage['total_tokens']}",
                "prompt_token": f"{token_usage['prompt_tokens']}",
                "cached_prompt_token": f"{token_usage['cached_prompt_tokens']}",
                "completion_token": f"{token_usage['completion_tokens']}",
                "successful_agent_request": f"{token_usage['successful_requests']}",
                "execution_id": f"{settings.CLOUD_RUN_EXECUTION}",
                "model": f"{settings.MODEL}",
                "location": f"{settings.EMBEDDING_LOCATION}"
            }
            send_email_toVTAF_User(job_record.get("user_email", ""), information)
        except Exception as e:
            logger.error(f"Failed to send job email: {e}", exc_info=True)
            raise
