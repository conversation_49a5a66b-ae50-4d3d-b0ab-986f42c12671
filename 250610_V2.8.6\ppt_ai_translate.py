import sys
import os
import warnings

# import certifi
# # import creds
# import ssl
from PyQt5.QtWidgets import (QApplication, QMainWindow, QMessageBox,
                             QLineEdit, QPushButton, QTextEdit, QLabel,
                             QVBoxLayout, QHBoxLayout, QWidget, QComboBox, QProgressBar)
from PyQt5.QtCore import QThread, pyqtSignal, QUrl
from PyQt5.QtGui import QDesktopServices
from google.oauth2 import credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from google.auth.transport.requests import Request
import httplib2
from googleapiclient.errors import HttpError
import vertexai_gemini as gemini

# if (not os.environ.get('PYTHONHTTPSVERIFY', '') and getattr(ssl, '_create_unverified_context', None)):
#     ssl._create_default_https_context = ssl._create_unverified_context

#     os.environ['GRPC_DNS_RESOLVER'] = 'native'

# warnings.filterwarnings("ignore", category=DeprecationWarning)
# import ssl, certifi
# print(ssl.get_default_verify_paths())       # 查看 OpenSSL 默认 CA 路径
# print(certifi.where())                      # 查看 certifi 提供的 CA 列表


SCOPES = [
    'https://www.googleapis.com/auth/presentations',
    'https://www.googleapis.com/auth/drive',
    'https://www.googleapis.com/auth/userinfo.profile',
    'https://www.googleapis.com/auth/userinfo.email',
    'openid'
]
CLIENT_SECRETS_FILE = 'client_secret_GoogleSheet_Slide_Doc.json'
TOKEN_FILE = 'authorized_user_1.json'
# CA_CERTS = './cacert.pem'  # 替换为你的 CA 证书文件路径

# 创建一个 httplib2.Http 对象，并指定 CA 证书
# http = httplib2.Http(ca_certs=CA_CERTS)
http = httplib2.Http(disable_ssl_certificate_validation=True)
# 创建一个自定义的 Request 对象，并指定 CA 证书
request = Request(session=http)


class WorkerThread(QThread):
    """用于执行耗时操作的线程"""
    finished = pyqtSignal(str, bool)
    update_log = pyqtSignal(str)
    progress = pyqtSignal(int) # Add progress signal

    def __init__(self, url, creds, target_language):
        super().__init__()
        self.url = url
        self.creds = creds
        self.target_language = target_language

    def run(self):
        try:
            presentation_id = get_presentation_id(self.url)
            if not presentation_id:
                self.finished.emit("无效的 Google Slides URL。", False)
                return

            self.update_log.emit(f"正在处理演示文稿: {presentation_id}")

            original_texts, object_id_order = extract_text_and_style(presentation_id, self.creds)
            total_texts = len(original_texts)
            # print(original_texts)
            self.update_log.emit(f"提取了 {total_texts} 个文本元素。")

            translated_texts = []
            for i, text_and_style in enumerate(original_texts):
                translated_text = translate_text_with_gemini([text_and_style], self.target_language)  # Translate one at a time
                translated_texts.extend(translated_text) # Add to the list
                progress_percentage = int(((i + 1) / total_texts) * 100)
                self.progress.emit(progress_percentage) # Emit progress
                print(f"Translated {i+1}/{total_texts} texts") # Debug

            self.update_log.emit("文本翻译完成。")

            new_presentation_id = create_translated_presentation(presentation_id, translated_texts, self.creds)
            # print(new_presentation_id)
            self.update_log.emit(f"创建了新的演示文稿: {new_presentation_id}")
            result = f"""
                    <html>
                    <body>
                        <p>
                            翻译完成！新的演示文稿: 
                            <a href=f"https://docs.google.com/presentation/d/{new_presentation_id}/edit">PPT link</a>
                        </p>
                    </body>
                    </html>
                """
            self.finished.emit(f"翻译完成！新的演示文稿: https://docs.google.com/presentation/d/{new_presentation_id}/edit", True)
            self.update_log.emit(f"翻译完成！新的演示文稿: https://docs.google.com/presentation/d/{new_presentation_id}/edit")
            # self.finished.emit(result, True)
            # self.update_log.emit(result)
            print(f"翻译完成！新的演示文稿: https://docs.google.com/presentation/d/{new_presentation_id}/edit")

        except HttpError as error:
            print(error.__traceback__.tb_lineno)
            print(error.__traceback__.tb_frame.f_code.co_filename)
            print(error.__traceback__.tb_frame.f_locals)
            self.finished.emit(f"发生错误: {error}", False)
            print(error)
        except Exception as e:
            print(e.__traceback__.tb_lineno)
            print(e.__traceback__.tb_frame.f_code.co_filename)
            print(e.__traceback__.tb_frame.f_locals)
            self.finished.emit(f"发生未知错误: {e}", False)
            print(f"发生未知错误: {e}")

class AI_Translate(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Google Slides Translator")
        self.init_ui()
        self.worker_thread = None
        self.creds = None

        # 尝试加载现有的 token
        if os.path.exists(TOKEN_FILE):
            try:
                with open(TOKEN_FILE, 'r') as token_file:
                    self.creds = credentials.Credentials.from_authorized_user_file(TOKEN_FILE, SCOPES)
            except Exception as e:
                print(f"加载 token 文件时出错: {e}")
                self.creds = None

        # 如果没有有效的 token，则进行 OAuth 2.0 流程
        if not self.creds or not self.creds.valid:
            if self.creds and self.creds.expired and self.creds.refresh_token:
                try:
                    # from google.auth.transport.requests import Request
                    # self.creds.refresh(request)
                    self.creds.refresh(httplib2.Http(disable_ssl_certificate_validation=True)) # 使用默认的 http 对象，或者你可以创建一个不进行证书验证的 httplib2.Http 对象
                except Exception as e:
                    print(f"刷新 token 时出错: {e}")
                    # 如果刷新失败，则重新进行 OAuth 2.0 流程
                    self.creds = None
            if not self.creds:
                flow = InstalledAppFlow.from_client_secrets_file(CLIENT_SECRETS_FILE, SCOPES)
                self.creds = flow.run_local_server(port=0)
            # 保存 token
            with open(TOKEN_FILE, 'w') as token_file:
                token_file.write(self.creds.to_json())

        # 初始化 Google API 客户端
        self.init_google_api_clients()

    def init_ui(self):
        """手动创建 UI"""
        self.urlLineEdit = QLineEdit()
        self.urlLineEdit.setPlaceholderText("请输入 Google Slides URL")
        self.translateButton = QPushButton("翻译")
        self.logTextEdit = QTextEdit()
        self.logTextEdit.setReadOnly(True)
        self.statusLabel = QLabel("就绪")

        self.languageComboBox = QComboBox()
        self.languageComboBox.addItem("Chinese (zh-CN)", "zh-CN")  # China
        self.languageComboBox.addItem("English (en)", "en")  # USA, UK, etc.
        self.languageComboBox.addItem("French (fr)", "fr")  # France
        self.languageComboBox.addItem("German (de)", "de")  # Germany
        self.languageComboBox.addItem("Spanish (es)", "es")  # Spain
        self.languageComboBox.addItem("Portuguese (pt)", "pt")  # Portugal, Brazil
        self.languageComboBox.addItem("Italian (it)", "it")  # Italy
        self.languageComboBox.addItem("Japanese (ja)", "ja")  # Japan
        self.languageComboBox.addItem("Korean (ko)", "ko")  # Korea
        self.languageComboBox.addItem("Turkish (tr)", "tr")  # Turkey
        self.languageComboBox.addItem("Polish (pl)", "pl")  # Poland
        self.languageComboBox.addItem("Russian (ru)", "ru")  # Russia
        self.languageComboBox.addItem("Czech (cs)", "cs")  # Czech Republic
        self.languageComboBox.addItem("Hungarian (hu)", "hu")  # Hungary
        self.languageComboBox.addItem("Indonesian (id)", "id")  # Indonesia
        self.languageComboBox.addItem("Thai (th)", "th")  # Thailand
        self.languageComboBox.addItem("Romanian (ro)", "ro")  # Romania
        self.languageComboBox.addItem("Serbian (sr)", "sr")  # Serbia
        self.languageComboBox.addItem("Dutch (nl)", "nl")  # Netherlands
        self.languageComboBox.addItem("Swedish (sv)", "sv")  # Sweden
        self.languageComboBox.addItem("Malay (ms)", "ms")  # Malaysia
        self.languageComboBox.addItem("Vietnamese (vi)", "vi")  # Vietnam
        self.languageComboBox.addItem("Greek (el)", "el")  # Greece
        self.languageComboBox.addItem("Slovak (sk)", "sk")  # Slovakia
        self.languageComboBox.addItem("Croatian (hr)", "hr")  # Croatia
        self.languageComboBox.addItem("Finnish (fi)", "fi")  # Finland
        self.languageComboBox.addItem("Danish (da)", "da")  # Denmark
        self.languageComboBox.addItem("Norwegian (no)", "no")  # Norway
        self.languageComboBox.addItem("Arabic (ar)", "ar")  # United Arab Emirates
        self.languageComboBox.addItem("Hebrew (he)", "he")  # Israel
        self.languageComboBox.addItem("Afrikaans (af)", "af")  # South Africa
        self.languageComboBox.addItem("Slovenian (sl)", "sl")  # Slovenia
        self.languageComboBox.addItem("Lithuanian (lt)", "lt")  # Lithuania
        self.languageComboBox.addItem("Latvian (lv)", "lv")  # Latvia
        self.languageComboBox.addItem("Estonian (et)", "et")  # Estonia
        self.languageComboBox.addItem("Bulgarian (bg)", "bg")  # Bulgaria
        self.languageComboBox.addItem("Ukrainian (uk)", "uk")  # Ukraine
        self.languageComboBox.addItem("Icelandic (is)", "is")  # Iceland
        self.languageComboBox.addItem("Irish (ga)", "ga")  # Ireland
        self.languageComboBox.addItem("Maltese (mt)", "mt")  # Malta
        self.languageComboBox.addItem("Albanian (sq)", "sq")  # Albania
        self.languageComboBox.addItem("Welsh (cy)", "cy")  # Wales
        self.languageComboBox.addItem("Galician (gl)", "gl")  # Galicia
        self.languageComboBox.addItem("Basque (eu)", "eu")  # Basque Country
        self.languageComboBox.addItem("Catalan (ca)", "ca")  # Catalonia
        self.languageComboBox.addItem("Belarusian (be)", "be")  # Belarus
        self.languageComboBox.addItem("Bosnian (bs)", "bs")  # Bosnia
        self.languageComboBox.addItem("Macedonian (mk)", "mk")  # Macedonia
        self.languageComboBox.addItem("Sinhala (si)", "si")  # Sri Lanka
        self.languageComboBox.addItem("Tamil (ta)", "ta")  # Sri Lanka
        self.languageComboBox.addItem("Nepali (ne)", "ne")  # Nepal
        self.languageComboBox.addItem("Bengali (bn)", "bn")  # Bangladesh
        self.languageComboBox.addItem("Urdu (ur)", "ur")  # Pakistan
        self.languageComboBox.addItem("Hindi (hi)", "hi")  # India
        self.languageComboBox.addItem("Gujarati (gu)", "gu")  # India
        self.languageComboBox.addItem("Kannada (kn)", "kn")  # India
        self.languageComboBox.addItem("Malayalam (ml)", "ml")  # India
        self.languageComboBox.addItem("Marathi (mr)", "mr")  # India
        self.languageComboBox.addItem("Oriya (or)", "or")  # India
        self.languageComboBox.addItem("Punjabi (pa)", "pa")  # India
        self.languageComboBox.addItem("Telugu (te)", "te")  # India
        self.languageComboBox.addItem("Zulu (zu)", "zu")  # South Africa
        self.languageComboBox.addItem("Xhosa (xh)", "xh")  # South Africa

        self.progressBar = QProgressBar() # Create progress bar

        hbox = QHBoxLayout()
        hbox.addWidget(self.urlLineEdit)
        hbox.addWidget(self.languageComboBox)
        hbox.addWidget(self.translateButton)

        vbox = QVBoxLayout()
        vbox.addLayout(hbox)
        vbox.addWidget(self.logTextEdit)
        vbox.addWidget(self.progressBar) # Add progress bar
        vbox.addWidget(self.statusLabel)

        central_widget = QWidget()
        central_widget.setLayout(vbox)
        self.setCentralWidget(central_widget)

        self.translateButton.clicked.connect(self.start_translation)

    def init_google_api_clients(self):
        """初始化 Google API 客户端"""
        try:
            self.slides_service = build('slides', 'v1', credentials=self.creds)
            self.drive_service = build('drive', 'v3', credentials=self.creds)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"初始化 Google API 客户端失败: {e}")

    def start_translation(self):
        """开始翻译过程"""
        url = self.urlLineEdit.text().strip()
        if not url:
            QMessageBox.warning(self, "警告", "请输入 Google Slides URL。")
            return

        target_language = self.languageComboBox.itemData(self.languageComboBox.currentIndex())

        self.statusLabel.setText("正在翻译...")
        self.translateButton.setEnabled(False)
        self.logTextEdit.clear()
        self.progressBar.setValue(0) # Reset progress bar

        self.worker_thread = WorkerThread(url, self.creds, target_language)
        self.worker_thread.finished.connect(self.on_translation_finished)
        self.worker_thread.update_log.connect(self.logTextEdit.append)
        self.worker_thread.progress.connect(self.update_progress) # Connect progress signal
        self.worker_thread.start()

    def on_translation_finished(self, message, success):
        """翻译完成后的处理"""
        self.translateButton.setEnabled(True)
        if success:
            self.statusLabel.setText("翻译完成！")
            QDesktopServices.openUrl(QUrl(message.split("文稿:")[-1].strip()))
            QMessageBox.information(self, "成功", message)
        else:
            self.statusLabel.setText("翻译失败。")
            QMessageBox.critical(self, "错误", message)

    def update_progress(self, value):
        """Updates the progress bar."""
        self.progressBar.setValue(value)

# --- 辅助函数 ---
def get_presentation_id(url):
    """从 Google Slides URL 中提取演示文稿 ID"""
    try:
        return url.split('/d/')[1].split('/')[0]
    except IndexError:
        return None


def extract_text_and_style(presentation_id, creds):
    """从演示文稿中提取所有文本及其样式，并保留文本框的编号"""
    results = []
    object_id_order = []  # 用于存储 objectId 的原始顺序
    slides_service = build('slides', 'v1', credentials=creds)
    # print(slides_service)
    print(1)
    presentation = slides_service.presentations().get(presentationId=presentation_id).execute()
    print(type(presentation))

    for slide in presentation.get('slides', []):
        for element in slide.get('pageElements', []):
            if 'shape' in element and 'text' in element['shape']:
                object_id = element['objectId']  # 获取文本框的编号
                if object_id not in object_id_order:
                    object_id_order.append(object_id)  # 记录 objectId 的原始顺序
                for paragraph in element['shape']['text'].get('textElements', []):
                    if 'textRun' in paragraph:
                        content = paragraph['textRun']['content'].strip()
                        # 替换换行符
                        content = content.replace('\n', ' ')
                        if content:  # 确保内容不为空
                            style = paragraph['textRun'].get('style', {})
                            results.append({'object_id': object_id, 'text': content, 'style': style})
                            # print(f"ObjectID: {object_id}, Text: {content}, Style: {style}")
    return results, object_id_order  # 返回文本内容和 objectId 的原始顺序
    


def translate_text_with_gemini(text_and_styles, target_language='zh-CN'):
    """使用 Gemini 逐个翻译文本，并保留原始样式和文本框编号"""
    if not text_and_styles:
        return []

    results = []
    for item in text_and_styles:
        # 1. 构建单个文本元素的提示
        prompt = f"You are a highly accurate translation engine. Translate the following text to {target_language}, considering its specific context. Do not add any explanatory notes or surrounding text. Icons and symbols should remain UNCHANGED in the translation. Your response MUST contain ONLY the translated text.\n\nText to translate: {item['text']}"

        # 2. 调用 Gemini API 翻译单个文本元素
        try:
            translated_text = gemini.get_chat_response(prompt)
            print(translated_text)

        except Exception as e:
            print(f"Translation error: {e}")
            translated_text = ''  # 出错时返回空字符串

        # 3. 创建包含翻译后的文本、原始样式和文本框编号的结果
        results.append({'object_id': item['object_id'], 'text': translated_text.strip(), 'style': item['style']})

    return results

def fill_presentation_with_translated_text(presentation_id, translated_texts_and_styles, creds):
    # print(translated_texts_and_styles)
    """Fills a Google Slides presentation with translated text, preserving original styles and text box IDs."""
    slides_service = build('slides', 'v1', credentials=creds)
    presentation = slides_service.presentations().get(presentationId=presentation_id).execute()

    requests = []
    request_count = 0  # 初始化 request 计数器

    # Group translated texts by object_id
    grouped_texts = {}
    for item in translated_texts_and_styles:
        object_id = item['object_id']
        if object_id not in grouped_texts:
            grouped_texts[object_id] = []
        grouped_texts[object_id].append(item)

    # Iterate through each object_id and its corresponding text items
    for object_id, text_items in grouped_texts.items():
        current_length = 0  # 初始化当前 object_id 的文本长度

        # Iterate through each text item for the current object_id
        for i, item in enumerate(text_items):
            # Create insertText request for each text item
            request_count += 1
            insert_text_request = {
                'insertText': {
                    'objectId': object_id,
                    'text': item['text'],
                    'insertionIndex': current_length  # Insert at the current length
                }
            }
            # print(f"Creating request #{request_count}: insertText for object {object_id}, text: {item['text']}, insertionIndex: {current_length}")
            requests.append(insert_text_request)

            # Create UpdateTextStyle request if there is a style
            if item['style']:
                request_count += 1
                update_text_style_request = {
                    'updateTextStyle': {
                        'objectId': object_id,
                        'textRange': {
                            'type': 'FIXED_RANGE',
                            'startIndex': current_length,
                            'endIndex': current_length + len(item['text'])
                        },
                        'style': item['style'],
                        'fields': ','.join(item['style'].keys())  # Specify the fields to update
                    }
                }
                # print(f"Creating request #{request_count}: updateTextStyle for object {object_id}, text: {item['text']}, startIndex: {current_length}, endIndex: {current_length + len(item['text'])}")
                requests.append(update_text_style_request)

            current_length += len(item['text'])  # 更新当前 object_id 的文本长度

    body = {'requests': requests}
    # print("Requests:", requests)  # 打印 requests 列表
    try:
        slides_service.presentations().batchUpdate(presentationId=presentation_id, body=body).execute()
        print("Presentation filled with translated text successfully.")
    except HttpError as e:
        print(f"Batch Insert Error: {e}")
        raise
    except Exception as e:
        print(e)

def clear_presentation_text(presentation_id, creds):
    """Clears all text from a Google Slides presentation."""
    slides_service = build('slides', 'v1', credentials=creds)
    presentation = slides_service.presentations().get(presentationId=presentation_id).execute()

    requests = []
    for slide in presentation.get('slides', []):
        for element in slide.get('pageElements', []):
            if 'shape' in element and 'text' in element['shape']:
                requests.append({
                    'deleteText': {
                        'objectId': element['objectId'],
                        'textRange': {
                            'type': 'ALL',
                        }
                    }
                })

    body = {'requests': requests}
    print("Requests:", requests)  # 打印 requests 列表
    try:
        slides_service.presentations().batchUpdate(presentationId=presentation_id, body=body).execute()
        print("Presentation text cleared successfully.")
    except HttpError as e:
        print(f"Batch Delete Error: {e}")
        raise

def create_translated_presentation(original_presentation_id, translated_texts, creds):
    """Creates a translated presentation, preserving original text styles."""
    drive_service = build('drive', 'v3', credentials=creds)
    copy_title = "Translated Presentation"
    body = {'name': copy_title}
    copied_presentation = drive_service.files().copy(
        fileId=original_presentation_id, body=body).execute()
    copied_presentation_id = copied_presentation.get('id')

    # 1. 清空演示文稿中的所有文本
    clear_presentation_text(copied_presentation_id, creds)

    # 2. 填充翻译后的文本
    fill_presentation_with_translated_text(copied_presentation_id, translated_texts, creds)

    return copied_presentation_id

if __name__ == '__main__':
    app = QApplication(sys.argv)
    main_window = AI_Translate()
    main_window.show()
    sys.exit(app.exec_())