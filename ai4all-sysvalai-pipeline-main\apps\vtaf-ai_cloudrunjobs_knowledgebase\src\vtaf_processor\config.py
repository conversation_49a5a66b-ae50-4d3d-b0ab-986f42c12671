from dotenv import load_dotenv
import os

# Load .env variables
load_dotenv()

GOOGLE_APPLICATION_CREDENTIALS = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
API_BASE = os.getenv("API_BASE")
FILE_PATH = os.getenv("FILE_PATH")
CHUNKING_STRATEGY = os.getenv("CHUNKING_STRATEGY")
COLLECTION_NAME = os.getenv("COLLECTION_NAME")
TARGET_AUDIENCE = os.getenv("TARGET_AUDIENCE")
ENABLE_SUMMARIZATION = os.getenv("ENABLE_SUMMARIZATION", "false").lower() == "true"

# Set credentials for Google SDK
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = GOOGLE_APPLICATION_CREDENTIALS
