<template>
  <home-jumbotron></home-jumbotron>
  <home-features></home-features>
  <home-context1></home-context1>
  <home-info></home-info>
  <home-carosuel></home-carosuel>
  <home-map></home-map>
  <home-footer></home-footer>
</template>

<script setup>
import { onMounted } from "vue";
import { storeToRefs } from "pinia";
import { useHomeStore } from "src/stores/home/<USER>";
import HomeJumbotron from "src/components/Home/HomeJumbotron.vue";
import HomeFeatures from "src/components/Home/HomeFeatures.vue";
import HomeContext1 from "src/components/Home/HomeContext1.vue";
import HomeCarosuel from "src/components/Home/HomeCarosuel.vue";
import HomeMap from "src/components/Home/HomeMap.vue";
import HomeFooter from "src/components/Home/HomeFooter.vue";
import HomeInfo from "src/components/Home/HomeInfo.vue";

const homeStore = useHomeStore();
// const {first_letter_email} = storeToRefs(homeStore);
const { get_user_email } = homeStore;

onMounted(async () => {
  await get_user_email();
});
</script>
<!--#########################################################################-->
<!-- <template>
  <home-jumbotron></home-jumbotron>
  <home-features></home-features>
  <home-context1></home-context1>
  <home-info></home-info>
  <home-carosuel></home-carosuel>
  <home-map></home-map>
  <home-footer></home-footer>
</template>

<script setup>
import { onMounted } from "vue";
import Lenis from "@studio-freight/lenis";
import { storeToRefs } from "pinia";
import { useHomeStore } from "src/stores/home/<USER>";
import HomeJumbotron from "src/components/Home/HomeJumbotron.vue";
import HomeFeatures from "src/components/Home/HomeFeatures.vue";
import HomeContext1 from "src/components/Home/HomeContext1.vue";
import HomeCarosuel from "src/components/Home/HomeCarosuel.vue";
import HomeMap from "src/components/Home/HomeMap.vue";
import HomeFooter from "src/components/Home/HomeFooter.vue";
import HomeInfo from "src/components/Home/HomeInfo.vue";

const homeStore = useHomeStore();
const {} = homeStore;

onMounted(() => {
  const lenis = new Lenis({
    duration: 1.2, // Speed of the smooth scrolling
    easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)), // Easing function
    direction: "vertical", // Scroll direction
    smooth: true,
    mouseMultiplier: 1, // Multiplier for the scroll speed
    smoothTouch: false, // Disable smooth scrolling on touch devices
  });

  function raf(time) {
    lenis.raf(time);
    requestAnimationFrame(raf);
  }

  requestAnimationFrame(raf);
});
</script> -->
<!--##############################################################-->
<!-- <template>
  <lenis-component></lenis-component>
</template>

<script setup>
import { onMounted } from "vue";
import Lenis from "@studio-freight/lenis";

import LenisComponent from "src/components/Home/LenisComponent.vue";
</script> -->
