from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import httpx
from routers import chat_with_knowledgebase_router, user_management_router

@asynccontextmanager
async def lifespan(app: FastAPI):
    app.state.http_client = httpx.AsyncClient()
    yield
    await app.state.http_client.aclose()

app = FastAPI(lifespan=lifespan)

# Allow all origins with specific methods and headers
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Change this to a list of allowed origins
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)


# Include the items router under a specific prefix
app.include_router(user_management_router.router, prefix="/conversation/user", tags=["user management"])

app.include_router(chat_with_knowledgebase_router.router, prefix="/conversation/with_knowledgebase", tags=['Chat With Knowledgebase'])