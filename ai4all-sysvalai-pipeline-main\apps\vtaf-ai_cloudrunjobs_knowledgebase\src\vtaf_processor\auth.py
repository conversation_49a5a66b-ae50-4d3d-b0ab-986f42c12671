# auth.py
import logging
import google.auth
import google.auth.transport.requests as google_requests

GOOGLE_CLOUD_SCOPES = [
    "https://www.googleapis.com/auth/cloud-platform"
]

def get_auth_headers() -> dict:
    try:
        credentials, _ = google.auth.default(scopes=GOOGLE_CLOUD_SCOPES)
        credentials.refresh(google_requests.Request())
        access_token = credentials.token
        return {"Authorization": f"Bearer {access_token}"}
    except Exception as e:
        logging.error(f"Failed to get auth headers: {e}")
        raise
