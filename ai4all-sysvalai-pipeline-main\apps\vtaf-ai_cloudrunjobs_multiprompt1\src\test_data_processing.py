#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据处理功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.data_processing import parse_test_cases, parse_test_script, parse_single_case, fields

def test_parse_test_cases():
    """Test test case parsing functionality"""
    print("=== Testing Test Case Parsing ===")
    
    # 测试数据
    test_response = """
Confidence Score: 85%

**Test Case ID**: TC_001
**Covered Requirement ID**: REQ_001, REQ_002
**Test Objective**: Verify basic functionality
**Test Condition**: 
1. System is initialized
2. User is logged in
**Test Action**: 
1. Click the button
2. Enter data
**Test Expectation**: 
1. System responds correctly
2. Data is saved

**Test Case ID**: TC_002
**Covered Requirement ID**: REQ_003
**Test Objective**: Verify error handling
**Test Condition**: Invalid input provided
**Test Action**: Submit invalid data
**Test Expectation**: Error message displayed
"""
    
    result = parse_test_cases(test_response)
    
    print(f"Confidence Score: {result.get('Confidence Score', 'Not found')}")
    print(f"Number of test cases: {len(result.get('Test Cases', []))}")
    
    for i, case in enumerate(result.get('Test Cases', [])):
        print(f"\nTest Case {i+1}:")
        for j, field_value in enumerate(case):
            if j < len(fields):
                print(f"  {fields[j]}: {field_value}")
    
    return len(result.get('Test Cases', [])) > 0

def test_parse_test_script():
    """测试测试脚本解析功能"""
    print("\n=== 测试测试脚本解析功能 ===")
    
    test_script_response = """
The confidence level is **90%** for this test script.

Here's the CAPL test script:

```capl
testcase TC_001()
{
  // Test implementation
  write("Starting test case TC_001");
  
  // Perform test actions
  if (checkCondition()) {
    write("Test passed");
  } else {
    write("Test failed");
  }
}
```

Confidence Score: 90%
"""
    
    result = parse_test_script(test_script_response)
    
    print(f"Confidence Score: {result.get('Confidence Score', 'Not found')}")
    print(f"CAPL Script found: {'Yes' if result.get('Capl') else 'No'}")
    
    if result.get('Capl'):
        print("CAPL Script content:")
        print(result['Capl'][:200] + "..." if len(result['Capl']) > 200 else result['Capl'])
    
    return result.get('Capl') is not None

def test_parse_single_case():
    """测试单个测试用例解析"""
    print("\n=== 测试单个测试用例解析功能 ===")
    
    single_case_text = """
**Test Case ID**: TC_SINGLE_001
**Covered Requirement ID**: REQ_SINGLE_001, REQ_SINGLE_002
**Test Objective**: Test single case parsing
**Test Condition**: 
1. Input data is valid
2. System is ready
**Test Action**: 
1. Execute function
2. Verify output
**Test Expectation**: 
1. Function returns expected result
2. No errors occur
"""
    
    result = parse_single_case(single_case_text)
    
    print("Parsed single case:")
    for field in fields:
        value = result.get(field, 'Not found')
        print(f"  {field}: {value}")
    
    return len(result) > 0

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    # 测试空输入
    empty_result = parse_test_cases("")
    print(f"Empty input test cases: {len(empty_result.get('Test Cases', []))}")
    
    # 测试无效格式
    invalid_result = parse_test_cases("This is not a valid test case format")
    print(f"Invalid format test cases: {len(invalid_result.get('Test Cases', []))}")
    
    # 测试只有置信度分数
    confidence_only = parse_test_cases("Confidence Score: 75%")
    print(f"Confidence only - Score: {confidence_only.get('Confidence Score', 'Not found')}")
    print(f"Confidence only - Cases: {len(confidence_only.get('Test Cases', []))}")
    
    return True

def run_all_tests():
    """运行所有测试"""
    print("Starting data processing functionality tests...\n")
    
    tests = [
        ("测试用例解析", test_parse_test_cases),
        ("测试脚本解析", test_parse_test_script),
        ("单个用例解析", test_parse_single_case),
        ("边界情况测试", test_edge_cases)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result, None))
            print(f"✓ {test_name}: {'PASS' if result else 'FAIL'}")
        except Exception as e:
            results.append((test_name, False, str(e)))
            print(f"✗ {test_name}: ERROR - {e}")

    print(f"\n=== Test Summary ===")
    passed = sum(1 for _, result, _ in results if result)
    total = len(results)
    print(f"Passed: {passed}/{total}")

    for test_name, result, error in results:
        status = "✓ PASS" if result else f"✗ FAIL{' - ' + error if error else ''}"
        print(f"  {test_name}: {status}")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
