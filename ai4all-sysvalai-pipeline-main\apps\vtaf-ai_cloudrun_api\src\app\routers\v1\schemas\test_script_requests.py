# file: test_script_requests.py

from pydantic import BaseModel, Field
from typing import Optional, Dict


class GenerateTestScriptRequest(BaseModel):
    """
    Request schema for generating a test script from a test case input.

    Attributes:
        testcase (Dict): The input test case as a structured JSON object.
        email (Optional[str]): Optional email address for notification.
        job_id (Optional[str]): Optional job identifier.
        enable_tracing (Optional[bool]): Whether tracing is enabled for the request.
    """
    testcase: Dict = Field(
        default_factory=dict,
        description="Structured test case to be converted into a test script"
    )
    email: Optional[str] = Field(
        default=None,
        description="Optional email address for notifications"
    )
    job_id: Optional[str] = Field(
        default=None,
        description="Optional job identifier"
    )
    enable_tracing: Optional[bool] = Field(
        default=True,
        description="Optional flag to enable tracing for the request"
    )
