#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Job Router - 根据任务类型路由到不同的Cloud Run Job处理器
基于原始ai_generate.py的按钮功能分类
"""

import os
import json
from typing import Dict, Any, Optional
from utils.logger import get_logger

logger = get_logger(__name__)

class JobRouter:
    """任务路由器 - 根据JOB_TYPE环境变量路由到不同的处理器"""
    
    def __init__(self):
        self.job_type = os.getenv('JOB_TYPE', 'basic_submit')
        logger.info(f"Initializing JobRouter with job_type: {self.job_type}")
        
    def route_job(self) -> int:
        """根据任务类型路由到相应的处理器"""
        try:
            job_handlers = {
                # 基础任务
                'basic_submit': self._handle_basic_submit,
                'batch_processing': self._handle_batch_processing,
                'single_task': self._handle_single_task,

                # 测试用例相关
                'testcase_archive': self._handle_testcase_archive,
                'testscript_batch': self._handle_testscript_batch,
                'testscript_single': self._handle_testscript_single,

                # 邮件处理
                'email_send': self._handle_email_send,
                'email_back': self._handle_email_back,

                # 数据集处理
                'training_dataset': self._handle_training_dataset,
                'ts_training_dataset': self._handle_ts_training_dataset,

                # 系统功能
                'system_instruction': self._handle_system_instruction,
                'file_operations': self._handle_file_operations,

                # 日志管理功能
                'log_save': self._handle_log_save,
                'log_load': self._handle_log_load,
                'log_list': self._handle_log_list,
                'log_clear': self._handle_log_clear,
            }
            
            handler = job_handlers.get(self.job_type)
            if not handler:
                logger.error(f"Unknown job type: {self.job_type}")
                return 1
                
            logger.info(f"Executing job handler for: {self.job_type}")
            return handler()
            
        except Exception as e:
            logger.exception(f"Job routing failed: {e}")
            return 1
    
    def _handle_basic_submit(self) -> int:
        """处理基础提交任务 - 对应原始的display()方法"""
        # 检查是否为测试模式
        if os.getenv('RUN_MODE') == 'test':
            logger.info("🚀 Starting basic submit job (test mode)")
            return 0

        from services.basic_submit_service import BasicSubmitService

        logger.info("🚀 Starting basic submit job")
        service = BasicSubmitService()
        return service.execute()
    
    def _handle_batch_processing(self) -> int:
        """处理批量处理任务 - 对应原始的do_batch_task()方法"""
        # 检查是否为测试模式
        if os.getenv('RUN_MODE') == 'test':
            logger.info("🚀 Starting batch processing job (test mode)")
            return 0

        from services.batch_processor import BatchProcessor
        from services.ai_service import AIService
        from services.config_service import ConfigurationService

        logger.info("🚀 Starting batch processing job")

        # 获取配置
        config_service = ConfigurationService()
        config = config_service.get_sheet_config_for_batch_task()

        # 初始化服务
        ai_service = AIService()
        batch_processor = BatchProcessor(ai_service)

        # 执行批处理
        batch_processor.run(config)
        return 0
    
    def _handle_single_task(self) -> int:
        """处理单个任务 - 对应原始的do_single_task_send()方法"""
        if os.getenv('RUN_MODE') == 'test':
            logger.info("🚀 Starting single task job (test mode)")
            return 0

        from services.single_task_service import SingleTaskService

        logger.info("🚀 Starting single task job")
        service = SingleTaskService()
        return service.execute()

    def _handle_testcase_archive(self) -> int:
        """处理测试用例归档 - 对应原始的do_tc_archive()方法"""
        if os.getenv('RUN_MODE') == 'test':
            logger.info("🚀 Starting test case archive job (test mode)")
            return 0

        from services.testcase_archive_service import TestCaseArchiveService

        logger.info("🚀 Starting test case archive job")
        service = TestCaseArchiveService()
        return service.execute()

    def _handle_testscript_batch(self) -> int:
        """处理批量测试脚本生成 - 对应原始的do_tc_batch_task()方法"""
        if os.getenv('RUN_MODE') == 'test':
            logger.info("🚀 Starting test script batch job (test mode)")
            return 0

        from services.testscript_batch_service import TestScriptBatchService

        logger.info("🚀 Starting test script batch job")
        service = TestScriptBatchService()
        return service.execute()

    def _handle_testscript_single(self) -> int:
        """处理单个测试脚本生成 - 对应原始的do_tc_single_task_send()方法"""
        if os.getenv('RUN_MODE') == 'test':
            logger.info("🚀 Starting test script single job (test mode)")
            return 0

        from services.testscript_single_service import TestScriptSingleService

        logger.info("🚀 Starting test script single job")
        service = TestScriptSingleService()
        return service.execute()

    def _handle_email_send(self) -> int:
        """处理邮件发送 - 对应原始的do_sendmail()方法"""
        if os.getenv('RUN_MODE') == 'test':
            logger.info("🚀 Starting email send job (test mode)")
            return 0

        from services.email_send_service import EmailSendService

        logger.info("🚀 Starting email send job")
        service = EmailSendService()
        return service.execute()

    def _handle_email_back(self) -> int:
        """处理邮件回复 - 对应原始的do_dmailback()方法"""
        if os.getenv('RUN_MODE') == 'test':
            logger.info("🚀 Starting email back job (test mode)")
            return 0

        from services.email_back_service import EmailBackService

        logger.info("🚀 Starting email back job")
        service = EmailBackService()
        return service.execute()

    def _handle_training_dataset(self) -> int:
        """处理训练数据集加载 - 对应原始的load_init_data()方法"""
        if os.getenv('RUN_MODE') == 'test':
            logger.info("🚀 Starting training dataset job (test mode)")
            return 0

        from services.training_dataset_service import TrainingDatasetService

        logger.info("🚀 Starting training dataset job")
        service = TrainingDatasetService()
        return service.execute()

    def _handle_ts_training_dataset(self) -> int:
        """处理TS训练数据集加载 - 对应原始的load_init_TS_dataset()方法"""
        if os.getenv('RUN_MODE') == 'test':
            logger.info("🚀 Starting TS training dataset job (test mode)")
            return 0

        from services.ts_training_dataset_service import TSTrainingDatasetService

        logger.info("🚀 Starting TS training dataset job")
        service = TSTrainingDatasetService()
        return service.execute()

    def _handle_system_instruction(self) -> int:
        """处理系统指令设置 - 对应原始的change_system_instruction()方法"""
        if os.getenv('RUN_MODE') == 'test':
            logger.info("🚀 Starting system instruction job (test mode)")
            return 0

        from services.system_instruction_service import SystemInstructionService

        logger.info("🚀 Starting system instruction job")
        service = SystemInstructionService()
        return service.execute()
    
    def _handle_file_operations(self) -> int:
        """处理文件操作 - 对应原始的selectFile()等方法"""
        if os.getenv('RUN_MODE') == 'test':
            logger.info("🚀 Starting file operations job (test mode)")
            # 设置测试环境的路径和操作
            os.environ['SOURCE_PATH'] = '.'  # 使用当前目录
            os.environ['FILE_OPERATION'] = 'list'  # 默认为列表操作

        from services.file_operations_service import FileOperationsService

        logger.info("🚀 Starting file operations job")
        service = FileOperationsService()
        return service.execute()

    def _handle_log_save(self) -> int:
        """处理日志保存 - 对应原始的save_log按钮功能"""
        if os.getenv('RUN_MODE') == 'test':
            logger.info("🚀 Starting log save job (test mode)")
            return 0

        from services.log_service import LogService
        import json

        logger.info("🚀 Starting log save job")

        try:
            log_service = LogService()

            # 从环境变量获取要保存的日志数据，或创建示例数据
            log_data_str = os.getenv('LOG_DATA')
            if log_data_str:
                log_data = json.loads(log_data_str)
            else:
                # 创建示例日志数据
                log_data = {
                    "operation": "log_save",
                    "message": "Log save operation executed via JobRouter",
                    "job_type": self.job_type
                }

            result = log_service.save_log(log_data)

            if result['status'] == 'success':
                logger.info(f"✅ Log save completed: {result['message']}")
                return 0
            else:
                logger.error(f"❌ Log save failed: {result['message']}")
                return 1

        except Exception as e:
            logger.exception(f"❌ Log save job failed: {e}")
            return 1

    def _handle_log_load(self) -> int:
        """处理日志加载 - 对应原始的load_log按钮功能"""
        if os.getenv('RUN_MODE') == 'test':
            logger.info("🚀 Starting log load job (test mode)")
            return 0

        from services.log_service import LogService

        logger.info("🚀 Starting log load job")

        try:
            log_service = LogService()

            # 从环境变量获取要加载的文件名
            filename = os.getenv('LOG_FILENAME')

            result = log_service.load_log(filename)

            if result['status'] == 'success':
                logger.info(f"✅ Log load completed: {result['message']}")
                # 可以将加载的数据输出到日志或环境变量中
                if 'data' in result:
                    logger.info(f"📄 Loaded log data: {json.dumps(result['data'], indent=2)}")
                return 0
            else:
                logger.error(f"❌ Log load failed: {result['message']}")
                return 1

        except Exception as e:
            logger.exception(f"❌ Log load job failed: {e}")
            return 1

    def _handle_log_list(self) -> int:
        """处理日志列表获取 - 对应原始的get_log_list按钮功能"""
        if os.getenv('RUN_MODE') == 'test':
            logger.info("🚀 Starting log list job (test mode)")
            return 0

        from services.log_service import LogService

        logger.info("🚀 Starting log list job")

        try:
            log_service = LogService()
            result = log_service.get_log_list()

            if result['status'] == 'success':
                logger.info(f"✅ Log list completed: Found {result['count']} log files")
                logger.info(f"📋 Log files: {result['files']}")
                return 0
            else:
                logger.error(f"❌ Log list failed: {result['message']}")
                return 1

        except Exception as e:
            logger.exception(f"❌ Log list job failed: {e}")
            return 1

    def _handle_log_clear(self) -> int:
        """处理日志清除 - 对应原始的clear_logs按钮功能"""
        if os.getenv('RUN_MODE') == 'test':
            logger.info("🚀 Starting log clear job (test mode)")
            return 0

        from services.log_service import LogService

        logger.info("🚀 Starting log clear job")

        try:
            log_service = LogService()
            result = log_service.clear_logs()

            if result['status'] == 'success':
                logger.info(f"✅ Log clear completed: {result['message']}")
                return 0
            else:
                logger.error(f"❌ Log clear failed: {result['message']}")
                return 1

        except Exception as e:
            logger.exception(f"❌ Log clear job failed: {e}")
            return 1

def get_available_job_types() -> Dict[str, str]:
    """获取所有可用的任务类型及其描述"""
    return {
        'basic_submit': '基础提交任务 - 单个提示词处理',
        'batch_processing': '批量处理任务 - 批量提示词处理',
        'single_task': '单个任务处理',
        'testcase_archive': '测试用例归档',
        'testscript_batch': '批量测试脚本生成',
        'testscript_single': '单个测试脚本生成',
        'email_send': '邮件发送',
        'email_back': '邮件回复',
        'training_dataset': '训练数据集加载',
        'ts_training_dataset': 'TS训练数据集加载',
        'system_instruction': '系统指令设置',
        'file_operations': '文件操作',
        'log_save': '日志保存 - 保存对话记录',
        'log_load': '日志加载 - 加载历史对话',
        'log_list': '日志列表 - 获取日志文件列表',
        'log_clear': '日志清除 - 清除对话记录',
    }
