from fastapi import APIRouter, HTTPException, Form, UploadFile, File
from google.cloud import bigquery
from utilities import get_bigquery_client, process_file, upload_file_to_storage, trigger_run_job, bigquery_insert_query, generate_signed_url, clear_folder
from configs.env import settings
import shutil
import traceback
import datetime
import os
import json
import uuid

router = APIRouter()
client = get_bigquery_client()

services_project_id = settings.SERVICES_PROJECT_ID
services_dataset_id = settings.SERVICES_DATASET_ID
services_bucket_name = settings.SERVICES_BUCKET_NAME
ai_testcase_job = settings.TESTCASE_GENERATION_JOB

UPLOAD_DIR = "uploads"
REQUIREMENTS_DIR = "requirements"

@router.get("/get_organization_inputs")
def get_organization_inputs():
    try:
        query = f"""
            SELECT 
                bg,
                ARRAY_AGG(STRUCT(pg, pl, org_uuid)) AS details
            FROM `{services_project_id}.{services_dataset_id}.VALEO_ORGANIZATION`
            GROUP BY bg
        """
        query_job = client.query(query=query)
        results = []
        for row in query_job.result():
            results.append({
                "bg": row["bg"],
                "details": row["details"]
            })
        return results
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)

@router.post("/get_algorithms_and_sensors")
def get_algorithms_and_sensors(org_uuid: str = Form(...)):
    try:
        sensor_query = f"""
            SELECT category, ARRAY_AGG(value) AS details
            FROM `{services_project_id}.{services_dataset_id}.PROJECT_CONFIGURATION`
            WHERE type = "sensor"
            AND relation LIKE '%{org_uuid}%'
            group by category
        """

        algorithm_query = f"""
            SELECT category, ARRAY_AGG(value) AS details
            FROM `{services_project_id}.{services_dataset_id}.PROJECT_CONFIGURATION`
            WHERE type = "algorithm"
            AND relation LIKE '%{org_uuid}%'
            GROUP BY category
        """

        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("org_uuid", "STRING", org_uuid)
            ]
        )
        sensor_query_job = client.query(query=sensor_query, job_config=job_config)
        algorithm_query_job = client.query(query=algorithm_query)

        # Convert to dictionary
        algorithm_dict = {row.category: row.details for row in algorithm_query_job.result()}
        sensor_dict = {row.category: row.details for row in sensor_query_job.result()}

        results = {
            "sensors" : sensor_dict,
            "algorithms" : algorithm_dict
        }
        return results
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)

@router.post("/get_functionality_inputs")
def get_functionality_inputs(org_uuid: str = Form(...)):
    try:
        query = f"""
            SELECT 
                category, 
                ARRAY_AGG(STRUCT(value, uuid)) AS details 
            FROM `{services_project_id}.{services_dataset_id}.EUF_FEATURE_CONFIG_TABLE`
            WHERE EXISTS (
                SELECT 1
                FROM UNNEST(SPLIT(relation, ',')) AS rel_uuid
                WHERE rel_uuid = @org_uuid
            )
            GROUP BY category
        """

        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("org_uuid", "STRING", org_uuid)
            ]
        )

        query_job = client.query(query=query, job_config=job_config)
        results = []
        for row in query_job.result():
            results.append({
                "category": row["category"],
                "details": row["details"]
            })
        return results
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)

@router.post("/upload_file")
def upload_file(
    file: UploadFile = File(...),
    user_email: str = Form(...),
    selected_rows: str = Form(...),
    business_group: str = Form(...),
    product_group: str = Form(...),
    product_line: str = Form(...),
    features: str = Form(...),
    algorithms: str = Form(...),
    sensors: str = Form(...),
):
    try:
        # Deserialize selected_rows from JSON string to list
        selected_rows_list = json.loads(selected_rows)

        uid = str(uuid.uuid4())

        # Generate a unique folder for this request
        request_upload_dir = os.path.join(UPLOAD_DIR, uid)
        print("request_upload_dir", request_upload_dir)
        os.makedirs(request_upload_dir, exist_ok=True)
        
        # Save the uploaded file
        file_path = os.path.join(request_upload_dir, file.filename)
        print("file_path", file_path)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        result = process_file(file_path, selected_rows_list)
        result = json.loads(result)
        inputs = {
            "feature": str(features),
            "sensors": str(sensors),
            "algorithms": str(algorithms),
            "requirements": result,
        }

        # Prepare the requirements directory for this request
        request_requirements_dir = os.path.join(REQUIREMENTS_DIR, uid)
        print("request_requirements_dir", request_requirements_dir)
        os.makedirs(request_requirements_dir, exist_ok=True)
        requirements_file_name = f"{uid}.json"
        requirements_path = os.path.join(request_requirements_dir, requirements_file_name)
        print("requirements_path", requirements_path)
        # requirements_file_path = uid + ".json"
        with open(f"{requirements_path}", "w") as json_file:
            json.dump(inputs, json_file, indent=4)

        upload_file_to_storage(
            bucket_name=services_bucket_name,
            file_path=requirements_path,
            destination_blob_name="vtaf-ai/user_input/" + requirements_file_name,
        )
        upload_file_to_storage(
            bucket_name=services_bucket_name,
            file_path=f"{file_path}",
            destination_blob_name=f"vtaf-ai/user_uploads/{uid}_{file.filename}",
        )

        env_vars = {
            "FILE_NAME": uid
        }

        execution_id = trigger_run_job(job_name=ai_testcase_job, environment_variables=env_vars)
        current_timestamp = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ")

        execute_query = f"""
            INSERT INTO `{services_project_id}.{services_dataset_id}.TC_JOB_HISTORY` (
                user_email,
                bg,
                pg,
                pl,
                timestamp,
                execution_id,
                user_upload,
                user_input,
                execution_status,
                is_project
            ) 
            VALUES (
                '{user_email}',
                '{business_group}',
                '{product_group}',
                '{product_line}',
                '{current_timestamp}',
                '{execution_id}',
                '{services_bucket_name}/vtaf-ai/user_uploads/{uid}_{file.filename}',
                '{services_bucket_name}/vtaf-ai/user_input/{requirements_file_name}',
                'started',
                False
            );
            """
        bigquery_insert_query(execute_query)

        shutil.rmtree(request_upload_dir, ignore_errors=True)
        shutil.rmtree(request_requirements_dir, ignore_errors=True)

        # Response with file name and selected rows
        return {"message": "Job is running", "execution_id": execution_id}
    except Exception as e:
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        raise HTTPException(status_code=500, detail=error_message)


