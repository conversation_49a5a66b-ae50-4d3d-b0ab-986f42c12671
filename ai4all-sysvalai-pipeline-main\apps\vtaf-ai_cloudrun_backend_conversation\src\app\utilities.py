from datetime import datetime
import json
import re
import traceback
from google.cloud import firestore, bigquery
from google.oauth2 import service_account
from fastapi import HTTPException
from configs.env import settings
from dependencies.ai4all_endpoints import generate_ai4all
from dependencies.vtaf_api_endpoints import search_vector

services_project_id = settings.SERVICES_PROJECT_ID
firestore_database_id = settings.FIRESTORE_DATABASE_ID
firestore_session_id = settings.FIRESTORE_SESSION_COLLECTION

def get_bigquery_client():
    # credentials = service_account.Credentials.from_service_account_file(
    #     "cp2673-dev.json",
    #     scopes=["https://www.googleapis.com/auth/cloud-platform"],
    # )
    client = bigquery.Client(project=services_project_id)
    return client

# Function to create and return a firestore client instance
def get_firestore_client(firestore_database):
    # credentials = service_account.Credentials.from_service_account_file(
    #     "cp2673-dev.json",
    #     scopes=["https://www.googleapis.com/auth/cloud-platform"],
    # )
    client = firestore.Client(project=services_project_id, database=firestore_database_id)
    # client = firestore.Client(project=services_project_id, credentials=credentials, database=firestore_database)
    return client
    
async def add_session_fs(email, session_id, title, variant_id, project):
    try:
        client = get_firestore_client(firestore_database_id)
        doc_ref = client.collection(firestore_session_id).document(email)
        doc = doc_ref.get()

        new_session = {
            "session_id": session_id,
            "created_at": datetime.utcnow(),
            "last_modified": datetime.utcnow(),
            "title": title,
            "variant_id": variant_id,
            "project": project,
        }

        if doc.exists:
            # Append to existing sessions
            existing_data = doc.to_dict()
            sessions = existing_data.get("sessions", [])
            sessions.append(new_session)
            doc_ref.update({
                "sessions": sessions
            })
        else:
            # Create new document
            doc_ref.set({
                "sessions": [new_session]
            })

        return {"message": "success", "session_id": session_id}
    except Exception as e:
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        raise HTTPException(status_code=500, detail=error_message)
    
def end_sessions_by_email(email):
    try:
        client = get_firestore_client(firestore_database_id)
        doc_ref = client.collection(firestore_session_id).document(email)
        doc = doc_ref.get()

        if not doc.exists:
            return {"message":"No Sessions Found"}

        data = doc.to_dict()
        sessions = data.get("sessions", [])

        if not sessions:
            return {"message":"No Sessions Found"}

        # Update all session dicts' alive field to False
        updated_sessions = []
        for session in sessions:
            session["alive"] = False
            updated_sessions.append(session)

        # Write back updated sessions list to Firestore
        doc_ref.update({
            "sessions": updated_sessions
        })

        return {
            "message": f"Updated {len(updated_sessions)} sessions to alive=False for email {email}",
            "updated_sessions_count": len(updated_sessions)
        }

    except Exception as e:
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        raise HTTPException(status_code=500, detail=error_message)
    
def update_session_fs_title(email, session_id, title, variant_id):
    try:
        client = get_firestore_client(firestore_database_id)
        doc_ref = client.collection(firestore_session_id).document(email)
        doc = doc_ref.get()

        if not doc.exists:
            raise HTTPException(status_code=404, detail=f"No document found for email '{email}'")

        data = doc.to_dict()
        sessions = data.get("sessions", [])

        session_found = False
        updated_sessions = []
        for session in sessions:
            if session.get("session_id") == session_id:
                session["title"] = title
                session["variant_id"] = variant_id
                session_found = True
            updated_sessions.append(session)

        if not session_found:
            raise HTTPException(status_code=404, detail=f"Session '{session_id}' not found for email '{email}'")

        doc_ref.update({
            "sessions": updated_sessions
        })

        return {"message": f"Session '{session_id}' alive status updated to {title}"}

    except Exception as e:
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        raise HTTPException(status_code=500, detail=error_message)
    
def update_session_fs_alive(email, session_id, status):
    try:
        client = get_firestore_client(firestore_database_id)
        doc_ref = client.collection(firestore_session_id).document(email)
        doc = doc_ref.get()

        if not doc.exists:
            raise HTTPException(status_code=404, detail=f"No document found for email '{email}'")

        data = doc.to_dict()
        sessions = data.get("sessions", [])

        session_found = False
        updated_sessions = []
        for session in sessions:
            if session.get("session_id") == session_id:
                session["alive"] = status
                session_found = True
            updated_sessions.append(session)

        if not session_found:
            raise HTTPException(status_code=404, detail=f"Session '{session_id}' not found for email '{email}'")

        doc_ref.update({
            "sessions": updated_sessions
        })

        return {"message": f"Session '{session_id}' alive status updated to {status}"}

    except Exception as e:
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        raise HTTPException(status_code=500, detail=error_message)
    
async def get_session_by_email_and_session_id(email, session_id):
    try:
        client = get_firestore_client(firestore_database_id)
        doc_ref = client.collection(firestore_session_id).document(email)
        doc = doc_ref.get()

        if not doc.exists:
            print(f"No document found for email: {email}")
            return None

        data = doc.to_dict()
        sessions = data.get("sessions", [])

        # Find the session with the matching session_id
        for session in sessions:
            if session.get("session_id") == session_id:
                return session

        print(f"Session with ID {session_id} not found for {email}")
        return None
    except Exception as e:
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        raise HTTPException(status_code=500, detail=error_message)
    
async def search_vector_db(id_token, ai4allclient, session_id, query, projects_to_search, jwt_access_token, collection_name):
    try:
        if collection_name == "ALL":
            payload = {
                    "query":query,
                    "collection_name":collection_name,
                    "limit":3,
                    "score_threshold":0.3,
                }
        else:
            payload = {
                    "query":query,
                    "collection_name":collection_name,
                    "limit":3,
                    "score_threshold":0.3,
                    "filter":{
                        "must":[
                            {
                                "key":"metadata.variant_uuid",
                                "match":{
                                "value": projects_to_search
                                }
                            },
                        ]
                    }
                }
        result = await search_vector(payload=payload, access_token=jwt_access_token)

        result_json = result.json()
        json_string = json.dumps(result_json, indent=4, sort_keys=True)
        
        knowlwdgebase_json = {
            "model_name" : "gemini-2.0-flash-latest",
            "stream" : False,
            "messages_history_type" : "buffer",
            "query" : f"""You are an intelligent assistant answering user queries based on the provided context from a knowledge base.

                Context: {json_string}

                Question:{query}"""
        }

        ai4all_result = await generate_ai4all(id_token=id_token, ai4allclient=ai4allclient, session_id=session_id, result_data=knowlwdgebase_json)
        return ai4all_result
    except Exception as e:
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        raise HTTPException(status_code=500, detail=error_message)
    
# Function to extract question from user messages
def keep_only_question(messages):
    for message in messages:
        if message.get("role") == "user":
            for part in message.get("parts", []):
                text = part.get("data", "")
                match = re.search(r'Question\s*:\s*(.*)', text, re.DOTALL)
                if match:
                    part["data"] = match.group(1).strip()
                else:
                    part["data"] = ""
    return messages