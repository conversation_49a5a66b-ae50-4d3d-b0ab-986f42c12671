<template>
  <div class="row big_row" style="margin-top: 5rem" v-if="$q.screen.gt.sm">
    <div class="map_content text-h3 text-white">
      Lorem ipsum dolor sit amet consectetur adipisicing elit<br />
      <span style="font-size: 20px; color: white"
        >Itaque nesciunt assumenda, tempor</span
      >
    </div>
    <div class="col-9">
      <q-img
        src="src/assets/home/<USER>"
        style="width: 90%; display: flex; margin: 0 auto"
      ></q-img>
    </div>
    <div class="col-3">
      <q-list>
        <q-item
          v-for="(country, index) in countries"
          :key="index"
          v-ripple
          style="max-width: 70%"
        >
          <q-item-section avatar>
            <q-avatar :style="{ backgroundColor: country.color }"></q-avatar>
          </q-item-section>
          <q-item-section style="color: white">{{
            country.label
          }}</q-item-section>
        </q-item>
      </q-list>
    </div>
  </div>
  <div class="entire_row" v-else>
    <div class="row small_row" style="margin-top: 5rem">
      <div class="map_content text-h3 text-white">
        Lorem ipsum dolor sit amet consectetur adipisicing elit<br />
        <span style="font-size: 20px; color: white"
          >Itaque nesciunt assumenda, tempor</span
        >
      </div>
    </div>
    <div class="row small_row">
      <q-img
        src="src/assets/home/<USER>"
        style="width: 90%; display: flex; margin: 0 auto"
      ></q-img>
    </div>
    <div class="row small_row_list">
      <div class="col-sm-6">
        <q-list class="list">
          <q-item class="list_item">
            <q-item-section avatar>
              <q-avatar style="background-color: #f400be"></q-avatar>
            </q-item-section>
            <q-item-section style="color: white">Tuam, Ireland</q-item-section>
          </q-item>
          <q-item class="list_item">
            <q-item-section avatar>
              <q-avatar style="background-color: #099fff"></q-avatar>
            </q-item-section>
            <q-item-section style="color: white"
              >Troy, United States</q-item-section
            >
          </q-item>
          <q-item class="list_item">
            <q-item-section avatar>
              <q-avatar style="background-color: #706338"></q-avatar>
            </q-item-section>
            <q-item-section style="color: white">Chennai, India</q-item-section>
          </q-item>
          <q-item class="list_item">
            <q-item-section avatar>
              <q-avatar style="background-color: #5e17eb"></q-avatar>
            </q-item-section>
            <q-item-section style="color: white"
              >Prague, Czech Republic</q-item-section
            >
          </q-item>
        </q-list>
      </div>
      <div class="col-sm-6">
        <q-list>
          <q-item class="list_item">
            <q-item-section avatar>
              <q-avatar style="background-color: #004aad"></q-avatar>
            </q-item-section>
            <q-item-section style="color: white">China</q-item-section>
          </q-item>
          <q-item class="list_item">
            <q-item-section avatar>
              <q-avatar style="background-color: #ff3131"></q-avatar>
            </q-item-section>
            <q-item-section style="color: white">Japan</q-item-section>
          </q-item>
          <q-item class="list_item">
            <q-item-section avatar>
              <q-avatar style="background-color: #ff5757"></q-avatar>
            </q-item-section>
            <q-item-section style="color: white">Germany</q-item-section>
          </q-item>
        </q-list>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useQuasar } from "quasar";
import { ref } from "vue";
const $q = useQuasar();

const countries = [
  { label: "Tuam, Ireland", color: "#F400BE", name: "Ireland" },
  { label: "Troy, United States", color: "#099FFF", name: "United States" },
  { label: "Chennai, India", color: "#706338", name: "India" },
  { label: "Prague, Czech Republic", color: "#5E17EB", name: "Czech Rep." },
  { label: "China", color: "#004AAD", name: "China" },
  { label: "Japan", color: "#FF3131", name: "Japan" },
  { label: "Germany", color: "#FF5757", name: "Germany" },
];
</script>

<style scoped>
.big_row {
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(to top, #014a88, #000);
}
.entire_row {
  background: linear-gradient(to top, #014a88, #000);
}

.small_row_list {
  margin-top: 1rem;
  border-radius: 2rem;
  margin-left: 5%;
  margin-right: 5%;
}

.map_content {
  text-align: center;
  margin-bottom: 1rem;
  padding-top: 2rem;
}
</style>

<!-- <template>
  <div class="row" style="margin-top: 5rem">
    <div class="map_content text-h3 text-white">
      Lorem ipsum dolor sit amet consectetur adipisicing elit<br />
      <span style="font-size: 20px; color: white"
        >Itaque nesciunt assumenda, tempor</span
      >
    </div>
    <div
      class="col-9"
      align="center"
      :style="{
        marginTop: $q.screen.gt.md ? '2rem' : '2rem',
        marginBottom: $q.screen.gt.md ? '3rem' : '0',
      }"
    >
      <div class="world-map-container" ref="mapContainer"></div>
    </div>
    <div class="col-3" v-if="$q.screen.gt.md">
      <q-list>
        <q-item
          v-for="(country, index) in countries"
          :key="index"
          v-ripple
          style="max-width: 70%"
        >
          <q-item-section avatar>
            <q-avatar :style="{ backgroundColor: country.color }"></q-avatar>
          </q-item-section>
          <q-item-section style="color: white">{{
            country.label
          }}</q-item-section>
        </q-item>
      </q-list>
    </div>
    <div class="row" v-else-if="$q.screen.lt.sm" style="margin-bottom: 3rem">
      <div class="col-12" align="center">
        <q-list>
          <q-item v-for="(country, index) in countries" :key="index" v-ripple>
            <q-item-section avatar>
              <q-avatar
                size="20px"
                :style="{ backgroundColor: country.color }"
              ></q-avatar>
            </q-item-section>
            <q-item-section style="color: white">{{
              country.label
            }}</q-item-section>
          </q-item>
        </q-list>
      </div>
    </div>
    <div v-else>
      <div class="row">
        <div class="col-12">
          <q-list
            style="
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: center;
            "
            class="justify-content-center"
          >

            <q-item
              v-for="(country, index) in countries.slice(0, 4)"
              :key="'top-' + index"
              v-ripple
            >
              <q-item-section avatar>
                <q-avatar
                  size="20px"
                  :style="{ backgroundColor: country.color }"
                ></q-avatar>
              </q-item-section>
              <q-item-section style="color: white">{{
                country.label
              }}</q-item-section>
            </q-item>
          </q-list>
        </div>
      </div>
      <div class="row">
        <div class="col-12" align="center">
          <q-list
            style="
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: center;
            "
            class="justify-content-center"
          >

            <q-item
              v-for="(country, index) in countries.slice(4)"
              :key="'bottom-' + index"
              v-ripple
            >
              <q-item-section avatar>
                <q-avatar
                  size="20px"
                  :style="{ backgroundColor: country.color }"
                ></q-avatar>
              </q-item-section>
              <q-item-section style="color: white">{{
                country.label
              }}</q-item-section>
            </q-item>
          </q-list>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useQuasar } from "quasar";
import { ref, onMounted, watch } from "vue";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
const $q = useQuasar();

const countries = [
  { label: "Tuam, Ireland", color: "red", name: "Ireland" },
  { label: "Troy, United States", color: "#014a88", name: "United States" },
  { label: "Chennai, India", color: "#018850", name: "India" },
  { label: "Prague, Czech Republic", color: "#880118", name: "Czech Rep." },
  { label: "China", color: "#d16806", name: "China" },
  { label: "Japan", color: "#11fa05", name: "Japan" },
  { label: "Germany", color: "#888601", name: "Germany" },
];
const mapContainer = ref(null);
const mapInstance = ref(null); // Store the map instance for future reference
const getZoomSize = () => {
  if ($q.screen.lt.sm) {
    return 0;
  } else if ($q.screen.lt.md) {
    return 1;
  } else {
    return 2;
  }
};

watch(
  () => $q.screen.name,
  (newScreen, oldScreen) => {
    if (newScreen !== oldScreen) {
      if (mapInstance.value) {
        mapInstance.value.remove(); // This destroys the previous map instance
      }
      maps(); // Recreate the map with the new settings
    }
  }
);

onMounted(() => {
  maps();
});

function maps() {
  const zoomSize = getZoomSize();
  const map = L.map(mapContainer.value, {
    scrollWheelZoom: false, // Disable zooming via scroll wheel
    // dragging: false,
  }).setView([20, 10], zoomSize);

  // Add the tile layer (using Stamen Toner)
  L.tileLayer(
    "https://stamen-tiles-{s}.a.ssl.fastly.net/toner/{z}/{x}/{y}.{ext}",
    {
      maxZoom: 18,
      attribution: "",
      subdomains: "abcd",
      ext: "png",
    }
  ).addTo(map);

  // Define colors for each country
  const countryColors = {
    China: "#d16806",
    Japan: "#11fa05",
    Germany: "#888601",
    India: "#018850",
    "United States": "#014a88",
    Ireland: "red",
    "Czech Rep.": "#880118",
  };

  // Define additional text for each country
  const additionalText = {
    "United States": "Troy",
    Ireland: "Tuam",
    "Czech Rep.": "Prague",
    India: "Chennai",
    China: "China",
    Japan: "Japan",
    Germany: "Germany",
  };

  // Load the single GeoJSON file for all countries
  fetch("/geojson/world-countries.geojson")
    .then((response) => response.json())
    .then((geoJson) => {
      L.geoJSON(geoJson, {
        style: (feature) => {
          const countryName = feature.properties.name;
          const fillColor = countryColors[countryName] || "white"; // Default color if not specified
          return { fillColor, fillOpacity: 0.7, color: "white", weight: 1 };
        },
        onEachFeature: (feature, layer) => {
          const countryName = feature.properties.name;
          const text = additionalText[countryName] || "";
          const popupContent = `<b>${countryName}</b><br>${text}`;
          layer.bindPopup(popupContent, {
            closeButton: false,
            autoClose: false,
          });
        },
      }).addTo(map);
    })
    .catch((error) => console.error("Error loading the GeoJSON data:", error));

  mapInstance.value = map;
}
</script>

<style scoped>
.world-map-container {
  width: 80%; /* Fit map container to parent width */
  height: 60vh; /* Adjust height as needed */
}
.row {
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(to top, #014a88, #000);
}

.map_content {
  text-align: center;
  margin-bottom: 1rem;
  padding-top: 2rem;
}
@media (max-width: 650px) {
  .world-map-container {
    height: 20vh;
    width: 100%; /* Decrease height further for small screens (mobile) */
  }
}
@media (max-width: 1000px) and (min-width: 650px) {
  .world-map-container {
    height: 40vh;
    width: 90%; /* Decrease height further for small screens (mobile) */
  }
}
</style> -->
