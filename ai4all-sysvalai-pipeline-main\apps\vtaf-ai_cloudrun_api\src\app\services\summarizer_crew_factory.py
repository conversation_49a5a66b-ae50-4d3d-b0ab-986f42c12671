# summarizer_crew_factory.py
import yaml
from crewai import Crew, Process, Agent, Task

# Load agent configurations from YAML
with open("app/config/crew/agents.yaml", "r") as f:
    agents = yaml.safe_load(f)

# Load task configurations from YAML
with open("app/config/crew/tasks.yaml", "r") as f:
    tasks = yaml.safe_load(f)

def create_summarizer_crew(request_email: str, llm):
    data_extractor_agent = Agent(
        config=agents["Data_Extractor_Agent"],
        llm=llm,
    )

    data_extractor_task = Task(
        name="Data Extraction Task",
        config=tasks["Data_Extraction_Task"],
        agent=data_extractor_agent,
    )

    summary_writer_agent = Agent(
        config=agents["Summary_Writer_Agent"],
        llm=llm,
    )

    summary_writer_task = Task(
        name="Summary Writing Task",
        config=tasks["Summary_Writer_Task"],
        agent=summary_writer_agent,
        context=[data_extractor_task],
    )

    crew = Crew(
        agents=[data_extractor_agent, summary_writer_agent],
        tasks=[data_extractor_task, summary_writer_task],
        process=Process.sequential,
    )
    crew.name = request_email or "<EMAIL>"

    return crew
