from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar, Union, cast

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..types import UNSET, Unset

if TYPE_CHECKING:
    from ..models.generation_config import GenerationConfig
    from ..models.generation_request_rag_config_type_0 import GenerationRequestRagConfigType0
    from ..models.message_content import MessageContent


T = TypeVar("T", bound="GenerationRequest")


@_attrs_define
class GenerationRequest:
    """
    Attributes:
        model_name (str):
        contents (list['MessageContent']):
        stream (Union[None, Unset, bool]):
        system_instruction (Union[None, Unset, str]):
        generation_config (Union['GenerationConfig', None, Unset]):
        tools_to_use (Union[None, Unset, list[str]]):
        collections_names (Union[None, Unset, list[str]]):
        rag_config (Union['GenerationRequestRagConfigType0', None, Unset]):
    """

    model_name: str
    contents: list["MessageContent"]
    stream: Union[None, Unset, bool] = UNSET
    system_instruction: Union[None, Unset, str] = UNSET
    generation_config: Union["GenerationConfig", None, Unset] = UNSET
    tools_to_use: Union[None, Unset, list[str]] = UNSET
    collections_names: Union[None, Unset, list[str]] = UNSET
    rag_config: Union["GenerationRequestRagConfigType0", None, Unset] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        from ..models.generation_config import GenerationConfig
        from ..models.generation_request_rag_config_type_0 import GenerationRequestRagConfigType0

        model_name = self.model_name

        contents = []
        for contents_item_data in self.contents:
            contents_item = contents_item_data.to_dict()
            contents.append(contents_item)

        stream: Union[None, Unset, bool]
        if isinstance(self.stream, Unset):
            stream = UNSET
        else:
            stream = self.stream

        system_instruction: Union[None, Unset, str]
        if isinstance(self.system_instruction, Unset):
            system_instruction = UNSET
        else:
            system_instruction = self.system_instruction

        generation_config: Union[None, Unset, dict[str, Any]]
        if isinstance(self.generation_config, Unset):
            generation_config = UNSET
        elif isinstance(self.generation_config, GenerationConfig):
            generation_config = self.generation_config.to_dict()
        else:
            generation_config = self.generation_config

        tools_to_use: Union[None, Unset, list[str]]
        if isinstance(self.tools_to_use, Unset):
            tools_to_use = UNSET
        elif isinstance(self.tools_to_use, list):
            tools_to_use = self.tools_to_use

        else:
            tools_to_use = self.tools_to_use

        collections_names: Union[None, Unset, list[str]]
        if isinstance(self.collections_names, Unset):
            collections_names = UNSET
        elif isinstance(self.collections_names, list):
            collections_names = self.collections_names

        else:
            collections_names = self.collections_names

        rag_config: Union[None, Unset, dict[str, Any]]
        if isinstance(self.rag_config, Unset):
            rag_config = UNSET
        elif isinstance(self.rag_config, GenerationRequestRagConfigType0):
            rag_config = self.rag_config.to_dict()
        else:
            rag_config = self.rag_config

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "model_name": model_name,
                "contents": contents,
            }
        )
        if stream is not UNSET:
            field_dict["stream"] = stream
        if system_instruction is not UNSET:
            field_dict["system_instruction"] = system_instruction
        if generation_config is not UNSET:
            field_dict["generation_config"] = generation_config
        if tools_to_use is not UNSET:
            field_dict["tools_to_use"] = tools_to_use
        if collections_names is not UNSET:
            field_dict["collections_names"] = collections_names
        if rag_config is not UNSET:
            field_dict["rag_config"] = rag_config

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.generation_config import GenerationConfig
        from ..models.generation_request_rag_config_type_0 import GenerationRequestRagConfigType0
        from ..models.message_content import MessageContent

        d = dict(src_dict)
        model_name = d.pop("model_name")

        contents = []
        _contents = d.pop("contents")
        for contents_item_data in _contents:
            contents_item = MessageContent.from_dict(contents_item_data)

            contents.append(contents_item)

        def _parse_stream(data: object) -> Union[None, Unset, bool]:
            if data is None:
                return data
            if isinstance(data, Unset):
                return data
            return cast(Union[None, Unset, bool], data)

        stream = _parse_stream(d.pop("stream", UNSET))

        def _parse_system_instruction(data: object) -> Union[None, Unset, str]:
            if data is None:
                return data
            if isinstance(data, Unset):
                return data
            return cast(Union[None, Unset, str], data)

        system_instruction = _parse_system_instruction(d.pop("system_instruction", UNSET))

        def _parse_generation_config(data: object) -> Union["GenerationConfig", None, Unset]:
            if data is None:
                return data
            if isinstance(data, Unset):
                return data
            try:
                if not isinstance(data, dict):
                    raise TypeError()
                generation_config_type_0 = GenerationConfig.from_dict(data)

                return generation_config_type_0
            except:  # noqa: E722
                pass
            return cast(Union["GenerationConfig", None, Unset], data)

        generation_config = _parse_generation_config(d.pop("generation_config", UNSET))

        def _parse_tools_to_use(data: object) -> Union[None, Unset, list[str]]:
            if data is None:
                return data
            if isinstance(data, Unset):
                return data
            try:
                if not isinstance(data, list):
                    raise TypeError()
                tools_to_use_type_0 = cast(list[str], data)

                return tools_to_use_type_0
            except:  # noqa: E722
                pass
            return cast(Union[None, Unset, list[str]], data)

        tools_to_use = _parse_tools_to_use(d.pop("tools_to_use", UNSET))

        def _parse_collections_names(data: object) -> Union[None, Unset, list[str]]:
            if data is None:
                return data
            if isinstance(data, Unset):
                return data
            try:
                if not isinstance(data, list):
                    raise TypeError()
                collections_names_type_0 = cast(list[str], data)

                return collections_names_type_0
            except:  # noqa: E722
                pass
            return cast(Union[None, Unset, list[str]], data)

        collections_names = _parse_collections_names(d.pop("collections_names", UNSET))

        def _parse_rag_config(data: object) -> Union["GenerationRequestRagConfigType0", None, Unset]:
            if data is None:
                return data
            if isinstance(data, Unset):
                return data
            try:
                if not isinstance(data, dict):
                    raise TypeError()
                rag_config_type_0 = GenerationRequestRagConfigType0.from_dict(data)

                return rag_config_type_0
            except:  # noqa: E722
                pass
            return cast(Union["GenerationRequestRagConfigType0", None, Unset], data)

        rag_config = _parse_rag_config(d.pop("rag_config", UNSET))

        generation_request = cls(
            model_name=model_name,
            contents=contents,
            stream=stream,
            system_instruction=system_instruction,
            generation_config=generation_config,
            tools_to_use=tools_to_use,
            collections_names=collections_names,
            rag_config=rag_config,
        )

        generation_request.additional_properties = d
        return generation_request

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
