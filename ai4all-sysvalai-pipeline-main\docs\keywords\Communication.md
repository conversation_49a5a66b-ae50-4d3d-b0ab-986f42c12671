# Group {Communication}
## Check_CANoe

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Check_CANoe.seq*


**Descriptions:**


***Command Set:***

	SysVar
		-SysVar=Name;Condition
	EnvVar
		-EnvVar=Name;Condition
	Signal
		-Signal=Name;Condition
	WaitForSysVarMatch
		-WaitForSysVarMatch=Name;CompareValue;Timeout
	WaitForEnvVarMatch
		-WaitForEnvVarMatch=Name;CompareValue;Timeout
	WaitForSignalMatch
		-WaitForSignalMatch=Name;CompareValue;Timeout
	WaitForSysVarMatchInRange
		-WaitForSysVarMatchInRange=Name;Timeout;LowerLimit;UpperLimit
	WaitForEnvVarMatchInRange
		-WaitForEnvVarMatchInRange=Name;Timeout;LowerLimit;UpperLimit
	WaitForSignalMatchInRange
		-WaitForSignalMatchInRange=Name;Timeout;LowerLimit;UpperLimit
	ServiceSignal
		-ServiceSignal=Name;Condition


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Name| String| NA| Xyz| To specify the name of the input parameter. | 
| Condition| String| NA| >=10X<15| Compare value for string
EQ ==
EGT ==+/-
NE !=
GT >
LT <
GE >=
LE <=
GTLT > <
GELE >= <=
GELT >= <
GTLE > <=
LTGT < >
LEGE <= >=
LEGT <= >
LTGE < >=| 
| CompareValue| String| NA| 10| Value which is compared to the signal/variable value in CANoe.| Required
| Timeout| String| NA| 1000| Maximum time that should be waited [ms].| Required
| LowerLimit| String| NA| 0.0|  Lower Limit value for Compare in Range| Required
| UpperLimit| String| NA| 10.0|  Upper Limit value for Compare in Range| Required

## Check_Diagnostic

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Check_Diagnostic.seq*


**Descriptions:** *This keyword is developed initially for bmw icam3 project where they will using this in pair of "Set_Diagnostic"

This command is used to check the respose of "Set_Diagnostic". This command will not make any communication to Vector Canoe.  But instead this command will check the response stored by "Set_Diagnostic". *


***Command Set:***

	Simple
		-Simple=Response
	Bytes
		-Bytes=Response;StartIndex;Length
	Search
		-Search=Response;Condition
	Bit
		-Bit=Response;StartIndex
	ASCII-Lookup
		-ASCII-Lookup=StartIndex;Lookup
	BytesMatch
		-BytesMatch=ResponseByte;Position;Status


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Response| String| NA| 00|  This input Parameter accepts value in below example formats. 
Example 1 - 00011234
Exampe 2 - 00 01 12 34| 
| StartIndex| String| NA| 0|  Index to start the comparison| 
| Length| String| NA| X==10|  Length of the overall return from canoe, to bypass this pass "<IGNORE>"

Example Valid Syntax  
"X==10"
"X>=15"| 
| Condition| Ring| AllowRTval=False,Present,NotPresent| Present|  used for the search keyword| 
| Lookup| String| NA| Serial|  Lookup string as a reference to serrach in file| Required
| ResponseByte| String| NA| 05/06/08|  This parameter has four different formats:

1. 01/02/05/06
2. 01-08
3.  05/09-0A
4. 02-08/A0| Required
| Position| String| NA| 0| Provide position of the byte which you want to compare.
Note: position(index) starts from 0.

You can provide a range of value example 0-90.| Required
| Status| Bool| NA| True|  Match Status should be either True/False.
True == Match
False == NotMatch| Required

## Check_DTC

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Check_DTC.seq*


**Descriptions:** *This Keyword is used to check the DTC status*


***Command Set:***

	Status
		-Status=Code;Status;QualifierName
	StatusMask
		-StatusMask=Code;StatusMask;QualifierName
	ServerActive
		-ServerActive=
	ServerInActive
		-ServerInActive=
	Count
		-Count=Count
	CountMask
		-CountMask=StatusMask;Count


***Input Description***



## Check_UART

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Check_UART.seq*


**Descriptions:** *This Keyword is used to check UART commands and UART status.*


***Command Set:***

	Raw
		-Raw=Alias;Command;Response
	Status
		-Status=Alias


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Alias| Ring| AllowRTval=False,UART1,UART2,UART3,UART4| | Specify the Alias of the UART device, value obtained for NI Max 
| Command| String| NA| \/\/\/\/\/| Specify the UART command.
| Response| String| NA| Xyz|  Specify the expected UART response

## Check_ULS

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Check_ULS.seq*


**Descriptions:** *This Keyword check whether the ULS Sensor is acitve or not*


***Command Set:***

	Active
		-Active=


***Input Description***



## Read_CANoe

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Read_CANoe.seq*


**Descriptions:** **


***Command Set:***

	SysVar
		-SysVar=Name;Condition
	EnvVar
		-EnvVar=Name;Condition
	Signal
		-Signal=Name;Condition


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Name| String| NA| Xyz| To specify the name of the input parameter. | 
| Condition| String| NA| X==10|  Compare value for string
EQ ==
EGT ==+/-
NE !=
GT >
LT <
GE >=
LE <=
GTLT > <
GELE >= <=
GELT >= <
GTLE > <=
LTGT < >
LEGE <= >=
LEGT <= >
LTGE < >=| Required

## Serial_Log

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Serial_Log.seq*


**Descriptions:** *This Keyword is used to send and log Serial commands*


***Command Set:***

	Start
		-Start=Alias
	Send
		-Send=Alias;Command
	Stop
		-Stop=
	Check
		-Check=Alias;Command;Response
	Validate
		-Validate=Value;Prefix;Suffix;Header
	CheckLog
		-CheckLog=LogLookup


***Input Description***



## Set_CANoe

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Set_CANoe.seq*


**Descriptions:** *This keyword is developed initially for bmw icam3 project where they will using this for setting the sysvar of Canoe*


***Command Set:***

	SysVar
		-SysVar=Name;Value
	Start
		-Start=Source;WaitSeconds
	Stop
		-Stop=Source
	Signal
		-Signal=Name;Value
	EnvVar
		-EnvVar=Name;Value
	Open
		-Open=SimulationFilePath
	Close
		-Close=
	Macro
		-Macro=Name;Wait_for_finish
	ServiceSignal
		-ServiceSignal=Name;Value


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Name| String| NA| BasicSubnet|  Name of the System Variable| 
| Value| String| NA| 5| Value of system variable| 
| SimulationFilePath| String| NA| Default|  If the value is default the path is obtained from the config file. | 
| Wait_for_finish| Bool| NA| False| On True, the Macros will wait till completion| 
| Source| String| NA| Default|  This is used for Start, Stop, Open.| 
| WaitSeconds| String| NA| Xyz|  | Optional

## Set_Diagnostic

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Set_Diagnostic.seq*


**Descriptions:** *This keyword is developed initially for bmw icam3 project where they will using this for sending command via Vector Canoe. *


***Command Set:***

	Simple
		-Simple=TxHEX;TimeoutSec;QualifierName;Network
	DID
		-DID=Name
	Append
		-Append=TxHEX;AppendHex;AppendCount


***Input Description***



## Set_UART

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Set_UART.seq*


**Descriptions:** *This Keyword is used to send UART commands.*


***Command Set:***

	Command
		-Command=Alias;Command


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Alias| Ring| AllowRTval=False,UART1,UART2,UART3,UART4| | Specify the Alias of the UART device, value obtained for NI Max.
| Command| String| NA| \/\/\/\/\/|  Specify the UART command.

