from google.cloud import secretmanager
from google.cloud import bigquery, storage
from google.cloud import run_v2
from google.oauth2 import service_account
from google.auth import default
from google import auth
from configs.env import settings
import google.auth
from google.auth.transport import requests
import json
import pandas as pd
from datetime import datetime, timedelta
import os

services_project_id = settings.SERVICES_PROJECT_ID
services_bucket_name = settings.SERVICES_BUCKET_NAME
services_account_email = settings.SERVICES_ACCOUNT_EMAIL

# Function to create and return a BigQuery client instance
def get_bigquery_client():
    # credentials = service_account.Credentials.from_service_account_info(
    #     BIGQUERY_KEY_PATH,
    #     scopes=["https://www.googleapis.com/auth/cloud-platform"],
    # )
    client = bigquery.Client(project=services_project_id)
    return client

def get_storage_client():
    client = storage.Client(project=services_project_id)
    return client

def get_run_job_client():
    # Load credentials from the service account file
    credentials, project = default()

    # Create the JobsClient with credentials
    client = run_v2.JobsClient(credentials=credentials)
    return client


def clear_folder(file_path_to_clear):
    for filename in os.listdir(file_path_to_clear):
        file_path = os.path.join(file_path_to_clear, filename)
        try:
            if os.path.isfile(file_path):
                os.unlink(file_path)  # Delete the file
        except Exception as e:
            print(f"Error deleting {file_path}: {e}")

def setup_application():
    UPLOAD_DIR = "uploads"
    # requirements
    REQUIREMENTS_DIR = "requirements"

    YAML_DIR = '/agent_yaml'
    AGENT_DIR_SUBFOLDERS = ["final"]

    for folder in AGENT_DIR_SUBFOLDERS:
        folder_path = os.path.join(YAML_DIR, folder)
        os.makedirs(folder_path, exist_ok=True)

    # Ensure upload directory exists
    os.makedirs(UPLOAD_DIR, exist_ok=True)

    os.makedirs(REQUIREMENTS_DIR, exist_ok=True)

def process_file(filepath, selected_row_indices):
    try:
        if filepath.endswith('.csv'):
            df = pd.read_csv(filepath)
        elif filepath.endswith('.xlsx'):
            # Check available sheets
            excel_file = pd.ExcelFile(filepath)
            if not excel_file.sheet_names:
                raise ValueError("No worksheets found in the Excel file.")
            df = pd.read_excel(excel_file, sheet_name=excel_file.sheet_names[0])
        else:
            raise ValueError("Unsupported file format. Please upload a .csv or .xlsx file.")
    except Exception as e:
        print(f"Error reading file: {e}")
        return json.dumps([])  # Return empty JSON to prevent further failure

    df = df.where(pd.notna(df), "None")
    rows = df.to_dict(orient='records')

    try:
        selected_data = [rows[int(index)] for index in selected_row_indices]
    except IndexError as e:
        print(f"Index error: {e}")
        return json.dumps([])

    return json.dumps(selected_data, indent=4)

def upload_file_to_storage(bucket_name, file_path, destination_blob_name):
    """
    Uploads an NDJSON file to Google Cloud Storage and sets the file name as metadata.

    :param bucket_name: Name of the GCS bucket.
    :param file_path: Local path to the NDJSON file.
    :param destination_blob_name: Name of the file in GCS.
    """
    gcs_client = get_storage_client()
    bucket = gcs_client.bucket(bucket_name)
    blob = bucket.blob(destination_blob_name)
    blob.upload_from_filename(file_path)
    print(f"File {file_path} uploaded to gs://{bucket_name}/{destination_blob_name}")


def trigger_run_job(project_id=services_project_id, region="europe-west1", job_name="", environment_variables={}):

    run_client = get_run_job_client()

    # Define the full job name path
    job_path = run_client.job_path(project_id, region, job_name)

    # Convert dict to Cloud Run env var format
    env_list = [run_v2.EnvVar(name=key, value=value) for key, value in environment_variables.items()]

    # Override environment variables
    overrides = run_v2.RunJobRequest(
        name=job_path,
        overrides=run_v2.RunJobRequest.Overrides(
            container_overrides=[
                run_v2.RunJobRequest.Overrides.ContainerOverride(
                    env=env_list
                )
            ]
        )
    )

    # Trigger the Cloud Run job
    response = run_client.run_job(overrides)
    execution_id = response.metadata.name.split("/")[-1]
    print(execution_id)

    print(f"Cloud Run job '{job_name}' triggered successfully.")

    return execution_id

def bigquery_insert_query(execute_query):
    client = get_bigquery_client()
    
    print(execute_query)
    # Execute the query
    result = client.query(query=execute_query)
    print(result)

def generate_signed_url(file_path):
    SCOPES = ['https://www.googleapis.com/auth/devstorage.full_control',
          'https://www.googleapis.com/auth/cloud-platform']
    scoped_credentials, project = google.auth.default(scopes=SCOPES)
    storage_client = get_storage_client()
    bucket = storage_client.bucket(services_bucket_name)
    blob = bucket.blob(file_path)
    expiration_time = datetime.utcnow() + timedelta(hours=2)
    r = requests.Request()
    scoped_credentials.refresh(r)
    signed_url = blob.generate_signed_url(
        version="v4",
        expiration=expiration_time,
        service_account_email=services_account_email,
        access_token=scoped_credentials.token
    )
    return signed_url

# def generate_signed_url(file_path):
#     storage_client = get_storage_client()
#     if file_path:
#         bucket = storage_client.bucket(services_bucket_name)
#         blob = bucket.blob(file_path)
#         expiration_time = datetime.utcnow() + timedelta(hours=2)
#         signed_url = blob.generate_signed_url(expiration=expiration_time)
#         return signed_url
#     else:
#         return "No Result File"

def str_presenter(dumper, data):
    if "\n" in data:  # multiline
        return dumper.represent_scalar('tag:yaml.org,2002:str', data, style='>')
    return dumper.represent_scalar('tag:yaml.org,2002:str', data)
    
def replace_placeholders(data, replacements):
    """Recursively replace placeholders in a nested dict or list."""
    if isinstance(data, dict):
        return {key: replace_placeholders(value, replacements) for key, value in data.items()}
    elif isinstance(data, list):
        return [replace_placeholders(item, replacements) for item in data]
    elif isinstance(data, str):
        for placeholder, replacement in replacements.items():
            data = data.replace(f'{{{placeholder}}}', replacement)
        return data
    else:
        return data