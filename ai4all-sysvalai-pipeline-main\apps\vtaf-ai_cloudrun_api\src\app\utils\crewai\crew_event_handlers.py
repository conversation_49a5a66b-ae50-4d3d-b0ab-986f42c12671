import logging

from crewai.utilities.events import (
    crewai_event_bus,
    CrewKickoffStartedEvent,
    CrewKickoffCompletedEvent,
    CrewKickoffFailedEvent,
    AgentExecutionStartedEvent,
    AgentExecutionCompletedEvent,
    AgentExecutionErrorEvent,
    ToolUsageStartedEvent,
    ToolUsageFinishedEvent,
    ToolUsageErrorEvent,
)
from app.utils.langfuse.langfuse_context import (
    trace_var,
    top_generation_var,
    agent_span_var,
    tool_span_var,
)
from app.core.constants import VERTEX_AI_MODEL

logger = logging.getLogger("vtaf")

def register_crewai_event_handlers(langfuse):
    @crewai_event_bus.on(CrewKickoffStartedEvent)
    def on_crew_started(source, event):
        try:
            trace = langfuse.trace(
                name=event.inputs.get("crew_name", None),
                user_id=source.name,
                input=event.inputs,
                session_id=event.inputs.get("job_id", None) if event.inputs else None,
            )
            top_generation = trace.generation(
                name="Crew Execution",
                input=event.inputs,
                model=VERTEX_AI_MODEL,
                metadata={"crew_name": event.crew_name},
            )
            trace_var.set(trace)
            top_generation_var.set(top_generation)
        except Exception as e:
            logger.error(f"Langfuse error on CrewKickoffStartedEvent: {e}")

    @crewai_event_bus.on(AgentExecutionStartedEvent)
    def on_agent_started(source, event):
        try:
            trace = trace_var.get()
            top_generation = top_generation_var.get()
        except LookupError:
            logger.error("Missing context for trace or generation")
            return
        try:
            agent_work = f"T: [{event.task.name}]" if event.task.name else f"Tool: [{event.tools[0].name}]"
            agent_span = langfuse.span(
                name=f"A: [{event.agent.role}] => {agent_work}",
                input=event.task_prompt,
                parent_observation_id=top_generation.id,
                trace_id=trace.id,
                metadata={
                    "agent_id": event.agent.id,
                    "task_id": event.task.id,
                    "task_name": event.task.name,
                    "agent_role": (event.agent.role or "").strip(),
                    "agent_goal": (event.agent.goal or "").strip(),
                    "agent_backstory": (event.agent.backstory or "").strip(),
                },
            )
            agent_span_var.set(agent_span)
        except Exception as e:
            logger.error(f"Langfuse error on AgentExecutionStartedEvent: {e}")

    @crewai_event_bus.on(AgentExecutionCompletedEvent)
    def on_agent_completed(source, event):
        try:
            agent_span = agent_span_var.get()
            agent_span.end(output=event.output or "")
        except Exception as e:
            logger.error(f"Langfuse error on AgentExecutionCompletedEvent: {e}")

    @crewai_event_bus.on(CrewKickoffCompletedEvent)
    def on_crew_completed(source, event):
        try:
            trace = trace_var.get()
            top_generation = top_generation_var.get()
            usage = {
                "input": source.usage_metrics.prompt_tokens,
                "output": source.usage_metrics.completion_tokens,
                "cache_read_input_tokens": source.usage_metrics.cached_prompt_tokens,
                "total": source.usage_metrics.total_tokens,
            }
            top_generation.end(
                output=event.output.raw, usage_details=usage
            )
            trace.update(output=event.output.raw)
        except Exception as e:
            logger.error(
                f"Langfuse error on CrewKickoffCompletedEvent (top_gen.end): {e}"
            )

    @crewai_event_bus.on(ToolUsageStartedEvent)
    def on_tool_started(source, event):
        try:
            agent_span = agent_span_var.get()
            trace = trace_var.get()
            tool_span = langfuse.span(
                name=f"{event.tool_name}",
                input=event.tool_args,
                parent_observation_id=agent_span.id,
                trace_id=trace.id,
            )
            tool_span_var.set(tool_span)
        except Exception as e:
            logger.error(f"Langfuse error on ToolUsageStartedEvent: {e}")

    @crewai_event_bus.on(ToolUsageFinishedEvent)
    def on_tool_completed(source, event):
        try:
            tool_span = tool_span_var.get()
            tool_span.end(output=event.output)
        except Exception as e:
            logger.error(f"Langfuse error on ToolUsageFinishedEvent: {e}")


    @crewai_event_bus.on(ToolUsageErrorEvent)
    def on_tool_error(source, event):
        try:
            tool_span = tool_span_var.get()
            tool_span.end(output=event.error, level="ERROR")
        except Exception as e:
            logger.error(f"Langfuse error on ToolUsageErrorEvent: {e}")

    @crewai_event_bus.on(AgentExecutionErrorEvent)
    def on_agent_error(source, event):
        try:
            agent_span = agent_span_var.get()
            agent_span.end(output=event.error, level="ERROR")
        except Exception as e:
            logger.error(f"Langfuse error on AgentExecutionErrorEvent: {e}")
        

    @crewai_event_bus.on(CrewKickoffFailedEvent)
    def on_crew_failed(source, event):
        try:
            top_generation = top_generation_var.get()
            top_generation.end(error=event.error, level="ERROR")
        except Exception as e:
            logger.error(f"Langfuse error on CrewKickoffFailedEvent: {e}")
