from collections.abc import Mapping
from typing import Any, <PERSON>V<PERSON>, Union, cast

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..types import UNSET, Unset

T = TypeVar("T", bound="ParseFileRequest")


@_attrs_define
class ParseFileRequest:
    """Request schema for parsing a file using the parsing API.
    Includes base64-encoded file content, parser selection, and output format options.

        Attributes:
            file_name (Union[Unset, str]): Name of the file to be parsed. Default: 'test/filepath.pdf'.
            file_content (Union[Unset, str]): Base64 encoded content of the file. Default:
                'base64_encoded_content_of_the_file'.
            parser (Union[None, Unset, str]): Parser to use. Defaults to 'auto'. Default: 'auto'.
            output_format (Union[Unset, list[str]]): List of output formats to generate. E.g., 'text', 'images', 'tables'.
    """

    file_name: Union[Unset, str] = "test/filepath.pdf"
    file_content: Union[Unset, str] = "base64_encoded_content_of_the_file"
    parser: Union[None, Unset, str] = "auto"
    output_format: Union[Unset, list[str]] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        file_name = self.file_name

        file_content = self.file_content

        parser: Union[None, Unset, str]
        if isinstance(self.parser, Unset):
            parser = UNSET
        else:
            parser = self.parser

        output_format: Union[Unset, list[str]] = UNSET
        if not isinstance(self.output_format, Unset):
            output_format = self.output_format

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update({})
        if file_name is not UNSET:
            field_dict["file_name"] = file_name
        if file_content is not UNSET:
            field_dict["file_content"] = file_content
        if parser is not UNSET:
            field_dict["parser"] = parser
        if output_format is not UNSET:
            field_dict["output_format"] = output_format

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        d = dict(src_dict)
        file_name = d.pop("file_name", UNSET)

        file_content = d.pop("file_content", UNSET)

        def _parse_parser(data: object) -> Union[None, Unset, str]:
            if data is None:
                return data
            if isinstance(data, Unset):
                return data
            return cast(Union[None, Unset, str], data)

        parser = _parse_parser(d.pop("parser", UNSET))

        output_format = cast(list[str], d.pop("output_format", UNSET))

        parse_file_request = cls(
            file_name=file_name,
            file_content=file_content,
            parser=parser,
            output_format=output_format,
        )

        parse_file_request.additional_properties = d
        return parse_file_request

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
