# generation_requests.py

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

class ContentPart(BaseModel):
    type: str
    data: str
    mimetype: Optional[str] = None
    filename: Optional[str] = None

class MessageContent(BaseModel):
    role: str
    parts: List[ContentPart]

class GenerationConfig(BaseModel):
    temperature: float
    top_k: int
    top_p: float
    maximum_output_tokens: int
    candidates_count: int
    stop_sequences: List[str]

class GenerationRequest(BaseModel):
    model_name: str
    contents: List[MessageContent]
    stream: Optional[bool] = Field(default_factory=bool)
    system_instruction: Optional[str] = Field(default_factory=str)
    generation_config: Optional[GenerationConfig] = Field(default_factory=dict)
    tools_to_use: Optional[List[str]] = Field(default_factory=list)
    collections_names: Optional[List[str]] = Field(default_factory=list)
    rag_config: Optional[Dict[str, Any]] = Field(default_factory=dict)
