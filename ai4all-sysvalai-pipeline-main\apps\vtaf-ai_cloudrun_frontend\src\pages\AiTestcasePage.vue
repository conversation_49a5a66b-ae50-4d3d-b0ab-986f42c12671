<template><router-view></router-view></template>

<script setup>
// import BgCard from "src/components/AI_Testcase/BgCard.vue";
import { storeToRefs } from "pinia";
import { useHomeStore } from "src/stores/home/<USER>";
import { onMounted } from "vue";

const home_store = useHomeStore();
// const { main_email, first_letter_email } = storeToRefs(home_store);
const { get_user_email } = home_store;

onMounted(async () => {
  await get_user_email();
});
</script>
