#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Basic Submit Service - 基础提交任务处理器
对应原始ai_generate.py中的display()方法
处理单个提示词的提交和响应
"""

import os
from typing import Dict, Any, Optional
from utils.logger import get_logger
from services.ai_service import AIService
from services.config_service import ConfigurationService

logger = get_logger(__name__)

class BasicSubmitService:
    """基础提交服务 - 处理单个提示词提交"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.config_service = ConfigurationService()
        
    def execute(self) -> int:
        """执行基础提交任务"""
        try:
            logger.info("🚀 Starting basic submit job")
            
            # 获取提示词
            prompt = self._get_prompt()
            if not prompt:
                logger.error("No prompt provided")
                return 1
            
            logger.info(f"Processing prompt: {prompt[:100]}...")
            
            # 生成响应
            response = self.ai_service.generate_response(prompt)
            
            if response.startswith("Error"):
                logger.error(f"AI service error: {response}")
                return 1
            
            # 保存结果（如果需要）
            self._save_result(prompt, response)
            
            logger.info("✅ Basic submit job completed successfully")
            return 0
            
        except Exception as e:
            logger.exception(f"Basic submit job failed: {e}")
            return 1
    
    def _get_prompt(self) -> Optional[str]:
        """获取提示词 - 从环境变量或配置文件"""
        # 1. 从环境变量获取
        prompt = os.getenv('PROMPT_TEXT')
        if prompt:
            logger.info("Prompt loaded from environment variable")
            return prompt
        
        # 2. 从配置文件获取
        try:
            config = self.config_service.get_sheet_config_for_batch_task()
            if 'prompt_text' in config:
                logger.info("Prompt loaded from config file")
                return config['prompt_text']
        except Exception as e:
            logger.warning(f"Failed to load prompt from config: {e}")
        
        # 3. 使用默认测试提示词
        default_prompt = "Hello, this is a test prompt for basic submit functionality."
        logger.info("Using default test prompt")
        return default_prompt
    
    def _save_result(self, prompt: str, response: str):
        """保存结果到日志或存储"""
        try:
            # 记录到日志
            logger.info("=" * 50)
            logger.info("BASIC SUBMIT RESULT")
            logger.info("=" * 50)
            logger.info(f"Prompt: {prompt}")
            logger.info(f"Response length: {len(response)} characters")
            logger.info(f"Response: {response[:500]}{'...' if len(response) > 500 else ''}")
            logger.info("=" * 50)
            
            # 如果配置了输出文件，保存到文件
            output_file = os.getenv('OUTPUT_FILE')
            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(f"Prompt: {prompt}\n\n")
                    f.write(f"Response: {response}\n")
                logger.info(f"Result saved to file: {output_file}")
                
        except Exception as e:
            logger.warning(f"Failed to save result: {e}")
