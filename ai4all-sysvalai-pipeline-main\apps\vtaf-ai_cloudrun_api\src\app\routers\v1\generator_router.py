"""
generator.py

Defines API endpoints for generations-related operations, including health check,
fetching supported models, and rag generation. Relies on token-based
authorization and external service communication.
"""

import httpx
import logging
import fastapi

from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import ORJSONResponse

from app.core.constants import GENERATIONS_URL
from app.utils.http_retry import fetch_with_retry
from app.dependencies.ai4all_auth import get_id_token
from app.dependencies.http_client_session import get_ai4all_client
from app.routers.v1.schemas.generation_requests import GenerationRequest

logger = logging.getLogger("vtaf")
generations_router = APIRouter(prefix="/generations")


@generations_router.get(
    "/health",
    response_class=ORJSONResponse,
    description="Health check endpoint for the generations service",
    summary="Health Check API",
    status_code=fastapi.status.HTTP_200_OK,
    operation_id="generator_health_check"
)
async def generator_health_chunk(client=Depends(get_ai4all_client)) -> dict:
    """
    Health check endpoint that forwards the request to the external parsing service.
    """
    logger.info("Generations Health check request received")

    try:
        response = await fetch_with_retry(f"{GENERATIONS_URL}/health", client=client)
    except Exception as exc:
        logger.error(f"Parsing health check failed: {exc}")
        raise HTTPException(status_code=502, detail=f"Health check failed: {exc}")

    return response.json()


@generations_router.get(
    "/supported-models",
    response_class=ORJSONResponse,
    description="Supported models for the generations service ",
    summary="Supported GenAI models API",
    status_code=fastapi.status.HTTP_200_OK,
)
async def get_supported_models(id_token: str = Depends(get_id_token), client=Depends(get_ai4all_client)):
    """
    Retrieves the list of supported generative AI models from the generations service.
    """
    headers = {"Authorization": f"Bearer {id_token}"}

    try:
        response = await client.get(
            f"{GENERATIONS_URL}/v1alpha2/supported-models",
            headers=headers,
            timeout=10,
        )
    except httpx.RequestError as exc:
        logger.error(f"Generations supported models request failed: {exc}")
        raise HTTPException(status_code=502, detail=f"Network error: {exc}") from exc

    if response.status_code != 200:
        logger.error(f"External API returned status {response.status_code}")
        raise HTTPException(status_code=500, detail="Failed to fetch supported models")

    return response.json()


@generations_router.post(
    "/rag",
    response_class=ORJSONResponse,
    description="Performs Retrieval Augmented Generation (RAG) using the generations service",
    summary="Retrieval Augmented Generation (RAG) API",
    status_code=fastapi.status.HTTP_200_OK,
)
async def rag(
    request: GenerationRequest, id_token: str = Depends(get_id_token), client=Depends(get_ai4all_client)
):
    """
    Sends a RAG request to the external generations service.
    """
    headers = {"Authorization": f"Bearer {id_token}"}

    try:
        response = await client.post(
            f"{GENERATIONS_URL}/v1alpha2",
            headers=headers,
            json=request.model_dump(),
            timeout=10,
        )
    except httpx.RequestError as exc:
        logger.error(f" Rag request failed: {exc}")
        raise HTTPException(status_code=502, detail=f"Network error: {exc}") from exc

    if response.status_code != 200:
        logger.error(f"External API returned status {response.status_code}")
        raise HTTPException(
            status_code=response.status_code,
            detail=response.json().get("detail", "Parsing failed"),
        )

    return response.json()
