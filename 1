@startuml
title Tbox DTC Scenarios - AE0016 - Detailed Test Cases

' Common Style
skinparam {
  rectangle {
    roundCorner 15
    shadowing true
    backgroundColor #EEEEFF
    borderColor #778899
  }
  arrowColor #778899
  defaultFontName Arial
}

' Function to create scenarios
!function scenario(testCaseId, voltage, time, gade, expectedResult)
rectangle testCaseId {
  object "Battery Voltage" as voltageId {
    Value: voltage
  }
  object "Duration" as durationId {
    Value: time
  }
  object "GADE Value" as gadeId {
    Value: gade
  }
  object "DTC Reported" as dtcId {
    Value: expectedResult
  }

  voltageId -[hidden]-> durationId
  durationId -[hidden]-> gadeId
  gadeId -[hidden]-> dtcId

  note top of voltageId: Voltage
  note top of durationId: Time (seconds)
  note top of gadeId: GADE (>=0)
  note top of dtcId: Expected Result
}
!endfunction

' Define Scenarios
scenario("TC1: Voltage Below, Time Above", "5.9V", "1.1s", ">=0", "Reported")
scenario("TC2: Voltage Above, Time Above", "6.6V", "1.1s", "Any", "NOT Reported")
scenario("TC3: Voltage Below, Time Below", "5.9V", "0.9s", "Any", "NOT Reported")
scenario("TC4: Voltage Within Tolerance, Time Within Tolerance", "6.2V", "1.0s", ">=0", "Reported")
scenario("TC5: Voltage Upper Tolerance, Time Below", "6.4V", "0.9s", "Any", "NOT Reported")
scenario("TC6: Voltage Lower Tolerance, Time Above", "6.0V", "1.1s", ">=0", "Reported")
scenario("TC7: Voltage Boundary 6.5V, Time 1.0s", "6.5V", "1.0s", "Any", "NOT Reported")
scenario("TC8: Voltage 6.0V, Time 1.0s", "6.0V", "1.0s", ">=0", "Reported")
scenario("TC9: Voltage 7.0V, Time 1.0s", "7.0V", "1.0s", "Any", "NOT Reported")
scenario("TC10: Voltage 6.5V, Time 0.9s", "6.5V", "0.9s", "Any", "NOT Reported")
scenario("TC11: Voltage 6.5V, Time 1.1s", "6.5V", "1.1s", "Any", "NOT Reported")
scenario("TC12: GADE Value Check", "6.0V", "1.1s", ">=0", "Reported")
scenario("TC13: GADE Value Check", "6.0V", "1.1s", "<0", "NOT Reported")

@enduml
