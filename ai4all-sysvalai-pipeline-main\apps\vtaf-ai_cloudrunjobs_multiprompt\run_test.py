#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的测试运行脚本
用于测试日志管理功能集成
"""

import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

def test_log_save():
    """测试日志保存功能"""
    print("🧪 测试日志保存功能")
    print("=" * 50)
    
    try:
        # 设置环境变量
        os.environ['JOB_TYPE'] = 'log_save'
        os.environ['RUN_MODE'] = 'production'
        
        # 设置要保存的日志数据
        import json
        from datetime import datetime
        
        sample_data = {
            "conversation_id": f"test_conv_{int(datetime.now().timestamp())}",
            "session_start": datetime.now().isoformat(),
            "conversations": [
                {
                    "user": "你好，这是一个测试对话",
                    "assistant": "你好！我是AI助手，很高兴为你服务。这是一个测试回复。",
                    "timestamp": datetime.now().isoformat()
                }
            ],
            "metadata": {
                "test_mode": True,
                "source": "run_test.py"
            }
        }
        
        os.environ['LOG_DATA'] = json.dumps(sample_data, ensure_ascii=False, indent=2)
        
        # 导入并执行
        from services.job_router import JobRouter
        router = JobRouter()
        result = router.route_job()
        
        if result == 0:
            print("✅ 日志保存测试成功！")
        else:
            print("❌ 日志保存测试失败！")
            
        return result == 0
        
    except Exception as e:
        print(f"❌ 日志保存测试异常: {e}")
        return False
    finally:
        # 清理环境变量
        for key in ['JOB_TYPE', 'LOG_DATA']:
            if key in os.environ:
                del os.environ[key]

def test_log_list():
    """测试日志列表功能"""
    print("\n🧪 测试日志列表功能")
    print("=" * 50)
    
    try:
        # 设置环境变量
        os.environ['JOB_TYPE'] = 'log_list'
        os.environ['RUN_MODE'] = 'production'
        
        # 导入并执行
        from services.job_router import JobRouter
        router = JobRouter()
        result = router.route_job()
        
        if result == 0:
            print("✅ 日志列表测试成功！")
        else:
            print("❌ 日志列表测试失败！")
            
        return result == 0
        
    except Exception as e:
        print(f"❌ 日志列表测试异常: {e}")
        return False
    finally:
        # 清理环境变量
        if 'JOB_TYPE' in os.environ:
            del os.environ['JOB_TYPE']

def test_log_load():
    """测试日志加载功能"""
    print("\n🧪 测试日志加载功能")
    print("=" * 50)
    
    try:
        # 设置环境变量
        os.environ['JOB_TYPE'] = 'log_load'
        os.environ['RUN_MODE'] = 'production'
        
        # 导入并执行
        from services.job_router import JobRouter
        router = JobRouter()
        result = router.route_job()
        
        if result == 0:
            print("✅ 日志加载测试成功！")
        else:
            print("❌ 日志加载测试失败！")
            
        return result == 0
        
    except Exception as e:
        print(f"❌ 日志加载测试异常: {e}")
        return False
    finally:
        # 清理环境变量
        if 'JOB_TYPE' in os.environ:
            del os.environ['JOB_TYPE']

def test_main_function():
    """测试通过main函数执行日志功能"""
    print("\n🧪 测试通过main函数执行日志功能")
    print("=" * 50)
    
    try:
        # 设置环境变量
        os.environ['EXECUTE_LOG_FUNCTIONS'] = 'true'
        os.environ['SKIP_CONVERSATION_TEST'] = 'true'
        os.environ['EXECUTE_DEFAULT_BUTTONS'] = 'false'
        os.environ['RUN_MODE'] = 'production'
        
        # 导入并执行
        from main import main
        result = main()
        
        if result == 0:
            print("✅ main函数日志功能测试成功！")
        else:
            print("❌ main函数日志功能测试失败！")
            
        return result == 0
        
    except Exception as e:
        print(f"❌ main函数测试异常: {e}")
        return False
    finally:
        # 清理环境变量
        for key in ['EXECUTE_LOG_FUNCTIONS', 'SKIP_CONVERSATION_TEST', 'EXECUTE_DEFAULT_BUTTONS', 'RUN_MODE']:
            if key in os.environ:
                del os.environ[key]

def main():
    """主测试函数"""
    print("🚀 开始日志管理功能简单测试")
    print("=" * 80)
    
    tests = [
        ("日志保存", test_log_save),
        ("日志列表", test_log_list),
        ("日志加载", test_log_load),
        ("main函数集成", test_main_function),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔧 执行测试: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    # 统计结果
    print("\n" + "="*80)
    print("📊 测试结果汇总")
    print("="*80)
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            success_count += 1
    
    total_tests = len(results)
    success_rate = (success_count / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"\n总计: {success_count}/{total_tests} 测试通过")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_count == total_tests:
        print("\n🎉 所有测试通过！日志管理功能集成成功！")
    else:
        print(f"\n⚠️ 有 {total_tests - success_count} 个测试失败，请检查配置")

if __name__ == '__main__':
    main()
