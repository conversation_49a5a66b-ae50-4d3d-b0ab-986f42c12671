from typing import Optional, List
from services.gemini_client import GeminiClient
from services.deepseek_client import DeepSeekClient
from configs.env import settings
from utils.logger import get_logger

logger = get_logger(__name__)

# 支持的模型列表
GEMINI_MODELS = [
    "gemini-1.5-pro-002",
    "gemini-2.5-pro-preview-05-06",
    "gemini-2.5-flash-preview-05-20",
    "gemini-2.0-flash-001",
    "gemini-2.0-flash-lite",
    "gemini-1.5-flash-002",
]

class AIService:
    def __init__(self):
        self.current_model = settings.DEFAULT_MODEL
        self.gemini_client = GeminiClient()
        self.deepseek_client = DeepSeekClient()
        self.system_instruction = None

    def generate_response(self, prompt: str, model: Optional[str] = None) -> str:
        """生成AI响应"""
        try:
            # 使用指定模型或当前模型
            target_model = model or self.current_model

            # 根据模型类型选择客户端
            if self._is_gemini_model(target_model):
                return self._generate_gemini_response(prompt, target_model)
            elif self._is_deepseek_model(target_model):
                return self._generate_deepseek_response(prompt, target_model)
            else:
                logger.warning(f"Unknown model: {target_model}, using default Gemini")
                return self._generate_gemini_response(prompt, settings.DEFAULT_MODEL)

        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            return f"Error generating response: {str(e)}"

    def _is_gemini_model(self, model: str) -> bool:
        """检查是否为Gemini模型"""
        return model in GEMINI_MODELS or model.startswith("gemini")

    def _is_deepseek_model(self, model: str) -> bool:
        """检查是否为DeepSeek模型"""
        return model in self.deepseek_client.model_list or model.startswith("deepseek")

    def _generate_gemini_response(self, prompt: str, model: str) -> str:
        """使用Gemini生成响应"""
        try:
            # 如果模型不同，切换模型
            if model != self.gemini_client.current_model:
                self.gemini_client.change_model(model, self.system_instruction)

            return self.gemini_client.get_response(prompt)
        except Exception as e:
            logger.error(f"Gemini response error: {e}")
            return f"Gemini error: {str(e)}"

    def _generate_deepseek_response(self, prompt: str, model: str) -> str:
        """使用DeepSeek生成响应"""
        try:
            return self.deepseek_client.get_response(model, prompt)
        except Exception as e:
            logger.error(f"DeepSeek response error: {e}")
            return f"DeepSeek error: {str(e)}"

    def change_model(self, model: str, system_instruction: Optional[str] = None):
        """切换模型"""
        self.current_model = model
        if system_instruction:
            self.system_instruction = system_instruction

        logger.info(f"Model changed to: {model}")

    def update_system_instruction(self, system_instruction: str):
        """更新系统指令"""
        self.system_instruction = system_instruction

        # 更新当前客户端的系统指令
        if self._is_gemini_model(self.current_model):
            # Gemini需要重新加载模型来更新系统指令
            self.gemini_client.change_model(self.current_model, system_instruction)
        elif self._is_deepseek_model(self.current_model):
            self.deepseek_client.update_system_instruction(system_instruction)

        logger.info("System instruction updated")

    def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        models = GEMINI_MODELS.copy()
        models.extend(self.deepseek_client.model_list)
        return models

    def count_tokens(self, text: str) -> int:
        """计算token数量（仅Gemini支持）"""
        try:
            if self._is_gemini_model(self.current_model):
                return self.gemini_client.model.count_tokens(text).total_tokens
            else:
                # DeepSeek暂不支持token计数，返回估算值
                return len(text.split()) * 1.3  # 粗略估算
        except Exception as e:
            logger.error(f"Token counting error: {e}")
            return 0