name: Release Documentation [DEV]

on:
  pull_request:
    branches: 
      - main  
  workflow_dispatch:
    inputs:
      pr_number:
        description: "Pull Request Number"
        required: true
        type: string

permissions:
  contents: write

jobs:
  build:
    name: Deploy Documentation
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Ensure full history is available
          github-server-url: 'https://github-ri.vnet.valeo.com'

      # - name: Fetch PR Branch
      #   run: |
      #     git fetch origin pull/${{ github.event.inputs.pr_number || github.event.pull_request.number }}/head:pr-${{ github.event.inputs.pr_number || github.event.pull_request.number }}
      #     git checkout pr-${{ github.event.inputs.pr_number || github.event.pull_request.number }}
      
      - name: Check and Print README.md
        run: |
          if [ -f README.md ]; then
            echo "README.md exists. Printing contents..."
            cat README.md
          else
            echo "README.md not found!"
          fi
         
      - name: Setup and Install Dependencies
        run: |
          python -m venv venv
          source venv/bin/activate
          pip install --upgrade uv
          uv pip install -r docs/requirements.txt

      - name: Configure Git Credentials
        run: |
          git config user.name github-actions[bot]
          git config user.email 41898282+github-actions[bot]@users.noreply.github.com
        
      - name: Release dev documentation
        run: |
          source venv/bin/activate
          mike deploy --push development
