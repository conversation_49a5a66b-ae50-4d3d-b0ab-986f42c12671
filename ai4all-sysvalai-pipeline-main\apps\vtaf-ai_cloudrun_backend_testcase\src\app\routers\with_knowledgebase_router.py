from fastapi import APIRouter, HTTPException, Form, UploadFile, File
from utilities import get_bigquery_client, process_file, replace_placeholders, upload_file_to_storage, trigger_run_job, bigquery_insert_query, str_presenter
from configs.env import settings
import datetime
import shutil
import traceback
import yaml
import json
import uuid
import os


router = APIRouter()
client = get_bigquery_client()

services_project_id = settings.SERVICES_PROJECT_ID
services_dataset_id = settings.SERVICES_DATASET_ID
services_bucket_name = settings.SERVICES_BUCKET_NAME
ai_testcase_job = settings.TESTCASE_GENERATION_JOB
UPLOAD_DIR = "uploads"

REQUIREMENTS_DIR = "requirements"
YAML_DIR = "/agent_yaml"

@router.get("/get_profile_projects")
def get_profile_projects():
    try:
        query = f"""
            SELECT * FROM `{services_project_id}.{services_dataset_id}.PROJECT_TABLE`;
        """
        # Run query
        query_job = client.query(query)
        results = query_job.result()

        projects = []
        for row in results:
            projects.append(row)
        sorted_data = sorted(projects, key=lambda x: x["project"])
        return sorted_data
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)

@router.get("/get_tree_variant_details/{project}")
def get_tree_variant_details(project: str):
    try:
        fetch_query = f"SELECT DISTINCT variant FROM `{services_project_id}.{services_dataset_id}.PROJECT_PROFILE_TABLE` WHERE PROJECT = '{project}'"
        query = client.query(fetch_query)
        results = query.result()

        variant_details = []
        for row in results:
            variant_details.append(row['variant'])
        variant_details.sort()
        return variant_details
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)


@router.get("/get_tree_euf_features_details/{variant_data}")
def get_tree_euf_features_details(variant_data: str):
    try:
        data = variant_data.split(",")
        fetch_query = f"SELECT DISTINCT euf_feature FROM `{services_project_id}.{services_dataset_id}.PROJECT_PROFILE_TABLE` WHERE project = '{data[0]}' AND variant = '{data[-1]}'"
        query = client.query(fetch_query)
        results = query.result()

        euf_features_details = []
        for row in results:
            euf_features_details.append(row["euf_feature"])
        euf_features_details.sort()
        return euf_features_details
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)

@router.post('/get_knowledgebase_profile')
def get_knowledgebase_profile(project:str = Form(...), variant:str = Form(...), euf_feature:str = Form(...)):
    try:
        fetch_query = f"""
            SELECT project, variant, euf_feature, project_uuid, variant_uuid, euf_feature_uuid,bg, pg, pl, sensors, algorithms, uuid FROM `{services_project_id}.{services_dataset_id}.PROJECT_PROFILE_TABLE` WHERE project = '{project}' AND variant = '{variant}' AND euf_feature = '{euf_feature}'
        """
        query = client.query(query=fetch_query)

        search_results = []

        for row in query.result():
            search_results.append(row)
        return search_results
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)

@router.post("/upload_file_knowledgebase")
def upload_file_knowledgebase(
    file: UploadFile = File(...),
    p_uuid: str = Form(...),
    v_uuid: str = Form(...),
    ef_uuid: str = Form(...),
    user_email: str = Form(...),
    selected_rows: str = Form(...),
    business_group: str = Form(...),
    product_group: str = Form(...),
    product_line: str = Form(...),
    features: str = Form(...),
    algorithms: str = Form(...),
    sensors: str = Form(...),
    project: str = Form(...),
    variant: str = Form(...),
    euf_feature: str = Form(...),
    agent_content: str = Form(...),
    task_content: str = Form(...),
    is_yaml_edited: str = Form(...),
):
    try:
        uid = str(uuid.uuid4())

        selected_rows_list = json.loads(selected_rows)

        # Create a unique subfolder inside UPLOAD_DIR for this request
        request_upload_dir = os.path.join(UPLOAD_DIR, uid)
        os.makedirs(request_upload_dir, exist_ok=True)

        # Similarly create unique folder for requirements
        request_requirements_dir = os.path.join(REQUIREMENTS_DIR, uid)
        os.makedirs(request_requirements_dir, exist_ok=True)

        # Save the uploaded file
        file_path = os.path.join(request_upload_dir, file.filename)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        result = process_file(file_path, selected_rows_list)
        result = json.loads(result)
        inputs = {
            "feature": str(features),
            "sensors": str(sensors),
            "algorithms": str(algorithms),
            "project_uuid": p_uuid,
            "variant_uuid": v_uuid,
            "euf_feature_uuid": ef_uuid,
            "requirements": result,
        }

        requirements_path = os.path.join(request_requirements_dir, uid + ".json")
        requirements_file_name = uid + ".json"
        with open(f"{requirements_path}", "w") as json_file:
            json.dump(inputs, json_file, indent=4)

        upload_file_to_storage(
            bucket_name=services_bucket_name,
            file_path=requirements_path,
            destination_blob_name=f"vtaf-ai/user_input/{requirements_file_name}",
        )
        upload_file_to_storage(
            bucket_name=services_bucket_name,
            file_path=f"{file_path}",
            destination_blob_name=f"vtaf-ai/user_uploads/{uid}_{file.filename}",
        )

        # Deserialize selected_rows from JSON string to list
        agent_content1 = json.loads(agent_content)
        task_content1 = json.loads(task_content)

        # Make sure the YAML_DIR and subfolders exist, or create a unique folder similarly if needed
        final_agent_yaml_path = os.path.join(YAML_DIR, "final", f"{uid}_agents.yml")
        final_task_yaml_path = os.path.join(YAML_DIR, "final", f"{uid}_tasks.yml")

        yaml.add_representer(str, str_presenter)
        with open(final_agent_yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(agent_content1, f, sort_keys=False)
        with open(final_task_yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(task_content1, f, sort_keys=False)
        # final_agent_yaml_path = YAML_DIR + '/final/' + uid + "_agents.yml"
        # final_task_yaml_path = YAML_DIR + '/final/' + uid + "_tasks.yml"

        upload_file_to_storage(
            bucket_name=services_bucket_name,
            file_path=final_agent_yaml_path,
            destination_blob_name="vtaf-ai/user_input/" + uid + "_agents.yml",
        )
        upload_file_to_storage(
            bucket_name=services_bucket_name,
            file_path=final_task_yaml_path,
            destination_blob_name="vtaf-ai/user_input/" + uid + "_tasks.yml",
        )

        env_vars = {
            "FILE_NAME": uid
        }

        execution_id = trigger_run_job(job_name=ai_testcase_job, environment_variables=env_vars)
        current_timestamp = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ")

        execute_query = f"""
            INSERT INTO `{services_project_id}.{services_dataset_id}.TC_JOB_HISTORY` (
                user_email,
                bg,
                pg,
                pl,
                timestamp,
                execution_id,
                user_upload,
                user_input,
                execution_status,
                project,
                project_variant,
                euf_feature,
                is_project,
                agent_yaml,
                task_yaml,
                is_yaml_edited
            ) 
            VALUES (
                '{user_email}',
                '{business_group}',
                '{product_group}',
                '{product_line}',
                '{current_timestamp}',
                '{execution_id}',
                '{services_bucket_name}/vtaf-ai/user_uploads/{uid}_{file.filename}',
                '{services_bucket_name}/vtaf-ai/user_input/{requirements_file_name}',
                'started',
                '{project}',
                '{variant}',
                '{euf_feature}',
                True,
                '{services_bucket_name}/vtaf-ai/user_input/{uid}_agents.yml',
                '{services_bucket_name}/vtaf-ai/user_input/{uid}_tasks.yml',
                {is_yaml_edited}

            );
            """
        bigquery_insert_query(execute_query)

        # clear_folder(UPLOAD_DIR)
        # clear_folder(REQUIREMENTS_DIR)
        # Clean up the request-specific upload and requirements folders
        shutil.rmtree(request_upload_dir, ignore_errors=True)
        shutil.rmtree(request_requirements_dir, ignore_errors=True)

        # Remove generated YAML files
        try:
            os.remove(final_agent_yaml_path)
        except Exception as e:
            print(f"Error deleting agent YAML file: {e}")

        try:
            os.remove(final_task_yaml_path)
        except Exception as e:
            print(f"Error deleting task YAML file: {e}")

        # Response with file name and selected rows
        return {"message": "Job is running", "execution_id": execution_id}
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)

@router.post("/get_yaml_content")
def get_yaml_content(functionality: str = Form(...), sensors: str = Form(...), algorithms: str = Form(...)):
    try:
        with open('/agent_yaml/initial/agents.yml', 'r') as f:
            agent_yaml_data = yaml.load(f, Loader=yaml.SafeLoader)
        with open('/agent_yaml/initial/tasks.yml', 'r') as f:
            task_yaml_data = yaml.load(f, Loader=yaml.SafeLoader)
        # Replace placeholders in task YAML
        task_yaml_data_replacements = {
            "topic": "Automotive",
            "feature": functionality,
            "sensors": sensors,
            "algorithms": algorithms
        }
        agent_yaml_data_replacements = {
            "topic": "Automotive"
        }

        processed_task_data = replace_placeholders(task_yaml_data, task_yaml_data_replacements)
        print(processed_task_data)
        processed_agent_data = replace_placeholders(agent_yaml_data, agent_yaml_data_replacements)
        print(processed_agent_data)

        response = {
            "agent_yaml" : processed_agent_data,
            "task_yaml" : processed_task_data
        } 
        return response
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)

