from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar, Union

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..types import UNSET, Unset

if TYPE_CHECKING:
    from ..models.chunk_string_request_parameters import ChunkStringRequestParameters


T = TypeVar("T", bound="ChunkStringRequest")


@_attrs_define
class ChunkStringRequest:
    """Request schema for chunking a string using the chunking API.
    Includes the string to chunk, strategy, and parameters.

        Attributes:
            string (Union[Unset, str]): String to be chunked. Default: 'string_to_chunk'.
            chunking_strategy (Union[Unset, str]): Chunking strategy to apply. Default: 'recursive-character-text-splitter'.
            parameters (Union[Unset, ChunkStringRequestParameters]): Optional parameters for the chunking strategy.
    """

    string: Union[Unset, str] = "string_to_chunk"
    chunking_strategy: Union[Unset, str] = "recursive-character-text-splitter"
    parameters: Union[Unset, "ChunkStringRequestParameters"] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        string = self.string

        chunking_strategy = self.chunking_strategy

        parameters: Union[Unset, dict[str, Any]] = UNSET
        if not isinstance(self.parameters, Unset):
            parameters = self.parameters.to_dict()

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update({})
        if string is not UNSET:
            field_dict["string"] = string
        if chunking_strategy is not UNSET:
            field_dict["chunking_strategy"] = chunking_strategy
        if parameters is not UNSET:
            field_dict["parameters"] = parameters

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.chunk_string_request_parameters import ChunkStringRequestParameters

        d = dict(src_dict)
        string = d.pop("string", UNSET)

        chunking_strategy = d.pop("chunking_strategy", UNSET)

        _parameters = d.pop("parameters", UNSET)
        parameters: Union[Unset, ChunkStringRequestParameters]
        if isinstance(_parameters, Unset):
            parameters = UNSET
        else:
            parameters = ChunkStringRequestParameters.from_dict(_parameters)

        chunk_string_request = cls(
            string=string,
            chunking_strategy=chunking_strategy,
            parameters=parameters,
        )

        chunk_string_request.additional_properties = d
        return chunk_string_request

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
