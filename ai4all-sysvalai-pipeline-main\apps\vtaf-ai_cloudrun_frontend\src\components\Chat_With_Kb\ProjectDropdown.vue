<template>
  <div class="q-ma-md">
    <q-select
      filled
      dense
      use-input
      input-debounce="0"
      v-model="selected_project_variant_uuid"
      outlined
      :options="filteredOptions"
      @filter="filterFn"
      @update:model-value="handleSelect"
      emit-value
      map-options
      clearable
      label="Select Project"
      :loading="projects_dropdown_loading"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useChatWithKnowledgebaseStore } from "src/stores/chat_with_kb/chat_with_knowledgebase_router";
import { storeToRefs } from "pinia";

const chat_with_kb_config = useChatWithKnowledgebaseStore();
const {
  projects_variants_dropdown,
  selected_project_variant,
  selected_project_variant_uuid,
  projects_dropdown_loading,
} = storeToRefs(chat_with_kb_config);
const { get_projects } = chat_with_kb_config;

// Turn { key: uuid } into dropdown options
const projectsOptions = computed(() =>
  Object.entries(projects_variants_dropdown.value).map(([label, value]) => ({
    label,
    value,
  }))
);

onMounted(async () => {
  await get_projects();
});

// Filtering support
const filterQuery = ref("");
const filteredOptions = computed(() =>
  projectsOptions.value.filter((option) =>
    option.label.toLowerCase().includes(filterQuery.value.toLowerCase())
  )
);
const filterFn = (val, update) => {
  filterQuery.value = val;
  update();
};

// Save both UUID and key when selected
const handleSelect = (uuid) => {
  selected_project_variant_uuid.value = uuid;
  const found = Object.entries(projects_variants_dropdown.value).find(
    ([key, val]) => val === uuid
  );
  selected_project_variant.value = found ? found[0] : null;
};
</script>
