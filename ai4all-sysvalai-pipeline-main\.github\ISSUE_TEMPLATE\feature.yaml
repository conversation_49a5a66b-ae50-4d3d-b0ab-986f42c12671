name: ✨ New Feature
description: Provide detailed specifications and track the progress of a new feature.
title: "[FEATURE] "
labels: ["feature"]

body:
  - type: markdown
    attributes:
      value: |
        Use this form to detail the implementation plan for a new feature. Please provide as much information as possible.

  - type: textarea
    id: feature-description
    attributes:
      label: Feature Description
      description: A brief overview of the feature and its purpose.
    validations:
      required: true

  - type: textarea
    id: feature-specification
    attributes:
      label: Feature Specification
      description: A detailed description of the feature's functionality and requirements.
    validations:
      required: true

  - type: textarea
    id: acceptance-criteria
    attributes:
      label: Acceptance Criteria
      description: List the specific criteria that must be met for the feature to be considered complete.
      placeholder: |
        - [ ] Item 1
        - [ ] Item 2
        - [ ] Item 3
    validations:
      required: true
  
  - type: dropdown
    id: feature-priority
    attributes:
      label: Feature Priority ⚡
      description: |
        Select the priority level for this feature.
        * **⚪ Not Defined:** I'm not sure of this info.
        * **🔴 P1:** Highest priority, must be addressed immediately.
        * **🟠 P2:** High priority, should be addressed soon.
        * **🟡 P3:** Medium priority, can wait for a while.
        * **🟢 P4:** Low priority, can be addressed later.
        * **🔵 P5:** Lowest priority, may not be addressed at all.
      options:
        - ⚪ Not Defined
        - 🔴 P1
        - 🟠 P2
        - 🟡 P3
        - 🟢 P4
        - 🔵 P5
    validations:
      required: true

  - type: dropdown
    id: feature-size
    attributes:
      label: Feature Size
      description: |
        Estimate the size of the feature.
        * **⚪ Not Defined:** I'm not sure of this info.
        * **🔵 XS:** Very small, <= 1 day. (2 Points)
        * **🟢 S** Small, ~ 2-3 days. (5 Points)
        * **🟡 M** Medium, <= 1 week. (10 Points)
        * **🟠 L** Large, ~ 7-8 days. (15 Points)
        * **🔴 XL:** Very large, <= 2 weeks. (20 Points)
      options:
        - ⚪ Not Defined
        - 🔵 XS (2 Points)
        - 🟢 S (5 Points)
        - 🟡 M (10 Points)
        - 🟠 L (15 Points)
        - 🔴 XL (20 Points)
    validations:
      required: true

  - type: textarea
    id: dependencies
    attributes:
      label: Dependencies
      description: List any other tasks or features that must be completed before this feature can be implemented.
      placeholder: |
        - #Dependency 1
        - #Dependency 2
        - #Dependency 3
    validations:
      required: false
      
  - type: textarea
    id: implementation-notes
    attributes:
      label: Implementation Notes (Optional)
      description: Additional notes, link or details relevant to the implementation process.