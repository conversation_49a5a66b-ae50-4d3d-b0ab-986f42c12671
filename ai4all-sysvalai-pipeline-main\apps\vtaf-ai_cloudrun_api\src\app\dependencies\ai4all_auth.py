"""
auth.py

Contains functionality for generating Google ID tokens using service account credentials.
The `generate_id_token` function is used to obtain a Google ID token for authentication
with specified services.
"""

import json
import httpx
import asyncio
import logging
import google.auth
import google.auth.transport.requests as google_requests

from datetime import datetime, timedelta, timezone
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from app.core.constants import (
    GOOGLE_CLOUD_SCOPES,
    AI4ALL_FRAMEWORK_CLIENT_ID,
    AI4ALL_SERVICE_ACCOUNT_EMAIL,
)


# Global token cache
_id_token_cache = None
ID_TOKEN_EXPIRY_BUFFER = 300  # 5 minutes
_id_token_lock = asyncio.Lock()
logger = logging.getLogger("vtaf")

async def generate_id_token() -> str:
    """
    Generates a Google ID token for the specified service account and audience.
    The ID token is used for authentication with the target service (e.g., AI4ALL framework).

    Raises:
        HTTPException: If credentials are missing or if token generation fails.
    """
    credentials, _ = google.auth.default(scopes=GOOGLE_CLOUD_SCOPES)

    # Refresh credentials to obtain a valid access token
    auth_request = google_requests.Request()
    await asyncio.to_thread(credentials.refresh, auth_request)
    access_token = credentials.token

    # Prepare the ID token request body
    id_token_request_body = {
        "audience": AI4ALL_FRAMEWORK_CLIENT_ID,
        "includeEmail": "true",
    }

    # Define the URL for generating the ID token
    id_token_url = (
        f"https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/"
        f"{AI4ALL_SERVICE_ACCOUNT_EMAIL}:generateIdToken"
    )

    try:
        # Make an asynchronous request to generate the ID token
        async with httpx.AsyncClient() as client:
            response = await client.post(
                id_token_url,
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                },
                data=json.dumps(id_token_request_body),
                timeout=10,
            )
    except Exception as e:
        logging.error(f"HTTP request failed: {e}")
        raise HTTPException(status_code=500, detail="Failed to contact IAM Credentials API")

    # Check if the response is successful
    if response.status_code != 200:
        raise HTTPException(status_code=401, detail="Failed to generate ID token")

    # Extract the ID token from the response
    id_token = response.json().get("token")
    if not id_token:
        raise HTTPException(status_code=401, detail="ID token not found in response")

    return id_token


async def get_id_token(force: bool = False) -> str:
    """
    Retrieves a Google ID token, refreshing it if expired or about to expire.
    
    Args:
        force (bool): If True, forces regeneration of the ID token.
    Returns:
        str: The Google ID token.
    """
    global _id_token_cache
    try:
        now = datetime.now(timezone.utc)
        
        # Fast path: return cached token if available, not forced, and not within buffer of expiry
        if (
            not force
            and _id_token_cache is not None
            and 'token' in _id_token_cache
            and 'expiry' in _id_token_cache
        ):
            expiry_with_buffer = _id_token_cache['expiry'] - timedelta(seconds=ID_TOKEN_EXPIRY_BUFFER)
            if now < expiry_with_buffer:
                return _id_token_cache['token']

        # Locking ensures only one coroutine can generate a new token at a time
        async with _id_token_lock:
            # Double check inside the lock
            if (
                not force
                and _id_token_cache is not None
                and 'token' in _id_token_cache
                and 'expiry' in _id_token_cache
            ):
                expiry_with_buffer = _id_token_cache['expiry'] - timedelta(seconds=ID_TOKEN_EXPIRY_BUFFER)
                if datetime.now(timezone.utc) < expiry_with_buffer:
                    return _id_token_cache['token']

            # Generate a new token
            token = await generate_id_token()
            # Set expiry to 1 hour from now (Google ID tokens are valid for 1 hour)
            expiry = datetime.now(timezone.utc) + timedelta(hours=1)
            _id_token_cache = {
                "token": token,
                "expiry": expiry,
            }
            return _id_token_cache['token']

    except Exception as e:
        logging.error(f"Error generating ID token: {e}")
        raise HTTPException(status_code=500, detail=str(e))