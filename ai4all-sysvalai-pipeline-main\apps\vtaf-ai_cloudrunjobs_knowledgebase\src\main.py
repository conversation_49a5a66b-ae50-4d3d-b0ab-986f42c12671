import asyncio
import datetime

from configs.env import settings
from services.bigquery_repository import <PERSON>QueryHandler
from services.dbc_parser_kb import load_dbc
from services.job_manager import JobManager
from services.pdf_parser_kb import run_job
from services.qdrant_storage import QdrantProcessor
from services.qdrant_token_manager import QdrantTokenManager
from utils.helper import save_file_with_status_update
from utils.logger import get_logger
from workflows.knowledge_flow import run_flow

logger = get_logger(__name__)
if __name__ == '__main__':
    start_timestamp = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
    token_usage = {
        "total_tokens": 0,
        "prompt_tokens": 0,
        "cached_prompt_tokens": 0,
        "completion_tokens": 0,
        "successful_requests": 0
    }
    bigquery_handler = BigQueryHandler()

    job_manager = JobManager(bigquery_handler, start_timestamp)

    try:

        qdrant_token_manager = QdrantTokenManager()
        qdrant_token = qdrant_token_manager.get_current_jwt()

        file_type = job_manager.get_file_type()
        project_detail = job_manager.get_project_details()
        qdrant_processor = QdrantProcessor(qdrant_token, project_detail)

        status, job_record = job_manager.poll_for_job_details()
        if status:
            if settings.INPUT_TYPE == 'Requirements' and (file_type == "csv" or file_type == "excel"):
                df, file_type = job_manager.read_file()
                job_manager.update_job_start()
                token_usage, status = asyncio.run(run_flow(qdrant_processor))
                gcs_blob_path = save_file_with_status_update(status, df)
                job_manager.update_job_complete(gcs_blob_path, token_usage)

            elif file_type == "dbc":
                job_manager.update_job_start()
                load_dbc_status = load_dbc(project_detail)
                if not load_dbc_status:
                    job_manager.update_job_complete(csv_bucket_path="NA", token_usage=token_usage)
                else:
                    job_manager.update_failed_status(start_timestamp, token_usage, load_dbc_status)

            else:
                job_manager.update_job_start()
                status = run_job(project_detail)
                job_manager.update_job_complete(csv_bucket_path="NA", token_usage=token_usage)

            status, job_record = job_manager.poll_for_job_details()
            job_manager.send_email(job_record, token_usage)
        else:
            logger.error(f"Job entry Not created")

    except Exception as e:
        logger.exception(f"Unexpected Exception: {str(e)}")
        job_manager.update_failed_status(start_timestamp, token_usage, f"Unexpected Exception: {str(e)}")
        status, job_record = job_manager.poll_for_job_details()
        job_manager.send_email(job_record, token_usage)
