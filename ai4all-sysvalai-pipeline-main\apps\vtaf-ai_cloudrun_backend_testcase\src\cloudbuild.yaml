steps:
- name: 'gcr.io/cloud-builders/docker'
  args: ['build', '-t', 'europe-west1-docker.pkg.dev/valeo-cp2508-dev/valeo-cp2508-vtaf-dev/ai-testcase', '.']
- name: 'gcr.io/cloud-builders/docker'
  args: ['push', 'europe-west1-docker.pkg.dev/valeo-cp2508-dev/valeo-cp2508-vtaf-dev/ai-testcase']
- name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
  entrypoint: gcloud
  args: ['run', 'deploy', 'ai-testcase', '--image', 'europe-west1-docker.pkg.dev/valeo-cp2508-dev/valeo-cp2508-vtaf-dev/ai-testcase', '--region', 'europe-west1', '--platform', 'managed', '--set-env-vars', 'ENVIRONMENT_PROJECT_ID=valeo-cp2508-dev,ENVIRONMENT_PROJECT_NUMBER=739631956186,SERVICES_PROJECT_ID=valeo-cp2673-acp,SERVICES_BUCKET_NAME=vtaf_ai_agents,ENVIRONMENT_SA_SECRET=cp2508-cloudrun,SERVICES_DATASET_ID=VTAF_AT']
options:
  logging: CLOUD_LOGGING_ONLY
