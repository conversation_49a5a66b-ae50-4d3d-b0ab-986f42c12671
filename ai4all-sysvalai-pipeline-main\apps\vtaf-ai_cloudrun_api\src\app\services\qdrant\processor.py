"""
processor.py

Defines the QdrantProcessor class used to interact with Qdrant for vector-based operations.
It uses VertexAI for embedding generation and Qdrant for vector storage and similarity search.

The processor provides methods to add documents and perform similarity search asynchronously.
"""

import logging
from uuid import uuid4

from langchain_core.documents import Document
from langchain_google_vertexai import VertexAIEmbeddings
from langchain_qdrant import QdrantVectorStore

from .client import create_qdrant_client
from app.core.constants import (
    VERTEX_EMBEDDING_MODEL,
    REGION,
    EMBEDDING_PARALLELISM,
)

logger = logging.getLogger("vtaf")


class QdrantProcessor:
    """
    Handles document embedding and vector operations using Qdrant and VertexAI.

    Attributes:
        collection_name (str): Name of the Qdrant collection to operate on.
        embedding (VertexAIEmbeddings): Embedding model instance.
        vector_store (QdrantVectorStore): Vector store for managing embeddings.
    """

    def __init__(self, collection_name: str):
        """
        Initializes a QdrantProcessor with the given collection name.

        Args:
            collection_name (str): The Qdrant collection to associate with this processor.
        """
        self.client = create_qdrant_client()
        self.collection_name = collection_name

        self.embedding = VertexAIEmbeddings(
            model_name=VERTEX_EMBEDDING_MODEL,
            location=REGION,
            request_parallelism=int(EMBEDDING_PARALLELISM),
        )

        self.vector_store = QdrantVectorStore(
            client=self.client,
            collection_name=self.collection_name,
            embedding=self.embedding,
        )

    async def add_document(self, content: str, metadata: dict):
        """
        Adds a document to the Qdrant vector store.

        Args:
            content (str): The textual content of the document.
            metadata (dict): Metadata associated with the document.

        Returns:
            The response from Qdrant after adding the document.
        """
        doc = Document(page_content=content, metadata=metadata, id=str(uuid4()))
        response = await self.vector_store.aadd_documents([doc])
        return response

    async def search(self, query: str, limit: int, score_threshold: float, filter=None):
        """
        Performs a similarity search on the vector store.

        Args:
            query (str): The search query string.
            limit (int): Maximum number of results to return.
            score_threshold (float): Minimum similarity score for results.
            filter (dict, optional): Metadata filter for search.

        Returns:
            A list of documents matching the search criteria.
        """
        results = await self.vector_store.asimilarity_search(
            query=query,
            k=limit,
            score_threshold=score_threshold,
            filter=filter,
        )
        return results
    
    def count_documents(self, filter=None):
        """
        Counts the number of documents in the collection, optionally filtered.

        Args:
            filter (Filter, optional): A Qdrant Filter object to apply.

        Returns:
            int: The number of documents matching the criteria.
        """
        
        results = self.client.count(
            collection_name=self.collection_name,
            count_filter=filter
        )
        return results
