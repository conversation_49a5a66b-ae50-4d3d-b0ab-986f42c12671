#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VTAF AI Multiprompt - Google Cloud Run 主入口 (重构版本)
支持多种部署方式：Google Cloud Run, Cloud Functions, 本地运行
支持多种任务类型，基于按钮功能分类的独立Cloud Run Jobs
"""

import os
import sys
import json
import time
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from configs.env import settings
from utils.logger import get_logger
from services.job_router import JobRouter
from services.ai_service import AIService
from services.batch_processor import BatchProcessor
from services.config_service import ConfigurationService

logger = get_logger(__name__)

def test_conversation_functionality():
    """测试对话功能"""
    logger.info("🧪 开始对话功能测试")
    logger.info("=" * 60)

    try:
        # 初始化 AI 服务
        logger.info("📝 初始化 AI 服务...")
        ai_service = AIService()
        logger.info("✅ AI 服务初始化成功")

        # 定义测试对话
        test_conversations = [
            {
                "name": "基础问候测试",
                "prompt": "你好！请回复'GCP对话测试成功'",
                "timeout": 30
            },
            {
                "name": "测试用例生成测试",
                "prompt": "请生成一个汽车功能测试用例，包含以下字段：Test Case ID, Test Objective, Test Condition, Test Action, Test Expectation",
                "timeout": 45
            },
            {
                "name": "简单计算测试",
                "prompt": "请计算 2+3 等于多少，并简短回答",
                "timeout": 20
            },
            {
                "name": "技术问题测试",
                "prompt": "请用一句话解释什么是 Vertex AI",
                "timeout": 30
            }
        ]

        logger.info(f"🔄 开始执行 {len(test_conversations)} 个对话测试...")

        results = []
        total_time = 0

        for i, test in enumerate(test_conversations, 1):
            logger.info(f"\n--- 对话测试 {i}/{len(test_conversations)}: {test['name']} ---")
            logger.info(f"📤 提示词: {test['prompt']}")

            start_time = time.time()

            try:
                # 发送对话请求
                response = ai_service.generate_response(test['prompt'])

                end_time = time.time()
                response_time = end_time - start_time
                total_time += response_time

                if response and not response.startswith("Error"):
                    logger.info(f"✅ 响应成功 (耗时: {response_time:.2f}秒)")
                    logger.info(f"📥 响应长度: {len(response)} 字符")
                    logger.info(f"📄 响应内容: {response[:200]}{'...' if len(response) > 200 else ''}")

                    # 测试 Token 计数
                    try:
                        token_count = ai_service.count_tokens(test['prompt'])
                        logger.info(f"🔢 提示词 Token 数: {token_count}")
                    except Exception as e:
                        logger.warning(f"Token 计数失败: {e}")

                    results.append({
                        "name": test['name'],
                        "success": True,
                        "response_time": response_time,
                        "response_length": len(response)
                    })
                else:
                    logger.error(f"❌ 响应失败: {response[:100] if response else 'No response'}")
                    results.append({
                        "name": test['name'],
                        "success": False,
                        "response_time": response_time,
                        "error": response[:100] if response else 'No response'
                    })

            except Exception as e:
                end_time = time.time()
                response_time = end_time - start_time
                total_time += response_time

                logger.exception(f"❌ 对话异常: {e}")
                results.append({
                    "name": test['name'],
                    "success": False,
                    "response_time": response_time,
                    "error": str(e)
                })

        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
        avg_response_time = total_time / total_count if total_count > 0 else 0

        logger.info(f"\n{'=' * 60}")
        logger.info("📊 对话功能测试结果统计")
        logger.info(f"{'=' * 60}")
        logger.info(f"✅ 成功测试: {success_count}/{total_count}")
        logger.info(f"📈 成功率: {success_rate:.1f}%")
        logger.info(f"⏱️  平均响应时间: {avg_response_time:.2f}秒")
        logger.info(f"⏱️  总测试时间: {total_time:.2f}秒")

        # 详细结果
        logger.info(f"\n📋 详细测试结果:")
        for result in results:
            status = "✅ 成功" if result['success'] else "❌ 失败"
            logger.info(f"  {result['name']}: {status} ({result['response_time']:.2f}秒)")
            if not result['success'] and 'error' in result:
                logger.info(f"    错误: {result['error']}")

        # 判断整体测试结果
        if success_count >= len(test_conversations) * 0.75:  # 75% 成功率
            logger.info(f"\n🎉 对话功能测试通过! (成功率: {success_rate:.1f}%)")
            return True
        else:
            logger.warning(f"\n⚠️  对话功能测试未完全通过 (成功率: {success_rate:.1f}%)")
            return False

    except Exception as e:
        logger.exception(f"❌ 对话功能测试失败: {e}")
        return False

def get_task_config():
    """获取任务配置 - 支持多种配置来源"""
    config = {}

    # 1. 优先从环境变量读取（适合 Google Cloud）
    config['task_type'] = os.getenv('TASK_TYPE', 'batch')
    config['prompt_sheet'] = os.getenv('PROMPT_SHEET_URL')
    config['test_spec_sheet'] = os.getenv('TEST_SPEC_SHEET_URL')
    config['prompt_sheet_name'] = os.getenv('PROMPT_SHEET_NAME')
    config['model'] = os.getenv('AI_MODEL', settings.DEFAULT_MODEL)

    # 2. 如果环境变量中没有表格配置，尝试从配置文件读取
    if not config['prompt_sheet'] or not config['test_spec_sheet']:
        logger.info("Environment variables not found, trying config file...")
        try:
            config_service = ConfigurationService()
            if config_service.config_exists():
                sheet_config = config_service.get_sheet_config_for_batch_task()
                if 'error' not in sheet_config:
                    config.update(sheet_config)
                    logger.info("Configuration loaded from config.ini")
                else:
                    logger.error(f"Config file error: {sheet_config['error']}")
            else:
                logger.warning("No config.ini file found")
        except Exception as e:
            logger.error(f"Failed to read config file: {e}")

    # 3. 从 Cloud Run 任务参数读取（如果存在）
    cloud_run_task_index = os.getenv('CLOUD_RUN_TASK_INDEX')
    cloud_run_task_count = os.getenv('CLOUD_RUN_TASK_COUNT')

    if cloud_run_task_index is not None:
        config['task_index'] = int(cloud_run_task_index)
        config['task_count'] = int(cloud_run_task_count) if cloud_run_task_count else 1
        logger.info(f"Running as Cloud Run task {config['task_index']}/{config['task_count']}")

    return config

def validate_config(config):
    """验证配置"""
    required_fields = ['prompt_sheet', 'test_spec_sheet']
    missing_fields = []

    for field in required_fields:
        if not config.get(field):
            missing_fields.append(field)

    if missing_fields:
        raise ValueError(f"Missing required configuration: {missing_fields}")

    logger.info("Configuration validation passed")

def log_environment_info():
    """记录环境信息"""
    logger.info("=" * 60)
    logger.info("VTAF AI Multiprompt - Environment Information (Test Version)")
    logger.info("=" * 60)
    logger.info(f"Project ID: {settings.PROJECT_ID}")
    logger.info(f"Region: {settings.REGION}")
    logger.info(f"Default Model: {settings.DEFAULT_MODEL}")

    # Google Cloud 环境检测
    if os.getenv('K_SERVICE'):
        logger.info(f"Running on Google Cloud Run: {os.getenv('K_SERVICE')}")
    elif os.getenv('FUNCTION_NAME'):
        logger.info(f"Running on Google Cloud Functions: {os.getenv('FUNCTION_NAME')}")
    elif os.getenv('GAE_SERVICE'):
        logger.info(f"Running on Google App Engine: {os.getenv('GAE_SERVICE')}")
    else:
        logger.info("Running in local/custom environment")

    # 认证方式检测
    if os.getenv('GOOGLE_APPLICATION_CREDENTIALS'):
        logger.info(f"Using service account file: {os.getenv('GOOGLE_APPLICATION_CREDENTIALS')}")
    elif hasattr(settings, 'SERVICE_ACCOUNT_INFO') and settings.SERVICE_ACCOUNT_INFO:
        logger.info("Using hardcoded service account credentials")
    else:
        logger.info("Using default Google Cloud credentials")

    logger.info("=" * 60)

def execute_default_button_functions():
    """执行默认的按钮功能：send_batch_reqs 和 send_single_req"""
    logger.info("🎯 执行默认按钮功能")
    logger.info("=" * 60)

    # 定义要执行的按钮功能
    button_functions = [
        {
            'name': 'Send Batch Reqs',
            'job_type': 'batch_processing',
            'description': '批量请求处理 - 对应send_batch_reqs按钮'
        },
        {
            'name': 'Send Single Req',
            'job_type': 'single_task',
            'description': '单个请求处理 - 对应send_single_req按钮'
        }
    ]

    results = []

    for i, func in enumerate(button_functions, 1):
        logger.info(f"\n--- 执行按钮功能 {i}/{len(button_functions)}: {func['name']} ---")
        logger.info(f"📝 描述: {func['description']}")
        logger.info(f"🔧 任务类型: {func['job_type']}")

        try:
            # 临时设置JOB_TYPE环境变量
            original_job_type = os.getenv('JOB_TYPE')
            os.environ['JOB_TYPE'] = func['job_type']

            # 初始化JobRouter并执行任务
            router = JobRouter()
            result = router.route_job()

            # 恢复原始JOB_TYPE
            if original_job_type:
                os.environ['JOB_TYPE'] = original_job_type
            elif 'JOB_TYPE' in os.environ:
                del os.environ['JOB_TYPE']

            if result == 0:
                logger.info(f"✅ {func['name']} 执行成功")
                results.append({'name': func['name'], 'success': True})
            else:
                logger.error(f"❌ {func['name']} 执行失败")
                results.append({'name': func['name'], 'success': False})

        except Exception as e:
            logger.exception(f"❌ {func['name']} 执行异常: {e}")
            results.append({'name': func['name'], 'success': False, 'error': str(e)})

    # 统计结果
    success_count = sum(1 for r in results if r['success'])
    total_count = len(results)
    success_rate = (success_count / total_count) * 100 if total_count > 0 else 0

    logger.info(f"\n{'=' * 60}")
    logger.info("📊 默认按钮功能执行结果")
    logger.info(f"{'=' * 60}")
    logger.info(f"✅ 成功执行: {success_count}/{total_count}")
    logger.info(f"📈 成功率: {success_rate:.1f}%")

    for result in results:
        status = "✅ 成功" if result['success'] else "❌ 失败"
        logger.info(f"  {result['name']}: {status}")
        if not result['success'] and 'error' in result:
            logger.info(f"    错误: {result['error']}")

    return success_count == total_count

def main():
    """主程序入口 - 默认执行send_batch_reqs和send_single_req按钮功能"""
    try:
        # 记录环境信息
        log_environment_info()

        # 检查运行模式
        run_mode = os.getenv('RUN_MODE', 'production')
        job_type = os.getenv('JOB_TYPE', 'batch_processing')  # 保持原有默认值
        test_only = os.getenv('TEST_CONVERSATION_ONLY', 'false').lower() == 'true'
        skip_conversation_test = os.getenv('SKIP_CONVERSATION_TEST', 'false').lower() == 'true'
        execute_default_buttons = os.getenv('EXECUTE_DEFAULT_BUTTONS', 'true').lower() == 'true'

        logger.info(f"运行模式: {run_mode}")
        logger.info(f"任务类型: {job_type}")
        logger.info(f"执行默认按钮功能: {execute_default_buttons}")

        # 默认执行对话功能测试（除非明确跳过）
        conversation_success = True  # 默认值
        if not skip_conversation_test:
            logger.info("🧪 执行对话功能测试...")
            conversation_success = test_conversation_functionality()

            if conversation_success:
                logger.info("✅ 对话功能测试通过")
            else:
                logger.warning("⚠️ 对话功能测试未完全通过，但继续执行任务...")
        else:
            logger.info("⏭️ 跳过对话功能测试")

        # 如果设置了仅测试模式，则在对话测试后退出
        if test_only:
            logger.info("🧪 仅运行对话功能测试模式，程序退出")
            if not skip_conversation_test and conversation_success:
                logger.info("✅ 对话测试完成，程序正常退出")
                return 0
            elif not skip_conversation_test:
                logger.error("❌ 对话测试失败")
                return 1
            else:
                logger.info("✅ 跳过测试模式完成")
                return 0

        # 执行默认按钮功能（send_batch_reqs 和 send_single_req）
        if execute_default_buttons:
            logger.info("🎯 执行默认按钮功能...")
            button_success = execute_default_button_functions()

            if button_success:
                logger.info("✅ 默认按钮功能执行完成")
                return 0
            else:
                logger.warning("⚠️ 部分默认按钮功能执行失败，继续执行指定任务...")

        # 继续执行JobRouter任务（如果指定了特定的JOB_TYPE）
        logger.info("🎯 继续执行JobRouter任务...")

        # 显示可用的任务类型
        from services.job_router import get_available_job_types
        available_jobs = get_available_job_types()
        logger.info(f"可用任务类型: {list(available_jobs.keys())}")

        if job_type not in available_jobs:
            logger.error(f"未知的任务类型: {job_type}")
            logger.info(f"可用的任务类型: {list(available_jobs.keys())}")
            return 1

        logger.info(f"执行任务: {available_jobs[job_type]}")

        # 初始化JobRouter并执行任务
        router = JobRouter()
        result = router.route_job()

        if result == 0:
            logger.info("✅ Job completed successfully")
        else:
            logger.error("❌ Job failed")

        return result

    except Exception as e:
        logger.exception(f"❌ Job failed: {e}")
        return 1

# Google Cloud Run / Cloud Functions 兼容入口点
def cloud_run_handler(request=None):
    """Google Cloud Run HTTP 处理器"""
    try:
        result = main()
        if result == 0:
            return {"status": "success", "message": "Job completed successfully"}, 200
        else:
            return {"status": "error", "message": "Job failed"}, 500
    except Exception as e:
        logger.exception(f"Cloud Run handler error: {e}")
        return {"status": "error", "message": str(e)}, 500

def cloud_function_handler(data, context):
    """Google Cloud Functions 处理器"""
    try:
        logger.info(f"Cloud Function triggered: {context.event_id}")
        result = main()
        return {"status": "success" if result == 0 else "error"}
    except Exception as e:
        logger.exception(f"Cloud Function handler error: {e}")
        raise

if __name__ == '__main__':
    # 本地运行或 Cloud Run 作业模式
    exit_code = main()
    sys.exit(exit_code)

