import os
import json
import configparser
from typing import Dict, Any, Optional
from utils.logger import get_logger
from services.sheet_manager import extract_sheet_id

logger = get_logger(__name__)

class ConfigurationService:
    """配置管理服务"""

    def __init__(self):
        self.config_file = "./config.ini"
        self.json_config_file = "multiprompt_config.json"
        self.default_config = {
            "prompt_sheet": "",
            "dataset_sheet_name": "Training_Dataset",
            "prompt_sheet_name": "",
            "test_spec_sheet": "",
            "ts_dataset_sheet_name": "TS_Training_Dataset",
            "ts_prompt_sheet_name": ""
        }
        
    def get_configuration(self) -> Dict[str, Any]:
        """获取当前配置"""
        try:
            # 优先读取 INI 配置文件
            if os.path.exists(self.config_file):
                return self._load_ini_config()
            # 回退到 JSON 配置文件
            elif os.path.exists(self.json_config_file):
                return self._load_json_config()
            else:
                return self.default_config
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            return self.default_config

    def _load_ini_config(self) -> Dict[str, Any]:
        """从 INI 文件加载配置"""
        config = configparser.ConfigParser()
        config.read(self.config_file, encoding='utf-8')

        result = self.default_config.copy()

        try:
            # 读取 SheetConfig 部分
            if config.has_section('SheetConfig'):
                sheet_config = config['SheetConfig']
                result['prompt_sheet'] = sheet_config.get('prompt_sheet', '')
                result['dataset_sheet_name'] = sheet_config.get('Dataset_sheet_name', 'Training_Dataset')
                result['prompt_sheet_name'] = sheet_config.get('prompt_sheet_name', '')

            # 读取 TestSpec_SheetConfig 部分
            if config.has_section('TestSpec_SheetConfig'):
                testspec_config = config['TestSpec_SheetConfig']
                result['test_spec_sheet'] = testspec_config.get('test_spec_sheet', '')
                result['ts_dataset_sheet_name'] = testspec_config.get('TS_Dataset_sheet_name', 'TS_Training_Dataset')
                result['ts_prompt_sheet_name'] = testspec_config.get('TS_prompt_sheet_name', '')

        except Exception as e:
            logger.error(f"Error parsing INI config: {e}")

        return result

    def _load_json_config(self) -> Dict[str, Any]:
        """从 JSON 文件加载配置"""
        try:
            with open(self.json_config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return {**self.default_config, **config}
        except Exception as e:
            logger.error(f"Error loading JSON config: {e}")
            return self.default_config
            
    def save_configuration(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """保存配置"""
        try:
            # 验证配置
            validation_result = self.validate_sheet_config(config)
            if validation_result != "valid":
                return {"status": "error", "message": validation_result}

            # 保存到 INI 文件
            self._save_ini_config(config)

            logger.info("Configuration saved successfully")
            return {"status": "success", "message": "Configuration saved"}

        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            return {"status": "error", "message": str(e)}

    def _save_ini_config(self, config: Dict[str, Any]):
        """保存配置到 INI 文件"""
        config_parser = configparser.ConfigParser()

        # 添加 SheetConfig 部分
        config_parser.add_section('SheetConfig')
        config_parser.set('SheetConfig', '; auto submit req and write case to sheet', '')
        config_parser.set('SheetConfig', 'prompt_sheet', config.get('prompt_sheet', ''))
        config_parser.set('SheetConfig', 'Dataset_sheet_name', config.get('dataset_sheet_name', 'Training_Dataset'))
        config_parser.set('SheetConfig', '; The worksheet name for a single feeding request (used for the "Send Single Req" button)', '')
        config_parser.set('SheetConfig', 'prompt_sheet_name', config.get('prompt_sheet_name', ''))

        # 添加 TestSpec_SheetConfig 部分
        config_parser.add_section('TestSpec_SheetConfig')
        config_parser.set('TestSpec_SheetConfig', 'test_spec_sheet', config.get('test_spec_sheet', ''))
        config_parser.set('TestSpec_SheetConfig', 'TS_Dataset_sheet_name', config.get('ts_dataset_sheet_name', 'TS_Training_Dataset'))
        config_parser.set('TestSpec_SheetConfig', 'TS_prompt_sheet_name', config.get('ts_prompt_sheet_name', ''))

        # 写入文件
        with open(self.config_file, 'w', encoding='utf-8') as f:
            config_parser.write(f)
            
    def validate_sheet_config(self, config: Dict[str, Any]) -> str:
        """
        验证表格配置
        Returns:
            "valid" 如果配置有效，否则返回错误信息
        """
        try:
            # 检查必要的字段
            prompt_sheet = config.get('prompt_sheet', '').strip()
            test_spec_sheet = config.get('test_spec_sheet', '').strip()

            if not prompt_sheet:
                return "prompt_sheet is required"
            if not test_spec_sheet:
                return "test_spec_sheet is required"

            # 验证表格ID格式
            prompt_sheet_id = extract_sheet_id(prompt_sheet)
            test_spec_sheet_id = extract_sheet_id(test_spec_sheet)

            if not prompt_sheet_id:
                return "Invalid prompt_sheet URL or ID"
            if not test_spec_sheet_id:
                return "Invalid test_spec_sheet URL or ID"

            # 这里可以添加权限检查
            # if not check_config_spread_sheet_permission(prompt_sheet_id):
            #     return "prompt_sheet does not have write permission!!!"
            # if not check_config_spread_sheet_permission(test_spec_sheet_id):
            #     return "test_spec_sheet does not have write permission!!!"

            return "valid"

        except Exception as e:
            logger.error(f"Config validation error: {e}")
            return f"config validation failed: {str(e)}"

    def get_sheet_config_for_batch_task(self) -> Dict[str, Any]:
        """
        获取批处理任务的表格配置
        Returns:
            配置字典或包含错误信息的字典
        """
        if not os.path.exists(self.config_file):
            return {"error": "config.ini not found"}

        try:
            config = self._load_ini_config()

            # 验证配置
            validation_result = self.validate_sheet_config(config)
            if validation_result != "valid":
                return {"error": validation_result}

            return {
                "prompt_sheet": config['prompt_sheet'],
                "test_spec_sheet": config['test_spec_sheet'],
                "prompt_sheet_name": config['prompt_sheet_name'],
                "dataset_sheet_name": config['dataset_sheet_name'],
                "ts_dataset_sheet_name": config['ts_dataset_sheet_name'],
                "ts_prompt_sheet_name": config['ts_prompt_sheet_name']
            }

        except Exception as e:
            logger.error(f"Failed to get sheet config: {e}")
            return {"error": f"config.ini is not valid!\n{str(e)}"}
        
    def get_model_options(self) -> Dict[str, Any]:
        """获取模型选项"""
        return {
            "gemini_models": [
                "gemini-2.5-pro-preview-05-06",
                "gemini-2.5-flash-preview-05-20",
                "gemini-2.0-flash-001",
                "gemini-2.0-flash-lite",
                "gemini-1.5-pro-002",
                "gemini-1.5-flash-002"
            ],
            "deepseek_models": [
                "deepseek-r1:70b",
                "deepseek-chat",
                "deepseek-coder"
            ],
            "regions": [
                "us-central1",
                "europe-west1",
                "asia-southeast1",
                "europe-west3",
                "asia-northeast1"
            ],
            "temperature_range": {"min": 0.0, "max": 2.0, "default": 0.5},
            "max_tokens_range": {"min": 1, "max": 65535, "default": 2048},
            "top_p_range": {"min": 0.0, "max": 1.0, "default": 1.0},
            "top_k_range": {"min": 1, "max": 100, "default": 32}
        }

    def create_default_config_file(self) -> bool:
        """创建默认的配置文件"""
        try:
            default_ini_content = """[SheetConfig]
; auto submit req and write case to sheet
prompt_sheet =
Dataset_sheet_name = Training_Dataset
; The worksheet name for a single feeding request (used for the "Send Single Req" button)
prompt_sheet_name =

[TestSpec_SheetConfig]
test_spec_sheet =
TS_Dataset_sheet_name = TS_Training_Dataset
TS_prompt_sheet_name =
"""

            with open(self.config_file, 'w', encoding='utf-8') as f:
                f.write(default_ini_content)

            logger.info(f"Created default config file: {self.config_file}")
            return True

        except Exception as e:
            logger.error(f"Failed to create default config file: {e}")
            return False

    def get_config_file_path(self) -> str:
        """获取配置文件路径"""
        return os.path.abspath(self.config_file)

    def config_exists(self) -> bool:
        """检查配置文件是否存在"""
        return os.path.exists(self.config_file)