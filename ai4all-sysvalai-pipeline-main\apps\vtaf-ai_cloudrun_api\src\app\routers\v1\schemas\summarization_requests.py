# file: summarization_requests.py

from pydantic import BaseModel, <PERSON>
from typing import Optional

class SummarizationRequest(BaseModel):
    """
    Request schema for summarizing a given text input.

    Attributes:
        text (str): The input text to summarize.
        email (Optional[str]): Optional email address for notifications.
        job_id (Optional[str]): Optional job identifier for tracking.
        enable_tracing (Optional[bool]): Enable or disable tracing for this request.
    """
    text: str = Field(
        default="",
        description="Text content to summarize"
    )
    email: Optional[str] = Field(
        default=None,
        description="Optional email address for notifications"
    )
    job_id: Optional[str] = Field(
        default=None,
        description="Optional job identifier"
    )
    enable_tracing: Optional[bool] = Field(
        default=True,
        description="Optional flag to enable tracing for the request"
    )
