import { ref } from "vue";
import { defineStore } from "pinia";
import { Notify } from "quasar";
import { chat_with_bqA<PERSON> } from "src/boot/axios";
import { marked } from "marked";

export const useChatWithBigqueryStore = defineStore("chat_bigquery_store", () => {
  const user_email = ref("");
  const dataset_dropdown = ref(['VTAF_AI_ACP']);
  const selected_dataset = ref("");
  const user_prompt = ref("");
  const bigquery_ai_response = ref("");
  const datasets_dropdown_loading = ref(false);
  const generated_sql = ref('');
  const userPrompt = ref('');
  const response_loading = ref(false);

  async function get_user_email() {
    try {
      console.log("starting");
0
      const response = await chat_with_bqApi.get(`/v1/utils/get_user`);
      if (response.status === 200) {
        // Success
        const data = response.data.data;
        user_email.value = data;
        console.log(data);
      } else {
      }
    } catch (err) {
      console.log(err);
    }
  }

  // async function get_datasets() {
  //   try {
  //     console.log("starting");

  //     const response = await chat_with_bqApi.get(`/v1/utils/get_user`);
  //     if (response.status === 200) {
  //       // Success
  //       const data = response.data.data;
  //       user_email.value = data;
  //       console.log(data);
  //     } else {
  //     }
  //   } catch (err) {
  //     console.log(err);
  //   }
  // }

  async function query_bigquery() {
    try {
      response_loading.value = true;

      const payload = {
        query: user_prompt.value,
        dataset: selected_dataset.value
      };

      const response = await chat_with_bqApi.post(`/v1/agents/bigquery_extract`, payload);
      if (response.status === 200) {
        // Success
        const data = response.data;
        bigquery_ai_response.value = response.data.output;;
        generated_sql.value = response.data.generated_query;
        console.log(data);
        response_loading.value=false;
      } else {
        response_loading.value = false;
      }
    } catch (err) {
      console.log(err);
      response_loading.value = false;
    }
  }

  return {
    user_email,
    dataset_dropdown,
    selected_dataset,
    user_prompt,
    bigquery_ai_response,
    datasets_dropdown_loading,
    generated_sql,
    userPrompt,
    response_loading,
    get_user_email,
    // get_datasets,
    query_bigquery,
  };
});
