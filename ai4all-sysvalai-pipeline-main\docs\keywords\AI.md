# Group {AI}
## Check_LLM

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Check_LLM.seq*


**Descriptions:** *This keyword will be used to verify the LLM JSON responses with the validation. It requires three inputs, Parameter Name from the JSON, Parameter value, Condition*


***Command Set:***

	CompareText
		-CompareText=ParameterName;ParameterValue;Condition
	CompareNumeric
		-CompareNumeric=ParameterName;ValCondition


***Input Description***



## Send_LLM

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Send_LLM.seq*


**Descriptions:** *This keyword will be used to prompt using Gemini based LLMs and gets the response. Please use TemplateLookup in configurations to use the prompt in testcases.*


***Command Set:***

	Prompt
		-Prompt=Template;Model;Temperature


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Template| String| NA| Template_1|  Template name from GenAI_PromptTemplateLookup table| Required
| Model| String| NA| Gemini-1.5-pro|  Google Model Garden variants like Gemini-1.5-pro, Gemini-1.5-flash, and Gemini-pro-vision| Optional
| Temperature| String| NA| 100|  The temperature setting for the prompt, affecting the creativity and randomness of the response.| Optional

