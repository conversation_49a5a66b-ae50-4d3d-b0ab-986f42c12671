system_understanding_task:
  description: >
    Conduct a thorough analysis of the complete {topic} {feature},
    including hardware components, subsystems, and overall architecture. 
    Identify potential areas for {feature} testing.
  expected_output: >
    A comprehensive document outlining the {feature} system's architecture, 
    {sensors}, {algorithms} and the integration points between hardware and software.
  agent: system_test_engineer

requirements_review_task:
  description: >
    Analyze the {feature} requirements documentation to identify testable conditions for {requirement}. 
    Collaborate with the Requirements Engineer to clarify any ambiguous or incomplete requirements.
  expected_output: >
    A documented list of identified requirements along with their corresponding testable conditions. Any unclear or ambiguous requirements 
    should be flagged for further clarification.
  agent: system_test_engineer

test_case_draft_task:
  description: >
    Develop detailed test cases to validate the functionality or feature and performance of the {feature} system, ensuring it meets the specified requirements. The test cases should comprehensively cover the core functionalities of the {feature}, focusing on its behavior under normal, exceptional, and edge-case scenarios. Ensure that the test cases are designed according to ISTQB standards and primarily validate the {feature}, rather than the underlying sensors or algorithms.This system is including {sensors} & {algorithms}.
  expected_output: >
    Test cases that thoroughly validate the {feature} with a well-rounded coverage. Each test case should follow the ISTQB structure and include the following sections: Precondition, Test Procedure, Reset.And provide Test Expectation corresponding to the Test Step in Test Procedure. The test cases should be focused on verifying the behavior and performance of the {feature}, avoiding unnecessary repetition. The numbering should be sequential, starting from 1 for sections in the Testcase.
  agent: system_test_engineer

test_case_writing_task:
  description: >
    Create structured test cases based on the analyzed requirement {requirement} to ensure comprehensive validation of the {feature} system. The test cases should focus on covering all key aspects of the {feature}, including core functionalities, performance, and behavior under various conditions. Ensure that positive, negative, and edge case scenarios are covered for each critical component of the feature.
  expected_output: >
    Test cases that provide complete coverage of all possible scenarios for the {feature}. Rewrite the draft test cases previously written and sent for QA review. Additionally, ensure that all feedback from the QA review points is addressed and incorporated into the updated test cases.
  agent: system_test_engineer

requirement_clarification_task:
  description: >
    Address and resolve queries raised by the System Test Engineer regarding unclear, ambiguous, or incomplete {requirement} for {feature}. 
    As an IREB-certified System Requirements Engineer, analyze concerns, provide precise clarifications, and engage with stakeholders when necessary 
    to ensure well-defined requirements.
  expected_output: >
    A structured document detailing the clarified requirements, stakeholder responses (if applicable), and any additional information provided 
    to resolve the queries.
  agent: system_requirements_engineer

quality_assurance_task:
  description: >
    Review the draft test cases for the {feature} system to ensure they meet the required quality standards. Validate the coverage of the {requirement} and assess the overall quality of the test cases. Identify any duplication or unnecessary test cases and provide feedback for improvement.
  expected_output: >
    Review the test case coverage for the {requirement} and assess the quality of the test cases. Provide detailed feedback, highlighting any duplicate or redundant test cases, and suggest improvements. Request the test engineer to validate and remove unnecessary test cases.
  agent: quality_assurance_engineer

hardware_integration_testing_task:
  description: >
    Test the integration of {topic} hardware components {sensors}, ECUs to ensure they function correctly 
    with the overall system software of {feature}.
  expected_output: >
    A report detailing the results of hardware integration tests, including any issues found with communication protocols.
  agent: hardware_integration_specialist

performance_testing_task:
  description: >
    Conduct performance testing on the {topic} system, ensuring that real-time requirements are met 
    and that the system functions properly under load.
  expected_output: >
    A performance report detailing system performance under various test conditions, including latency, throughput, and stress testing results.
  agent: performance_tester

functional_testing_task:
  description: >
    Perform functional testing on individual system components of {feature} to ensure they meet their expected behavior 
    and perform as specified in the {requirement}.
  expected_output: >
    A report documenting the results of functional tests, including any defects found and their severity.
  agent: functional_tester

integration_testing_task:
  description: >
    Conduct integration testing to ensure that all subsystems, including hardware and software components, 
    communicate properly and work together as a cohesive {feature} system.
  expected_output: >
    A report detailing integration test results, including successful communications, data exchanges, and any integration issues identified.
  agent: integration_tester

answer_valeo_technical_questions:
  description: >
    The agent should answer technical questions related to Valeo's products, software, hardware, algorithms like {algorithms}, and standards.
  expected_output: >
    Clarify any doubts or questions about the products or requirement {requirement} of the {feature}.
  agent: valeo_subject_matter_expert

explain_valeo_standards_and_processes:
  description: The agent should explain Valeo's internal standards, development processes, and quality guidelines.
  expected_output: >
    Clarify any doubts or questions about the Valeo's internal standards, development processes, and quality guidelines of {feature}.
  agent: valeo_subject_matter_expert

testing_strategy_guidance:
    description: >
      Guide the system test engineer in developing effective testing strategies that encompass functional, performance, and safety aspects of the {feature} system.
    expected_output: >
      Provide recommendations on the most effective test strategies and methodologies based on the {feature} system's requirement {requirement}, ensuring comprehensive coverage for testing all aspects of functionality and performance.
    agent: subject_matter_expert

standards_and_compliance_clarification:
    description: >
      Provide guidance on {topic} standards, regulations, and compliance requirement {requirement} relevant to the {feature} system being tested.
    expected_output: >
      Ensure that the system test engineer is aware of relevant standards and regulations, and help interpret those to ensure that testing is conducted in compliance with industry norms and safety standards.
    agent: subject_matter_expert

risk_analysis_support:
  description: >
    Help the system test engineer assess and identify potential risks to the {feature} system’s performance, safety, and compliance. As well as {algorithms} algorithms limitations.
  expected_output: >
    Work with the system test engineer to conduct risk assessments, identify critical failure points, and ensure appropriate mitigation strategies are in place during testing and optimization for requirement {requirement}.
  agent: subject_matter_expert

test_case_clarification:
  description: >
    Assist the system test engineer by clarifying any ambiguities in the test cases and providing guidance on the correct execution of tests.
  expected_output: >
    Work closely with the system test engineer to ensure that test cases are understood and executed correctly. Clarify any complex or unclear requirements in test case design and execution for requirement {requirement}.
  agent: subject_matter_expert