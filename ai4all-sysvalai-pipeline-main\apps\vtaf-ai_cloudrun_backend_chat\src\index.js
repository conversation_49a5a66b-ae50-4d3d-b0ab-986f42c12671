const { VertexAI, HarmCategory, HarmBlockThreshold } = require('@google-cloud/vertexai');
const express = require('express');
const cors = require('cors');
const multer = require('multer');

const app = express();
const PORT = process.env.PORT || 3000;
const services_project_id = process.env.SERVICES_PROJECT_ID;
const upload = multer();

app.use(express.json());
app.use(cors()); // This will allow requests from any origin

// Set the path to your service account JSON file
// process.env.GOOGLE_APPLICATION_CREDENTIALS = 'service_account.json'; 

// const vertex_ai = new VertexAI({ project: project, location: location });
// Declare the chat instance at a higher scope for reuse
let chat = null;
// Function to create a new chat instance
function initializeChat(model_name, temperature, output_tokens, region) {
  const vertex_ai = new VertexAI({ project: services_project_id, location: region });
  
  const generativeModel = vertex_ai.getGenerativeModel({
    model: model_name,
    generationConfig: { maxOutputTokens: output_tokens, temperature: temperature },
  });
  chat = generativeModel.startChat({});
}

app.get('/chat/get_user', (req, res) => {
  // Check if the X-Goog-Authenticated-User-Email header is present
  let userEmail = req.headers['x-goog-authenticated-user-email'];

  let email;
  if (userEmail) {
      // Extract the email from the header
      email = userEmail.split(':')[1];
  } else {
      // Use a default email if the header is not present
      email = "<EMAIL>";
  }

  res.json({ data: email });
});

// Endpoint to handle queries
app.post('/chat/query', upload.single('file_input'), async (req, res) => {
  let request;
  
  const { user_prompt, gemini_version, file_name, file_type, temperature, output_tokens, region } = req.body;
  const vertex_ai = new VertexAI({ project: services_project_id, location: region });
  const file_input = req.file;
  const textPart = {text: user_prompt}
  if (file_input) {
    
    const base64String = file_input.buffer.toString('base64');
    const filePart = {inline_data: {data: base64String, mimeType: file_input.mimetype}};
    
    request = {
      contents: [
        { role: 'user', parts: [textPart, filePart] }, // user_prompt as a separate part
         // filePart as another content
      ],
    };
  } else {
    request = {
      contents: [{ role: 'user', parts: [textPart] }],
    };
  }

  try {
    const generativeModel = vertex_ai.getGenerativeModel({
      model: gemini_version,
      safety_settings: [
        {
          category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
          threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        },
      ],
      generationConfig: { maxOutputTokens: output_tokens, temperature: temperature },
    });

    const resp = await generativeModel.generateContent(request);
    
    const text = resp.response.candidates[0].content.parts[0].text;

    // Log the response and send it back to the client
    res.json({ response: text });
  } catch (error) {
    console.error('Error generating content:', error);
    res.status(500).json({ error: 'An error occurred while generating content' });
  }
});

// Endpoint to handle streaming response
app.post('/chat/stream', upload.single('file_input'), async (req, res) => {
  let request;

  const { user_prompt, gemini_version, temperature, output_tokens, region } = req.body;
  const vertex_ai = new VertexAI({ project: services_project_id, location: region });
  const file_input = req.file;
  const textPart = { text: user_prompt };

  if (file_input) {
    const base64String = file_input.buffer.toString('base64');
    const filePart = { inline_data: { data: base64String, mimeType: file_input.mimetype } };
    request = {
      contents: [{ role: 'user', parts: [textPart, filePart] }],
    };
  } else {
    request = {
      contents: [{ role: 'user', parts: [textPart] }],
    };
  }

  try {
    const generativeModel = vertex_ai.getGenerativeModel({
      model: gemini_version,
      safety_settings: [
        {
          category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
          threshold: 'BLOCK_MEDIUM_AND_ABOVE',
        },
      ],
      generationConfig: { maxOutputTokens: output_tokens, temperature: temperature },
    });

    const streamingResult = await generativeModel.generateContentStream(request);
    
    res.setHeader('Content-Type', 'text/plain'); // Set the content type for plain text response
    
    for await (const item of streamingResult.stream) {
      const chunk = item.candidates[0].content.parts[0].text;
      res.write(chunk); // Stream each chunk
      
    }
    
    res.end(); // Close the streaming
  } catch (error) {
    console.error('Error generating content:', error);
    res.status(500).json({ error: 'An error occurred while generating content' });
  }
});

// Endpoint for chat interactions
app.post('/chat/multichat', async (req, res) => {
  const { user_prompt, gemini_version, temperature, output_tokens, region, clear } = req.body;
  console.log(user_prompt, gemini_version, temperature, output_tokens, region, clear);
  
  console.log("using this");
  
  // If clear is true, initialize a new chat session
  if (clear || !chat) {
    console.log("initializing");
    console.log(typeof(clear))
    
    initializeChat(gemini_version, temperature, output_tokens, region);
  }

  try {
    const result = await chat.sendMessageStream(user_prompt);

    res.setHeader('Content-Type', 'text/plain'); // Set the content type for plain text response
    for await (const item of result.stream) {
      const chunk = item.candidates[0].content.parts[0].text;
      res.write(chunk); // Stream each chunk
    }
    res.end(); // Close the streaming

  } catch (error) {
    console.error('Error during chat interaction:', error);
    res.status(500).json({ error: 'An error occurred during chat interaction.' });
  }
});

// Endpoint for chat interactions
app.post('/chat/multichat1', async (req, res) => {
  const { user_prompt, gemini_version, temperature, output_tokens, region, clear } = req.body;  
  console.log(typeof(clear));
  
  console.log(user_prompt, gemini_version, temperature, output_tokens, region, clear);
  
  // If clear is true, initialize a new chat session
  if (clear || !chat) {
    console.log(typeof(clear))
    initializeChat(gemini_version, temperature, output_tokens, region);
  }

  try {
    const result = await chat.sendMessage(user_prompt);

    // res.setHeader('Content-Type', 'text/plain'); // Set the content type for plain text response
    // for await (const item of result.stream) {
    //   const chunk = item.candidates[0].content.parts[0].text;
    //   res.write(chunk); // Stream each chunk
    //   console.log("AI response : ", chunk);
    // }
    // const resp  = result.candidates[0].content.parts[0].text;
    console.log(result);
    
    const resp = result.response.candidates[0].content.parts[0].text;
    console.log(resp);
    
    res.json({ response: resp});
  } catch (error) {
    console.error('Error during chat interaction:', error);
    res.status(500).json({ error: 'An error occurred during chat interaction.' });
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});
