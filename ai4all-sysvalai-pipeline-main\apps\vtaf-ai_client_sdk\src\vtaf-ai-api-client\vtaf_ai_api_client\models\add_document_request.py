from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar

from attrs import define as _attrs_define
from attrs import field as _attrs_field

if TYPE_CHECKING:
    from ..models.add_document_request_metadata import AddDocumentRequestMetadata


T = TypeVar("T", bound="AddDocumentRequest")


@_attrs_define
class AddDocumentRequest:
    """
    Attributes:
        content (str):
        metadata (AddDocumentRequestMetadata):
        collection_name (str):
    """

    content: str
    metadata: "AddDocumentRequestMetadata"
    collection_name: str
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        content = self.content

        metadata = self.metadata.to_dict()

        collection_name = self.collection_name

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "content": content,
                "metadata": metadata,
                "collection_name": collection_name,
            }
        )

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.add_document_request_metadata import AddDocumentRequestMetadata

        d = dict(src_dict)
        content = d.pop("content")

        metadata = AddDocumentRequestMetadata.from_dict(d.pop("metadata"))

        collection_name = d.pop("collection_name")

        add_document_request = cls(
            content=content,
            metadata=metadata,
            collection_name=collection_name,
        )

        add_document_request.additional_properties = d
        return add_document_request

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
