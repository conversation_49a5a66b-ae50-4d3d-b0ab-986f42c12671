from pydantic_settings import BaseSettings


class AppSettings(BaseSettings):
    PROJECT_ID: str
    FILE_NAME: str
    BQ_DATA_SET: str
    BQ_TABLE: str
    SOURCE_PATH: str
    CLOUD_RUN_EXECUTION: str
    SERVICES_PROJECT_NUMBER: str
    DOMAIN_NAME: str
    VTAF_AI_CLIENT_ID: str
    SHARED_FOLDER_ID: str
    MODEL: str
    LOCATION: str
    BUCKET_NAME: str

    class Config:
        env_file = None
        case_sensitive = True


settings = AppSettings()
