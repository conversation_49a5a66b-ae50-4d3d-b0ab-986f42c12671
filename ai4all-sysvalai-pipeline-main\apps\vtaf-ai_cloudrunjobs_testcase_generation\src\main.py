from Utilities.gsheet_manager import DriveUploader
from Utilities.secret_manager import GCPSecretManager
from vtaf_agent.vtaf_crew import vtaf_crew
from Utilities.send_email import send_email_toVTAF_User
from Utilities.BigQuery import bq_update_query, bq_select_query
from Utilities.Storage import update_gcs_file_metadata
from Utilities.langfuse_crewai import LangfuseEventListener
from langfuse import Langfuse
import os
import json
import pandas as pd
import datetime
import time

# Get environment variables
CLOUD_RUN_JOB = os.getenv("CLOUD_RUN_JOB", "")  #job-vtaf-tc-generayion
CLOUD_RUN_EXECUTION = os.getenv("CLOUD_RUN_EXECUTION", "")  #job-vtaf-tc-generation-xxxxx
FILE_NAME = os.getenv("FILE_NAME", "")
MODEL = os.getenv("MODEL", "")
LOCATION = os.getenv("LOCATION", "")
SERVICES_PROJECT_ID = os.getenv("SERVICES_PROJECT_ID", "")
SERVICES_DATASET_ID = os.getenv("SERVICES_DATASET_ID", "")
SERVICES_BUCKET_NAME = os.getenv("SERVICES_BUCKET_NAME", "")
USER_EMAIL = os.getenv("SERVICES_PROJECT_ID", "<EMAIL>")
LANGFUSE_URL = os.getenv("LANGFUSE_URL", "")

gcp_secret_manager = GCPSecretManager()
langfuse_secret_key=gcp_secret_manager.read_secret("vtaf-ai-langfuse-secret-key")
langfuse_public_key=gcp_secret_manager.read_secret("vtaf-ai-langfuse-public-key")

lf = Langfuse(
        secret_key=langfuse_secret_key,
        public_key=langfuse_public_key,
        host=LANGFUSE_URL,
    )

listener = LangfuseEventListener(langfuse=lf)
#Job Variables
bucket_prefix = f"{SERVICES_BUCKET_NAME}/vtaf-ai/job_history/{CLOUD_RUN_JOB}/{CLOUD_RUN_EXECUTION}"
workspace_path = f"/mnt/vtaf-ai/vtaf-ai/job_history/{CLOUD_RUN_JOB}/{CLOUD_RUN_EXECUTION}"
total_token_usage = {
    "total_tokens": 0,
    "prompt_tokens": 0,
    "cached_prompt_tokens": 0,
    "completion_tokens": 0,
    "successful_requests": 0
}


def generate_test_case(feature, sensor, algorithm, requirement, project_uuid, variant_uuid, euf_feature_uuid,
                       AGENTS_CONFIG_PATH, TASKS_CONFIG_PATH):
    """
    Generate a test case by calling vtaf_crew and processing its response.
    Consolidates test_action into steps and extracts expectation and methodology.
    """
    inputs = {
        "topic": "Automotive",
        "feature": str(feature),
        "sensors": str(sensor),
        "algorithms": str(algorithm),
        "requirement": str(requirement),
        "project_uuid": str(project_uuid),
        "variant_uuid": str(variant_uuid),
        "euf_feature_uuid": str(euf_feature_uuid)
    }
    agent_qa_feedback = ''
    processed_test_cases = []
    result = None
    Status = None
    retries = 0
    max_retries = 3
    delay = 5  # seconds

    # Retry mechanism
    while retries < max_retries:

        result, Status = vtaf_crew(inputs=inputs, model=MODEL, location=LOCATION,
                                   agents_config_path=AGENTS_CONFIG_PATH, tasks_config_path=TASKS_CONFIG_PATH)
        # If result is not None, break the loop
        if result is not None:
            break

        # If result is None, increment the retry counter and wait before retrying
        retries += 1
        print(f"||Retry {retries}/{max_retries} failed. Retrying in {delay} seconds...||")
        time.sleep(delay)
        Status += "Maximumm Retry Exceeded!"

    try:
        print(f"||Status of VTAF Crew {Status}||")
        crew_result = result.tasks_output

        # Loop through tasks and find the QA Engineer's feedback
        for task in crew_result:
            if "Quality Assurance Engineer" in str(task.agent):
                agent_qa_feedback = task.raw
                break  # Exit loop once found

        testcases = result["testcases"]
        for testcase in testcases:
            test_description = testcase.get("testcase_description", "")
            test_action = testcase.get("test_action", {})
            test_expectation = testcase.get("test_expectation", "")
            testcase_methodology = testcase.get("testcase_creation_methodology", "")
            steps = []
            if "Precondition" in test_action:
                steps.append(f"Precondition:\n {test_action['Precondition']}\n")
            if "Testprocedure" in test_action:
                steps.append(f"Test Procedure:\n {test_action['Testprocedure']}\n")
            if "Reset" in test_action:
                steps.append(f"Reset:\n {test_action['Reset']}")

            processed_test_case = {
                "test_description": test_description,
                "steps": "\n".join(steps),
                "expectation": test_expectation,
                "methodology": testcase_methodology,
            }
            processed_test_cases.append(processed_test_case)
    except Exception as e:
        print(f"Unexpected Exception {str(e)}")
        print("||Warning: 'testcases' is missing or not a list. Defaulting to an empty list.||")
        processed_test_case = {
            "test_description": "Error in Agent Execution",
            "steps": f"Precondition:\n Test Procedure:\n1.Error:{e}\n 2. VTAF Crew Status:{Status} in Creating the testcase by VTAF AI -> {CLOUD_RUN_EXECUTION} \n Reset:",
            "expectation": "1. NA",
            "methodology": "NA",
        }
        processed_test_cases.append(processed_test_case)

    return processed_test_cases, result, agent_qa_feedback  # Return the list of dictionaries


def generate_test_cases(IN_FILE):
    """
    Generate a list of test cases based on feature, sensor, algorithm, and selected rows.
    """
    test_cases = []
    data = {}
    agent_qa_feedbacks = []

    if os.path.exists(IN_FILE):
        print(f"File '{IN_FILE}' exists.")

        with open(IN_FILE, 'r') as json_file:
            data = json.load(json_file)

        # Assign values to variables as strings
        feature = str(data.get("feature", ""))
        sensor = str(data.get("sensors", ""))
        algorithm = str(data.get("algorithms", ""))
        selected_reqs = data.get("requirements", [])
        project_uuid, variant_uuid, euf_feature_uuid = data.get("project_uuid", ""), data.get("variant_uuid",
                                                                                              ""), data.get(
            "euf_feature_uuid", "")

        if project_uuid and variant_uuid and euf_feature_uuid:

            AGENTS_CONFIG_PATH = f"/mnt/vtaf-ai/vtaf-ai/user_input/{FILE_NAME}_agents.yml"
            TASKS_CONFIG_PATH = f"/mnt/vtaf-ai/vtaf-ai/user_input/{FILE_NAME}_tasks.yml"
            if not os.path.exists(AGENTS_CONFIG_PATH) and not os.path.exists(TASKS_CONFIG_PATH):
                print(f"Files '{AGENTS_CONFIG_PATH}' and '{TASKS_CONFIG_PATH}' does not exist.")
                return test_cases

        else:
            AGENTS_CONFIG_PATH = "vtaf_agent/config/agents.yaml"
            TASKS_CONFIG_PATH = "vtaf_agent/config/tasks.yaml"

        TOTAL = len(selected_reqs)

        update_query = f"""
        UPDATE `{SERVICES_PROJECT_ID}.{SERVICES_DATASET_ID}.TC_JOB_HISTORY`
        SET trace_url = '', trace_id = '{CLOUD_RUN_EXECUTION}', total_req = {int(TOTAL)}, completed_req = 0
        WHERE execution_id="{CLOUD_RUN_EXECUTION}";
        """
        bq_update_query(update_query)

        # Loop through each item and index
        for req in enumerate(selected_reqs, start=1):
            nested_dict = req[1]
            # lowest_value_key = min(nested_dict, key=lambda k: len(nested_dict[k]))
            req_id = next(iter(nested_dict.values()))

            print(f"╭──────────Generating Testcase for Requirement ID: {req_id}──────────╮\n||\n||")
            # Generate test case data for each row in the selected rows
            test_data, result, agent_qa_feedback = generate_test_case(feature, sensor, algorithm, req[1], project_uuid, variant_uuid,
                                                   euf_feature_uuid, AGENTS_CONFIG_PATH, TASKS_CONFIG_PATH)

            #saving geneated Testcase in the form of JSON
            os.makedirs(f"{workspace_path}/TestCases", exist_ok=True)
            with open(f"{workspace_path}/TestCases/{req_id}.json", 'w') as file:
                json.dump(test_data, file, indent=4)

            print(f"|{req[0]} OF {TOTAL} Completed!|")

            if result is None:
                pass
            else:
                token_usage = result.token_usage
                # Update total token usage
                total_token_usage["total_tokens"] += int(token_usage.total_tokens)
                total_token_usage["prompt_tokens"] += int(token_usage.prompt_tokens)
                total_token_usage["cached_prompt_tokens"] += int(token_usage.cached_prompt_tokens)
                total_token_usage["completion_tokens"] += int(token_usage.completion_tokens)
                total_token_usage["successful_requests"] += int(token_usage.successful_requests)

            update_query = f"""
            UPDATE `{SERVICES_PROJECT_ID}.{SERVICES_DATASET_ID}.TC_JOB_HISTORY`
            SET total_req = {int(TOTAL)}, completed_req = {int(req[0])}
            WHERE execution_id="{CLOUD_RUN_EXECUTION}";
            """
            bq_update_query(update_query)

            for test_step in test_data:
                steps = test_step.get("steps", "No steps provided")
                description = test_step.get("test_description", "No description provided")
                expectation = test_step.get("expectation", "No expectation provided")
                methodology = test_step.get("methodology", "No methodology provided")

                # Create a dictionary for the individual test case
                test_case = {
                    "id": f"TC_{len(test_cases) + 1:02d}",  # Generate a unique test case ID (e.g., TC01, TC02...)
                    "test_description": description,  # Test description based on row data
                    "steps": steps,  # Test actions from the extracted data
                    "expectation": expectation,  # Expectation based on row data
                    "methodology": methodology,  # Test methodology description
                    "req_id": req_id,
                }
                test_cases.append(test_case)
            feed_back = {
                "REQ_ID": req_id,
                "AI_Agent_QA_feedback": agent_qa_feedback
            }
            agent_qa_feedbacks.append(feed_back)
            print("||\n||\n╰───────────────────────────────────────────────────────────────────────────╯")
        lf.flush()
        return test_cases, agent_qa_feedbacks
    else:
        print(f"File '{IN_FILE}' does not exist.")
        return test_cases


def fetch_row_with_query():
    query = f"""
    SELECT user_email FROM `{SERVICES_PROJECT_ID}.{SERVICES_DATASET_ID}.TC_JOB_HISTORY` 
    WHERE execution_id = '{CLOUD_RUN_EXECUTION}'
    """
    retries = 0
    max_retries = 5
    base_delay = 5  # seconds

    while retries < max_retries:
        try:
            rows = bq_select_query(query)  # Your BigQuery query execution function
            if rows:
                print("The requested Job is available in BigQuery")
                os.environ["USER_EMAIL"] = rows[0][0]
                return rows  # Return the result directly
            else:
                print("The requested Job is not available in BigQuery")
        except Exception as e:
            print(f"An error occurred during the query execution: {e}")

        retries += 1
        wait_time = base_delay * (2 ** retries)
        print(f"Waiting for {wait_time} seconds before retrying...")
        time.sleep(wait_time)

    # If after all retries no rows were found, return None or an empty list
    print("Max retries reached, returning no results.")
    return None


# Start script
def main():
    print(
        "<=========================Cloud Run Job Execution Started with ID: **" + CLOUD_RUN_EXECUTION + "**=========================>")
    """
    Update BQ Table here with: execution_status: running 
    """
    result = fetch_row_with_query()

    time.sleep(10)

    start_timestamp = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
    start_query = f"""
    UPDATE `{SERVICES_PROJECT_ID}.{SERVICES_DATASET_ID}.TC_JOB_HISTORY`
    SET job_start_time = '{start_timestamp}', execution_status = 'running', llm_model = '{MODEL}', llm_location = '{LOCATION}'
    WHERE execution_id= '{CLOUD_RUN_EXECUTION}';
    """
    bq_update_query(start_query)

    if result is None:
        print("Job is not created in the BigQuery")

        time.sleep(10)
        #waiting for previous query execution

        completed_timestamp = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        # Calculate the duration between the two timestamps
        duration = datetime.datetime.strptime(completed_timestamp,
                                              "%Y-%m-%dT%H:%M:%S.%fZ") - datetime.datetime.strptime(start_timestamp,
                                                                                                    "%Y-%m-%dT%H:%M:%S.%fZ")
        days = duration.days
        hours, remainder = divmod(duration.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)

        completed_query = f"""
        UPDATE `{SERVICES_PROJECT_ID}.{SERVICES_DATASET_ID}.TC_JOB_HISTORY`
        SET job_start_time = '{start_timestamp}', job_end_time = '{completed_timestamp}', status = 'failed', job_duration = '{days}D {hours}H {minutes}M {seconds}S', execution_status = 'completed', output_json = '"Error occurred in the Execution": Job is not created in BigQuery.', output_csv = 'NA', total_token = {int(total_token_usage["total_tokens"])}, prompt_token = {int(total_token_usage["prompt_tokens"])}, cached_prompt_token = {int(total_token_usage["cached_prompt_tokens"])}, completion_token = {int(total_token_usage["completion_tokens"])}, successfull_agent_request = {int(total_token_usage["successful_requests"])}
        WHERE execution_id="{CLOUD_RUN_EXECUTION}";
        """
        bq_update_query(completed_query)
    else:
        print("Job is created in the BigQuery")

        #Creating a workspace folder for maintaining the Job activities
        os.makedirs(workspace_path, exist_ok=True)
        print("Workspace Folder created successfully!")

        try:
            #Saving the input file in the workspace folder
            input_file = f"/mnt/vtaf-ai/vtaf-ai/user_input/{FILE_NAME}.json"


            #generating testcases
            print("Generating Testcases Started...")
            testcases, agent_qa_feedbacks = generate_test_cases(input_file)

            #saving geneated Testcase in the form of JSON
            with open(f"{workspace_path}/{CLOUD_RUN_EXECUTION}_TestCases.json", 'w') as file:
                json.dump(testcases, file, indent=4)

            #Saving the Generated Testcase in the form of CSV
            testcases_df = pd.json_normalize(testcases)
            feedbacks_df = pd.json_normalize(agent_qa_feedbacks)

            with pd.ExcelWriter(f'{workspace_path}/{CLOUD_RUN_EXECUTION}_TestCases.xlsx') as writer:
                testcases_df.to_excel(writer, sheet_name='Testcases', index=False)
                feedbacks_df.to_excel(writer, sheet_name='Agent_QA_Feedbacks', index=False)

            output_json = f"{bucket_prefix}/{CLOUD_RUN_EXECUTION}_TestCases.json"
            output_xlsx = f"{bucket_prefix}/{CLOUD_RUN_EXECUTION}_TestCases.xlsx"

            update_gcs_file_metadata(output_json)
            update_gcs_file_metadata(output_xlsx)
            uploader = DriveUploader(gcp_secret_manager)
            file_id, status = uploader.upload_excel_as_sheet(f'{workspace_path}/{CLOUD_RUN_EXECUTION}_TestCases.xlsx')
            if status:
                raise RuntimeError("❌ Failed to upload file to Google Drive.")
            print("Generated Testcases saved into the workspace folder!")

            time.sleep(10)
            # Read the CSV file into a DataFrame
            num_of_tc = len(testcases)

            print(f"Number of Testcases generated: {num_of_tc}.")

            completed_timestamp = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
            # Calculate the duration between the two timestamps
            duration = datetime.datetime.strptime(completed_timestamp,
                                                  "%Y-%m-%dT%H:%M:%S.%fZ") - datetime.datetime.strptime(start_timestamp,
                                                                                                        "%Y-%m-%dT%H:%M:%S.%fZ")
            days = duration.days
            hours, remainder = divmod(duration.seconds, 3600)
            minutes, seconds = divmod(remainder, 60)

            completed_query = f"""
            UPDATE `{SERVICES_PROJECT_ID}.{SERVICES_DATASET_ID}.TC_JOB_HISTORY`
            SET job_start_time = '{start_timestamp}', job_end_time = '{completed_timestamp}', status = 'passed', job_duration = '{days}D {hours}H {minutes}M {seconds}S', execution_status = 'completed', output_json = '{output_json}', output_csv = '{file_id}', total_token = {int(total_token_usage["total_tokens"])}, prompt_token = {int(total_token_usage["prompt_tokens"])}, cached_prompt_token = {int(total_token_usage["cached_prompt_tokens"])}, completion_token = {int(total_token_usage["completion_tokens"])}, successfull_agent_request = {int(total_token_usage["successful_requests"])}, num_of_tc = {int(num_of_tc)}
            WHERE execution_id="{CLOUD_RUN_EXECUTION}";
            """
            bq_update_query(completed_query)
            print("Execution Completed Successfully!")

            #waiting for previous query execution
            time.sleep(10)

            fetch_query = f"""
            SELECT user_email, bg, pg, pl, total_req, completed_req, user_upload, user_input
            FROM `{SERVICES_PROJECT_ID}.{SERVICES_DATASET_ID}.TC_JOB_HISTORY`
            WHERE execution_id = '{CLOUD_RUN_EXECUTION}'
            """
            userdetails = bq_select_query(fetch_query)

            information = {
                "job_start_time": f"{start_timestamp}",
                "job_end_time": f"{completed_timestamp}",
                "job_duration": f"{days}D {hours}H {minutes}M {seconds}S",
                "bg": f"{userdetails[0][1]}",
                "pg": f"{userdetails[0][2]}",
                "pl": f"{userdetails[0][3]}",
                "job_status": "Passed",
                "execution_status": "Completed",
                "output_json": f"{output_json}",
                "output_csv": f"{DriveUploader.get_drive_file_url(file_id)}",
                "total_token": f"{total_token_usage['total_tokens']}",
                "prompt_token": f"{total_token_usage['prompt_tokens']}",
                "cached_prompt_token": f"{total_token_usage['cached_prompt_tokens']}",
                "completion_token": f"{total_token_usage['completion_tokens']}",
                "successful_agent_request": f"{total_token_usage['successful_requests']}",
                "total_req": f"{userdetails[0][4]}",
                "completed_req": f"{userdetails[0][5]}",
                "execution_id": f"{CLOUD_RUN_EXECUTION}",
                "user_upload": f"{userdetails[0][6]}",
                "user_input": f"{userdetails[0][7]}",
                "model": f"{MODEL}",
                "location": f"{LOCATION}"
            }
            send_email_toVTAF_User(userdetails[0][0], information)

        except Exception as e:

            completed_timestamp = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
            # Calculate the duration between the two timestamps
            duration = datetime.datetime.strptime(completed_timestamp,
                                                  "%Y-%m-%dT%H:%M:%S.%fZ") - datetime.datetime.strptime(start_timestamp,
                                                                                                        "%Y-%m-%dT%H:%M:%S.%fZ")
            days = duration.days
            hours, remainder = divmod(duration.seconds, 3600)
            minutes, seconds = divmod(remainder, 60)

            time.sleep(15)
            #waiting for previous query execution

            failed_query = f"""
            UPDATE `{SERVICES_PROJECT_ID}.{SERVICES_DATASET_ID}.TC_JOB_HISTORY`
            SET job_end_time = '{completed_timestamp}', status = 'failed', job_duration = '{days}D {hours}H {minutes}M {seconds}S', execution_status = 'completed', output_json = 'Error occurred in the Execution', output_csv = 'NA'
            WHERE execution_id="{CLOUD_RUN_EXECUTION}";
            """
            bq_update_query(failed_query)

            #waiting for previous query execution
            time.sleep(10)

            fetch_query = f"""
            SELECT user_email, bg, pg, pl, total_req, completed_req, user_upload, user_input
            FROM `{SERVICES_PROJECT_ID}.{SERVICES_DATASET_ID}.TC_JOB_HISTORY`
            WHERE execution_id = '{CLOUD_RUN_EXECUTION}'
            """
            userdetails = bq_select_query(fetch_query)

            information = {
                "job_start_time": f"{start_timestamp}",
                "job_end_time": f"{completed_timestamp}",
                "job_duration": f"{days}D {hours}H {minutes}M {seconds}S",
                "bg": f"{userdetails[0][1]}",
                "pg": f"{userdetails[0][2]}",
                "pl": f"{userdetails[0][3]}",
                "job_status": "Failed",
                "execution_status": "Completed",
                "output_json": f'"Error occurred in the Execution": {e}',
                "output_csv": "NA",
                "total_token": f"{total_token_usage['total_tokens']}",
                "prompt_token": f"{total_token_usage['prompt_tokens']}",
                "cached_prompt_token": f"{total_token_usage['cached_prompt_tokens']}",
                "completion_token": f"{total_token_usage['completion_tokens']}",
                "successful_agent_request": f"{total_token_usage['successful_requests']}",
                "total_req": f"{userdetails[0][4]}",
                "completed_req": f"{userdetails[0][5]}",
                "execution_id": f"{CLOUD_RUN_EXECUTION}",
                "user_upload": f"{userdetails[0][6]}",
                "user_input": f"{userdetails[0][7]}",
                "model": f"{MODEL}",
                "location": f"{LOCATION}"
            }
            send_email_toVTAF_User(userdetails[0][0], information)
            print(f"Error occurred in the Execution: {e}")

    print("<=========================Cloud Run Job Execution Ended=========================>")


if __name__ == "__main__":
    main()
