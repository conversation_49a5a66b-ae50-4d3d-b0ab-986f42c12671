<!-- <template>
  <div class="marquee_content text-h3 text-weight-medium text-light-blue-10">
    Across 10+ projects used VTAF.AI<br />backbone of quality software delivery
  </div>
  <div class="marquee_content_below text-grey-7">
    Lorem ipsum dolor sit amet consectetur, adipisicing elit. Quibusdam error
    asperiores<br />
    optio at, dolorem commodi quasi nisi dicta? Officiis, corporis!
  </div>
  <div class="marquee" v-if="$q.screen.gt.xs">
    <div class="track">
      <template v-for="duplicate in [1, 2]">
        <q-card
          v-for="item in items"
          :key="`item-${item.id}-${duplicate}`"
          class="my-card"
        >
          <q-card-section>
            <img :src="item.image" alt="Item Image" class="card-image" />
          </q-card-section>
        </q-card>
      </template>
    </div>
  </div>
  <div class="marquee_small q-pa-md" v-else>
    <q-carousel
      class="small_carousel rounded-borders"
      animated
      v-model="slide"
      arrows
      navigation
      infinite
      swipeable
      control-color="deep-purple-10"
    >
      <q-carousel-slide :name="1">
        <q-card class="small_card">
          <q-card-section>
            <div class="row small_row">
              <div class="col small_col">
                <q-avatar size="140px"
                  ><img src="src/assets/home/<USER>"
                /></q-avatar>
              </div>
              <div class="col small_col">
                <q-avatar size="140px"
                  ><img src="src/assets/home/<USER>"
                /></q-avatar>
              </div>
            </div>
            <div class="row small_row">
              <div class="col small_col">
                <q-avatar size="140px"
                  ><img src="src/assets/home/<USER>"
                /></q-avatar>
              </div>
              <div class="col small_col">
                <q-avatar size="140px"
                  ><img src="src/assets/home/<USER>"
                /></q-avatar>
              </div>
            </div>
            <div class="row small_row">
              <div class="col small_col">
                <q-avatar size="140px"
                  ><img src="src/assets/home/<USER>"
                /></q-avatar>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </q-carousel-slide>
      <q-carousel-slide :name="2">
        <q-card class="small_card">
          <q-card-section>
            <div class="row small_row">
              <div class="col small_col">
                <q-avatar size="140px"
                  ><img src="src/assets/home/<USER>"
                /></q-avatar>
              </div>
              <div class="col small_col">
                <q-avatar size="140px"
                  ><img src="src/assets/home/<USER>"
                /></q-avatar>
              </div>
            </div>
            <div class="row small_row">
              <div class="col small_col">
                <q-avatar size="140px"
                  ><img src="src/assets/home/<USER>"
                /></q-avatar>
              </div>
              <div class="col small_col">
                <q-avatar size="140px"
                  ><img src="src/assets/home/<USER>"
                /></q-avatar>
              </div>
            </div>
            <div class="row small_row">
              <div class="col small_col">
                <q-avatar size="140px"
                  ><img src="src/assets/home/<USER>"
                /></q-avatar>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </q-carousel-slide>
      <q-carousel-slide :name="3">
        <q-card class="small_card">
          <q-card-section>
            <div class="row small_row">
              <div class="col small_col">
                <q-avatar square size="100px"
                  ><img src="src/assets/home/<USER>"
                /></q-avatar>
              </div>
              <div class="col small_col">
                <q-avatar square size="100px"
                  ><img src="src/assets/home/<USER>"
                /></q-avatar>
              </div>
            </div>
            <div class="row small_row">
              <div class="col small_col">
                <q-avatar square size="100px"
                  ><img src="src/assets/home/<USER>"
                /></q-avatar>
              </div>
              <div class="col small_col">
                <q-avatar square size="140px"
                  ><img src="src/assets/home/<USER>"
                /></q-avatar>
              </div>
            </div>
            <div class="row small_row" style="margin-bottom: 1rem">
              <div class="col small_col">
                <q-avatar square size="140px"
                  ><img src="src/assets/home/<USER>"
                /></q-avatar>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </q-carousel-slide>
      <q-carousel-slide :name="4">
        <q-card class="small_card">
          <q-card-section>
            <div class="row small_row">
              <div class="col small_col">
                <q-avatar size="140px"
                  ><img src="src/assets/home/<USER>"
                /></q-avatar>
              </div>
              <div class="col small_col">
                <q-avatar size="140px"
                  ><img src="src/assets/home/<USER>"
                /></q-avatar>
              </div>
            </div>
            <div class="row small_row">
              <div class="col small_col">
                <q-avatar size="140px"
                  ><img src="src/assets/home/<USER>"
                /></q-avatar>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </q-carousel-slide>
    </q-carousel>
  </div>
</template>

<script setup>
import { ref } from "vue";

const slide = ref(1);

const items = ref([
  {
    id: 1,
    title: "Card 1",
    image: "src/assets/home/<USER>",
  },
  {
    id: 2,
    title: "Card 2",
    image: "src/assets/home/<USER>",
  },
  {
    id: 3,
    title: "Card 3",
    image: "src/assets/home/<USER>",
  },
  {
    id: 4,
    title: "Card 4",
    image: "src/assets/home/<USER>",
  },
  {
    id: 5,
    title: "Card 5",
    image: "src/assets/home/<USER>",
  },
  {
    id: 6,
    title: "Card 6",
    image: "src/assets/home/<USER>",
  },
  {
    id: 7,
    title: "Card 7",
    image: "src/assets/home/<USER>",
  },
  {
    id: 8,
    title: "Card 8",
    image: "src/assets/home/<USER>",
  },
  {
    id: 9,
    title: "Card 9",
    image: "src/assets/home/<USER>",
  },
  {
    id: 10,
    title: "Card 10",
    image: "src/assets/home/<USER>",
  },
  {
    id: 11,
    title: "Card 10",
    image: "src/assets/home/<USER>",
  },
  {
    id: 12,
    title: "Card 10",
    image: "src/assets/home/<USER>",
  },
  {
    id: 13,
    title: "Card 10",
    image: "src/assets/home/<USER>",
  },
  {
    id: 14,
    title: "Card 10",
    image: "src/assets/home/<USER>",
  },
  {
    id: 15,
    title: "Card 10",
    image: "src/assets/home/<USER>",
  },
  {
    id: 16,
    title: "Card 10",
    image: "src/assets/home/<USER>",
  },
  {
    id: 17,
    title: "Card 10",
    image: "src/assets/home/<USER>",
  },
  {
    id: 18,
    title: "Card 10",
    image: "src/assets/home/<USER>",
  },
]);
</script>

<style scoped>
.marquee {
  margin: 0 auto;
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 160px;
}

.my-card {
  min-width: 200px;
  margin-right: 20px;
  height: 150px;
  border: 1px solid #228fc9;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

@keyframes marquee {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-100%);
  }
}

@media screen and (max-width: 768px) {
  /* Adjust animation speed for smaller screens */
  @keyframes marquee {
    0% {
      transform: translateX(0%);
    }
    100% {
      transform: translateX(-700%);
    }
  }
  .marquee_content {
    /* Add margin-left for small screens */
    margin-left: 5%;
    margin-right: 5%;
  }
  .marquee_content_below {
    margin-left: 5%;
    margin-right: 5%;
  }
}

.track {
  display: flex;
  white-space: nowrap;
  animation: marquee 40s linear infinite;
}

.marquee_content {
  text-align: center;
  margin-top: 3rem;
  margin-bottom: 1rem;
}
.marquee_content_below {
  text-align: center;
  margin-bottom: 3rem;
  font-size: 20px;
}
.card-image {
  width: 100%;
  display: flex;
  margin: 0 auto;
}
.small_card {
  height: 100%;
  animation: scaleIn 0.5s ease-out forwards;
}
.small_row {
  text-align: center;
  margin-top: 1rem;
}
.small_carousel {
  height: 100%;
  background: linear-gradient(to bottom, #014a88, #000);
}
.small_col {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
@keyframes scaleIn {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style> -->

<template>
  <div class="container">
    <div class="marquee_content text-h3 text-weight-medium">
      Across 10+ projects used VTAF.AI<br />backbone of quality software
      delivery
    </div>
    <div class="marquee_content_below">
      Lorem ipsum dolor sit amet consectetur, adipisicing elit. Quibusdam error
      asperiores<br />
      optio at, dolorem commodi quasi nisi dicta? Officiis, corporis!
    </div>
    <div class="slider" v-if="$q.screen.gt.xs">
      <div class="slide-track">
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" style="width: 70%" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <!-- doubled slide -->
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" style="width: 70%" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
        <div class="slide">
          <img src="src/assets/home/<USER>" alt="" />
        </div>
      </div>
    </div>
    <div class="marquee_small q-pa-md" v-else>
      <q-carousel
        class="small_carousel rounded-borders"
        animated
        v-model="slide"
        arrows
        navigation
        infinite
        swipeable
        control-color="deep-purple-10"
      >
        <q-carousel-slide :name="1">
          <q-card class="small_card">
            <q-card-section>
              <div class="row small_row">
                <div class="col small_col">
                  <q-avatar size="140px"
                    ><img src="src/assets/home/<USER>"
                  /></q-avatar>
                </div>
                <div class="col small_col">
                  <q-avatar size="140px"
                    ><img src="src/assets/home/<USER>"
                  /></q-avatar>
                </div>
              </div>
              <div class="row small_row">
                <div class="col small_col">
                  <q-avatar size="140px"
                    ><img src="src/assets/home/<USER>"
                  /></q-avatar>
                </div>
                <div class="col small_col">
                  <q-avatar size="140px"
                    ><img src="src/assets/home/<USER>"
                  /></q-avatar>
                </div>
              </div>
              <div class="row small_row">
                <div class="col small_col">
                  <q-avatar size="140px"
                    ><img src="src/assets/home/<USER>"
                  /></q-avatar>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </q-carousel-slide>
        <q-carousel-slide :name="2">
          <q-card class="small_card">
            <q-card-section>
              <div class="row small_row">
                <div class="col small_col">
                  <q-avatar size="140px"
                    ><img src="src/assets/home/<USER>"
                  /></q-avatar>
                </div>
                <div class="col small_col">
                  <q-avatar size="140px"
                    ><img src="src/assets/home/<USER>"
                  /></q-avatar>
                </div>
              </div>
              <div class="row small_row">
                <div class="col small_col">
                  <q-avatar size="140px"
                    ><img src="src/assets/home/<USER>"
                  /></q-avatar>
                </div>
                <div class="col small_col">
                  <q-avatar size="140px"
                    ><img src="src/assets/home/<USER>"
                  /></q-avatar>
                </div>
              </div>
              <div class="row small_row">
                <div class="col small_col">
                  <q-avatar size="140px"
                    ><img src="src/assets/home/<USER>"
                  /></q-avatar>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </q-carousel-slide>
        <q-carousel-slide :name="3">
          <q-card class="small_card">
            <q-card-section>
              <div class="row small_row">
                <div class="col small_col">
                  <q-avatar square size="100px"
                    ><img src="src/assets/home/<USER>"
                  /></q-avatar>
                </div>
                <div class="col small_col">
                  <q-avatar square size="100px"
                    ><img src="src/assets/home/<USER>"
                  /></q-avatar>
                </div>
              </div>
              <div class="row small_row">
                <div class="col small_col">
                  <q-avatar square size="100px"
                    ><img src="src/assets/home/<USER>"
                  /></q-avatar>
                </div>
                <div class="col small_col">
                  <q-avatar square size="140px"
                    ><img src="src/assets/home/<USER>"
                  /></q-avatar>
                </div>
              </div>
              <div class="row small_row" style="margin-bottom: 1rem">
                <div class="col small_col">
                  <q-avatar square size="140px"
                    ><img src="src/assets/home/<USER>"
                  /></q-avatar>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </q-carousel-slide>
        <q-carousel-slide :name="4">
          <q-card class="small_card">
            <q-card-section>
              <div class="row small_row">
                <div class="col small_col">
                  <q-avatar size="140px"
                    ><img src="src/assets/home/<USER>"
                  /></q-avatar>
                </div>
                <div class="col small_col">
                  <q-avatar size="140px"
                    ><img src="src/assets/home/<USER>"
                  /></q-avatar>
                </div>
              </div>
              <div class="row small_row">
                <div class="col small_col">
                  <q-avatar size="140px"
                    ><img src="src/assets/home/<USER>"
                  /></q-avatar>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </q-carousel-slide>
      </q-carousel>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
const slide = ref(1);

const items = ref([
  {
    id: 1,
    title: "Card 1",
    image: "src/assets/home/<USER>",
  },
  {
    id: 2,
    title: "Card 2",
    image: "src/assets/home/<USER>",
  },
  {
    id: 3,
    title: "Card 3",
    image: "src/assets/home/<USER>",
  },
  {
    id: 4,
    title: "Card 4",
    image: "src/assets/home/<USER>",
  },
  {
    id: 5,
    title: "Card 5",
    image: "src/assets/home/<USER>",
  },
  {
    id: 6,
    title: "Card 6",
    image: "src/assets/home/<USER>",
  },
  {
    id: 7,
    title: "Card 7",
    image: "src/assets/home/<USER>",
  },
  {
    id: 8,
    title: "Card 8",
    image: "src/assets/home/<USER>",
  },
  {
    id: 9,
    title: "Card 9",
    image: "src/assets/home/<USER>",
  },
  {
    id: 10,
    title: "Card 10",
    image: "src/assets/home/<USER>",
  },
  {
    id: 11,
    title: "Card 10",
    image: "src/assets/home/<USER>",
  },
  {
    id: 12,
    title: "Card 10",
    image: "src/assets/home/<USER>",
  },
  {
    id: 13,
    title: "Card 10",
    image: "src/assets/home/<USER>",
  },
  {
    id: 14,
    title: "Card 10",
    image: "src/assets/home/<USER>",
  },
  {
    id: 15,
    title: "Card 10",
    image: "src/assets/home/<USER>",
  },
  {
    id: 16,
    title: "Card 10",
    image: "src/assets/home/<USER>",
  },
  {
    id: 17,
    title: "Card 10",
    image: "src/assets/home/<USER>",
  },
  {
    id: 18,
    title: "Card 10",
    image: "src/assets/home/<USER>",
  },
]);
</script>

<style scoped>
.container {
  background: linear-gradient(to top, #014a88, #000);
}
.slider {
  height: 250px;
  margin: auto;
  position: relative;
  width: 100%;
  display: grid;
  place-items: center;
  overflow: hidden;
}

.slide-track {
  display: flex;
  width: calc(250px * 36);
  animation: scroll 80s linear infinite;
}

.slide-track:hover {
  animation-play-state: paused;
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(-250px * 18));
  }
}

.slide {
  background: white;
  border-radius: 0.5rem;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
  border: 1px solid blue;
  width: 80%;
  display: flex;
  align-items: center;
  perspective: 100px;
}

img {
  display: flex;
  margin: 0 auto;
  width: 80%;
  transition: transform 1s;
}

img:hover {
  transform: translateZ(10px);
}

.slider::before,
.slider::after {
  content: "";
  height: 100%;
  position: absolute;
  width: 15%;
  z-index: 2;
}

.slider::before {
  left: 0;
  top: 0;
}

.slider::after {
  right: 0;
  top: 0;
  transform: rotateZ(180deg);
}
.marquee_content {
  font-family: "Courier New", Courier, monospace;
  padding-top: 3rem;
  color: aliceblue;
  text-align: center;
  margin-bottom: 1rem;
}
.marquee_content_below {
  color: aliceblue;
  text-align: center;
  margin-bottom: 2rem;
  font-size: 20px;
}
.card-image {
  width: 100%;
  display: flex;
  margin: 0 auto;
}
.small_card {
  height: 100%;
  animation: scaleIn 0.5s ease-out forwards;
}
.small_row {
  text-align: center;
  margin-top: 1rem;
}
.small_carousel {
  height: 100%;
  background: linear-gradient(to bottom, #014a88, #000);
}
.small_col {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
@keyframes scaleIn {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
@media screen and (max-width: 768px) {
  /* Adjust animation speed for smaller screens */
  @keyframes marquee {
    0% {
      transform: translateX(0%);
    }
    100% {
      transform: translateX(-700%);
    }
  }
  .marquee_content {
    /* Add margin-left for small screens */
    margin-left: 5%;
    margin-right: 5%;
  }
  .marquee_content_below {
    margin-left: 5%;
    margin-right: 5%;
  }
}
</style>
