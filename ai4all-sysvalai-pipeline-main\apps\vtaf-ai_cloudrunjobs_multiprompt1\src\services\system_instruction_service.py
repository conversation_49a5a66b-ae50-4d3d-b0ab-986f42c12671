#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
System Instruction Service - 系统指令服务
对应原始ai_generate.py中的change_system_instruction()方法
处理系统指令设置任务
"""

import os
from typing import Dict, Any, List, Optional
from utils.logger import get_logger
from services.ai_service import AIService
from services.config_service import ConfigurationService

logger = get_logger(__name__)

class SystemInstructionService:
    """系统指令服务"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.config_service = ConfigurationService()
        
    def execute(self) -> int:
        """执行系统指令设置任务"""
        try:
            logger.info("🚀 Starting system instruction job")
            
            # 获取配置
            config = self._get_config()
            self._validate_config(config)
            
            # 获取系统指令
            instructions = self._get_system_instructions(config)
            
            if not instructions:
                logger.warning("No system instructions provided")
                return 1
            
            # 更新系统指令
            result = self._update_system_instructions(instructions)
            
            if result:
                logger.info("✅ System instruction job completed successfully")
                return 0
            else:
                logger.error("❌ System instruction job failed")
                return 1
            
        except Exception as e:
            logger.exception(f"System instruction job failed: {e}")
            return 1
    
    def _get_config(self) -> Dict[str, Any]:
        """获取配置"""
        config = {}
        
        # 从环境变量获取
        config['system_instructions'] = os.getenv('SYSTEM_INSTRUCTIONS')  # JSON格式的指令列表
        config['instruction_file'] = os.getenv('INSTRUCTION_FILE')  # 指令文件路径
        config['instruction_mode'] = os.getenv('INSTRUCTION_MODE', 'replace')  # replace/append
        
        return config
    
    def _validate_config(self, config: Dict[str, Any]):
        """验证配置"""
        # 至少需要一种指令来源
        if not config.get('system_instructions') and not config.get('instruction_file'):
            raise ValueError("No system instructions source provided (SYSTEM_INSTRUCTIONS or INSTRUCTION_FILE)")
        
        logger.info("System instruction configuration validation passed")
    
    def _get_system_instructions(self, config: Dict[str, Any]) -> List[str]:
        """获取系统指令"""
        instructions = []
        
        # 1. 从环境变量获取（JSON格式）
        if config.get('system_instructions'):
            try:
                import json
                env_instructions = json.loads(config['system_instructions'])
                if isinstance(env_instructions, list):
                    instructions.extend(env_instructions)
                elif isinstance(env_instructions, str):
                    instructions.append(env_instructions)
                logger.info(f"Loaded {len(instructions)} instructions from environment variable")
            except Exception as e:
                logger.error(f"Failed to parse system instructions from environment: {e}")
        
        # 2. 从文件获取
        if config.get('instruction_file'):
            file_instructions = self._load_instructions_from_file(config['instruction_file'])
            instructions.extend(file_instructions)
        
        # 3. 如果没有指令，使用默认指令
        if not instructions:
            instructions = self._get_default_instructions()
        
        logger.info(f"Total system instructions loaded: {len(instructions)}")
        return instructions
    
    def _load_instructions_from_file(self, file_path: str) -> List[str]:
        """从文件加载指令"""
        instructions = []
        
        try:
            if not os.path.exists(file_path):
                logger.warning(f"Instruction file not found: {file_path}")
                return instructions
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                
                # 尝试解析为JSON
                try:
                    import json
                    json_instructions = json.loads(content)
                    if isinstance(json_instructions, list):
                        instructions = json_instructions
                    elif isinstance(json_instructions, str):
                        instructions = [json_instructions]
                except json.JSONDecodeError:
                    # 如果不是JSON，按行分割
                    instructions = [line.strip() for line in content.split('\n') if line.strip()]
            
            logger.info(f"Loaded {len(instructions)} instructions from file: {file_path}")
            
        except Exception as e:
            logger.error(f"Failed to load instructions from file {file_path}: {e}")
        
        return instructions
    
    def _get_default_instructions(self) -> List[str]:
        """获取默认系统指令"""
        return [
            "You are a helpful AI assistant for test case generation and automation.",
            "Please provide clear, accurate, and detailed responses.",
            "When generating test cases, include all required fields: Test Case ID, Test Objective, Test Condition, Test Action, Test Expectation."
        ]
    
    def _update_system_instructions(self, instructions: List[str]) -> bool:
        """更新系统指令"""
        try:
            logger.info(f"Updating system instructions with {len(instructions)} instructions")
            
            # 记录指令内容
            for i, instruction in enumerate(instructions, 1):
                logger.info(f"Instruction {i}: {instruction[:100]}{'...' if len(instruction) > 100 else ''}")
            
            # 更新AI服务的系统指令
            self.ai_service.update_system_instruction(instructions)
            
            logger.info("System instructions updated successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update system instructions: {e}")
            return False
