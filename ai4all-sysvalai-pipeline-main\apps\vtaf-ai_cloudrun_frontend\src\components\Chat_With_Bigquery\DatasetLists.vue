<template>
  <div class="q-ma-md" style="max-width: 300px;">
    <q-select
label-color="blue-10"
            use-input
            input-debounce="0"
      v-model="selected_dataset"
      outlined
      :options="filteredFunctions"
            @filter="filterFn"
            emit-value
            map-options
            clearable
            label="Select Dataset"
            :loading="datasets_dropdown_loading"
    />
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useChatWithBigqueryStore } from "src/stores/chat/chat_with_bigquery-store";
import { storeToRefs } from "pinia";

const chat_with_bigquery_config = useChatWithBigqueryStore();
const { dataset_dropdown, selected_dataset, datasets_dropdown_loading} =
  storeToRefs(chat_with_bigquery_config);
const { } = chat_with_bigquery_config

// onMounted(async () => {
//   datasets_dropdown_loading.value = true;
//   await get_datasets();
//   datasets_dropdown_loading.value = false;
// });
const filterQuery = ref("");
const filteredFunctions = computed(() => {
  return dataset_dropdown.value.filter((table) =>
    table.toLowerCase().includes(filterQuery.value.toLowerCase())
  );
});
const filterFn = (val, update) => {
  filterQuery.value = val;
  update();
};
</script>
