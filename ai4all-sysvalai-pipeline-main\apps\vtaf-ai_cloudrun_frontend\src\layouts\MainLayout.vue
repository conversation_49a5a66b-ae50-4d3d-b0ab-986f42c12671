<template>
  <q-layout view="lHh Lpr lFf">
    <div>
      <q-toolbar class="text-white" style="background-color: #014a88">
        <q-btn
          v-if="$q.screen.lt.md"
          flat
          dense
          round
          icon="menu"
          aria-label="Menu"
          @click="left = !left"
        />
        <q-btn to="/" flat>
          <q-avatar size="42px">
            <img src="../assets/home/<USER>" />
          </q-avatar>
        </q-btn>
        <q-toolbar-title class="col-auto text-bold">
          VTAF<span class="text-h5 text-bold">.AI</span>
        </q-toolbar-title>
        <q-toolbar-title class="col flex flex-center" v-if="$q.screen.gt.sm">
          <q-tabs align="center">
            <q-route-tab to="/" label="Home" />
            <!-- <q-route-tab to="/products" label="Products" /> -->
            <!-- <q-route-tab to="/documentation" label="Documentation" /> -->
            <q-route-tab label="Chat" @click="openChatInNewTab" />
             <!-- <q-tab name="chats" label="chats" @mouseover="showChatMenu = true" @mouseleave="startHideTimer">
              <q-menu
                v-model="showChatMenu"
                transition-show="jump-down"
                transition-hide="jump-up"
                anchor="bottom middle"
                self="top middle"
                @mouseover="cancelHideTimer"
                @mouseleave="startHideTimer"
                class="text-white q-pa-lg"
                style="border-radius: 10px; box-shadow: 0 4px 18px rgba(0, 0, 0, 0.1); width: 70%;background-color: #044278"
              >
                <div class="row q-col-gutter-xl">
                  <div class="col">

                    <q-list dense bordered class="rounded-borders">
                      <q-item clickable v-close-popup to="/chat_with_knowledgebase" class="hoverable-item">
                        <q-item-section avatar>
                          <q-icon name="smart_toy" />
                        </q-item-section>
                        <q-item-section class="text-subtitle1">VTAF.AI - Knowledgebase</q-item-section>
                      </q-item>

                    </q-list>
                  </div>

                  <div class="col">
                    <q-list dense bordered class="rounded-borders">
                      <q-item clickable v-close-popup to="/chat_with_bigquery" class="hoverable-item">
                        <q-item-section avatar>
                          <q-icon name="cloud_circle" />
                        </q-item-section>
                        <q-item-section class="text-subtitle1">VTAF.AI - Bigquery</q-item-section>
                      </q-item>

                    </q-list>
                  </div>

                  <div class="col">
                    <div class="text-h6 text-white q-mb-sm">Vertex</div>
                    <q-list dense bordered class="rounded-borders">
                      <q-item clickable v-close-popup @click="openChatInNewTab" class="hoverable-item">
                        <q-item-section avatar>
                          <q-icon name="chat" />
                        </q-item-section>
                        <q-item-section>Chat With VDA</q-item-section>
                      </q-item>
                    </q-list>
                  </div>
                </div>
              </q-menu>
            </q-tab> -->
            <q-route-tab to="/ai_testcase" label="AI Testcase" />
            <q-route-tab to="/project_profile" label="Project Profile" />
            <q-route-tab to="/ai_testscript" label="AI Testscript" />

          </q-tabs>
        </q-toolbar-title>
        <q-space />
        <!-- Added q-space to push q-avatar to the end -->
        <!-- <span class="q-mt-md q-mr-sm">v.4.0.0</span> -->
         <q-btn
          size="15px"
          flat
          round
          dense
          icon="help_outline"
          class="q-mr-sm"
          ref="helpMenuBtn"
        >
          <q-menu
            transition-show="rotate"
            transition-hide="rotate"
            class="bg-white text-blue-10"
          >
            <q-list style="min-width: 150px">
              <q-item
                clickable
                v-close-popup
                @click="
                  openLink(
                    'https://docs.google.com/document/d/1lG1agAOkKZxKIQ_rlyszqgqoyz8_wcsCnWrIj6-s-Sw/edit?usp=sharing'
                  )
                "
              >
                <q-item-section>User Manual</q-item-section>
              </q-item>
              <q-item
                clickable
                v-close-popup
                @click="
                  openLink(
                    'https://issupport.valeo.com/servicedesk/customer/portal/145'
                  )
                "
              >
                <q-item-section>Get Support</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
        <q-avatar size="35px" color="red-7" text-color="white">
          {{ first_letter_email }}
          <q-tooltip
            anchor="bottom middle"
            self="top middle"
            class="bg-white text-blue-10 text-body1"
          >
            {{ main_email }}
          </q-tooltip>
        </q-avatar>
      </q-toolbar>
    </div>

    <q-drawer
      show-if-above
      v-model="left"
      side="left"
      bordered
      v-if="$q.screen.lt.md"
    >
      <q-list>
        <q-item clickable to="/" exact>
          <q-item-section avatar>
            <q-icon name="home" />
          </q-item-section>
          <q-item-section> Home </q-item-section>
        </q-item>
        <q-item clickable to="/chat" exact>
          <q-item-section avatar>
            <q-icon name="message" />
          </q-item-section>
          <q-item-section> Chat </q-item-section>
        </q-item>
        <q-item clickable to="/ai_testcase" exact>
          <q-item-section avatar>
            <q-icon name="ai" />
          </q-item-section>
          <q-item-section> AI Testcase </q-item-section>
        </q-item>
        <q-item clickable to="/project_profile" exact>
          <q-item-section avatar>
            <q-icon name="ai" />
          </q-item-section>
          <q-item-section> Project Profile </q-item-section>
        </q-item>
        <q-item clickable to="/ai_testscript" exact>
          <q-item-section avatar>
            <q-icon name="ai" />
          </q-item-section>
          <q-item-section> AI Testscript </q-item-section>
        </q-item>
      </q-list>
    </q-drawer>

    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { useHomeStore } from "src/stores/home/<USER>";
import { storeToRefs } from "pinia";

const homeStore = useHomeStore();
const { main_email, first_letter_email } = storeToRefs(homeStore);

const left = ref(false);

function openLink(url) {
  window.open(url, "_blank");
}

const showChatMenu = ref(false)
let hideTimer = null

function startHideTimer () {
  hideTimer = setTimeout(() => (showChatMenu.value = false), 300)
}

function cancelHideTimer () {
  clearTimeout(hideTimer)
}

function openChatInNewTab() {
  window.open('https://cp2532.apps-dev.valeo.com/vda/chat', '_blank')
}
</script>

<style scoped>
.hoverable-item {
  transition: background-color 0.2s;
  border-radius: 8px;
}
.hoverable-item:hover {
  background: transparent;
}
.rounded-borders {
  border-radius: 8px;
}
</style>
