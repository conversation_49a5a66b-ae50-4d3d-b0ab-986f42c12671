extraction_task:
  description: >
    The Data Extraction task involves scanning through large, complex datasets—such as test requirements, reports and pull out the most critical and relevant information. 
    This includes filtering out irrelevant data, decoding encrypted files when necessary, and organizing the extracted content as a way that makes it easy to analyze or act upon. 
    The task requires precision and efficiency to ensure only the most important data is capture, 
    requirement: {requirement}
  expected_output: >
    The expected output is a json data curated, organized set of key data points extracted from the files, presented in a clear and accessible md format. 
    This could include:
      1. Relevant logs or entries from a system report
      2. Key insights from large data sets
      3. A concise summary or list of findings that highlight important details for the crew's decision-making
    The final output should ensure that no vital information is overlooked, and provided with exactly what they need to proceed with their objectives

summary_task:
  description: >
    The Summary task involves taking the extracted data and creating a concise and informative summary.
    This summary should highlight the key findings and insights from the extracted data, making it easy for the crew to understand and act upon.  
    The task requires strong summarization skills to condense large amounts of information into a clear and accessible format.
  expected_output: >
    The expected output is a concise and informative summary of the extracted data, which should include any relevant reference IDs, such as requirements, if applicable.
    This summary should highlight the key findings and insights, making it easy for the crew to understand and act upon.  
    The summary should be presented in a clear and accessible format, such as markdown.
