import vertexai
from vertexai.generative_models import GenerativeModel, GenerationConfig
from configs.env import settings
from utils.logger import get_logger
import os

logger = get_logger(__name__)

# 可选导入 caching 功能（如果可用）
try:
    from vertexai.preview import caching
    CACHING_AVAILABLE = True
except ImportError:
    CACHING_AVAILABLE = False
    logger.warning("vertexai.preview.caching not available, caching features disabled")

class GeminiClient:
    def __init__(self):
        self.model = None
        self.chat = None
        self.current_model = settings.DEFAULT_MODEL
        self.config = GenerationConfig(
            max_output_tokens=2048,
            temperature=0.5,
            top_p=1,
            top_k=32
        )
        self._initialize_vertex_ai()
        
    def _initialize_vertex_ai(self):
        """初始化 Vertex AI - 支持多种认证方式"""
        try:
            # 认证优先级：
            # 1. Google Cloud 默认认证 (在 GCP 环境中)
            # 2. 硬编码服务账号信息
            # 3. 服务账号文件

            credentials = None

            # 1. 在 Google Cloud 环境中，优先使用默认认证
            try:
                from google.auth import default
                default_creds, _ = default()

                # 检查是否在 Google Cloud 环境中
                if hasattr(default_creds, 'service_account_email'):
                    credentials = default_creds
                    logger.info("Using Google Cloud default credentials")
            except Exception as e:
                logger.debug(f"Default credentials not available: {e}")

            # 2. 如果没有默认认证，尝试硬编码服务账号
            if not credentials and hasattr(settings, 'SERVICE_ACCOUNT_INFO') and settings.SERVICE_ACCOUNT_INFO:
                from google.oauth2 import service_account
                credentials = service_account.Credentials.from_service_account_info(
                    settings.SERVICE_ACCOUNT_INFO
                )
                logger.info("Using hardcoded service account credentials")

            # 3. 如果没有硬编码认证，尝试服务账号文件
            if not credentials and os.path.exists(settings.SERVICE_ACCOUNT_FILE):
                from google.oauth2 import service_account
                credentials = service_account.Credentials.from_service_account_file(
                    settings.SERVICE_ACCOUNT_FILE
                )
                logger.info("Using service account file credentials")

            # 初始化 Vertex AI
            if credentials:
                vertexai.init(
                    project=settings.PROJECT_ID,
                    location=settings.REGION,
                    credentials=credentials
                )
            else:
                # 最后回退到默认认证（可能在本地开发环境中）
                vertexai.init(
                    project=settings.PROJECT_ID,
                    location=settings.REGION
                )
                logger.info("Using Vertex AI default authentication")

            self._load_model(self.current_model)
            logger.info("Vertex AI initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Vertex AI: {e}")
            raise
        
    def _load_model(self, model_name, system_instruction=None):
        """加载指定模型"""
        try:
            self.model = GenerativeModel(
                model_name=model_name,
                system_instruction=system_instruction or []
            )
            self.chat = self.model.start_chat(response_validation=False)
            self.current_model = model_name
            logger.info(f"Model loaded: {model_name}")
        except Exception as e:
            logger.error(f"Failed to load model {model_name}: {e}")
            raise
            
    def change_model(self, model_name, system_instruction=None, chat_history=None):
        """切换模型"""
        self._load_model(model_name, system_instruction)
        if chat_history:
            self.chat._history = chat_history
            
    def get_response(self, prompt):
        """获取AI响应"""
        try:
            response = self.chat.send_message(
                prompt, 
                stream=False, 
                generation_config=self.config
            )
            return response.text
        except Exception as e:
            logger.error(f"Failed to get response: {e}")
            return f"Error: {str(e)}"
            
    def switch_region(self, region):
        """切换区域"""
        vertexai.init(
            project=settings.PROJECT_ID,
            location=region
        )
        logger.info(f"Switched to region: {region}")