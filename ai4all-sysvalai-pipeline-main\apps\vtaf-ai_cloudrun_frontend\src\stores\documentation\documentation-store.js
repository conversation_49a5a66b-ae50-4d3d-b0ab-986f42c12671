import { ref } from "vue";
import { defineStore } from "pinia";
import { Notify, Loading } from "quasar";
import { ai_testcaseApi } from "src/boot/axios";

export const useDocumentationStore = defineStore("documentation_store", () => {
  const user_email = ref("");

  async function get_user_email() {
    try {
      Loading.show();
      const response = await ai_testcaseApi.get(`/get_user`);
      const data = response.data.data;
      user_email.value = data;
      console.log(data);
    } catch (err) {
      handleFetchError(err);
    } finally {
      Loading.hide();
    }
  }
  return {
    user_email,
    get_user_email,
  };
});
