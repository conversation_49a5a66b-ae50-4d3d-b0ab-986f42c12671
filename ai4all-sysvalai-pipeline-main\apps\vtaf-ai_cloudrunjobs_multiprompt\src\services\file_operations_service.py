#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
File Operations Service - 文件操作服务
对应原始ai_generate.py中的selectFile()等方法
处理文件操作任务
"""

import os
import shutil
from typing import Dict, Any, List, Optional
from pathlib import Path
from utils.logger import get_logger

logger = get_logger(__name__)

class FileOperationsService:
    """文件操作服务"""
    
    def __init__(self):
        self.max_file_size = 7 * 1024 * 1024  # 7MB，对应原始代码的限制
        
    def execute(self) -> int:
        """执行文件操作任务"""
        try:
            logger.info("🚀 Starting file operations job")
            
            # 获取配置
            config = self._get_config()
            self._validate_config(config)
            
            # 执行文件操作
            operation = config.get('operation', 'upload')
            
            if operation == 'upload':
                result = self._handle_file_upload(config)
            elif operation == 'download':
                result = self._handle_file_download(config)
            elif operation == 'copy':
                result = self._handle_file_copy(config)
            elif operation == 'delete':
                result = self._handle_file_delete(config)
            elif operation == 'list':
                result = self._handle_file_list(config)
            else:
                logger.error(f"Unknown file operation: {operation}")
                return 1
            
            if result:
                logger.info("✅ File operations job completed successfully")
                return 0
            else:
                logger.error("❌ File operations job failed")
                return 1
            
        except Exception as e:
            logger.exception(f"File operations job failed: {e}")
            return 1
    
    def _get_config(self) -> Dict[str, Any]:
        """获取配置"""
        config = {}
        
        # 从环境变量获取
        config['operation'] = os.getenv('FILE_OPERATION', 'upload')  # upload/download/copy/delete/list
        config['source_path'] = os.getenv('SOURCE_PATH')
        config['target_path'] = os.getenv('TARGET_PATH')
        config['file_pattern'] = os.getenv('FILE_PATTERN', '*')
        config['max_file_size'] = int(os.getenv('MAX_FILE_SIZE', str(self.max_file_size)))
        config['allowed_extensions'] = os.getenv('ALLOWED_EXTENSIONS', '').split(',') if os.getenv('ALLOWED_EXTENSIONS') else []
        
        return config
    
    def _validate_config(self, config: Dict[str, Any]):
        """验证配置"""
        operation = config.get('operation')
        
        if operation in ['upload', 'copy']:
            if not config.get('source_path'):
                raise ValueError("SOURCE_PATH is required for upload/copy operations")
            if not config.get('target_path'):
                raise ValueError("TARGET_PATH is required for upload/copy operations")
        elif operation == 'download':
            if not config.get('source_path'):
                raise ValueError("SOURCE_PATH is required for download operation")
        elif operation == 'delete':
            if not config.get('source_path'):
                raise ValueError("SOURCE_PATH is required for delete operation")
        
        logger.info("File operations configuration validation passed")
    
    def _handle_file_upload(self, config: Dict[str, Any]) -> bool:
        """处理文件上传"""
        try:
            source_path = Path(config['source_path'])
            target_path = Path(config['target_path'])
            
            logger.info(f"Uploading file from {source_path} to {target_path}")
            
            # 检查源文件是否存在
            if not source_path.exists():
                logger.error(f"Source file does not exist: {source_path}")
                return False
            
            # 检查文件大小
            file_size = source_path.stat().st_size
            if file_size > config['max_file_size']:
                logger.error(f"File size ({file_size} bytes) exceeds maximum allowed size ({config['max_file_size']} bytes)")
                return False
            
            # 检查文件扩展名
            if config['allowed_extensions']:
                file_ext = source_path.suffix.lower()
                if file_ext not in config['allowed_extensions']:
                    logger.error(f"File extension {file_ext} not allowed. Allowed: {config['allowed_extensions']}")
                    return False
            
            # 创建目标目录
            target_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 复制文件
            shutil.copy2(source_path, target_path)
            
            logger.info(f"File uploaded successfully: {target_path}")
            return True
            
        except Exception as e:
            logger.error(f"File upload failed: {e}")
            return False
    
    def _handle_file_download(self, config: Dict[str, Any]) -> bool:
        """处理文件下载"""
        try:
            source_path = Path(config['source_path'])
            target_path = Path(config.get('target_path', './downloads'))
            
            logger.info(f"Downloading file from {source_path} to {target_path}")
            
            # 检查源文件是否存在
            if not source_path.exists():
                logger.error(f"Source file does not exist: {source_path}")
                return False
            
            # 创建目标目录
            if target_path.is_dir() or not target_path.suffix:
                target_path = target_path / source_path.name
            
            target_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 复制文件
            shutil.copy2(source_path, target_path)
            
            logger.info(f"File downloaded successfully: {target_path}")
            return True
            
        except Exception as e:
            logger.error(f"File download failed: {e}")
            return False
    
    def _handle_file_copy(self, config: Dict[str, Any]) -> bool:
        """处理文件复制"""
        try:
            source_path = Path(config['source_path'])
            target_path = Path(config['target_path'])
            
            logger.info(f"Copying file from {source_path} to {target_path}")
            
            # 检查源文件是否存在
            if not source_path.exists():
                logger.error(f"Source file does not exist: {source_path}")
                return False
            
            # 创建目标目录
            target_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 复制文件
            shutil.copy2(source_path, target_path)
            
            logger.info(f"File copied successfully: {target_path}")
            return True
            
        except Exception as e:
            logger.error(f"File copy failed: {e}")
            return False
    
    def _handle_file_delete(self, config: Dict[str, Any]) -> bool:
        """处理文件删除"""
        try:
            source_path = Path(config['source_path'])
            
            logger.info(f"Deleting file: {source_path}")
            
            # 检查文件是否存在
            if not source_path.exists():
                logger.warning(f"File does not exist: {source_path}")
                return True  # 文件不存在也算成功
            
            # 删除文件
            if source_path.is_file():
                source_path.unlink()
                logger.info(f"File deleted successfully: {source_path}")
            elif source_path.is_dir():
                shutil.rmtree(source_path)
                logger.info(f"Directory deleted successfully: {source_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"File delete failed: {e}")
            return False
    
    def _handle_file_list(self, config: Dict[str, Any]) -> bool:
        """处理文件列表"""
        try:
            source_path = Path(config.get('source_path', '.'))
            file_pattern = config.get('file_pattern', '*')
            
            logger.info(f"Listing files in {source_path} with pattern {file_pattern}")
            
            # 检查目录是否存在
            if not source_path.exists():
                logger.error(f"Directory does not exist: {source_path}")
                return False
            
            # 列出文件
            if source_path.is_file():
                files = [source_path]
            else:
                files = list(source_path.glob(file_pattern))
            
            logger.info(f"Found {len(files)} files:")
            for file_path in files:
                file_size = file_path.stat().st_size if file_path.exists() else 0
                logger.info(f"  {file_path.name} ({file_size} bytes)")
            
            return True
            
        except Exception as e:
            logger.error(f"File list failed: {e}")
            return False
