/*@!Encoding:1252*/
variables
{
  dword port = 4;
  dword rate = 115200;
  enum Camera_ID {Front_camera = 0, Left_camera = 1, Right_camera = 2, Rear_camera = 3};
  enum Camera_Error {cam_Normal = 0, cam_Open = 1, cam_SC_TO_GND = 2, cam_SC_TO_VBAT = 3};
}


/* To Inject or Remove Failure on Camera                                                     */
/* Para1: Camera_ID {Front_camera = 0, Left_camera = 1, Right_camera = 2, Rear_camera = 3}   */
/* Para2: Camera_Error {cam_Normal = 0, cam_Open = 1, cam_SC_TO_GND = 2, cam_SC_TO_VBAT = 3} */
/* Author Ray, 20200605,          Reviewed by XiaokuiYi 20200608                             */
testfunction tstf_Camera_FailureInject(enum Camera_ID camera, enum Camera_Error cam_error)
{
  setTestId(0);

  func_Camera_FailureInject(camera, cam_error);
}


testfunction tstf_Camera_FailureRemove(enum Camera_ID camera)
{
  setTestId(0);

  func_Camera_FailureInject(camera, cam_Normal);
}

int func_Camera_FailureInject(enum Camera_ID camera, enum Camera_Error cam_error)
{
  port = @sysvar::Fault_Box::RS232_ComPort;
  rate = @sysvar::Fault_Box::RS232_Baudrate;

  func_config_port(port, rate);                               // RS232 Configuration:close--> open--> config
  
  incTestStepId();
  if(cam_error != cam_Normal)
  {
    testStep(gTestIdStr, "Inject Failure [ %s ] on [ %s ].  ",cam_error.name(),camera.name());
  }
  else
  {
    testStep(gTestIdStr, "Remove Failure on [ %s ].  ", camera.name());
  }
  
   if(camera == 0)
   {
    switch (cam_error)
    {
      case 0:
      func_send232("<SET_CAM_VIDEO,1,1>", port);                              //RS232 Send Command
      break;
      case 1:
      func_send232("<SET_CAM_VIDEO,1,2>", port);
      break;
      case 2:
      func_send232("<SET_CAM_VIDEO,1,3>", port);
      break;
      case 3:
      func_send232("<SET_CAM_VIDEO,1,4>", port);
      break;
      default :
         testStepFail(gTestIdStr, "cam_error [ %s ] is not Valid!",cam_error.name());
      break;
    }
  }
  else if(camera == 1)
  {
    switch (cam_error)
    {
      case 0:
      func_send232("<SET_CAM_VIDEO,2,1>", port);
      break;
      case 1:
      func_send232("<SET_CAM_VIDEO,2,2>", port);
      break;
      case 2:
      func_send232("<SET_CAM_VIDEO,2,3>", port);
      break;
      case 3:
      func_send232("<SET_CAM_VIDEO,2,4>", port);
      break;
      default :
         testStepFail(gTestIdStr, "cam_error [ %s ] is not Valid!",cam_error.name());
      break;
    }
   }
   else if(camera == 2)
   {
    switch (cam_error)
    {
      case 0:
      func_send232("<SET_CAM_VIDEO,3,1>", port);
      break;
      case 1:
      func_send232("<SET_CAM_VIDEO,3,2>", port);
      break;
      case 2:
      func_send232("<SET_CAM_VIDEO,3,3>", port);
      break;
      case 3:
      func_send232("<SET_CAM_VIDEO,3,4>", port);
      break;
      default :
         testStepFail(gTestIdStr, "cam_error [ %s ] is not Valid!",cam_error.name());
      break;
    }
   }
    else if(camera == 3)
   {
    switch (cam_error)
    {
      case 0:
      func_send232("<SET_CAM_VIDEO,4,1>", port);
      break;
      case 1:
      func_send232("<SET_CAM_VIDEO,4,2>", port);
      break;
      case 2:
      func_send232("<SET_CAM_VIDEO,4,3>", port);
      break;
      case 3:
      func_send232("<SET_CAM_VIDEO,4,4>", port);
      break;
      default :
         testStepFail(gTestIdStr, "cam_error [ %s ] is not Valid!",cam_error.name());
      break;
    }
   }
    else
      testStepFail(gTestIdStr, "camera [ %s ] is not Valid!",camera.name());
  
//  incTestStepId();
//  if(cam_error != cam_Normal)
//  {
//    testStep(gTestIdStr, "Has Injected Failure [ %s ] on [ %s ].",cam_error.name(),camera.name());
//  }
//  else
//  {
//    testStep(gTestIdStr, "Has Removed Failure on [ %s ].  ", camera.name());
//  }
  return 1;
}

void func_config_port(dword port, dword rate)
{
//  rs232Close(port);
  rs232Open(port);                                  //COMPort
  rs232Configure(port, rate, 8, 1, 0);
  incTestStepId();
  testStep(gTestIdStr, "Configuration complete ! COM%d Configure Baudrate %d. ", port, rate);
}


void func_send232(char command[], dword port)
{
  int i, length;
  byte buffer[20];
  byte result[20];
  char res[20];
  int size = 20;
  
  incTestStepId();
  length = strlen(command);
  for ( i=0;i<length;i++)
  {
    buffer[i] = command[i];
  }
  
  if(1 == RS232Send(port, buffer, length))
  {
    testStep(gTestIdStr, "To Send Command in COM%d", port);
    if(1 == RS232Receive(port,result,size))                            //Request receive blocks of bytes from a serial port
    {
      incTestStepId();
      testStep(gTestIdStr, "Receive operation start properly in COM%d", port);
    }
  }
  else
  {
    testStepFail(gTestIdStr, "Error, Fail to Send Command in COM%d", port);
  }
   
//  RS232OnReceive(port,result,size);
//  if (1 == RS232Receive(port,result,size))
//  {
//
//    if (result[1] == 65 && result[2] == 67 && result[3] == 75 )       //ACK
//      {
//        testStepPass(gTestIdStr, "ACK is received, command sucessful. ");
//      }
//      else
//        testStepFail(gTestIdStr, "Failed command, ACK is not received. ");
//      for(i = 1; i<= 3 ; i++)
//      {
//        res[i] = result[i];
//        write("%c", result[i]);
//      }
//  }
}



/* This is the RS232 Receive Bytes Callback Function*/
/* Para1: port   -- selected port     */
/* Para2: buffer -- The buffer given to the start receive call.  */
/* Para3: number -- The number of bytes which have been received.  */
RS232OnReceive(dword port, byte buffer[], dword number) {

  int i;
  char res[20];
  
  incTestStepId();
  if(number != 1)
  {
    for(i=0; i< number; i++)
    {
      res[i]=buffer[i];
    }
//    write ("COM%d received %d Bytes comment %s", port, number, res);
//    write ("COM%d received Comment %s", port, res);
    
//    if(number == 5 && buffer[1] == 'A' && buffer[2] == 'C' && buffer[3] == 'K')      //ACK
    if(buffer[1] == 'A' && buffer[2] == 'C' && buffer[3] == 'K')      //ACK
    {
//     write("Command OK");
     testStepPass(gTestIdStr, "Command OK ! ACK is received, COM%d received Comment %s", port, res);
    }
//     else if(number == 6 && buffer[1] == 'N' && buffer[2] == 'A' && buffer[3] == 'C' && buffer[4] == 'K')   //NACK
     else if(buffer[1] == 'N' && buffer[2] == 'A' && buffer[3] == 'C' && buffer[4] == 'K')   //NACK
    {
//     write("Command NOK");
     testStepFail(gTestIdStr, "Failed Command, Negative ACK is received, COM%d received Comment %s", port, res);
    }
    else
    {
//      write("Error");
      testStepFail(gTestIdStr, "Failed Command, Error received, COM%d received Comment %s", port, res);
    }
  }
  else if(number == 0)
  {
   testStepFail(gTestIdStr, "Failed Command, No ACK received. ");
  }
  
}
