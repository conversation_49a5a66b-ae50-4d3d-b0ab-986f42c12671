from vtaf_ai_api_client.api.chunking_v_1 import (
    chunk_single_string_v1_chunking_single_string_chunking_post,
    chunking_health_check,
    get_supported_chunking_strategies_v1_chunking_supported_chunking_strategies_get,
)
from vtaf_ai_api_client.models import ChunkStringRequest


class ChunkingAPI:
    def __init__(self, client):
        self.client = client

    def health_check(self):
        """
        Ping the health check endpoint of the chunking service.
        """
        return chunking_health_check.sync(client=self.client).additional_properties

    def supported_strategies(self, force: bool = False):
        """
        Get supported chunking strategies.
        """
        return get_supported_chunking_strategies_v1_chunking_supported_chunking_strategies_get.sync(
            client=self.client,
            force=force,
        )

    def chunk_string(
        self,
        string: str,
        strategy: str = "recursive-character-text-splitter",
        parameters: dict = None,
        force: bool = False,
    ):
        """
        Chunk a given string using the specified strategy and parameters.
        """
        request = ChunkStringRequest(
            string=string,
            chunking_strategy=strategy,
            parameters=parameters or {},
        )
        return chunk_single_string_v1_chunking_single_string_chunking_post.sync(
            client=self.client,
            body=request,
            force=force,
        )
