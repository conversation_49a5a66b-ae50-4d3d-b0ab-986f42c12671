<!-- <template>
  <div class="background-container">
    <div class="text-container">
      <div
        class="text-h2 q-pt-lg text-weight-medium q-pl-sm q-pr-sm text-white"
        style="text-align: center"
      >
        One Test Automation Framework for Valeo<br />
      </div>
      <div
        class="q-pt-lg q-pb-lg q-pl-sm q-pr-sm text-white text-h5"
        style="text-align: center"
      >
        Conciseness, Readability, Maintainability and Reusability across Teams,
        Projects, Centres, Sites and Valeo!
      </div>
    </div>
  </div>
</template>

<style scoped>
.background-container {
  margin-top: 3rem;
  background-image: url("src/assets/home/<USER>"); /* Replace 'path/to/your/image.jpg' with the actual path to your image */
  height: 300px;
  background-size: cover;
  display: flex;
  align-items: center; /* Vertically center the content */
  margin-bottom: 2rem;
}

.text-container {
  width: 100%;
  text-align: center;
}

@media screen and (max-width: 600px) {
  .background-container {
    height: 550px;
  }
}
</style> -->
<!--#################################################################################-->
<!-- <template>
  <div class="background-container">
    <div class="text-container">
      <div
        ref="headerText"
        class="text-h2 q-pt-lg text-weight-medium q-pl-sm q-pr-sm text-white"
        style="text-align: center"
      >
        One Test Automation Framework for Testing<br />
      </div>
      <div
        ref="subHeaderText"
        class="q-pt-lg q-pb-lg q-pl-sm q-pr-sm text-white text-h5"
        style="text-align: center"
      >
        Conciseness, Readability, Maintainability and Reusability across Teams,
        Projects, Centres, Sites and Testing!
      </div>
    </div>
  </div>
</template>

<script>
import { onMounted, ref } from "vue";
import Lenis from "@studio-freight/lenis";
import { gsap } from "gsap";

export default {
  setup() {
    const headerText = ref(null);
    const subHeaderText = ref(null);

    onMounted(() => {
      // Initialize Lenis for smooth scrolling
      const lenis = new Lenis({
        duration: 1.2,
        easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)), // Ease-out
        direction: "vertical",
        smooth: true,
      });

      function raf(time) {
        lenis.raf(time);
        requestAnimationFrame(raf);
      }

      requestAnimationFrame(raf);

      // GSAP animation on scroll
      gsap.from(headerText.value, {
        scale: 0, // Start at 0 zoom
        opacity: 0, // Start fully transparent
        scrollTrigger: {
          trigger: headerText.value,
          start: "top 80%", // Start when the top of the element is 80% from the top of the viewport
          end: "top 50%",
          scrub: true, // Smoothly animate as the user scrolls
        },
      });

      gsap.from(subHeaderText.value, {
        opacity: 0, // Fade in effect
        y: 50, // Slight upward movement
        scrollTrigger: {
          trigger: subHeaderText.value,
          start: "top 70%", // Start the animation when element reaches 70% of the viewport
          end: "top 40%",
          scrub: true, // Sync with scroll
        },
      });
    });

    return {
      headerText,
      subHeaderText,
    };
  },
};
</script>

<style scoped>
.background-container {
  background-color: #000; /* Set your desired background color */
  height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-container {
  width: 100%;
  max-width: 1200px;
}

.text-h2 {
  font-size: 2.5rem;
}

.text-h5 {
  font-size: 1.25rem;
}
</style> -->
<!--#################################################################################-->
<template>
  <div class="background-container" v-if="$q.screen.gt.sm">
    <div class="text-container">
      <div
        ref="headerText"
        class="text-h2 q-pt-lg text-weight-medium q-pl-sm q-pr-sm text-white"
        style="text-align: left"
      >
        One Test Automation Framework for Valeo<br />
      </div>
      <div
        ref="subHeaderText"
        class="q-pt-lg q-pb-lg q-pl-sm q-pr-sm text-white text-h5"
        style="text-align: left"
      >
        Conciseness, Readability, Maintainability and Reusability across Teams,
        Projects, Centres, Sites and Testing!
      </div>
    </div>

    <div ref="featureHeading" class="feature-heading text-white text-h2">
      Feature-Heading
    </div>

    <div class="features-container">
      <div class="image-container">
        <img
          ref="eyeImage"
          src="src/assets/home/<USER>"
          alt="Eye for Automation"
          class="eye-image"
        />
      </div>
      <div ref="feature1" class="feature text-white">
        <q-card class="my-card">
          <q-card-section>
            <q-chip
              color="red-9"
              text-color="white"
              icon="directions"
              style="width: 10rem; height: 60px"
            >
              Get directions
            </q-chip>
          </q-card-section>
        </q-card>
      </div>
      <div ref="feature2" class="feature text-white">
        <q-card class="my-card">
          <q-card-section>
            <q-chip
              color="red-9"
              text-color="white"
              icon="directions"
              style="width: 10rem; height: 60px"
            >
              Get directions
            </q-chip>
          </q-card-section>
        </q-card>
      </div>
      <div ref="feature3" class="feature text-white">
        <q-card class="my-card">
          <q-card-section>
            <q-chip
              color="red-9"
              text-color="white"
              icon="directions"
              style="width: 10rem; height: 60px"
            >
              Get directions
            </q-chip>
          </q-card-section>
        </q-card>
      </div>
      <div ref="feature4" class="feature text-white">
        <q-card class="my-card">
          <q-card-section>
            <q-chip
              color="red-9"
              text-color="white"
              icon="directions"
              style="width: 10rem; height: 60px"
            >
              Get directions
            </q-chip>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
  <div class="background-container-small" v-else>
    <div class="text-container-small">
      <div
        class="text-h2 q-pt-lg text-weight-medium q-pl-sm q-pr-sm text-white"
        style="text-align: center"
      >
        One Test Automation Framework for Valeo<br />
      </div>
      <div
        class="q-pt-lg q-pb-lg q-pl-sm q-pr-sm text-white text-h5"
        style="text-align: center"
      >
        Conciseness, Readability, Maintainability and Reusability across Teams,
        Projects, Centres, Sites and Valeo!
      </div>
    </div>
  </div>
</template>

<script>
import { onMounted, ref } from "vue";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

export default {
  setup() {
    const headerText = ref(null);
    const subHeaderText = ref(null);
    const featureHeading = ref(null);
    const feature1 = ref(null);
    const feature2 = ref(null);
    const feature3 = ref(null);
    const feature4 = ref(null);
    const eyeImage = ref(null);

    onMounted(() => {
      // Register ScrollTrigger plugin
      gsap.registerPlugin(ScrollTrigger);

      // Pin the section and trigger animations like a slide
      ScrollTrigger.create({
        trigger: ".background-container",
        pin: true, // Pin the container
        start: "top top", // Start pinning when container hits top of the viewport
        end: "+=100%", // Pin for a scrollable distance (this is relative to the container)
        scrub: true, // Smooth scrubbing during scroll
      });

      // GSAP animation on scroll for header and subheader
      gsap.fromTo(
        headerText.value,
        { scale: 0, opacity: 0 },
        {
          scale: 1,
          opacity: 1,
          scrollTrigger: {
            trigger: headerText.value,
            start: "top 80%",
            end: "top 50%",
            scrub: true,
          },
        }
      );

      gsap.fromTo(
        subHeaderText.value,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          scrollTrigger: {
            trigger: subHeaderText.value,
            start: "top 70%",
            end: "top 40%",
            scrub: true,
          },
        }
      );

      // Animation for "Feature-Heading" to appear after header moves to the left
      gsap.fromTo(
        featureHeading.value,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          scrollTrigger: {
            trigger: featureHeading.value,
            start: "top 90%", // Appears just after subheader moves
            scrub: true,
          },
        }
      );

      // Slide-in animation for features
      gsap.fromTo(
        feature1.value,
        { opacity: 0, x: -100 },
        {
          opacity: 1,
          x: 0,
          scrollTrigger: {
            trigger: feature1.value,
            start: "top 40%",
            end: "top 10%",
            scrub: true,
          },
        }
      );

      gsap.fromTo(
        feature2.value,
        { opacity: 0, x: -100 },
        {
          opacity: 1,
          x: 0,
          scrollTrigger: {
            trigger: feature2.value,
            start: "top 30%",
            end: "top 0%",
            scrub: true,
          },
        }
      );

      gsap.fromTo(
        feature3.value,
        { opacity: 0, x: -100 },
        {
          opacity: 1,
          x: 0,
          scrollTrigger: {
            trigger: feature3.value,
            start: "top 20%",
            end: "top -10%",
            scrub: true,
          },
        }
      );

      gsap.fromTo(
        feature4.value,
        { opacity: 0, x: -100 },
        {
          opacity: 1,
          x: 0,
          scrollTrigger: {
            trigger: feature4.value,
            start: "top 10%",
            end: "top -20%",
            scrub: true,
          },
        }
      );

      // Animation for the eye image to appear after the last feature
      gsap.fromTo(
        eyeImage.value,
        { scale: 0, opacity: 0, rotation: -45 },
        {
          scale: 1,
          opacity: 1,
          rotation: 0,
          scrollTrigger: {
            trigger: eyeImage.value,
            start: "top 90%",
            scrub: true,
          },
        }
      );
    });

    return {
      headerText,
      subHeaderText,
      featureHeading,
      feature1,
      feature2,
      feature3,
      feature4,
      eyeImage,
    };
  },
};
</script>

<style scoped>
.background-container {
  background-color: #000;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 100px 0;
  position: relative; /* Keep relative for pinning */
}

.text-container {
  width: 100%;
  max-width: 1200px;
  text-align: center;
}

.text-h2 {
  font-size: 2.5rem;
}

.text-h5 {
  font-size: 1.25rem;
}

.feature-heading {
  margin-top: 50px;
  text-align: center;
  opacity: 0; /* Initially hidden */
}

.features-container {
  width: 100%;
  max-width: 1200px;
  display: flex;
  flex-direction: row; /* Align image and features horizontally */
  align-items: center; /* Align items to the left */
  margin-top: 50px;
}

.feature {
  font-size: 1.5rem;
  margin-bottom: 30px;
  opacity: 0; /* Initial opacity set to 0 for animation */
}

.image-container {
  margin-left: 50px; /* Space between the features and image */
}

.eye-image {
  width: 150px;
  height: 150px;
  opacity: 0; /* Initial opacity set to 0 for animation */
}
.my-card {
  margin-left: 2rem;
  background: transparent;
}
.background-container-small {
  background-image: url("src/assets/home/<USER>"); /* Replace 'path/to/your/image.jpg' with the actual path to your image */
  height: 300px;
  background-size: cover;
  display: flex;
  align-items: center; /* Vertically center the content */
  margin-bottom: 2rem;
}

.text-container-small {
  width: 100%;
  text-align: center;
}

@media screen and (max-width: 600px) {
  .background-container {
    height: 550px;
  }
}
</style>
