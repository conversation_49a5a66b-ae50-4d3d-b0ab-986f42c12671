import os
from pathlib import Path

from google.cloud import storage

SERVICES_PROJECT_ID = os.getenv("SERVICES_PROJECT_ID", "")


def update_gcs_file_metadata(filepath):
    gcs_client = storage.Client(project=SERVICES_PROJECT_ID)
    bucket_name = filepath.split("/")[0]
    blob_name = "/".join(filepath.split("/")[1:])
    bucket = gcs_client.bucket(bucket_name)
    blob = bucket.blob(blob_name)
    filepath = Path(filepath)
    if filepath.suffix == '.xlsx':
        blob.content_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    else:
        blob.content_type = "application/octet-stream"
    blob.patch()
    print(f"Updated {blob_name} content type to {blob.content_type}, {bucket_name}/{blob_name}")
