import os
import time
import base64
import requests
import google.oauth2.id_token as id_token
import google.auth.transport.requests as google_requests

from configs.env import settings
from utils.logger import get_logger

logger = get_logger(__name__)


class VtafApiClients:
    def __init__(self):
        self.api_base = "https://" + settings.DOMAIN_NAME + "/vtaf-api"
        self.chunking_strategy = settings.CHUNKING_STRATEGY
        self.collection_name = settings.COLLECTION_NAME
        self.file_path = os.path.join(settings.SOURCE_PATH, settings.FILE_NAME)
        self.timeout = 60

        creds = id_token.fetch_id_token_credentials(request=google_requests.Request(), audience=settings.VTAF_AI_CLIENT_ID)
        self.session = google_requests.AuthorizedSession(creds) 

    # Chunk a string of text using the configured strategy
    def chunk_text(self, text: str) -> list:
        url = f"{self.api_base}/v1/chunking/single-string-chunking"
        payload = {
            "string": text,
            "chunking_strategy": self.chunking_strategy,
            "parameters": {}
        }

        try:
            response = self.session.post(url, json=payload, timeout=self.timeout)
            response.raise_for_status()
            return response.json().get("results", [])
        except requests.exceptions.RequestException as e:
            logger.error(f"Chunking error: {e}")
            return []

    # Encode a file as a base64 string
    def encode_file_to_base64(self, file_path: str) -> str:
        with open(file_path, "rb") as file:
            return base64.b64encode(file.read()).decode("utf-8")

    # Parse the file content using the remote parsing API
    def parse_file(self) -> list:
        file_name = os.path.basename(self.file_path)
        file_content = self.encode_file_to_base64(self.file_path)
        url = f"{self.api_base}/v1/parsing/single-file-parsing"
        payload = {
            "file_name": file_name,
            "file_content": file_content,
            "parser": "auto",
            "output_format": ["text"]
        }

        try:
            response = self.session.post(url, json=payload, timeout=self.timeout)
            response.raise_for_status()
            return response.json().get("results", [])
        except requests.exceptions.RequestException as e:
            logger.error(f"Parsing error: {e}")
            return []

    # Check health of a specific service endpoint with retries
    def check_health(self, service: str) -> bool:
        url = f"{self.api_base}/v1/{service}/health"
        for attempt in range(1, 4):
            try:
                response = self.session.get(url, timeout=self.timeout)
                response.raise_for_status()
                logger.info(f"{service.capitalize()} is healthy.")
                return True
            except requests.exceptions.RequestException as e:
                logger.warning(f"Attempt {attempt} failed: {e}")
                logger.info("Retrying in 10 seconds...")
                time.sleep(10)

        logger.error(f"{service.capitalize()} health check failed after 3 attempts.")
        return False

    # Summarize a block of text using the summarization endpoint
    def summarize_text(self, text: str) -> str:
        url = f"{self.api_base}/v1/agents/summarize_text"
        payload = {"text": text}

        try:
            response = self.session.post(url, json=payload, timeout=self.timeout * 2)
            response.raise_for_status()
            return response.json().get("raw", "")
        except requests.exceptions.RequestException as e:
            logger.error(f"Summarization error: {e}")
            return ""

    # Add processed chunks to the vector database
    def add_chunks_to_vector_db(self, chunks: list, project_details: dict):
        url = f"{self.api_base}/v1/vectordb/documents/add"

        for idx, chunk in enumerate(chunks):
            payload = {
                "content": chunk,
                "metadata": {
                    "source": settings.FILE_NAME,
                    "chunk_index": idx,
                    "project_uuid": settings.VTAF_AI_PROJECT_ID,
                    "project": project_details.get("project"),
                    "variant_uuid": settings.VTAF_AI_PROJECT_VAR_ID,
                    "variant": project_details.get("variant"),
                    "euf_feature_uuid": settings.VTAF_AI_EUF_FEA_UID,
                    "euf_feature": project_details.get("euf_feature"),
                    "type": settings.INPUT_TYPE,
                    "profile_uuid": settings.PROJECT_PROFILE_UUID,
                    "job_id": settings.CLOUD_RUN_EXECUTION,
                },
                "collection_name": self.collection_name
            }

            try:
                response = self.session.post(url, json=payload, timeout=self.timeout)
                response.raise_for_status()
            except requests.exceptions.RequestException as e:
                logger.error(f"Error adding chunk {idx}: {e}")
