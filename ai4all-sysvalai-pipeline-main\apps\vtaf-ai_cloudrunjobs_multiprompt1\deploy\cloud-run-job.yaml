# Google Cloud Run Job 配置
apiVersion: run.googleapis.com/v1
kind: Job
metadata:
  name: vtaf-ai-multiprompt-job
  labels:
    app: vtaf-ai-multiprompt
spec:
  template:
    spec:
      template:
        spec:
          taskCount: 1
          parallelism: 1
          taskTimeout: 3600s  # 1 hour timeout
          restartPolicy: OnFailure
          containers:
          - name: vtaf-ai-multiprompt
            image: gcr.io/PROJECT_ID/vtaf-ai-multiprompt:latest
            env:
            # Google Cloud 项目配置
            - name: PROJECT_ID
              value: "valeo-cp2673-dev"
            - name: PROJECT_NUMBER
              value: "203210506053"
            - name: REGION
              value: "us-central1"
            - name: GOOGLE_CLOUD_PROJECT
              value: "valeo-cp2673-dev"
            
            # AI 模型配置
            - name: DEFAULT_MODEL
              value: "gemini-1.5-pro"
            - name: AI_MODEL
              value: "gemini-1.5-pro"
            
            # 任务配置
            - name: TASK_TYPE
              value: "batch"
            - name: PROMPT_SHEET_URL
              value: "YOUR_PROMPT_SHEET_URL"
            - name: TEST_SPEC_SHEET_URL
              value: "YOUR_TEST_SPEC_SHEET_URL"
            - name: PROMPT_SHEET_NAME
              value: "YOUR_PROMPT_SHEET_NAME"
            
            # Google Sheets 配置
            - name: GOOGLE_SHEETS_ENABLED
              value: "true"
            
            # 日志配置
            - name: LOG_LEVEL
              value: "INFO"
            - name: DEBUG
              value: "false"
            
            resources:
              limits:
                cpu: "2"
                memory: "4Gi"
              requests:
                cpu: "1"
                memory: "2Gi"
            
            # 使用 Google Cloud 默认服务账号
            # 或者指定自定义服务账号
            # serviceAccountName: vtaf-ai-service-account
