import sys
import logging

from config import FILE_PATH, ENABLE_SUMMARIZATION
from auth import get_auth_headers
from health_check import check_health
from file_utils import parse_file
from chunking import chunk_text
from summarization import summarize_text
from vector_db import add_chunks_to_vector_db

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")


def run_job() -> str:
    headers = get_auth_headers()
    if not headers:
        raise RuntimeError("Authentication failed. Job aborted.")

    if not check_health("parsing", headers):
        raise RuntimeError("Parsing health check failed. Job aborted.")

    parsed_data = parse_file(FILE_PATH, headers)
    if not parsed_data:
        raise RuntimeError("Parsing failed. Job aborted.")

    pages = [entry["page_content"] for entry in parsed_data]

    for idx, page_text in enumerate(pages):
        logging.info(f"Processing page {idx + 1}/{len(pages)}")

        if not check_health("chunking", headers):
            raise RuntimeError("Chunking health check failed. Skipping page.")

        chunks_data = chunk_text(page_text, headers)
        if not chunks_data:
            logging.warning(f"No chunks for page {idx}. Skipping.")
            continue

        chunks = [chunk["text"] for chunk in chunks_data]

        if ENABLE_SUMMARIZATION:
            if not check_health("agents/kb", headers):
                raise RuntimeError(
                    "Summarization service unhealthy. Skipping summarization."
                )
            else:
                chunks = [summarize_text(chunk, headers) for chunk in chunks]

        if not check_health("vectordb", headers):
            raise RuntimeError("Vector DB unhealthy. Skipping storage.")

        add_chunks_to_vector_db(chunks, FILE_PATH, headers)
    logging.info("Job completed.")
    return "success"


if __name__ == "__main__":
    job_status = "failed"
    try:
        logging.info("Job started.")
        job_status = run_job()
    except Exception as e:
        logging.error(f"Job failed with error: {e}")
    finally:
        # TODO: Add the email and log entry to bigquery logic here 
        sys.exit(0 if job_status == "success" else 1)

