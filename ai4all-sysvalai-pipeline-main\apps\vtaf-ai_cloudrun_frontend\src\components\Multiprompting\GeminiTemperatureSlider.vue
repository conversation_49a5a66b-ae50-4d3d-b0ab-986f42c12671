<template>
  <div class="q-mt-md q-ml-md q-mr-md">
  <fieldset>
  <legend class="text-bold q-mb-md">Temperature</legend>
  <q-slider
      class="q-pa-sm"
      v-model="temperature"
      :min="0"
      :max="2"
      :step="0.1"
      label
      label-always
      color="primary"
    />
    <div class="text-right">{{ temperature.toFixed(1) }}</div>
 </fieldset>
 </div>
</template>

<script setup>
import { ref } from 'vue';

const temperature = ref(0.5);
</script>
