##############################################################
# Project level Variables
##############################################################

variable "project_id" {
  description = "The GCP project to use for Valeo"
  type        = string
}
variable "region" {
  description = "The GCP region to create resources in"
  type        = string
}
variable "zone" {
  description = "The GCP zone to create resources in"
  type        = string
}
variable "location" {
  description = "The GCP zone to create resources in"
  type        = string
}

variable "environment" {
  description = "The environment to use for"
  type        = string
}

variable "vtaf_ai_project" {
  description = "The project to use for"
  type        = string
}

variable "vtaf_ai_service_name" {
  description = "Cloud Run application name"
  type        = string
}

##############################################################
# Bigquery Variables
##############################################################

variable "bigquery_dataset_id" {
  description = "The dataset to use for"
  type        = string
}


##############################################################
# Artifact Registry Variables
##############################################################

variable "repositories" {
  type = list(object({
    repository_name        = string
    location               = optional(string, "europe-west1")
    format                 = optional(string, "docker")
    description            = string
    labels                 = optional(map(string), {})
    cleanup_policy_dry_run = optional(bool, false)
    cleanup_policies = optional(map(object({
      action = optional(string)
      condition = optional(object({
        tag_state             = optional(string)
        tag_prefixes          = optional(list(string))
        version_name_prefixes = optional(list(string))
        package_name_prefixes = optional(list(string))
        older_than            = optional(string)
        newer_than            = optional(string)
      }), null)
      most_recent_versions = optional(object({
        package_name_prefixes = optional(list(string))
        keep_count            = optional(number)
      }), null)
    })), {})
  }))
  description = "List of repositories and cleanup policies"
}

##############################################################
# Service accounts Variables
##############################################################

variable "service_account_file" {
  description = "The service account file name"
  type        = string
}

variable "service_account_email" {
  description = "The Service account email the cloud run going to use"
  type        = string
}

##############################################################
# Bucket Variables
##############################################################

variable "buckets_config" {
  description = "These settings apply to all buckets within the project."
  type = object({
    versioning       = optional(bool, true)
    bucket_viewers   = optional(list(string), [])
    prefix           = optional(string, "")
    bucket_admins    = optional(list(string), [])
    set_admin_roles  = optional(bool, true)
    set_viewer_roles = optional(bool, true)
    location         = optional(string, "europe-west1")
    storage_class    = optional(string, "STANDARD")
    labels           = optional(map(string), {})
    lifecycle_rules = optional(set(object({
      action    = map(string)
      condition = map(string)
    })), [])
  })
}

variable "buckets" {
  description = "List of bucket configurations"
  type = list(object({
    bucket_name      = string
    location         = optional(string)
    versioning       = optional(bool)
    force_destroy    = optional(bool, true)
    prefix           = optional(string)
    storage_class    = optional(string)
    set_admin_roles  = optional(bool)
    bucket_admins    = optional(list(string))
    set_viewer_roles = optional(bool)
    bucket_viewers   = optional(list(string))
    bucket_labels    = optional(map(string), {})
    lifecycle_rules = optional(set(object({
      action    = map(string)
      condition = map(string)
    })), [])
  }))
}

##############################################################
# Cloud Function
##############################################################

variable "config_sheet_id" {
  description = "Sheet ID of config sheet"
  type        = string
}

##############################################################
# VTAF.AI Knowledgebase Cloud Run Job Variables
##############################################################

variable "vtaf_ai_cloudrunjobs_testcase_generation_image" {
  description = "Image name for the cloud run service"
  type        = string
}

variable "vtaf_ai_cloudrunjobs_testcase_generation_image_version" {
  description = "Image version for the cloud run service"
  type        = string
}

variable "vtaf_ai_cloudrunjobs_knowledgebase_image" {
  description = "Image name for the cloud run service"
  type        = string
}

variable "vtaf_ai_cloudrunjobs_knowledgebase_image_version" {
  description = "Image version for the cloud run service"
  type        = string
}

variable "vtaf_ai_cloudrunjobs_doors_embedding_image" {
  description = "Image name for the cloud run service"
  type        = string
}

variable "vtaf_ai_cloudrunjobs_doors_embedding_image_version" {
  description = "Image version for the cloud run service"
  type        = string
}


variable "vtaf_ai_cloudrunjobs_test_script_generation_image" {
  description = "Image name for the cloud run service"
  type        = string
}

variable "vtaf_ai_cloudrunjobs_test_script_generation_image_version" {
  description = "Image version for the cloud run service"
  type        = string
}

variable "vtaf_ai_cloudrunjobs_multiprompt_image" {
  description = "Image name for the cloud run service"
  type        = string
}

variable "vtaf_ai_cloudrunjobs_multiprompt_image_version" {
  description = "Image version for the cloud run service"
  type        = string
}


variable "kcrj_env_vars" {
  type = list(object({
    name  = string
    value = string
  }))
  description = "Knowledgebase Cloud Run Job Environmental variable"
}

variable "tcrj_env_vars" {
  type = list(object({
    name  = string
    value = string
  }))
  description = "Testcase generation Cloud Run Job Environmental variable"
}

variable "ecrj_env_vars" {
  type = list(object({
    name  = string
    value = string
  }))
  description = "Doors Embedding Cloud Run Job Environmental variable"
}

variable "tsrj_env_vars" {
  type = list(object({
    name  = string
    value = string
  }))
  description = "Test Script generation Cloud Run Job Environmental variable"
}

variable "mpcrj_env_vars" {
  type = list(object({
    name  = string
    value = string
  }))
  description = "Multiprompt testcase generation Cloud Run Job Environmental variable"
}

##############################################################
# cloud run Variables
##############################################################

variable "services" {
  description = "List of services with their configurations"
  type = list(object({
    service_name                     = optional(string)
    repository_name                  = string
    image_name                       = string
    image_tag                        = string
    location                         = optional(string, "europe-west1")
    working_dir                      = optional(string, null)
    depends_on_container             = optional(list(string), null)
    container_args                   = optional(list(string), null)
    container_command                = optional(list(string), null)
    env_vars                         = optional(map(string), {})
    timeout                          = optional(string)
    max_instances                    = optional(number, 100)
    min_instances                    = optional(number, 0)
    max_instance_request_concurrency = optional(string, "80")
    ingress                          = optional(string, "INGRESS_TRAFFIC_INTERNAL_LOAD_BALANCER")
    env_secret_vars = optional(map(object({
      secret  = string
      version = string
    })), {})
    volume_mounts = optional(list(object({
      name       = string
      mount_path = string
    })), [])
    ports = optional(object({
      name           = optional(string, "http1")
      container_port = optional(number, 8080)
    }), {})
    cpu_limit          = optional(string, "1000m")
    memory_limit       = optional(string, "512Mi")
    service_app_labels = optional(map(string), {})
    volumes = optional(list(object({
      name = string
      secret = optional(object({
        secret       = string
        default_mode = optional(string)
        items = optional(object({
          path    = string
          version = optional(string)
          mode    = optional(string)
        }))
      }))
      cloud_sql_instance = optional(object({
        instances = optional(list(string))
      }))
      empty_dir = optional(object({
        medium     = optional(string)
        size_limit = optional(string)
      }))
      gcs = optional(object({
        bucket    = string
        read_only = optional(string)
      }))
      nfs = optional(object({
        server    = string
        path      = string
        read_only = optional(string)
      }))
    })), [])
    members                       = optional(list(string), [])
    cloud_run_deletion_protection = optional(bool)
    service_account               = optional(string, "")
    use_default_svc_account       = bool
    create_service_account        = optional(bool, false)
    service_account_project_roles = optional(list(string), [])
    template_annotations          = optional(map(string), {})
    traffic = optional(list(object({
      type     = optional(string, "TRAFFIC_TARGET_ALLOCATION_TYPE_LATEST")
      percent  = optional(number, 100)
      revision = optional(string, null)
      tag      = optional(string, null)
    })), [])
  }))
}

variable "vtaf_ai_artifactory_url" {
  description = "The GCP url for the artifact registry"
  type        = string
}
variable "vtaf_ai_artifactory_name" {
  description = "The GCP project Artifactory name for Valeo"
  type        = string
}

variable "delete_contents_on_destroy" {
  description = "(Optional) If set to true, delete all the tables in the dataset when destroying the resource; otherwise, destroying the resource will fail if tables are present."
  type        = bool
}

##############################################################
# IP-Address Variables
##############################################################
variable "ip_addresses" {
  description = "The IP addresses to create"
  type = list(
    object({
      name         = string
      address_type = optional(string, "INTERNAL")
      address      = optional(string)
      description  = string
      global       = optional(bool, false)
      labels       = optional(map(string))
    })
  )
}

##############################################################
# IAM Variables
##############################################################
variable "devops_sa_name" {
  description = "The ID (name) of the service account for the DevOps team"
  type        = string
}

variable "github_deployer_sa_name" {
  description = "The ID (name) of the service account for the DevOps team"
  type        = string
}

variable "fw_dev_sa_name" {
  description = "The ID (name) of the service account for the framework development team"
  type        = string
}

variable "roles_bindings" {
  description = "Map of custom roles to their members"
  type        = map(list(string))
}

variable "sa_bindings" {
  description = "Map of service accounts to their roles and members"
  type = map(object({
    role    = string
    members = list(string)
  }))
}


##############################################################
# Load Balancer Variables
##############################################################
variable "load_balancer_config" {
  type = object({
    name                  = string
    default_service       = string
    ip_address_name       = optional(string)
    main_client_id        = optional(string)
    enable_ssl            = bool
    ssl_domains           = list(string)
    iap_application_title = string
    labels                = optional(map(string), {})
    host_rules = list(object({
      hosts        = list(string)
      path_matcher = string
    }))
    path_matchers = map(object({
      default_service  = string
      advanced_routing = optional(bool, false)
      path_rules = optional(list(object({
        paths   = list(string)
        service = optional(string)
        url_redirect = optional(object({
          host_redirect          = optional(string)
          path_redirect          = optional(string)
          prefix_redirect        = optional(string)
          redirect_response_code = optional(string)
          strip_query            = optional(bool)
        }))
        route_action = optional(object({
          weighted_backend_services = optional(list(object({
            backend_service = string
            weight          = number
            header_action = optional(object({
              request_headers_to_add = optional(list(object({
                header_name  = string
                header_value = string
                replace      = bool
              })), [])
              request_headers_to_remove = optional(list(string), [])
              response_headers_to_add = optional(list(object({
                header_name  = string
                header_value = string
                replace      = bool
              })), [])
              response_headers_to_remove = optional(list(string), [])
            }))
          })), [])
          url_rewrite = optional(object({
            path_prefix_rewrite = optional(string)
            host_rewrite        = optional(string)
          }))
          timeout = optional(object({
            seconds = number
            nanos   = number
          }))
          retry_policy = optional(object({
            num_retries      = number
            retry_conditions = list(string)
            per_try_timeout = optional(object({
              seconds = number
              nanos   = number
            }))
          }))
          request_mirror_policy = optional(object({
            backend_service = string
          }))
          cors_policy = optional(object({
            allow_origins     = list(string)
            allow_methods     = list(string)
            allow_headers     = list(string)
            expose_headers    = list(string)
            max_age           = number
            allow_credentials = bool
            disabled          = bool
          }))
          fault_injection_policy = optional(object({
            delay = optional(object({
              percentage = number
              fixed_delay = optional(object({
                seconds = number
                nanos   = number
              }))
            }))
            abort = optional(object({
              http_status = number
              percentage  = number
            }))
          }))
        }), {})
      })), [])
      route_rules = optional(list(object({
        priority = number
        service  = optional(string)
        match_rules = object({
          prefix_match    = optional(string)
          full_path_match = optional(string)
          ignore_case     = optional(bool, false)
          header_matches = optional(list(object({
            header_name = string
            exact_match = string
          })), [])
          query_parameter_matches = optional(list(object({
            name        = string
            exact_match = string
          })), [])
        })
        route_action = optional(object({
          weighted_backend_services = optional(list(object({
            backend_service = string
            weight          = number
            header_action = optional(object({
              request_headers_to_add = optional(list(object({
                header_name  = string
                header_value = string
                replace      = bool
              })), [])
              request_headers_to_remove = optional(list(string), [])
              response_headers_to_add = optional(list(object({
                header_name  = string
                header_value = string
                replace      = bool
              })), [])
              response_headers_to_remove = optional(list(string), [])
            }))
          })), [])
          url_rewrite = optional(object({
            path_prefix_rewrite = optional(string)
            host_rewrite        = optional(string)
          }))
          timeout = optional(object({
            seconds = number
            nanos   = number
          }))
          retry_policy = optional(object({
            num_retries      = number
            retry_conditions = list(string)
            per_try_timeout = optional(object({
              seconds = number
              nanos   = number
            }))
          }))
          request_mirror_policy = optional(object({
            backend_service = string
          }))
          cors_policy = optional(object({
            allow_origins     = list(string)
            allow_methods     = list(string)
            allow_headers     = list(string)
            expose_headers    = list(string)
            max_age           = number
            allow_credentials = bool
            disabled          = bool
          }))
          fault_injection_policy = optional(object({
            delay = optional(object({
              percentage = number
              fixed_delay = optional(object({
                seconds = number
                nanos   = number
              }))
            }))
            abort = optional(object({
              http_status = number
              percentage  = number
            }))
          }))
        }), {})
        url_redirect = optional(object({
          host_redirect          = optional(string)
          path_redirect          = optional(string)
          redirect_response_code = optional(string)
          strip_query            = optional(bool)
        }))
        header_action = optional(object({
          request_headers_to_add = optional(list(object({
            header_name  = string
            header_value = string
            replace      = bool
          })), [])
          request_headers_to_remove = optional(list(string), [])
          response_headers_to_add = optional(list(object({
            header_name  = string
            header_value = string
            replace      = bool
          })), [])
          response_headers_to_remove = optional(list(string), [])
        }))
      })), [])
    }))
    backend_services = map(object({
      service_type     = string
      enable_iap       = optional(bool, false)
      oauth2_client_id = optional(string)
    }))
  })
  description = "Configuration for the load balancer, including routing rules and backend services."
}



