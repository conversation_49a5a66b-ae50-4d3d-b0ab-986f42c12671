# Google Cloud 部署指南

## 🎯 概述

本指南介绍如何将 VTAF AI Multiprompt 部署到 Google Cloud，支持多种认证方式和部署模式。

## 🔐 认证方式支持

代码已更新为支持多种认证方式，按优先级自动选择：

### 1. **Google Cloud 默认认证** (推荐生产环境)
- 在 Google Cloud 环境中自动使用
- 无需额外配置文件
- 使用 Compute Engine 默认服务账号或自定义服务账号

### 2. **硬编码服务账号** (快速测试)
- 在 `configs/env.py` 中配置
- 适合快速测试和开发

### 3. **服务账号文件**
- 使用 `service-account.json` 文件
- 通过 `GOOGLE_APPLICATION_CREDENTIALS` 环境变量指定

### 4. **OAuth 认证文件**
- 使用 `client_secret.json` 文件
- 适合本地开发

## 🚀 部署步骤

### 步骤 1: 准备环境

```bash
# 1. 安装 Google Cloud CLI
curl https://sdk.cloud.google.com | bash
exec -l $SHELL

# 2. 登录 Google Cloud
gcloud auth login
gcloud auth application-default login

# 3. 设置项目
gcloud config set project valeo-cp2673-dev
```

### 步骤 2: 配置环境变量

编辑 `deploy/cloud-run-job.yaml` 文件，设置以下环境变量：

```yaml
env:
# 必须配置
- name: PROJECT_ID
  value: "valeo-cp2673-dev"
- name: PROMPT_SHEET_URL
  value: "你的Google表格URL"
- name: TEST_SPEC_SHEET_URL
  value: "你的测试规格表格URL"
- name: PROMPT_SHEET_NAME
  value: "你的提示词表格名称"

# 可选配置
- name: AI_MODEL
  value: "gemini-1.5-pro"
- name: REGION
  value: "europe-west1"
```

### 步骤 3: 部署到 Google Cloud

```bash
# 1. 进入部署目录
cd deploy

# 2. 运行部署脚本
chmod +x deploy.sh
./deploy.sh
```

### 步骤 4: 运行作业

```bash
# 执行批处理作业
gcloud run jobs execute vtaf-ai-multiprompt-job --region=us-central1

# 查看作业状态
gcloud run jobs describe vtaf-ai-multiprompt-job --region=us-central1

# 查看执行历史
gcloud run jobs executions list --job=vtaf-ai-multiprompt-job --region=us-central1
```

## 📊 监控和日志

### 查看日志

```bash
# 查看最新日志
gcloud logging read 'resource.type="cloud_run_job"' --limit=50

# 查看特定作业的日志
gcloud logging read 'resource.type="cloud_run_job" AND resource.labels.job_name="vtaf-ai-multiprompt-job"' --limit=100

# 实时查看日志
gcloud logging tail 'resource.type="cloud_run_job"'
```

### 监控指标

在 Google Cloud Console 中查看：
- Cloud Run Jobs 页面
- Cloud Logging 页面
- Cloud Monitoring 页面

## 🔧 配置选项

### 环境变量配置

| 变量名 | 必需 | 默认值 | 说明 |
|--------|------|--------|------|
| `PROJECT_ID` | ✅ | - | Google Cloud 项目 ID |
| `PROMPT_SHEET_URL` | ✅ | - | 提示词表格 URL |
| `TEST_SPEC_SHEET_URL` | ✅ | - | 测试规格表格 URL |
| `PROMPT_SHEET_NAME` | ✅ | - | 提示词表格名称 |
| `AI_MODEL` | ❌ | `gemini-1.5-pro` | 使用的 AI 模型 |
| `REGION` | ❌ | `europe-west1` | Google Cloud 区域 |
| `TASK_TYPE` | ❌ | `batch` | 任务类型 |
| `LOG_LEVEL` | ❌ | `INFO` | 日志级别 |

### 资源配置

在 `cloud-run-job.yaml` 中调整：

```yaml
resources:
  limits:
    cpu: "2"        # CPU 限制
    memory: "4Gi"   # 内存限制
  requests:
    cpu: "1"        # CPU 请求
    memory: "2Gi"   # 内存请求
```

### 超时配置

```yaml
taskTimeout: 3600s  # 1小时超时
```

## 🛠️ 故障排除

### 常见问题

1. **认证失败**
   ```bash
   # 检查认证状态
   gcloud auth list
   
   # 重新认证
   gcloud auth application-default login
   ```

2. **权限不足**
   ```bash
   # 检查 IAM 权限
   gcloud projects get-iam-policy valeo-cp2673-dev
   
   # 添加必要权限
   gcloud projects add-iam-policy-binding valeo-cp2673-dev \
     --member="serviceAccount:YOUR_SERVICE_ACCOUNT" \
     --role="roles/aiplatform.user"
   ```

3. **表格访问失败**
   - 确保服务账号有表格访问权限
   - 检查表格 URL 是否正确
   - 验证表格共享设置

### 调试模式

设置环境变量启用调试：

```yaml
- name: DEBUG
  value: "true"
- name: LOG_LEVEL
  value: "DEBUG"
```

## 📈 扩展和优化

### 并行处理

修改 `cloud-run-job.yaml` 启用并行处理：

```yaml
taskCount: 5      # 并行任务数
parallelism: 3    # 同时运行的任务数
```

### 定时执行

使用 Cloud Scheduler 定时触发：

```bash
gcloud scheduler jobs create http vtaf-ai-multiprompt-schedule \
  --schedule="0 9 * * 1-5" \
  --uri="https://europe-west1-run.googleapis.com/apis/run.googleapis.com/v1/namespaces/valeo-cp2673-dev/jobs/vtaf-ai-multiprompt-job:run" \
  --http-method=POST \
  --oauth-service-account-email=YOUR_SERVICE_ACCOUNT
```

## 🎉 总结

现在你的应用已经完全兼容 Google Cloud 部署：

✅ **多种认证方式** - 自动适配不同环境
✅ **Cloud Run Jobs** - 适合批处理任务
✅ **环境变量配置** - 灵活的配置管理
✅ **完整监控** - 日志和指标支持
✅ **错误处理** - 健壮的错误恢复机制

主入口点是 `main.py`，它会自动检测运行环境并选择合适的认证方式。
