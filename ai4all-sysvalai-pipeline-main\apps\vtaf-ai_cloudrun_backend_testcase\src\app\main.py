# app/main.py
from contextlib import asynccontextmanager
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
import httpx
from routers import user_management_router, with_knowledgebase_router, without_knowledgebase_router, create_knowledgebase_router, create_project_profile_router, job_management_router, run_testscript_generation_router
from utilities import setup_application

from logging_middleware import RequestLoggingMiddleware

@asynccontextmanager
async def lifespan(app: FastAPI):
    app.state.http_client = httpx.AsyncClient()
    yield
    await app.state.http_client.aclose()

app = FastAPI(lifespan=lifespan)

setup_application()

# Allow all origins with specific methods and headers
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Change this to a list of allowed origins
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)
app.add_middleware(RequestLoggingMiddleware)

# Include the items router under a specific prefix

app.include_router(user_management_router.router, prefix="/ai_testcase/user", tags=["user management"])

app.include_router(job_management_router.router, prefix="/ai_testcase/job", tags=["jobs management"])

app.include_router(without_knowledgebase_router.router, prefix="/ai_testcase/run", tags=["testcase without knowledgebase"])

app.include_router(with_knowledgebase_router.router, prefix="/ai_testcase/run_kb", tags=["testcase with knowledgebase"])

app.include_router(create_project_profile_router.router, prefix="/ai_testcase/profile", tags=["project profile creation"])

app.include_router(create_knowledgebase_router.router, prefix="/ai_testcase/knowledgebase", tags=["knowledgebase creation"])

app.include_router(run_testscript_generation_router.router, prefix="/ai_testcase/run_ts", tags=["test script generation"])

