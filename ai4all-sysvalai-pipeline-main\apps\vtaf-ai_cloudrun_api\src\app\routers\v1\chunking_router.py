"""
chunking.py

Defines API endpoints for chunking-related operations, including health check,
fetching supported parsers, and single file parsing. Relies on token-based
authorization and external service communication.
"""

import httpx
import fastapi
import logging

from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import ORJ<PERSON>NResponse

from app.core.constants import CHUNKING_URL
from app.utils.http_retry import fetch_with_retry
from app.dependencies.ai4all_auth import get_id_token
from app.dependencies.http_client_session import get_ai4all_client
from app.routers.v1.schemas.chunking_requests import ChunkStringRequest

logger = logging.getLogger("vtaf")
chunking_router = APIRouter(prefix="/chunking")


@chunking_router.get(
    "/health",
    response_class=ORJSONResponse,
    description="Health check endpoint for the data chunking service",
    summary="Health Check API",
    status_code=fastapi.status.HTTP_200_OK,
    operation_id="chunking_health_check"
)
async def chunker_health_check(client=Depends(get_ai4all_client)) -> dict:
    """
    Health check endpoint that forwards the request to the external chunking service.
    """
    logger.info("Chunk Health check request received")

    try:
        response = await fetch_with_retry(f"{CHUNKING_URL}/health", client=client)
    except Exception as exc:
        logger.error(f"Parsing health check failed: {exc}")
        raise HTTPException(status_code=502, detail=f"Health check failed: {exc}")

    return response.json()


@chunking_router.get(
    "/supported-chunking-strategies",
    response_class=ORJSONResponse,
    description="Supported chunking strategies for the data chunking service",
    summary="Supported Chunking Strategies API",
    status_code=fastapi.status.HTTP_200_OK,
)
async def get_supported_chunking_strategies(id_token: str = Depends(get_id_token), client=Depends(get_ai4all_client)):
    """
    Retrieves the list of supported chunking strategies from the external service.
    """
    headers = {"Authorization": f"Bearer {id_token}"}

    try:
        response = await client.get(
                f"{CHUNKING_URL}/v1alpha1/supported-chunking-strategies",
                headers=headers,
                timeout=10,
            )
    except httpx.RequestError as exc:
        logger.error(f"Chunking strategies request failed: {exc}")
        raise HTTPException(status_code=502, detail=f"Network error: {exc}") from exc

    if response.status_code != 200:
        logger.error(f"External API returned status {response.status_code}")
        raise HTTPException(
            status_code=500, detail="Failed to fetch supported chunking strategies"
        )

    return response.json()


@chunking_router.post(
    "/single-string-chunking",
    response_class=ORJSONResponse,
    description="Chunks a single string using the data chunking service",
    summary="Single String Chunking API",
    status_code=fastapi.status.HTTP_200_OK,
)
async def chunk_single_string(
    request: ChunkStringRequest, id_token: str = Depends(get_id_token), client=Depends(get_ai4all_client)
):
    """
    Chunks a single input string using the external chunking service.
    """
    headers = {"Authorization": f"Bearer {id_token}"}

    try:
        response = await client.post(
            f"{CHUNKING_URL}/v1alpha1",
            headers=headers,
            json=request.model_dump(),
            timeout=10,
            )
    except httpx.RequestError as exc:
        logger.error(f" Single string chunking request failed: {exc}")
        raise HTTPException(status_code=502, detail=f"Network error: {exc}") from exc

    if response.status_code != fastapi.status.HTTP_200_OK:
        logger.error(f"External API returned status {response.status_code}")
        raise HTTPException(status_code=500, detail="Failed to chunk text")

    return response.json()
