name: vtaf-sdk_build_publish

on:
  push:
    branches:
      - main
    paths:
      - 'apps/vtaf-ai_client_sdk/**'

jobs:
  sdk_publish_job:
    name: Build and Upload vtaf-sdk
    runs-on: ubuntu-latest
    permissions:
      contents: 'read'
      id-token: 'write'

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v2

      - name: Update CA Certificate
        run: |
          echo "${{ secrets.JFROG_CERTIFICATE }}" > certificate.crt
          sudo cp certificate.crt /usr/local/share/ca-certificates/
          sudo update-ca-certificates
          cat certificate.crt

      - name: JFrog Ping Test
        run: jf rt ping --url ${{ vars.JFROG_URL }}/artifactory

      - name: JFrog Add Config
        run: jf c add --access-token ${{ secrets.JFROG_TOKEN }} --url ${{ vars.JFROG_URL }}

      - name: JFrog Configuration Check
        run: jf c s

      - name: JFrog List Artifacts
        run: jf rt s --recursive ri-artifactory-test-local/*

      - name: 🐍 Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: 📦 Install Poetry
        run: |
          pip install poetry
          poetry config virtualenvs.create false

      - name: 🧹 Clean old build artifacts
        working-directory: ./apps/vtaf-ai_client_sdk/src/vtaf-ai-api-client
        run: |
          rm -rf dist *.egg-info
          find . -type d -name "__pycache__" -exec rm -rf {} +

      - name: 📜 Install dependencies
        working-directory: ./apps/vtaf-ai_client_sdk/src/vtaf-ai-api-client
        run: poetry install --no-root

      - name: 🏗️ Build wheel and sdist
        working-directory: ./apps/vtaf-ai_client_sdk/src/vtaf-ai-api-client
        run: poetry build

      - name: 🔐 Authenticate to Google Cloud
        uses: digit-actions/configure-gcp-credentials@v2.1.3
        with:
          credentials_json: '${{ secrets.TECH_KEY_DEV }}'

      - name: 📤 Upload to GCP Artifact Registry (PyPI)
        working-directory: ./apps/vtaf-ai_client_sdk/src/vtaf-ai-api-client
        run: |
          pip install twine
          twine upload \
            --repository-url https://europe-west1-python.pkg.dev/valeo-cp2673-dev/vtaf-ai-pypi/ \
            --username=oauth2accesstoken \
            --password="$(gcloud auth print-access-token)" \
            dist/*
