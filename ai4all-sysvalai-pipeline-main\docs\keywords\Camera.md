# Group {Camera}
## Capture_Video

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Capture_Video.seq*


**Descriptions:** *This Keyword is used to snap a video for specific duration in seconds.*


***Command Set:***

	Full
		-Full=Alias;FPS;Seconds
	Start
		-Start=Alias;FPS
	Stop
		-Stop=Alias


***Input Description***



## Snap_Image

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Snap_Image.seq*


**Descriptions:** *This keyword is used to snap the image*


***Command Set:***

	Full
		-Full=Source
	ROI
		-ROI=ROI;Source


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| ROI| String| NA| 185:183;187:184| Enter the ROI in this field.
Example format
ROI="185:183;187:184"| Required
| Source| String| NA| ECU| Config Camera name from NI Max| Optional

