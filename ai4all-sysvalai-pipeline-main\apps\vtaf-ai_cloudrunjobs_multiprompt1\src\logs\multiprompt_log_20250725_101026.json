{"session_id": "test_session_001", "conversation": [{"role": "user", "content": "你好，请帮我生成一个汽车测试用例"}, {"role": "assistant", "content": "好的！以下是一个汽车功能测试用例：\n\nTest Case ID: TC_001\nTest Objective: 验证车辆启动功能\nTest Condition: 车辆处于停车状态，钥匙在车内\nTest Action: 1. 踩下刹车踏板 2. 按下启动按钮\nTest Expectation: 发动机成功启动，仪表盘显示正常"}, {"role": "user", "content": "很好，再生成一个关于刹车系统的测试用例"}, {"role": "assistant", "content": "这里是刹车系统测试用例：\n\nTest Case ID: TC_002\nTest Objective: 验证紧急刹车功能\nTest Condition: 车辆以50km/h速度行驶\nTest Action: 用力踩下刹车踏板\nTest Expectation: 车辆在安全距离内完全停止，ABS系统正常工作"}], "metadata": {"timestamp": "2025-07-25T10:10:26.077518", "model": "gemini-1.5-pro", "total_tokens": 150}, "timestamp": "20250725_101026", "saved_at": "2025-07-25T10:10:26.093211"}