"""
parsing.py

Defines API endpoints for parsing-related operations, including health check,
fetching supported parsers, and single file parsing. Relies on token-based
authorization and external service communication.
"""

import httpx
import logging
import fastapi

from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import ORJSONResponse

from app.core.constants import PARSINGS_URL
from app.utils.http_retry import fetch_with_retry
from app.dependencies.ai4all_auth import get_id_token
from app.dependencies.http_client_session import get_ai4all_client
from app.routers.v1.schemas.file_parsing_requests import ParseFileRequest

logger = logging.getLogger("vtaf")
parsing_router = APIRouter(prefix="/parsing")


@parsing_router.get(
    "/health",
    response_class=ORJSONResponse,
    description="Health check endpoint for the data parsing service",
    summary="Health Check API",
    status_code=fastapi.status.HTTP_200_OK,
    operation_id="parsing_health_check"
)
async def parser_health_check(client=Depends(get_ai4all_client)) -> dict:
    """
    Health check endpoint that forwards the request to the external parsing service.
    """
    logger.info("Parsing Health check request received")

    try:
        response = await fetch_with_retry(f"{PARSINGS_URL}/health", client=client)
    except Exception as exc:
        logger.error(f"Parsing health check failed: {exc}")
        raise HTTPException(status_code=502, detail=f"Health check failed: {exc}")

    return response.json()


@parsing_router.get(
    "/supported-parsers",
    response_class=ORJSONResponse,
    description="Supported parsers for the data parsing service ",
    summary="Supported Parsers API",
    status_code=fastapi.status.HTTP_200_OK,
)
async def get_supported_parsers(id_token: str = Depends(get_id_token), client=Depends(get_ai4all_client)):
    """
    Retrieves the list of supported parsers from the external parsing service.
    """
    headers = {"Authorization": f"Bearer {id_token}"}

    try:
        response = await client.get(
            f"{PARSINGS_URL}/v1alpha1/supported-parsers",
            headers=headers,
            timeout=10,
        )
    except httpx.RequestError as exc:
        logger.error(f"Parsing strategies request failed: {exc}")
        raise HTTPException(status_code=502, detail=f"Parsing network error: {exc}") from exc

    if response.status_code != 200:
        logger.error(f"External API returned status {response.status_code}")
        raise HTTPException(status_code=500, detail="Failed to fetch supported parsers")

    return response.json()


@parsing_router.post(
    "/single-file-parsing",
    response_class=ORJSONResponse,
    description="Parses a single file using the data parsing service ",
    summary="Single File Parsing API",
    status_code=fastapi.status.HTTP_200_OK,
)
async def parse_single_file(
    request: ParseFileRequest, id_token: str = Depends(get_id_token), client=Depends(get_ai4all_client)
):
    """
    Sends a file parsing request to the external parsing service.
    """
    headers = {"Authorization": f"Bearer {id_token}"}

    try:
        response = await client.post(
            f"{PARSINGS_URL}/v1alpha1",
            headers=headers,
            json=request.model_dump(),
            timeout=40,
        )
    except httpx.RequestError as exc:
        logger.error(f" Single file parsing request failed: {exc}")
        raise HTTPException(status_code=502, detail=f"Network error: {exc}") from exc

    if response.status_code != 200:
        logger.error(f"External API returned status {response.status_code}")
        raise HTTPException(
            status_code=response.status_code,
            detail=response.json().get("detail", "Parsing failed"),
        )

    return response.json()
