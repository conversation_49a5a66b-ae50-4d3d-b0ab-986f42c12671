from google.api_core.exceptions import BadRequest, GoogleAPIError
from google.cloud import bigquery

from configs.env import settings
from utils.logger import get_logger

logger = get_logger(__name__)


class BigQueryHandler:
    def __init__(self):
        try:
            # Initialize BigQuery client with explicit project from env settings
            self.client = bigquery.Client(project=settings.PROJECT_ID)
            self.table_id = f"{settings.PROJECT_ID}.{settings.BQ_DATA_SET}.{settings.BQ_TABLE}"
            logger.info("BigQuery client initialized successfully.")
        except Exception as e:
            logger.error(f"Failed to initialize BigQuery client: {e}")
            raise

    def bq_select_query(self, query: str):
        """
        Executes a SELECT query and returns the results.

        Args:
            query (str): SQL select query.

        Returns:
            list: List of rows or empty list on failure.
        """
        try:
            query_job = self.client.query(query)
            results = query_job.result()
            logger.info(f"Select query executed successfully, fetched {results.total_rows} rows.")
            return results
        except (BadRequest, GoogleAPIError) as e:
            logger.error(f"BigQuery API error while executing select query: {e}")
        except Exception as e:
            logger.error(f"Unexpected error while executing select query: {e}")
        return []
