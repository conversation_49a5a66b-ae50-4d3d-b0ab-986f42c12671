from vtaf_ai_api_client.api.agents_v_1 import (
    demo_doors_extract_v1_agents_bigquery_extract_post,
    generate_test_script_v1_agents_generate_test_script_post,
    health_check_v1_agents_health_get,
    summarize_text_v1_agents_summarize_text_post,
)
from vtaf_ai_api_client.models import (
    GenerateTestScriptRequest,
    QueryRequest,
    SummarizationRequest,
)


class AgentsAPI:
    def __init__(self, client):
        self.client = client

    def health_check(self):
        return health_check_v1_agents_health_get.sync(client=self.client).additional_properties

    def summarize_text(self, text: str, email: str = None, job_id: str = None, enable_tracing: bool = True):
        req = SummarizationRequest(text=text, email=email, job_id=job_id, enable_tracing=enable_tracing)
        return summarize_text_v1_agents_summarize_text_post.sync(client=self.client, body=req)

    def generate_test_script(self, testcase: dict, email: str = None, job_id: str = None, enable_tracing: bool = True):
        req = GenerateTestScriptRequest(testcase=testcase, email=email, job_id=job_id, enable_tracing=enable_tracing)
        return generate_test_script_v1_agents_generate_test_script_post.sync(client=self.client, body=req)

    def bigquery_extract(self, query: str, dataset: str):
        req = QueryRequest(query=query, dataset=dataset)
        return demo_doors_extract_v1_agents_bigquery_extract_post.sync(client=self.client, body=req)
