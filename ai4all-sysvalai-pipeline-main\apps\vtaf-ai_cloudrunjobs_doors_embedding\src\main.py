from configs.env import settings
from services.bigquery_repository import BigQueryHandler
from services.vtaf_api_clients import VtafApiClients
from utils.logger import get_logger

logger = get_logger(__name__)


def fetch_projects(bigquery):
    try:
        projects = ', '.join(f"'{p.strip()}'" for p in settings.Projects.split(','))

        query = (
                f"SELECT DISTINCT(project) FROM `{settings.PROJECT_ID}.{settings.BQ_DATA_SET}.{settings.BQ_TABLE}` "
                f"WHERE project IN ({projects}) ORDER BY project"
            )
        results = bigquery.bq_select_query(query)
        return results
    except Exception as e:
        logger.error(f"Error fetching project: {e}")


def fetch_requirements(project):
    try:
        query = (f'SELECT * FROM `{settings.PROJECT_ID}.{settings.BQ_DATA_SET}.{settings.BQ_TABLE}`'
                 f'Where project = "{project}"'
                 f'And aObjectStatus = "Accepted"'
                 f'and aObjectType in ("Req functional", "Req non functional")'
                 f'And aVerificationStrategy = "SysTest"')
        results = bigquery.bq_select_query(query)
        return results

    except Exception as e:
        logger.error(f"Error fetching requirements {project.get('project')}: {e}")


def process_projects(project, vtaf_apis):
    try:
        results = fetch_requirements(project.get('project'))
        for row in results:
            requirement_data = dict(row.items())
            logger.info(f"Processing Requirements: {requirement_data.get('Object_Identifier')}")
            if requirement_data.get("Object_Text"):
                summarize_data = vtaf_apis.summarize_text(requirement_data.get("Object_Text"))
                if summarize_data.strip():
                    metadata = {
                        "Project": requirement_data.get("Project"),
                        "Module_name": requirement_data.get("Module_name"),
                        "project_str_code": requirement_data.get("project_str_code"),
                        "project_custom_id": requirement_data.get("project_custom_id"),
                        "UniqueObjectIndex": requirement_data.get("UniqueObjectIndex"),
                        "Object_Identifier": requirement_data.get("Object_Identifier"),
                        "aObjectStatus": requirement_data.get("aObjectStatus"),
                        "aObjectType": requirement_data.get("aObjectType"),
                        "Object_Heading": requirement_data.get("Object_Heading"),
                        "aVerificationStrategy": requirement_data.get("aVerificationStrategy"),
                        "aAffected": requirement_data.get("aAffected"),
                        "aASIL": requirement_data.get("aASIL"),
                        "In_links": requirement_data.get("In_links"),
                        "Out_links": requirement_data.get("Out_links"),
                        "job_id": settings.CLOUD_RUN_EXECUTION
                    }
                    vtaf_apis.add_chunks_to_vector_db(summarize_data.strip(), metadata)
    except Exception as e:
        logger.error(f"Error processing project {project.get('project')}: {e}")


if __name__ == '__main__':
    bigquery = BigQueryHandler()
    vtaf_apis = VtafApiClients()

    all_projects = fetch_projects(bigquery)

    task_index = int(settings.CLOUD_RUN_TASK_INDEX)
    num_tasks = int(settings.CLOUD_RUN_TASK_COUNT)

    all_projects = [dict(row.items()) for row in all_projects]
    logger.info(f"Processing project: {all_projects[task_index]}")
    process_projects(all_projects[task_index], vtaf_apis)
