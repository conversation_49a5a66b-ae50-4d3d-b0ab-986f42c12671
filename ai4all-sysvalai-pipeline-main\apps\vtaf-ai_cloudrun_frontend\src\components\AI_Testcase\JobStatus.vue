<template>
  <q-page class="flex flex-center q-pa-md">
    <q-card
      class="q-pa-lg full-width status-card"
      v-if="current_job_execution_id_status != null"
    >
      <q-card-section>
        <div class="text-h5">Job Status</div>
        <div class="text-subtitle1 text-blue-10">
          Execution ID: {{ current_job_execution_id_status.execution_id }}
        </div>
      </q-card-section>

      <q-separator />

      <!-- First Row -->
      <q-card-section class="row q-col-gutter-md">
        <div class="col-4">
          <q-item-label class="text-bold">BG:</q-item-label>
          <q-item-label class="text-h6 text-blue-10">{{
            current_job_execution_id_status.bg
          }}</q-item-label>
        </div>
        <div class="col-4">
          <q-item-label class="text-bold">PG:</q-item-label>
          <q-item-label class="text-h6 text-blue-10">{{
            current_job_execution_id_status.pg
          }}</q-item-label>
        </div>
        <div class="col-4">
          <q-item-label class="text-bold">PL:</q-item-label>
          <q-item-label class="text-h6 text-blue-10">{{
            current_job_execution_id_status.pl
          }}</q-item-label>
        </div>
      </q-card-section>

      <!-- Second Row -->
      <q-card-section class="row q-col-gutter-md">
        <div class="col-4">
          <q-item-label class="text-bold">Start Time:</q-item-label>
          <q-item-label class="text-h6 text-blue-10">{{
            formatDate(current_job_execution_id_status.job_start_time)
          }}</q-item-label>
        </div>
        <div class="col-4">
          <q-item-label class="text-bold">End Time:</q-item-label>
          <q-item-label class="text-h6 text-blue-10">{{
            formatDate(current_job_execution_id_status.job_end_time)
          }}</q-item-label>
        </div>
        <div class="col-4">
          <q-item-label class="text-bold">Job Duration:</q-item-label>
          <q-item-label class="text-h6 text-blue-10">{{
            current_job_execution_id_status.job_duration || "--"
          }}</q-item-label>
        </div>
      </q-card-section>

      <!-- Third Row -->
      <q-card-section class="row q-col-gutter-md">
        <div class="col-6">
          <q-item-label class="text-bold">Job Created By:</q-item-label>
          <q-item-label class="text-h6 text-blue-10">{{
            current_job_execution_id_status.user_email
          }}</q-item-label>
        </div>
        <div class="col-6">
          <q-item-label class="text-bold">Execution Status:</q-item-label>
          <q-item-label
            :class="
              getStatusColor(current_job_execution_id_status.execution_status)
            "
            class="text-h6"
          >
            {{ current_job_execution_id_status.execution_status }}
          </q-item-label>
        </div>
      </q-card-section>

      <q-separator />

      <!-- Fourth Row -->
      <q-card-section class="row q-col-gutter-md">
        <div class="col-6">
          <q-item-label class="text-bold">Completed Requirements</q-item-label>
          <q-item-label class="text-h6 text-blue-10">{{
            current_job_execution_id_status.completed_req
          }}</q-item-label>
        </div>
        <div class="col-6">
          <q-item-label class="text-bold">Total Requirements:</q-item-label>
          <q-item-label class="text-h6 text-blue-10"
            >{{ current_job_execution_id_status.total_req }}
          </q-item-label>
        </div>
      </q-card-section>

      <q-card-section class="row q-col-glutter-md">
        <div class="col-6">
          <q-item-label class="text-bold">Result Status:</q-item-label>
          <q-item-label
            class="text-h6 text-blue-10"
            :class="getStatusColor(current_job_execution_id_status.status)"
            >{{ current_job_execution_id_status.status }}
          </q-item-label>
        </div>
      </q-card-section>
      <q-card-section class="row q-col-glutter-md">
        <div class="col-6">
          <q-btn
            v-if="
              current_job_execution_id_status.execution_status == 'completed' &&
              current_job_execution_id_status.status == 'passed' &&
              current_job_execution_id_status.signed_url != ''
            "
            color="blue-10"
            label="Download Testcase"
            @click="downloadFile"
          />
          <q-btn
            v-if="
              current_job_execution_id_status.execution_status == 'completed' &&
              current_job_execution_id_status.status == 'passed' &&
              current_job_execution_id_status.drive_url != ''
            "
            color="blue-10"
            label="View Testcase"
            @click="viewTestcase"
          />
        </div>
        <div class="col-6">
          <q-btn color="blue-10" label="Refresh" @click="refreshStatus" />
        </div>
      </q-card-section>
    </q-card>
  </q-page>
  <q-inner-loading :showing="job_status_loading">
    <q-spinner-hourglass size="75px" color="blue-10" />
    <div class="q-mt-md text-blue-10 text-h6 text-weight-bold">
      Getting Job Status...
    </div>
  </q-inner-loading>
</template>

<script setup>
import { onMounted } from "vue";
import { useJobManagementStore } from "src/stores/ai_testcase/job-management-store";
import { storeToRefs } from "pinia";
import { useRoute, useRouter } from "vue-router";
// import { defineProps } from "vue";

const props = defineProps({
  execution_id: {
    type: String,
    required: true,
  },
});

const router = useRouter();
const route = useRoute();

const ai_testcase_config = useJobManagementStore();
const {
  current_job_execution_id_status,
  current_job_execution_id,
  job_status_loading,
} = storeToRefs(ai_testcase_config);
const { get_job_status } = ai_testcase_config;

onMounted(() => {
  console.log(route.params.execution_id);
  current_job_execution_id.value = route.params.execution_id;
  if (current_job_execution_id.value === "") {
    router.push("/ai_testcase");
  } else {
    get_job_status();
  }
});

const formatDate = (timestamp) => {
  if (!timestamp) return "--:--";
  return new Date(timestamp).toLocaleString();
};

const getStatusColor = (status) => {
  if (status === "started") return "text-blue-10";
  if (status === "completed" || status === "passed") return "text-green-14";
  if (status === "failed") return "text-red-8";
  return "text-grey";
};

const refreshStatus = () => {
  get_job_status();
};

const downloadFile = () => {
  const url = current_job_execution_id_status.value?.signed_url;
  if (url) {
    window.open(url, "_blank");
  } else {
    console.error("Signed URL is missing");
  }
};

const viewTestcase = () => {
  const url = current_job_execution_id_status.value?.drive_url;
  if (url) {
    window.open(url, "_blank");
  } else {
    console.error("Drive URL is missing");
  }
}
</script>

<style scoped>
.status-card {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
}
</style>
