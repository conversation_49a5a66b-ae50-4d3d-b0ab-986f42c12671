name: 🚧 Enabler
description: Track the details of an enabler task
title: "[ENABLER] "
labels: ["enabler"]

body:
  - type: markdown
    attributes:
      value: |
        ## 🚧 Enabler

        Describe the enabler task or project, its goals, and its size

  - type: dropdown
    id: enabler-type
    attributes:
      label: Enabler Type
      description: |
        Select the category that best describes this enabler.
        * **🔭 Exploration:** (Research, prototyping, feasibility studies)
        * **🏗️ Infrastructure:** (Hardware, servers, networks, cloud resources)
        * **📐 Architecture:** (System design, frameworks, technology choices)
        * **🔐 Security:** (Vulnerability patching, security hardening, access controls)
        * **⚖️ Compliance:** (Adherence to regulations, standards, or internal policies)
      options:
        - 🔭 Exploration
        - 🏗️ Infrastructure
        - 📐 Architecture
        - 🔐 Security
        - ⚖️ Compliance
    validations:
      required: true

  - type: textarea
    id: enabler-description
    attributes:
      label: Enabler Description
      description: Explain the purpose of the enabler, the problem it addresses, and the benefits it will bring.
    validations:
      required: true

  - type: textarea
    id: goals-and-objectives
    attributes:
      label: Goals and Objectives
      description: List the specific goals and measurable objectives the enabler aims to achieve.
    validations:
      required: true

  - type: dropdown
    id: enabler-priority
    attributes:
      label: Enabler Priority ⚡
      description: |
        Select the priority level for this enabler.
        * **⚪ Not Defined:** I'm not sure of this info.
        * **🔴 P1:** Highest priority, must be addressed immediately.
        * **🟠 P2:** High priority, should be addressed soon.
        * **🟡 P3:** Medium priority, can wait for a while.
        * **🟢 P4:** Low priority, can be addressed later.
        * **🔵 P5:** Lowest priority, may not be addressed at all.
      options:
        - ⚪ Not Defined
        - 🔴 P1
        - 🟠 P2
        - 🟡 P3
        - 🟢 P4
        - 🔵 P5
    validations:
      required: true

  - type: dropdown
    id: feature-size
    attributes:
      label: Enabler Size
      description: |
        Estimate the size of the enabler.
        * **⚪ Not Defined:** I'm not sure of this info.
        * **🔵 XS:** Very small, <= 1 day. (2 Points)
        * **🟢 S** Small, ~ 2-3 days. (5 Points)
        * **🟡 M** Medium, <= 1 week. (10 Points)
        * **🟠 L** Large, ~ 7-8 days. (15 Points)
        * **🔴 XL:** Very large, <= 2 weeks. (20 Points)
      options:
        - ⚪ Not Defined
        - 🔵 XS (2 Points)
        - 🟢 S (5 Points)
        - 🟡 M (10 Points)
        - 🟠 L (15 Points)
        - 🔴 XL (20 Points)
    validations:
      required: true

  - type: textarea
    id: dependencies
    attributes:
      label: Dependencies (Optional)
      description: List any tasks, resources, or approvals that need to be in place before starting the enabler.

  - type: textarea
    id: additional-notes
    attributes:
      label: Additional Notes (Optional)
      description: Include any relevant details, resources, or links.