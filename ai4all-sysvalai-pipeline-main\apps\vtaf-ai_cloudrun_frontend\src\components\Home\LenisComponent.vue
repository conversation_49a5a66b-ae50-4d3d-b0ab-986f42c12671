<!-- <template>
  <div>
    <section id="vertical">
      <div class="container">
        <div class="vertical__content">
          <div class="col col_left">
            <h2 class="vertical__heading">
              <span>About</span><span>Smooth</span><span>Scroll</span>
            </h2>
          </div>
          <div class="col col_right">
            <div
              class="vertical__item"
              v-for="(item, index) in items"
              :key="index"
            >
              <h3>{{ item.title }}</h3>
              <p>{{ item.text }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section id="horizontal">
      <div class="container">
        <div class="horizontal__content">
          <div
            class="horizontal__item"
            v-for="number in [1, 2, 3, 4, 5]"
            :key="number"
          >
            <div class="horizontal__num">{{ number }}</div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { onMounted } from "vue";
import Lenis from "@studio-freight/lenis";
import { gsap } from "gsap";
import ScrollTrigger from "gsap/ScrollTrigger";

export default {
  name: "SmoothScroll",
  data() {
    return {
      items: [
        {
          title: "Smooth Scroll Lenis",
          text: "Lenis is an open-source library built to standardize scroll experiences and sauce up websites with butter-smooth navigation, all while using the platform and keeping it accessible.",
        },
        {
          title: "Smooth Scroll Lenis",
          text: "Lenis is an open-source library built to standardize scroll experiences and sauce up websites with butter-smooth navigation, all while using the platform and keeping it accessible.",
        },
        {
          title: "Smooth Scroll Lenis",
          text: "Lenis is an open-source library built to standardize scroll experiences and sauce up websites with butter-smooth navigation, all while using the platform and keeping it accessible.",
        },
        {
          title: "Smooth Scroll Lenis",
          text: "Lenis is an open-source library built to standardize scroll experiences and sauce up websites with butter-smooth navigation, all while using the platform and keeping it accessible.",
        },
      ],
    };
  },
  onMounted() {
    gsap.registerPlugin(ScrollTrigger);

    const lenis = new Lenis({
      duration: 1.2,
      easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
    });

    function raf(time) {
      lenis.raf(time);
      ScrollTrigger.update();
      requestAnimationFrame(raf);
    }

    requestAnimationFrame(raf);

    const section_1 = document.getElementById("vertical");
    const col_left = document.querySelector(".col_left");
    const timeln = gsap.timeline({ paused: true });

    timeln.fromTo(
      col_left,
      { y: 0 },
      { y: "170vh", duration: 1, ease: "none" },
      0
    );

    ScrollTrigger.create({
      animation: timeln,
      trigger: section_1,
      start: "top top",
      end: "bottom center",
      scrub: true,
    });

    const section_2 = document.getElementById("horizontal");
    let box_items = gsap.utils.toArray(".horizontal__item");

    gsap.to(box_items, {
      xPercent: -100 * (box_items.length - 1),
      ease: "sine.out",
      scrollTrigger: {
        trigger: section_2,
        pin: true,
        scrub: 3,
        snap: 1 / (box_items.length - 1),
        end: "+=" + section_2.offsetWidth,
      },
    });
  },
};
</script>

<style scoped>

html {
  scroll-behavior: initial;
  overflow: hidden;
}

html,
body {
  width: 100%;
  min-height: 100%;
}

body {
  font-family: Slussen;
  font-size: 16px;
  font-weight: 400;
  background: #000;
  color: #fff;
}

h2 {
  font-size: 60px;
  font-weight: 900;
  line-height: 85%;
  border-left: 3px solid #ff98a2;
  padding: 25px;
  margin: 0;
}

h2 span {
  display: block;
}

h3 {
  font-size: 20px;
  font-stretch: expanded;
  color: #ff98a2;
  line-height: 100%;
}

h2,
h3,
h4 {
  text-transform: uppercase;
}

.container {
  width: 95%;
  margin: auto;
}

section {
  padding: 50px 0;
}

.col {
  width: 50%;
}

#vertical {
  height: 200vh;
  width: 100vw;
}

.vertical__content {
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.vertical__content .col_left {
  height: 100%;
}

.vertical__content .col.col_right {
  width: 40%;
}

.vertical__item:not(:last-child) {
  margin-bottom: 240px;
}

#horizontal {
  padding: 100px 0;
}

.horizontal__content {
  display: flex;
}

.horizontal__item {
  border: 1px solid #efefef;
  padding: 200px 150px;
}
.horizontal__item:not(:last-child) {
  margin-right: 50px;
}

.horizontal__num {
  font-size: 80px;
  font-weight: 900;
  font-stretch: condensed;
  color: #ff98a2;
}
</style> -->

<!--##########################################################-->
<!-- <template>
  <div class="html1">
    <section id="vertical">
      <div class="container">
        <div class="vertical__content">
          <div class="col col_left" :style="getfeaturesStyle">
            <h2 class="vertical__heading">
              <span>ABOUT</span><span>VTAF</span><span>FEATURES</span>
            </h2>
          </div>
          <div class="col col_right">
            <div
              class="vertical__item"
              v-for="(item, index) in cardData"
              :key="index"
            >
              <q-card class="my-card" :style="getCardStyle">
                <q-card-section class="card-content">
                  <div class="text-h4" style="color: #84f593">
                    {{ item.title }}
                  </div>
                  <q-avatar square size="100px" class="card-avatar">
                    <img :src="item.image" />
                  </q-avatar>
                  <div class="card-description">{{ item.description }}</div>
                </q-card-section>
              </q-card>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section id="horizontal">
      <div class="container">
        <div class="horizontal__content">
          <div
            class="horizontal__item"
            v-for="number in [1, 2, 3, 4]"
            :key="number"
          >
            <div class="horizontal__num">{{ number }}</div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template> -->
<!--###########################################################################################-->
<!-- <template>
  <div class="html1">
    <section id="vertical" class="parallax-section">
      <div class="parallax-background"></div>
      <div class="container">
        <div class="vertical__content">
          <div class="col col_left" :style="getfeaturesStyle">
            <h2 class="vertical__heading">
              <span>ABOUT</span><span>VTAF</span><span>FEATURES</span>
            </h2>
          </div>
          <div class="col col_right">
            <div
              class="vertical__item"
              v-for="(item, index) in cardData"
              :key="index"
            >
              <q-card class="my-card" :style="getCardStyle">
                <q-card-section class="card-content">
                  <div class="text-h4" style="color: #84f593">
                    {{ item.title }}
                  </div>
                  <q-avatar square size="100px" class="card-avatar">
                    <img :src="item.image" />
                  </q-avatar>
                  <div class="card-description">{{ item.description }}</div>
                </q-card-section>
              </q-card>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section id="horizontal">
      <div class="container">
        <div class="horizontal__content">
          <div
            class="horizontal__item"
            v-for="(item, index) in slides"
            :key="index"
          >
            <q-card class="slide">
              <div class="slide_image_holder">
                <q-img :src="item.image" class="slide_image"></q-img>
              </div>
              <div class="slide_title text-green-10">
                <span style="font-weight: bold">{{ item.title }}</span
                ><br />
                <span style="font-size: 20px; font-style: italic">
                  {{ item.subtitle }}
                </span>
              </div>
              <div class="slide_quote_image">
                <q-icon name="format_quote" size="50px" color="green" />
              </div>
              <div class="slide_text text-green-10">
                <span style="font-size: 20px; font-style: italic">
                  {{ item.text }}
                </span>
              </div>
            </q-card>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { useQuasar } from "quasar";
import { onMounted, computed } from "vue";
import Lenis from "@studio-freight/lenis";
import { gsap } from "gsap";
const $q = useQuasar();
import ScrollTrigger from "gsap/ScrollTrigger";

const getCardStyle = computed(() => {
  const cardStyle = {
    background: "transparent",
    minHeight: "15rem",
    maxHeight: "15rem",
  };

  if ($q.screen.gt.sm && $q.screen.lt.lg) {
    cardStyle.minHeight = "18rem";
    cardStyle.maxHeight = "18rem";
  }

  return cardStyle;
});

const cardData = [
  {
    title: "LabVIEW",
    image: "src/assets/home/<USER>",
    description:
      "LabVIEW is a graphical programming environment that provides unique productivity accelerators for test system development.",
  },
  {
    title: "TestStand",
    image: "src/assets/home/<USER>",
    description:
      "TestStand is a test executive software used to develop test software for products which are produced by an enterprise.",
  },
  {
    title: "Python",
    image: "src/assets/home/<USER>",
    description:
      "Python is a general-purpose language, meaning it can be used to create variety of programs and isn't specialized for any problems.",
  },
  {
    title: "CAPL",
    image: "src/assets/home/<USER>",
    description:
      "CAPL is a procedural programming language similar to C developed by vector. Execution of blocks is controlled by events.",
  },
];
const slides = [
  {
    name: "Slide 1",
    text: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Aspernatur eum quas nisi mollitia quam earum quis",
    image: "src/assets/home/<USER>",
    title: "Title 1",
    subtitle: "Subtitle 1",
  },
  {
    name: "Slide 2",
    text: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Aspernatur eum quas nisi mollitia quam earum quis",
    image: "src/assets/home/<USER>",
    title: "Title 2",
    subtitle: "Subtitle 2",
  },
  {
    name: "Slide 3",
    text: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Aspernatur eum quas nisi mollitia quam earum quis",
    image: "src/assets/home/<USER>",
    title: "Title 3",
    subtitle: "Subtitle 3",
  },
  {
    name: "Slide 4",
    text: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Aspernatur eum quas nisi mollitia quam earum quis",
    image: "src/assets/home/<USER>",
    title: "Title 4",
    subtitle: "Subtitle 4",
  },
];
onMounted(() => {
  gsap.registerPlugin(ScrollTrigger);

  const lenis = new Lenis({
    duration: 1.2,
    easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
  });

  function raf(time) {
    lenis.raf(time);
    ScrollTrigger.update();
    requestAnimationFrame(raf);
  }

  requestAnimationFrame(raf);

  const section_1 = document.getElementById("vertical");
  const col_left = document.querySelector(".col_left");
  const timeln = gsap.timeline({ paused: true });

  timeln.fromTo(
    col_left,
    { y: 0 },
    { y: "170vh", duration: 1, ease: "none" },
    0
  );

  ScrollTrigger.create({
    animation: timeln,
    trigger: section_1,
    start: "top top",
    end: "bottom center",
    scrub: true,
  });

  const section_2 = document.getElementById("horizontal");
  let box_items = gsap.utils.toArray(".horizontal__item");

  gsap.to(box_items, {
    xPercent: -100 * (box_items.length - 1),
    ease: "sine.out",
    scrollTrigger: {
      trigger: section_2,
      pin: true,
      scrub: 3,
      snap: 1 / (box_items.length - 1),
      end: "+=" + section_2.offsetWidth,
    },
  });
});
</script>

<style scoped>
.html1 {
  scroll-behavior: initial;
  overflow: hidden;
  width: 100%;
  min-height: 100%;
  font-family: Slussen;
  font-size: 16px;
  font-weight: 400;
  background: #000;
  color: #fff;
}

h2 {
  font-size: 60px;
  font-weight: 900;
  line-height: 85%;
  border-left: 3px solid #84f593;
  padding: 25px;
  margin: 0;
}

h2 span {
  display: block;
}

.container {
  width: 95%;
  margin: auto;
}

section {
  padding: 50px 0;
}

.col {
  width: 50%;
}

#vertical {
  height: 200vh;
  width: 100vw;
}

.vertical__content {
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.vertical__content .col_left {
  height: 100%;
}

.vertical__content .col.col_right {
  width: 40%;
}

.vertical__item:not(:last-child) {
  margin-bottom: 240px;
}

.my-card {
  background: transparent;
  min-height: 15rem;
  max-height: 15rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.card-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.card-avatar {
  margin-left: 20px;
  margin-right: 20px;
  transition: transform 0.8s ease;
}

.card-avatar:hover {
  transform: scale(1.1); /* Zoom in the avatar */
}

.card-description {
  flex-grow: 1;
  text-align: left;
  font-size: 20px;
}

#horizontal {
  padding: 100px 0;
}

.horizontal__content {
  display: flex;
}

.horizontal__item {
  border: 1px solid #84f593;
  padding: 20px 15px;
}

.horizontal__item:not(:last-child) {
  margin-right: 50px;
}

.slide {
  min-width: 400px;
  min-height: 400px;
}

.slide_image {
  text-align: center;
  border-radius: 1rem;
  width: 30%;
  margin: 0 auto;
  margin-bottom: 2rem;
  margin-top: 2rem;
  margin-left: 3rem;
}

.slide_title {
  margin-left: 3rem;
}

.slide_quote_image {
  margin-left: 2.5rem;
}

.slide_text {
  margin-left: 3rem;
}
.parallax-section {
  position: relative;
  overflow: hidden;
}

.parallax-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("path_to_your_image.jpg"); /* Replace with your image */
  background-size: cover;
  background-position: center;
  z-index: -1;
  transform: translateZ(-1px) scale(2); /* Makes the background move slower */
}

/* Additional styling to ensure proper layout */
.container {
  position: relative;
  z-index: 1;
}
</style> -->
<!--#############################################################################-->
<template>
  <div class="scrolling-container" ref="scrollingContainer">
    <div class="card-container" ref="cardContainer">
      <div
        v-for="(card, index) in cards"
        :key="index"
        class="card"
        :style="{ transform: `translateX(${card.translateX}px)` }"
      >
        <q-card>
          <q-card-section>
            <h6>{{ card.title }}</h6>
            <p>{{ card.content }}</p>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, nextTick } from "vue";
import Lenis from "@studio-freight/lenis";
import { gsap } from "gsap";

export default {
  name: "ScrollingCards",
  setup() {
    const cards = ref([
      { title: "Card 1", content: "Content for Card 1", translateX: 100 },
      { title: "Card 2", content: "Content for Card 2", translateX: 100 },
      { title: "Card 3", content: "Content for Card 3", translateX: 100 },
      { title: "Card 4", content: "Content for Card 4", translateX: 100 },
      { title: "Card 5", content: "Content for Card 5", translateX: 100 },
    ]);

    const scrollingContainer = ref(null);
    const cardContainer = ref(null);
    const lenis = new Lenis();

    const initScroll = () => {
      lenis.on("scroll", (e) => {
        // GSAP animation for cards as you scroll
        const scrollY = e.scroll.y;

        cards.value.forEach((card, index) => {
          if (scrollY > index * 300) {
            gsap.to(card, {
              translateX: 0,
              duration: 0.5,
              ease: "power2.out",
            });
          }
        });
      });

      requestAnimationFrame(initScroll);
    };

    onMounted(() => {
      nextTick(() => {
        initScroll();
      });
      lenis.start();
    });

    return {
      cards,
      scrollingContainer,
      cardContainer,
    };
  },
};
</script>

<style scoped>
.scrolling-container {
  position: relative;
  height: 100vh;
  overflow: hidden;
}

.card-container {
  position: sticky;
  top: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding-left: 2rem;
}

.card {
  margin-left: 2rem;
  opacity: 0;
  transform: translateX(100%);
}
</style>
