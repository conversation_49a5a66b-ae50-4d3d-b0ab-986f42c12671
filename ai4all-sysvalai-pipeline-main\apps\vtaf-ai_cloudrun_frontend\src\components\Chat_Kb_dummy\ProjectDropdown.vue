<template>
  <div class="q-ma-md">
    <q-select
    filled
            dense
            use-input
            input-debounce="0"
      v-model="selected_project"
      outlined
      :options="filteredFunctions"
            @filter="filterFn"
            emit-value
            map-options
            clearable
            label="Select Project"
    />
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useChatWithKbStore } from "src/stores/chat_with_kb_dummy-store";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";

const router = useRouter();

const ai_testcase_config = useChatWithKbStore();
const { user_email, project_dropdown, selected_project, user_prompt, output } =
  storeToRefs(ai_testcase_config);
const { get_user_email, get_projects, get_response } = ai_testcase_config

onMounted(async () => {
  await get_projects();
});
const filterQuery = ref("");
const filteredFunctions = computed(() => {
  return project_dropdown.value.filter((table) =>
    table.toLowerCase().includes(filterQuery.value.toLowerCase())
  );
});
const filterFn = (val, update) => {
  filterQuery.value = val;
  update();
};
</script>
