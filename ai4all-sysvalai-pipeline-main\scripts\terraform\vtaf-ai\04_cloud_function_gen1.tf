# Generates An Archive Of The Source Code Compressed As A .zip file.

data "archive_file" "vtaf_ai_config_sync_function_source" {
  type        = "zip"
  source_dir  = "../../../apps/vtaf-ai_cloudfunction_sheet_sync/src"
  output_path = "${path.module}/vtaf-ai_cloudfunction_sheet_sync.zip"
}

# Add Source Code Zip To The Cloud Function's Bucket (Cloud_function_bucket)

resource "google_storage_bucket_object" "vtaf_ai_config_sync_function_zip" {
  source       = data.archive_file.vtaf_ai_config_sync_function_source.output_path
  content_type = "application/zip"
  name         = "src-${data.archive_file.vtaf_ai_config_sync_function_source.output_md5}.zip"
  bucket       = "${var.project_id}-vtaf-ai-config-sync-function-bucket"
  depends_on = [
    data.archive_file.vtaf_ai_config_sync_function_source
  ]
}

# To Create the Cloud Function For VTAF.AI Config sync GEN1 Cloud Function

resource "google_cloudfunctions_function" "vtaf_ai_config_sync_function" {
  provider = google-beta

  project = var.project_id
  region  = var.region

  name        = "vtaf-ai-cloudfunction-sheet-sync"
  description = "${var.vtaf_ai_project} configuration synchronization function"
  runtime     = "python311"

  available_memory_mb = 1024
  timeout             = 60

  source_archive_bucket = "${var.project_id}-vtaf-ai-config-sync-function-bucket"
  source_archive_object = google_storage_bucket_object.vtaf_ai_config_sync_function_zip.name
  trigger_http          = true
  entry_point           = "vtaf_config_sync"

  environment_variables = {
    BQ_PROJECT_ID = var.project_id
    BQ_DATA_SET   = var.bigquery_dataset_id
    LOCATION      = var.location
    SHEET_ID      = var.config_sheet_id
  }

  ingress_settings             = "ALLOW_ALL"
  https_trigger_security_level = "SECURE_ALWAYS"
  service_account_email        = var.service_account_email

}




