from langfuse import Langfuse

# langfuse = Langfuse(
#   secret_key="sk-lf-ac16d133-77b6-4cbc-95eb-f9ac5c40b5df",
#   public_key="pk-lf-26998b31-1372-4a8b-8b23-900cc63b873c",
#   host="http://che7-w10172:3000"
# )


langfuse = Langfuse(
  secret_key="sk-lf-407f8278-81ee-4b25-82a3-7d213490f592",
  public_key="pk-lf-66a44f8b-507e-4929-82a0-709d6fc4ef46",
  host="https://cp2532.apps-dev.valeo.com/utils/langfuse/v3"
)

print(langfuse.client.health.health())
try:
    trace = langfuse.trace(
    name = "docs-retrieval",
    user_id = "user__935d7d1d-8625-4ef4-8651-544613e7bd22",
    metadata = {
        "email": "<EMAIL>",
    },
    tags = ["production"]
    )

    trace.update(input="Hi there")
    langfuse.flush()
except:
    print("error")