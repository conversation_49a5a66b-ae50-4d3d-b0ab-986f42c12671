#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TS Training Dataset Service - TS训练数据集服务
对应原始ai_generate.py中的load_init_TS_dataset()方法
处理TS（Test Script）训练数据集加载任务
"""

import os
import json
from typing import Dict, Any, List, Optional
from utils.logger import get_logger
from services.ai_service import AIService
from services.sheet_manager import SheetManager
from services.config_service import ConfigurationService

logger = get_logger(__name__)

class TSTrainingDatasetService:
    """TS训练数据集服务"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.sheet_manager = SheetManager()
        self.config_service = ConfigurationService()
        self.ts_head_files = {}
        self.ts_head_file_prompt = ""
        
    def execute(self) -> int:
        """执行TS训练数据集加载任务"""
        try:
            logger.info("🚀 Starting TS training dataset job")
            
            # 获取配置
            config = self._get_config()
            self._validate_config(config)
            
            # 连接到TS数据集表格
            if not self.sheet_manager.connect_sheet(config['ts_dataset_sheet']):
                raise Exception("Failed to connect to TS dataset sheet")
            
            # 加载TS训练数据集
            ts_dataset = self._load_ts_training_dataset(config)
            
            # 处理TS数据集
            result = self._process_ts_training_dataset(config, ts_dataset)
            
            if result:
                logger.info("✅ TS training dataset job completed successfully")
                return 0
            else:
                logger.error("❌ TS training dataset job failed")
                return 1
            
        except Exception as e:
            logger.exception(f"TS training dataset job failed: {e}")
            return 1
    
    def _get_config(self) -> Dict[str, Any]:
        """获取配置"""
        config = {}
        
        # 从环境变量获取
        config['ts_dataset_sheet'] = os.getenv('TS_DATASET_SHEET_URL')
        config['ts_dataset_sheet_name'] = os.getenv('TS_DATASET_SHEET_NAME', 'TSTrainingData')
        config['ts_head_files_config'] = os.getenv('TS_HEAD_FILES_CONFIG')  # JSON格式
        config['ts_head_file_prompt'] = os.getenv('TS_HEAD_FILE_PROMPT', '')
        config['auto_submit'] = os.getenv('AUTO_SUBMIT', 'false').lower() == 'true'
        
        # 如果环境变量中没有配置，尝试从配置文件读取
        if not config['ts_dataset_sheet']:
            try:
                if self.config_service.config_exists():
                    sheet_config = self.config_service.get_sheet_config_for_batch_task()
                    if 'error' not in sheet_config:
                        config['ts_dataset_sheet'] = sheet_config.get('test_spec_sheet')
                        logger.info("TS dataset sheet configuration loaded from config.ini")
            except Exception as e:
                logger.error(f"Failed to read config file: {e}")
        
        return config
    
    def _validate_config(self, config: Dict[str, Any]):
        """验证配置"""
        required_fields = ['ts_dataset_sheet', 'ts_dataset_sheet_name']
        missing_fields = []
        
        for field in required_fields:
            if not config.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            raise ValueError(f"Missing required configuration for TS training dataset: {missing_fields}")
        
        logger.info("TS training dataset configuration validation passed")
    
    def _load_ts_training_dataset(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """加载TS训练数据集"""
        try:
            ts_dataset_sheet_name = config['ts_dataset_sheet_name']
            
            logger.info(f"Loading TS training dataset from sheet: {ts_dataset_sheet_name}")
            
            # 读取TS数据集
            ts_dataset = self.sheet_manager.read_by_sheet_name(ts_dataset_sheet_name)
            
            if not ts_dataset:
                logger.warning(f"No TS data found in sheet: {ts_dataset_sheet_name}")
                return []
            
            logger.info(f"Loaded {len(ts_dataset)} TS training records")
            return ts_dataset
            
        except Exception as e:
            logger.error(f"Failed to load TS training dataset: {e}")
            raise
    
    def _process_ts_training_dataset(self, config: Dict[str, Any], ts_dataset: List[Dict[str, Any]]) -> bool:
        """处理TS训练数据集"""
        try:
            if not ts_dataset:
                logger.warning("No TS training dataset to process")
                return True
            
            # 加载头文件配置
            self._load_ts_head_files(config)
            
            # 处理TS头文件提示词
            self.ts_head_file_prompt = config.get('ts_head_file_prompt', '')
            
            # 构建TS头文件数据结构
            self._build_ts_head_file_structure(ts_dataset)
            
            # 如果启用自动提交，处理TS训练提示词
            if config.get('auto_submit'):
                self._process_ts_training_prompts(ts_dataset)
            
            logger.info("TS training dataset processing completed")
            return True
            
        except Exception as e:
            logger.error(f"Failed to process TS training dataset: {e}")
            return False
    
    def _load_ts_head_files(self, config: Dict[str, Any]):
        """加载TS头文件配置"""
        try:
            ts_head_files_config = config.get('ts_head_files_config')
            if ts_head_files_config:
                self.ts_head_files = json.loads(ts_head_files_config)
                logger.info(f"Loaded {len(self.ts_head_files)} TS head files from config")
            else:
                logger.info("No TS head files configuration found")
                self.ts_head_files = {}
                
        except Exception as e:
            logger.warning(f"Failed to load TS head files config: {e}")
            self.ts_head_files = {}
    
    def _build_ts_head_file_structure(self, ts_dataset: List[Dict[str, Any]]):
        """构建TS头文件数据结构"""
        try:
            # 从数据集中提取模块信息并构建头文件映射
            modules = set()
            
            for record in ts_dataset:
                # 尝试从不同字段提取模块信息
                module = None
                for field_name in ['module', 'category', 'test_module', 'component']:
                    if field_name in record and record[field_name]:
                        module = str(record[field_name]).strip()
                        if '_' in module:
                            module = module.split('_')[1]  # 提取模块名
                        break
                
                if module:
                    modules.add(module)
            
            logger.info(f"Found {len(modules)} modules in TS dataset: {list(modules)}")
            
            # 为每个模块设置头文件（如果配置中有的话）
            for module in modules:
                if module not in self.ts_head_files:
                    # 如果配置中没有该模块的头文件，可以设置默认值
                    self.ts_head_files[module] = f"default_header_for_{module}.h"
            
        except Exception as e:
            logger.warning(f"Failed to build TS head file structure: {e}")
    
    def _process_ts_training_prompts(self, ts_dataset: List[Dict[str, Any]]):
        """处理TS训练提示词（如果启用自动提交）"""
        try:
            logger.info("Processing TS training prompts with auto-submit")
            
            processed_count = 0
            last_module = None
            
            for record in ts_dataset:
                try:
                    # 提取模块信息
                    module = self._extract_module_from_record(record)
                    
                    # 如果模块变化，处理头文件
                    if module != last_module:
                        last_module = module
                        self._process_ts_head_file(module)
                    
                    # 提取提示词
                    prompt = self._extract_prompt_from_record(record)
                    
                    if prompt:
                        # 发送提示词到AI服务
                        response = self.ai_service.generate_response(prompt)
                        
                        if not response.startswith("Error"):
                            processed_count += 1
                            logger.debug(f"Processed TS training prompt for module {module}")
                        else:
                            logger.warning(f"Failed to process TS training prompt: {response}")
                            
                except Exception as e:
                    logger.warning(f"Error processing TS training record: {e}")
            
            logger.info(f"Processed {processed_count} TS training prompts")
            
        except Exception as e:
            logger.error(f"Failed to process TS training prompts: {e}")
    
    def _extract_module_from_record(self, record: Dict[str, Any]) -> Optional[str]:
        """从记录中提取模块名"""
        for field_name in ['module', 'category', 'test_module', 'component']:
            if field_name in record and record[field_name]:
                module = str(record[field_name]).strip()
                if '_' in module:
                    return module.split('_')[1]
                return module
        return 'default'
    
    def _extract_prompt_from_record(self, record: Dict[str, Any]) -> Optional[str]:
        """从记录中提取提示词"""
        for field_name in ['prompt', 'test_prompt', 'script_prompt', 'description']:
            if field_name in record and record[field_name]:
                return str(record[field_name]).strip()
        return None
    
    def _process_ts_head_file(self, module: str):
        """处理TS头文件"""
        try:
            head_file = self.ts_head_files.get(module)
            if head_file and self.ts_head_file_prompt:
                logger.info(f"Processing TS head file for module: {module}")
                combined_prompt = f"{head_file}\n{self.ts_head_file_prompt}"
                self.ai_service.generate_response(combined_prompt)
        except Exception as e:
            logger.warning(f"Failed to process TS head file for module {module}: {e}")
