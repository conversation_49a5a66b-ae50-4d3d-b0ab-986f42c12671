import pathlib

import pandas as pd

from configs.env import settings
from services.cloud_storage import update_gcs_blob_content_type
from utils.logger import get_logger

logger = get_logger(__name__)



def save_file_with_status_update(
    status: str,
    dataframe: pd.DataFrame
) -> str:
    """
    Updates the 'Status' column in the DataFrame and saves it as a CSV file
    with a '_results.csv' suffix in the specified directory. Then updates
    the GCS blob metadata accordingly.

    Args:
        directory (str | pathlib.Path): Local directory path where the file is saved.
        status (str): Status value to set for all rows.
        dataframe (pd.DataFrame): DataFrame to update and save.

    Raises:
        Exception: Propagates exceptions on file saving or GCS metadata update failures.
    """
    try:
        df = dataframe.copy()
        df['Status'] = status

        base_file_path = pathlib.Path(settings.SOURCE_PATH) / settings.FILE_NAME
        csv_file_name = f"{base_file_path.stem}_results.csv"
        csv_file_path = base_file_path.parent / csv_file_name


        # Save DataFrame to CSV
        df.to_csv(csv_file_path, index=False)
        logger.info(f"Saved DataFrame with status to {csv_file_path}")

        # Construct GCS path relative to bucket root
        gcs_blob_path = pathlib.Path(settings.FILE_NAME).parent / csv_file_name

        # Update metadata on GCS
        update_gcs_blob_content_type(str(gcs_blob_path))
        logger.info(f"Updated GCS metadata for blob {gcs_blob_path}")
        return gcs_blob_path
    except Exception as e:
        logger.error(f"Failed to save DataFrame or update GCS metadata: {e}", exc_info=True)
        raise