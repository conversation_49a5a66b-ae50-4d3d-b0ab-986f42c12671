import os
import time
import base64
import requests
import google.oauth2.id_token as id_token
import google.auth.transport.requests as google_requests

from configs.env import settings
from utils.logger import get_logger

logger = get_logger(__name__)


class VtafApiClients:
    def __init__(self):
        self.api_base = "https://" + settings.DOMAIN_NAME + "/vtaf-api"
        self.collection_name = settings.COLLECTION_NAME
        self.timeout = 60

        creds = id_token.fetch_id_token_credentials(request=google_requests.Request(),
                                                    audience=settings.VTAF_AI_CLIENT_ID)
        self.session = google_requests.AuthorizedSession(creds)

    # Summarize a block of text using the summarization endpoint
    def summarize_text(self, text: str) -> str:
        url = f"{self.api_base}/v1/agents/summarize_text"
        payload = {"text": text,
                   "job_id":settings.CLOUD_RUN_EXECUTION}

        try:
            response = self.session.post(url, json=payload, timeout=self.timeout * 2)
            response.raise_for_status()
            return response.json().get("raw", "")
        except requests.exceptions.RequestException as e:
            logger.error(f"Summarization error: {e}")
            logger.error(f"Error adding summarize_data payload: {payload}")
            return ""

    # Add processed chunks to the vector database
    def add_chunks_to_vector_db(self, summarize_data: dict, metadata: dict):
        url = f"{self.api_base}/v1/vectordb/documents/add"

        payload = {
            "content": summarize_data,
            "metadata": metadata,
            "collection_name": self.collection_name
        }

        try:
            response = self.session.post(url, json=payload, timeout=self.timeout)
            response.raise_for_status()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error adding summarize_data {metadata.get('Object_Identifier')}: {e}")
            logger.error(f"Error adding summarize_data payload: {payload}")
