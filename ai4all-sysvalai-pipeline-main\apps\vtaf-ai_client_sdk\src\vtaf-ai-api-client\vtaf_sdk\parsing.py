# parsing.py
from vtaf_ai_api_client.api.parsing_v_1 import (
    get_supported_parsers_v1_parsing_supported_parsers_get,
    parse_single_file_v1_parsing_single_file_parsing_post,
    parsing_health_check,
)
from vtaf_ai_api_client.models import ParseFileRequest


class ParsingAPI:
    def __init__(self, client):
        self.client = client

    def health_check(self):
        return parsing_health_check.sync(client=self.client).additional_properties

    def parse_file(
        self,
        file_name: str,
        file_content: str,
        parser: str = "auto",
        output_format: list[str] = ["text"],
        force: bool = False,
    ):
        """
        Wraps the parse_single_file endpoint.
        Accepts plain parameters and constructs the model internally.
        """
        request = ParseFileRequest(
            file_name=file_name,
            file_content=file_content,
            parser=parser,
            output_format=output_format,
        )
        return parse_single_file_v1_parsing_single_file_parsing_post.sync(
            client=self.client,
            body=request,
            force=force,
        )

    def supported_parsers(self, force: bool = False):
        return get_supported_parsers_v1_parsing_supported_parsers_get.sync(client=self.client, force=force)
