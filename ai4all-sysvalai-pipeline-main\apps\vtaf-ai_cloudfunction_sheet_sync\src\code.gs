const PROJECT_ID = "valeo-cp2673-acp";  // Replace with your Project ID
const LOCATION = "europe-west1";      // Replace with your Job's region
const JOB_NAME = "job-vtaf-config-sync"; // Replace with your Cloud Run Job Name
const editedBy = Session.getActiveUser().getEmail();

function onEdit(e) {
  const sheet = e.source.getActiveSheet();
  const sheet_name = sheet.getName();
  const row_index = sheet.getActiveCell().getRowIndex().toString();
  const column_index = sheet.getActiveCell().getColumnIndex()

  if (sheet_name && row_index && column_index) {
    const column_letter = getColumnLetter(column_index);
    triggerCloudRunJob(sheet_name, row_index, column_letter)

} else {
  Logger.log("One or more values are empty or undefined.");
}

  if (sheet_name !== "Config") return;

  const allowedGroup = "<EMAIL>";
  const editedA1 = sheet.getRange(row_index, column_index).getA1Notation();

  const protections = sheet.getProtections(SpreadsheetApp.ProtectionType.RANGE);

  // Try to find existing protection for this column starting from row 2
  const existingProtection = protections.find(p => {
    try {
      const r = p.getRange();
      return r.getSheet().getName() === sheet.getName() &&
             r.getColumn() === column_index &&
             r.getNumColumns() === 1 &&
             r.getRow() === 2;
    } catch (err) {
      return false;
    }
  });

  // Get last row with data in this column
  const columnValues = sheet.getRange(2, column_index, sheet.getMaxRows() - 1).getValues();
  let lastDataRow = columnValues.length + 1;
  for (let i = columnValues.length - 1; i >= 0; i--) {
    if (columnValues[i][0] !== "") {
      lastDataRow = i + 2;
      break;
    }
  }

  const newRange = sheet.getRange(2, column_index, lastDataRow - 1); // A2:A<last>

  if (existingProtection) {
    const r = existingProtection.getRange();
    const oldLastRow = r.getLastRow();

    if (lastDataRow  > oldLastRow) {
      // Extend the range only if needed
      existingProtection.setRange(newRange);
      Logger.log(`🔄 Extended protection to ${sheet_name}!${newRange.getA1Notation()}`);
    } else {
      Logger.log(`🔒 Existing protection already covers: ${sheet_name}!${editedA1}`);
    }
  } else {
    // No existing protection, create a new one
    const protection = newRange.protect();
    const colLetter = newRange.getA1Notation().match(/[A-Z]+/)[0];
    protection.setDescription(`Protected column ${sheet_name}!${colLetter}`);

    protection.removeEditors(protection.getEditors());

    try {
      protection.addEditor(allowedGroup);
    } catch (err) {
      Logger.log("Error adding group: " + err.message);
    }

    if (protection.canDomainEdit()) {
      protection.setDomainEdit(false);
    }

    Logger.log(`✅ Created protection: ${sheet_name}!${newRange.getA1Notation()}`);
  }

}

function getColumnLetter(columnIndex) {
  let letter = "";
  while (columnIndex > 0) {
    let temp = (columnIndex - 1) % 26;
    letter = String.fromCharCode(temp + 65) + letter;
    columnIndex = (columnIndex - temp - 1) / 26;
  }
  return letter;
}


function triggerCloudRunJob(sheet_name, row_index, column_letter) {

  const url = `https://europe-west1-valeo-cp2673-acp.cloudfunctions.net/vtaf-config-sync`;

  const payload = {
    SHEET_NAME: sheet_name,
    ROW_INDEX: row_index,
    COLUMN_LETTER: column_letter
  };



  const options = {
    method: "POST",
    headers: {
    'Authorization': 'bearer ' + ScriptApp.getIdentityToken(),
      "Content-Type": "application/json",
    },
    payload: JSON.stringify(payload),
    muteHttpExceptions: true
  };

  try {
    const response = UrlFetchApp.fetch(url, options);
    
    // Log the response for debugging
    Logger.log("Response Code: " + response.getResponseCode());
    Logger.log("Response Body: " + response.getContentText());
  } catch (error) {
    Logger.log("Error: " + error.toString());
  }

}
