[tool.poetry]
name = "vtaf-sdk"
version = "1.0.1"
description = "Wrapped VTAF AI API SDK with client library and simplified interface"
authors = ["<NAME_EMAIL>"]
readme = "README.md"

# 🧩 Include both generated SDK and your wrapper
packages = [
    { include = "vtaf_ai_api_client" },
    { include = "vtaf_sdk" }
]

# 📝 If you want to include type hints (optional)
include = ["vtaf_ai_api_client/py.typed", "vtaf_sdk/py.typed", "CHANGELOG.md"]

[tool.poetry.dependencies]
python = "^3.9"
httpx = "^0.28.1"
attrs = ">=22.2.0"
python-dateutil = "^2.8.0"
google-auth = "^2.40.3"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
line-length = 120

[tool.ruff.lint]
select = ["F", "I", "UP"]
