<template>
  <q-stepper
    v-model="step"
    animated
    flat
    class="full-width"
    done-color="blue-10"
    active-color="light-blue-5"
    inactive-color="red-7"
  >
    <!-- Step 1: Select BG (Still uses Cards) -->
    <q-step :name="1" :title="step1Title" icon="mdi-domain" :done="step > 1">
      <div class="row q-mb-md">
        <q-card
          v-for="item in organization_inputs"
          :key="item.bg"
          class="col-4 cursor-pointer bg-card"
          :class="getCardHoverClass(item.bg)"
          @click="selectBG(item)"
          bordered
          hoverable
        >
          <q-img :src="getImageUrl(item.bg)" :alt="item.bg" height="200px" />
          <q-card-section
            class="text-center text-h5 q-pa-md"
            :class="getTextColorClass(item.bg)"
          >
            {{ item.bg }}
          </q-card-section>
        </q-card>
      </div>
      <q-btn
        label="Next"
        color="primary"
        class="q-mt-md"
        @click="step++"
        :disable="!selectedBG"
      />
    </q-step>

    <!-- Step 2: Select PG (Use Buttons) -->
    <q-step :name="2" :title="step2Title" icon="diversity_3" :done="step > 2">
      <div class="column items-center q-gutter-sm q-mb-md">
        <q-btn
          v-for="pg in distinctPGs"
          :key="pg"
          @click="selectPG(pg)"
          color="blue-10"
          class="pg-btn"
          outline
        >
          {{ pg }}
        </q-btn>
      </div>
      <div class="q-mt-md row justify-between">
        <q-btn label="Previous" color="secondary" @click="step--" />
        <q-btn
          label="Next"
          color="primary"
          @click="step++"
          :disable="!selectedPG"
        />
      </div>
    </q-step>

    <!-- Step 3: Select PL (Use Buttons) -->
    <q-step :name="3" :title="step3Title" icon="category" :done="step > 3">
      <div class="column items-center q-gutter-sm q-mb-md">
        <q-btn
          v-for="item in filteredPLs"
          :key="item.pl"
          @click="selectPL(item)"
          color="blue-10"
          class="pl-btn"
          outline
        >
          {{ item.pl }}
        </q-btn>
      </div>
      <q-btn
        label="Previous"
        color="secondary"
        class="q-mt-md"
        @click="step--"
      />
    </q-step>
    <!-- Step 4: Generating Data (Use Buttons) -->
    <q-step :name="4" title="Fetching data...." icon="download">
      <div class="column q-gutter-sm q-mb-md">
        <q-inner-loading :showing="stepper_loading">
          <div class="q-mt-md text-blue-10 text-h6 text-weight-bold">
            Preparing Data
          </div>
          <q-spinner-bars size="75px" color="blue-10" />
        </q-inner-loading>
      </div>
    </q-step>
  </q-stepper>
  <q-inner-loading :showing="initial_loading">
    <div class="q-mt-md text-blue-10 text-h6 text-weight-bold">
      Getting Organization Details...
    </div>
    <q-spinner-bars size="75px" color="blue-10" />
  </q-inner-loading>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRunTcGenerationStore } from "src/stores/ai_testcase/run-tc-generation-store.js";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";

const router = useRouter();

const ai_testcase_config = useRunTcGenerationStore();
const {
  organization_inputs,
  selectedBG,
  selectedPG,
  selectedPL,
  selectedOrgUUID,
  functionality_inputs,
  selectedFunctionality,
  selectedFunctionalityUuid,
  algorithm_dropdown,
  sensor_dropdown,
  selectedAlgorithms,
  selectedSensors,
  stepper_loading,
  initial_loading,
  current_job_execution_id,
} = storeToRefs(ai_testcase_config);
const {
  get_user_email,
  get_org_inputs,
  get_algorithms_and_sensor_input,
  get_functionality_input,
} = ai_testcase_config;

// const home_store_config = useHomeStore();
// const { main_email, first_letter_email } = storeToRefs(home_store_config);

const pl_pg_for_bg = ref("");
const step = ref(1);

// Step 1: Select BG
function selectBG(item) {
  selectedBG.value = item.bg;
  pl_pg_for_bg.value = item;
  step.value = 2;
}

const step1Title = computed(() => {
  return step.value > 1 && selectedBG.value
    ? selectedBG.value
    : "Business Group";
});

const step2Title = computed(() => {
  return step.value > 2 && selectedPG.value
    ? selectedPG.value
    : "Product Group";
});

const step3Title = computed(() => {
  return step.value > 3 && selectedPL.value ? selectedPL.value : "Product Line";
});

function getImageUrl(name) {
  return `src/assets/home/<USER>
}

// Step 2: Get distinct PGs from selectedBG
const distinctPGs = computed(() => {
  const pgSet = new Set();
  pl_pg_for_bg.value?.details?.forEach((d) => pgSet.add(d.pg));
  return Array.from(pgSet);
});

function selectPG(pg) {
  selectedPG.value = pg;
  step.value = 3;
}

// Step 3: Filter PLs based on selected PG
const filteredPLs = computed(() => {
  return (
    pl_pg_for_bg.value?.details?.filter((d) => d.pg === selectedPG.value) || []
  );
});

async function selectPL(item) {
  step.value = 4;
  console.log(item);
  selectedPL.value = item.pl;
  selectedOrgUUID.value = item.org_uuid;

  await get_algorithms_and_sensor_input();
  await get_functionality_input();
  route_to_functional_inputs();
}

function getTextColorClass(bg) {
  const colorMap = {
    brain: "text-teal text-bold",
    light: "text-amber-8 text-bold",
    power: "text-blue-9 text-bold",
  };
  return colorMap[bg.toLowerCase()] || "text-black";
}

function getCardHoverClass(bg) {
  const colorMap = {
    brain: "hover-brain",
    light: "hover-light",
    power: "hover-power",
  };
  return colorMap[bg.toLowerCase()] || "";
}

function route_to_functional_inputs() {
  router.push("/ai_testcase/create_job/functionalities");
}
onMounted(async () => {
  await get_user_email();
  // main_email.value = user_email.value;
  // first_letter_email.value = main_email.value.charAt(0).toUpperCase();
  selectedBG.value = "";
  selectedPG.value = "";
  selectedPL.value = "";
  selectedFunctionality.value = "";
  selectedFunctionalityUuid.value = "";
  functionality_inputs.value = [];
  algorithm_dropdown.value = null;
  sensor_dropdown.value = null;
  selectedOrgUUID.value = "";
  selectedAlgorithms.value = {};
  selectedSensors.value = {};
  current_job_execution_id.value = "";
  await get_org_inputs();
});
</script>

<style scoped>
.q-card.bg-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  min-height: 100px;
  padding: 12px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  animation: fadeInUp 0.5s ease both;
}

/* Button enhancements */
.q-btn {
  transition: all 0.3s ease;
  border-radius: 10px;
  font-weight: 600;
}

.q-btn:hover {
  transform: scale(1.03);
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1);
}

.q-btn:active {
  transform: scale(0.97);
}

/* Stepper animation */
.q-stepper {
  animation: fadeIn 0.5s ease-in;
}

.pg-btn {
  width: 50%;
}

.pl-btn {
  width: 50%;
}

.hover-brain:hover {
  background: linear-gradient(145deg, #e0f7f4, #b2dfdb); /* Teal-ish */
}

.hover-light:hover {
  background: linear-gradient(145deg, #fff8e1, #ffe082); /* Yellow-ish */
}

.hover-power:hover {
  background: linear-gradient(145deg, #e3f2fd, #90caf9); /* Blue-ish */
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.98);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
