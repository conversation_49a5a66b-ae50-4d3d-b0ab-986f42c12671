import { ref } from "vue";
import { defineStore } from "pinia";
import { Notify } from "quasar";
import { chat<PERSON><PERSON> } from "src/boot/axios";
import { marked } from "marked";

export const useChatV2Store = defineStore("chat_v2_store", () => {
  const gemini_version = ref("gemini-1.5-flash");
  const uploaded_prompt_file = ref(null);
  const uploaded_prompt = ref("");
  const prompt = ref("");
  const prompt_response = ref("");
  const uploaded_file = ref(null);
  const file_name = ref("");
  const file_type = ref("");
  const prompt_loading = ref(false);
  const prompt_banner_loading = ref(false);
  const messages = ref([]);
  const temperature = ref(1);
  const output_tokens = ref(8192);
  const chat_hisory_clear = ref(true);
  const chat_type = ref("single");
  const selectedRegion = ref("us-central1");
  const responseMessage = ref({
    name: "VTAF.AI",
    avatar: "/src/assets/home/<USER>",
    text: "",
    sent: false,
  });

  // function to get response for the prompt
  async function getPromptResponse() {
    try {
      // Create a new FormData object
      const formData = new FormData();

      // Append the fields to the FormData object
      formData.append("gemini_version", gemini_version.value);
      formData.append("user_prompt", prompt.value);
      formData.append("temperature", temperature.value);
      formData.append("output_tokens", output_tokens.value);

      // Ensure 'uploaded_file.value' is a File object
      if (uploaded_file.value) {
        formData.append("file_input", uploaded_file.value); // Add the file input
        formData.append("file_name", file_name.value);
        formData.append("file_type", file_type.value);
      }

      const response = await chatApi.post("/query", formData, {
        headers: {
          "Content-Type": "multipart/form-data", // Set content type for FormData
        },
      });
      prompt_response.value = response.data.response;
      console.log(prompt_response.value);
      prompt_banner_loading.value = false;
      prompt_loading.value = false;
    } catch (err) {
      // Handle the error here
      console.error("Error Getting Response:", err);
      Notify.create({
        type: "negative",
        message: "Failed to change model!",
      });
    } finally {
      prompt_banner_loading.value = false;
      prompt_loading.value = false;
      console.log("finished");
    }
  }

  async function getPromptResponseStream() {
    try {
      // Create a new FormData object
      const formData = new FormData();

      // Append the fields to the FormData object
      formData.append("gemini_version", gemini_version.value);
      formData.append("user_prompt", prompt.value);
      formData.append("temperature", temperature.value);
      formData.append("output_tokens", output_tokens.value);

      // Ensure 'uploaded_file.value' is a File object
      if (uploaded_file.value) {
        formData.append("file_input", uploaded_file.value); // Add the file input
        formData.append("file_name", file_name.value);
        formData.append("file_type", file_type.value);
      }

      const response = await fetch(
        "https://cp2673.apps-test.valeo.com/chat/stream",
        // "http://127.0.0.1:3000/chat/stream",
        {
          method: "POST",
          body: formData,
        }
      );
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      // Process the stream as it's received
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        // Decode and append new content to the response box
        const chunk = decoder.decode(value, { stream: true });
        console.log(chunk);
        if (!chunk) {
          return;
        }
        prompt_response.value += chunk;
        responseMessage.value.text = marked(prompt_response.value);
      }
      // prompt_response.value = response.data.response;
      console.log(prompt_response.value);
      prompt_banner_loading.value = false;
      prompt_loading.value = false;
    } catch (err) {
      // Handle the error here
      console.error("Error Getting Response:", err);
      Notify.create({
        type: "negative",
        message: "Failed to change model!",
      });
    } finally {
      prompt_banner_loading.value = false;
      prompt_loading.value = false;
      console.log("finished");
    }
  }

  async function getMultiChatResponseStream() {
    try {
      // Create a new FormData object
      const formData = new FormData();
      let request_data = prompt.value;
      // Append the fields to the FormData object
      formData.append("gemini_version", gemini_version.value);
      formData.append("user_prompt", request_data);
      formData.append("temperature", temperature.value);
      formData.append("output_tokens", output_tokens.value);
      formData.append("region", selectedRegion.value);
      formData.append("clear", chat_hisory_clear.value);
      prompt.value = "";
      const response = await fetch(
        "https://cp2673.apps-test.valeo.com/chat/multichat",
        // "http://127.0.0.1:3000/chat/multichat",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            gemini_version: gemini_version.value,
            user_prompt: request_data,
            temperature: temperature.value,
            output_tokens: output_tokens.value,
            clear: chat_hisory_clear.value,
          }),
        }
      );
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      // Process the stream as it's received
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        // Decode and append new content to the response box
        const chunk = decoder.decode(value, { stream: true });
        console.log(chunk);
        if (!chunk) {
          return;
        }
        chat_hisory_clear.value = false;
        prompt_response.value += chunk;
        responseMessage.value.text = marked(prompt_response.value);
      }
    } catch (err) {
      // Handle the error here
      console.error("Error Getting Response:", err);
      Notify.create({
        type: "negative",
        message: "Failed to change model!",
      });
    } finally {
      console.log("finished");
    }
  }
  return {
    gemini_version,
    uploaded_prompt_file,
    uploaded_prompt,
    prompt,
    prompt_response,
    uploaded_file,
    file_name,
    file_type,
    prompt_loading,
    prompt_banner_loading,
    messages,
    responseMessage,
    temperature,
    output_tokens,
    chat_hisory_clear,
    chat_type,
    selectedRegion,
    getPromptResponse,
    getPromptResponseStream,
    getMultiChatResponseStream,
  };
});
