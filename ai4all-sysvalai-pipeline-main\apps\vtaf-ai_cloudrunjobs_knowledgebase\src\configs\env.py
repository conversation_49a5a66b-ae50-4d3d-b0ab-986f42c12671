from pydantic_settings import BaseSettings


class AppSettings(BaseSettings):
    PROJECT_ID: str
    QDRANT_CLIENT_URL: str
    QDRANT_CLIENT_PORT: int
    QDRANT_IAP_CLIENT_ID: str
    EMBEDDING_MODEL_NAME: str
    EMBEDDING_LOCATION: str
    EMBEDDING_PARALLELISM: int
    COLLECTION_NAME: str
    SERVICE_ACCOUNT_EMAIL: str
    OTEL_SDK_DISABLED: str  # could be bool, but environment vars are strings usually
    MODEL: str
    FILE_NAME: str
    BQ_DATA_SET: str
    BQ_TABLE: str
    PROJECT_PROFILE_UUID: str
    SOURCE_PATH: str
    VTAF_AI_PROJECT_ID: str
    VTAF_AI_PROJECT_VAR_ID: str
    VTAF_AI_EUF_FEA_UID: str
    INPUT_TYPE: str
    BUCKET_NAME: str
    CLOUD_RUN_EXECUTION: str
    SERVICES_PROJECT_NUMBER: str
    DOMAIN_NAME: str
    CHUNKING_STRATEGY: str
    ENABLE_SUMMARIZATION: str
    VTAF_AI_CLIENT_ID: str

    class Config:
        env_file = None
        case_sensitive = True


settings = AppSettings()


