from pydantic_settings import BaseSettings


class AppSettings(BaseSettings):
    PROJECT_ID: str
    COLLECTION_NAME: str
    BQ_DATA_SET: str
    BQ_TABLE: str
    VTAF_AI_CLIENT_ID: str
    DOMAIN_NAME: str
    CLOUD_RUN_TASK_INDEX: str
    CLOUD_RUN_TASK_COUNT: str
    CLOUD_RUN_EXECUTION: str
    Projects: str

    class Config:
        env_file = None
        case_sensitive = True


settings = AppSettings()
