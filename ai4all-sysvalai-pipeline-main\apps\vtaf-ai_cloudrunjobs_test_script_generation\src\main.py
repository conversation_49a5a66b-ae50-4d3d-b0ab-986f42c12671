import datetime
import os

from services.bigquery_repository import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from services.generate_test_script import generate_test_script
from services.gsheet_manager import DriveUploader
from services.job_manager import JobManager
from services.secret_manager import GCPSecretManager
from utils.logger import get_logger

# os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "valeo-cp2673-dev-6e4b62cfd776.json"

logger = get_logger(__name__)
if __name__ == '__main__':
    start_timestamp = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
    token_usage = {
        "total_tokens": 0,
        "prompt_tokens": 0,
        "cached_prompt_tokens": 0,
        "completion_tokens": 0,
        "successful_requests": 0
    }
    bigquery_handler = BigQueryHandler()
    gcp_secret_manager = GCPSecretManager()
    job_manager = JobManager(bigquery_handler, start_timestamp, gcp_secret_manager)
    uploader = DriveUploader(gcp_secret_manager)

    try:
        status, job_record = job_manager.poll_for_job_details()
        if status:
            test_cases = uploader.read_full_sheet()
            job_manager.update_job_start()
            test_scripts, token_usage, status = generate_test_script(test_cases, job_record.get("user_email", ""))
            if status:
                file_id = uploader.upload_json_directly_to_sheet(test_scripts)
                if file_id:
                    job_manager.update_job_complete(file_id, token_usage)
                else:
                    job_manager.update_failed_status(token_usage, f"Unexpected Exception: file not uploaded")
            else:
                job_manager.update_failed_status(token_usage, f"Unexpected Exception: {str(test_scripts)}")
                status, job_record = job_manager.poll_for_job_details()

            status, job_record = job_manager.poll_for_job_details()
            job_manager.send_email(job_record, token_usage)
        else:
            logger.error(f"Job entry Not created")

    except Exception as e:
        logger.exception(f"Unexpected Exception: {str(e)}")
        job_manager.update_failed_status(token_usage, f"Unexpected Exception: {str(e)}")
        status, job_record = job_manager.poll_for_job_details()
        job_manager.send_email(job_record, token_usage)
