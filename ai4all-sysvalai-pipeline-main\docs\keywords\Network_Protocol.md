# Group {Network_Protocol}
## Check_Ping

- [ ] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** **


**Descriptions:** *This keyword will be used to verify the Ping responses with the validation.*


***Command Set:***

	Response
		-Response=Parameter;Condition


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Parameter| Ring| AllowRTval=False,SentPackets,ReceivedPackets,LostPackets,MinRoundTripTime,MaxRoundTripTime,AvgRoundTripTime| LostPackets| Select which ping result they want to validate| Required
| Condition| String| NA| X==0|  Provide the condition for the return value  comparision

X==10
X>=10
>=5 X <=10 | Required

## Send_HTTP

- [ ] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** **


**Descriptions:** *The SendHttp function is designed to send HTTP requests to a specified URL, supporting a variety of HTTP methods, including GET, POST, PUT, and DELETE. It provides flexibility to interact with web APIs by allowing you to customize the request with headers, parameters, and data details as needed.*


***Command Set:***

	GET
		-GET=Alias;Timeout
	POST
		-POST=Alias;Timeout
	PUT
		-PUT=Alias;Timeout
	DELETE
		-DELETE=Alias;Timeout


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Alias| String| NA| Xyz| Specify the Alias of the Http Request.| Required
| Timeout| String| NA| 1| The number of seconds to wait before the request times out.| Optional

## Send_Ping

- [ ] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** **


**Descriptions:** *    Perform a ping check to determine the reachability of a specified host.*


***Command Set:***

	Request
		-Request=Target;Source;TxByte;EchoCount


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Target| String| NA| <<Alias>>|   This contains reference alias to lookup table.| Required
| Source| String| NA| ************| Source address to use| Optional
| TxByte| String| NA| 32| Send buffer size| Optional
| EchoCount| String| NA| 4| Number of echo requests to send| Optional

## Send_TCP

- [ ] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** **


**Descriptions:** *Send a TCP message to a specified host and port.
*


***Command Set:***

	Message
		-Message=Alias;Message


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Alias| String| NA| Xyz|   This contains reference alias to lookup table.| Required
| Message| String| NA| Hello|  The message to be sent over the TCP connection| Required

## Send_Telnet

- [ ] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** **


**Descriptions:** *This keyword sends various commands to Telnet*


***Command Set:***

	MKDIR
		-MKDIR=Alias;Directory
	RMDIR
		-RMDIR=Alias;Directory
	CP
		-CP=Alias;FilePath;DestinationPath
	MV
		-MV=Alias;FilePath;DestinationPath
	RM
		-RM=Alias;FilePath
	Call
		-Call=Alias;Command;Prompt


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Alias| String| NA| ECU_test|  This contains reference alias to lookup table.| Required
| Directory| String| NA| /home/<USER>/Xyz| Input DIrectory| Required
| FilePath| String| NA| /home/<USER>/sample.js|  File Location| Required
| DestinationPath| String| NA| /home/<USER>/Xyz|  Destination Path of the file| Required
| Command| String| NA| ls|  | Required
| Prompt| String| NA| $ | used to wait until the system shows the prompt| Required

## Send_UDP

- [ ] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** **


**Descriptions:** *Send a UDP message to a specified host and port.
*


***Command Set:***

	Message
		-Message=Alias;Message


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Alias| String| NA| Xyz|   This contains reference alias to lookup table.| Required
| Message| String| NA| Hello|  The message to be sent over the UDP connection| Required

## Telnet_Log

- [ ] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** **


**Descriptions:** *This Keyword is used to send and log Serial commands*


***Command Set:***

	Start
		-Start=Alias
	Send
		-Send=Alias;Command
	Stop
		-Stop=
	Check
		-Check=Alias;Command;Response
	CheckLog
		-CheckLog=LogLookup
	CheckBit
		-CheckBit=BitPosition;ExpectedBitValue


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Alias| String| NA| Xyz| Specify the Alias of the Telnet.| 
| Command| String| NA| Xyz| Specify the Telnet command.| 
| Response| String| NA| Xyz|  This holds the response data to Check| 
| LogLookup| String| NA| Xyz|  Reference to  Lookup for Telnet logs in Telnet_Log.xlsx| Required
| BitPosition| String| NA| Xyz|  The bit position to check (0-based, from LSB)| Required
| ExpectedBitValue| String| NA| Xyz|  The expected bit value| Required

