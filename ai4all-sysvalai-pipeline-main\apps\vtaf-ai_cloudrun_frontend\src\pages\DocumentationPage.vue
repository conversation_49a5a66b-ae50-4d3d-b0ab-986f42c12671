<template>
  <div>
    <iframe
      src="/docs/guide/index.html"
      style="width: 100%; height: 800px; border: none"
    ></iframe>
  </div>
</template>

<script setup>
import { storeToRefs } from "pinia";
import { useHomeStore } from "src/stores/home/<USER>";
import { useDocumentationStore } from "src/stores/documentation/documentation-store";
import { onMounted } from "vue";

const documentation_config = useDocumentationStore();
const { user_email } = storeToRefs(documentation_config);
const { get_user_email } = documentation_config;

const home_store_config = useHomeStore();
const { main_email, first_letter_email } = storeToRefs(home_store_config);

onMounted(async () => {
  await get_user_email();
  main_email.value = user_email.value;
  first_letter_email.value = main_email.value.charAt(0).toUpperCase();
});
</script>

<style scoped>
/* Add any custom styles here */
</style>
