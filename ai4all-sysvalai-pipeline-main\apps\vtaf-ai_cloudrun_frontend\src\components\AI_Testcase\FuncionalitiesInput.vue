<template>
  <div class="row justify-center q-pa-md">
    <!-- LEFT Timeline Block -->
    <div
      class="col-4 bg-white rounded-borders q-pa-md shadow-2"
      style="max-width: fit-content; margin-right: 1.5rem"
    >
      <q-img
        :src="getImageUrl(selectedBG)"
        :alt="selectedBG"
        height="100px"
        width="150px"
        class="rounded-borders shadow-1"
        style="object-fit: cover"
      />
      <div class="text-h6 text-blue-10 text-bold q-mt-md">
        {{ selectedBG }}
      </div>

      <q-icon name="south" size="lg" color="primary" />
      <div class="text-h6 text-blue-10 text-bold q-mt-md">
        {{ selectedPG }}
      </div>

      <q-icon name="south" size="lg" color="primary" />
      <div class="text-h6 text-blue-10 text-bold q-mt-md">
        {{ selectedPL }}
      </div>

      <q-btn
        :disable="!selectedFunctionality"
        class="q-mt-lg"
        label="ADD REQUIREMENTS FILE"
        color="blue-10"
        icon-right="next_plan"
        @click="go_to_file_upload()"
      ></q-btn>
    </div>
    <div class="col-8 justify-center">
      <!-- RIGHT: Functionality Card -->
      <q-card
        class="q-pa-lg bg-white shadow-4 rounded-borders animated-card full-width-card"
      >
        <q-card-section class="text-center">
          <div
            class="row items-center justify-center q-mb-md functionality-header"
          >
            <q-icon
              name="build_circle"
              size="28px"
              color="blue-10"
              class="q-mr-sm functionality-icon"
            />
            <div class="text-h5 text-blue-10">Functionality</div>
          </div>

          <q-select
            filled
            dense
            use-input
            input-debounce="0"
            v-model="selectedFunctionality"
            :options="filteredFunctions"
            @filter="filterFn"
            label="Choose an option"
            emit-value
            map-options
            class="q-mx-auto glowing-select"
            style="max-width: 300px; width: 100%"
            clearable
          />
        </q-card-section>
      </q-card>

      <!-- Algorithm Selection -->
      <div
        v-if="selectedFunctionality && Object.keys(algorithm_dropdown).length"
        class="q-mt-md"
      >
        <q-card class="q-pa-md">
          <q-card-section class="text-h6">Algorithm Selection</q-card-section>
          <q-list bordered separator>
            <q-expansion-item
              v-for="(items, algorithmName) in algorithm_dropdown"
              :key="algorithmName"
              expand-separator
              :label="algorithmName"
              header-class="text-black"
            >
              <q-card-section>
                <div v-for="item in items" :key="item">
                  <q-checkbox
                    :model-value="
                      selectedAlgorithms[algorithmName]?.includes(item) || false
                    "
                    :label="item"
                    @update:model-value="
                      (val) =>
                        updateAlgorithmSelection(val, algorithmName, item)
                    "
                  />
                </div>
              </q-card-section>
            </q-expansion-item>
          </q-list>
        </q-card>
      </div>
      <!-- Sensor Selection -->
      <div
        v-if="selectedFunctionality && Object.keys(sensor_dropdown).length"
        class="q-mt-md"
      >
        <q-card class="q-pa-md">
          <q-card-section class="text-h6">Sensors</q-card-section>
          <q-list bordered separator>
            <q-expansion-item
              v-for="(items, categoryName) in sensor_dropdown"
              :key="categoryName"
              expand-separator
              :label="categoryName"
              header-class="text-black"
              expand-icon-class="text-black"
            >
              <q-card-section>
                <div v-for="item in items" :key="item">
                  <q-checkbox
                    :model-value="
                      selectedSensors[categoryName]?.includes(item) || false
                    "
                    :label="item"
                    @update:model-value="
                      (val) => updateSensorList(val, categoryName, item)
                    "
                  />
                </div>
              </q-card-section>
            </q-expansion-item>
          </q-list>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, computed, watch } from "vue";
import { useRouter } from "vue-router";
import { useRunTcGenerationStore } from "src/stores/ai_testcase/run-tc-generation-store.js";
import { storeToRefs } from "pinia";

const ai_testcase_config = useRunTcGenerationStore();
const {
  user_email,
  selectedBG,
  selectedPG,
  selectedPL,
  functionality_inputs,
  selectedFunctionality,
  selectedFunctionalityUuid,
  functions_dropdown,
  selectedOrgUUID,
  algorithms_and_sensor_inputs,
  selectedSensors,
  selectedAlgorithms,
  algorithm_dropdown,
  sensor_dropdown,
  selectedFile,
  selectedRows,
  tableData,
  columns,
} = storeToRefs(ai_testcase_config);
const { get_user_email } = ai_testcase_config;

// const home_store_config = useHomeStore();
// const { main_email, first_letter_email } = storeToRefs(home_store_config);

const router = useRouter();

// Helpers to load images
function getImageUrl(name) {
  return `src/assets/home/<USER>
}

const go_to_file_upload = () => {
  router.push("/ai_testcase/create_job/functionalities/file_upload");
};

onMounted(async () => {
  await get_user_email();
  // main_email.value = user_email.value;
  // first_letter_email.value = main_email.value.charAt(0).toUpperCase();
  if (selectedBG.value === "") {
    router.push("/ai_testcase/create_job");
  } else {
    selectedFile.value = null;
    selectedRows.value = [];
    tableData.value = [];
    columns.value = [];
    selectedFunctionality.value = "";
  }
});

// Function to update algorithm selection and store it in dictionary format
const updateAlgorithmSelection = (isChecked, category, item) => {
  if (isChecked) {
    if (!selectedAlgorithms.value[category]) {
      selectedAlgorithms.value[category] = [];
    }
    if (!selectedAlgorithms.value[category].includes(item)) {
      selectedAlgorithms.value[category].push(item);
    }
  } else {
    selectedAlgorithms.value[category] = selectedAlgorithms.value[
      category
    ].filter((i) => i !== item);
    if (selectedAlgorithms.value[category].length === 0) {
      delete selectedAlgorithms.value[category]; // Remove category if empty
    }
  }
};

const updateSensorList = (isChecked, categoryName, item) => {
  if (isChecked) {
    if (!selectedSensors.value[categoryName]) {
      selectedSensors.value[categoryName] = [];
    }
    if (!selectedSensors.value[categoryName].includes(item)) {
      selectedSensors.value[categoryName].push(item);
    }
  } else {
    selectedSensors.value[categoryName] = selectedSensors.value[
      categoryName
    ].filter((i) => i !== item);
    if (selectedSensors.value[categoryName].length === 0) {
      delete selectedSensors.value[categoryName]; // Remove category if empty
    }
  }
};

watch(selectedFunctionality, (newVal) => {
  if (!newVal) {
    selectedFunctionalityUuid.value = null;
    return;
  }

  for (const group of functionality_inputs.value) {
    const match = group.details.find((item) => item.value === newVal);
    if (match) {
      selectedFunctionalityUuid.value = match.uuid;
      return;
    }
  }

  // If no match found
  selectedFunctionalityUuid.value = null;
});

// Filter query for real-time filtering
const filterQuery = ref("");

const filteredFunctions = computed(() => {
  return functions_dropdown.value.filter((table) =>
    table.toLowerCase().includes(filterQuery.value.toLowerCase())
  );
});

const filterFn = (val, update) => {
  filterQuery.value = val;
  update();
};
</script>

<style scoped>
.rounded-borders {
  border-radius: 12px;
}

.animated-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.3s;
}
.animated-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.functionality-header {
  transition: all 0.2s ease;
}

.functionality-icon {
  transition: transform 0.3s ease-in-out;
}
.functionality-header:hover .functionality-icon {
  transform: rotate(10deg) scale(1.1);
}

.glowing-select input:focus {
  box-shadow: 0 0 0 2px rgba(0, 136, 255, 0.2);
  border-radius: 8px;
  outline: none;
  transition: box-shadow 0.3s ease-in-out;
}

/* Mobile responsiveness tweaks */
@media (max-width: 600px) {
  .functionality-header .text-h5 {
    font-size: 1.1rem;
  }

  .functionality-icon {
    font-size: 24px;
  }

  .q-pa-xl {
    padding: 1.5rem !important;
  }

  .full-width-card {
    padding: 1.5rem !important;
  }
}
</style>
