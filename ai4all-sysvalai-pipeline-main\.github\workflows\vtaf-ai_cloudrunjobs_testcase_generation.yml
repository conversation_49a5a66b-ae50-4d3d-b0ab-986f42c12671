# name: vtaf-ai_testcase_generation

# on:
#   push:
#     branches:
#       - main
#     paths:
#       - 'apps/vtaf-ai_cloudrunjobs_testcase_generation/**'
# jobs:
#   job_id:
#     permissions:
#       contents: 'read'
#       id-token: 'write'
      
#     runs-on: linux
#     steps:
#     - name: Checkout code
#       uses: actions/checkout@v2
#     - name: Update CA Certificate # 
#       run: |
#           echo "${{ secrets.JFROG_CERTIFICATE }}" > certificate.crt
#           sudo cp certificate.crt /usr/local/share/ca-certificates/
#           sudo update-ca-certificates
#           cat certificate.crt
#     - name: JFrog Ping Test # 
#       run: jf rt ping --url ${{ vars.JFROG_URL }}/artifactory
#     - name: J<PERSON>rog Add Config # 
#       run: jf c add --access-token ${{ secrets.JFROG_TOKEN }} --url ${{ vars.JFROG_URL }}
#     - name: JFrog Configuration Check
#       run: jf c s
#     - name: <PERSON><PERSON><PERSON> List Artifacts # 
#       run: jf rt s --recursive ri-artifactory-test-local/*
#     - name: Docker list images
#       run: docker images    
#     - name: <PERSON><PERSON><PERSON> to docker # 
#       run: jf docker login -u ${{secrets.JF<PERSON>G_USER_NAME}} -p ${{ secrets.JFROG_TOKEN }} ${{ vars.JFROG_URL }}
#     - uses: 'digit-actions/configure-gcp-credentials@v2.1.3'
#       with:
#         credentials_json: '${{ secrets.TECH_KEY_DEV }}'
      
#     - name: 'Authorize Docker push'
#       run: gcloud auth configure-docker europe-west1-docker.pkg.dev

#     - id: 'build'
#       name: 'Build and push Docker image'
#       run: |
#               docker build -t europe-west1-docker.pkg.dev/valeo-cp2673-dev/vtaf-ai-artifactory/vtaf_ai_cloudrunjobs_testcase_generation ./apps/vtaf-ai_cloudrunjobs_testcase_generation/src/
#               docker push europe-west1-docker.pkg.dev/valeo-cp2673-dev/vtaf-ai-artifactory/vtaf_ai_cloudrunjobs_testcase_generation	 

#     # - name: Deploy
#     #   run: |-
#     #       gcloud run deploy vtaf-ai-agents \
#     #       --region europe-west1 \
#     #       --image europe-docker.pkg.dev/valeo-cp2673-dev/vtaf-testcase-agent/vtaf-testcase \
#     #       --platform "managed" \
#     #       --port 5000 \
#     #       --quiet

name: vtaf-ai_testcase_generation

on:
  push:
    branches:
      - main
      - acp
    paths:
      - 'apps/vtaf-ai_cloudrunjobs_testcase_generation/**'

jobs:
  main_job:
    if: github.ref == 'refs/heads/main'
    name: vtaf-ai_testcase_generation_dev
    runs-on: linux
    permissions:
      contents: 'read'
      id-token: 'write'

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Update CA Certificate
        run: |
          echo "${{ secrets.JFROG_CERTIFICATE }}" > certificate.crt
          sudo cp certificate.crt /usr/local/share/ca-certificates/
          sudo update-ca-certificates
          cat certificate.crt

      - name: JFrog Ping Test
        run: jf rt ping --url ${{ vars.JFROG_URL }}/artifactory

      - name: JFrog Add Config
        run: jf c add --access-token ${{ secrets.JFROG_TOKEN }} --url ${{ vars.JFROG_URL }}

      - name: JFrog Configuration Check
        run: jf c s

      - name: JFrog List Artifacts
        run: jf rt s --recursive ri-artifactory-test-local/*

      - name: Docker list images
        run: docker images

      - name: Register to Docker
        run: jf docker login -u ${{ secrets.JFROG_USER_NAME }} -p ${{ secrets.JFROG_TOKEN }} ${{ vars.JFROG_URL }}

      - uses: digit-actions/configure-gcp-credentials@v2.1.3
        with:
          credentials_json: '${{ secrets.TECH_KEY_DEV }}'

      - name: Authorize Docker push
        run: gcloud auth configure-docker europe-west1-docker.pkg.dev

      - id: build
        name: Build and push Docker image
        run: |
          docker build -t europe-west1-docker.pkg.dev/valeo-cp2673-dev/vtaf-ai-artifactory/vtaf_ai_cloudrunjobs_testcase_generation ./apps/vtaf-ai_cloudrunjobs_testcase_generation/src/
          docker push europe-west1-docker.pkg.dev/valeo-cp2673-dev/vtaf-ai-artifactory/vtaf_ai_cloudrunjobs_testcase_generation

  acp_job:
    if: github.ref == 'refs/heads/acp'
    name: vtaf-ai_testcase_generation_acp
    runs-on: linux
    permissions:
      contents: 'read'
      id-token: 'write'

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Update CA Certificate
        run: |
          echo "${{ secrets.JFROG_CERTIFICATE }}" > certificate.crt
          sudo cp certificate.crt /usr/local/share/ca-certificates/
          sudo update-ca-certificates
          cat certificate.crt

      - name: JFrog Ping Test
        run: jf rt ping --url ${{ vars.JFROG_URL }}/artifactory

      - name: JFrog Add Config
        run: jf c add --access-token ${{ secrets.JFROG_TOKEN }} --url ${{ vars.JFROG_URL }}

      - name: JFrog Configuration Check
        run: jf c s

      - name: JFrog List Artifacts
        run: jf rt s --recursive ri-artifactory-test-local/*

      - name: Docker list images
        run: docker images

      - name: Register to Docker
        run: jf docker login -u ${{ secrets.JFROG_USER_NAME }} -p ${{ secrets.JFROG_TOKEN }} ${{ vars.JFROG_URL }}

      - uses: digit-actions/configure-gcp-credentials@v2.1.3
        with:
          credentials_json: '${{ secrets.TECH_KEY_ACP }}'

      - name: Authorize Docker push
        run: gcloud auth configure-docker europe-west1-docker.pkg.dev

      - id: build
        name: Build and push Docker image
        run: |
          docker build -t europe-west1-docker.pkg.dev/valeo-cp2673-acp/vtaf-ai-artifactory/vtaf_ai_cloudrunjobs_testcase_generation ./apps/vtaf-ai_cloudrunjobs_testcase_generation/src/
          docker push europe-west1-docker.pkg.dev/valeo-cp2673-acp/vtaf-ai-artifactory/vtaf_ai_cloudrunjobs_testcase_generation

