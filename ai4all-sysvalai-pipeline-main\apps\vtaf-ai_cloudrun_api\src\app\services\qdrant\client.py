"""
client.py

Provides a factory method to create and configure a Qdrant client instance.
The client supports JWT-based authentication and uses a token provider
for automatic ID token generation (e.g., for IAP-secured endpoints).
"""

import logging
from qdrant_client import QdrantClient

from app.core.constants import (
    QDRANT_CLIENT_URL,
    QDRANT_CLIENT_PORT,
    QDRANT_JWT,
)
from app.services.qdrant.token_manager import get_id_token

logger = logging.getLogger("vtaf")


def create_qdrant_client() -> QdrantClient:
    """
    Initializes and returns a configured Qdrant client instance.

    Uses:
        - Base URL and port from environment configuration.
        - JWT as an API key (optional depending on setup).
        - ID token provider for authentication with IAP or secured Qdrant endpoints.

    Returns:
        QdrantClient: A ready-to-use instance of QdrantClient.
    """
    logger.info("Creating Qdrant client")

    return QdrantClient(
        url=QDRANT_CLIENT_URL,
        port=QDRANT_CLIENT_PORT,
        api_key=QDRANT_JWT,
        auth_token_provider=get_id_token,
        timeout=30,
    )
