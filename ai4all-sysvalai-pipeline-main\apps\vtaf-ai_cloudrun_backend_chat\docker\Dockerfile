# Use the official lightweight Node.js 12 image.
# https://hub.docker.com/_/node
FROM node:18 as buildenv
# Create and change to the app directory.
WORKDIR /app
# Copy application dependency manifests to the container image.
# A wildcard is used to ensure both package.json AND package-lock.json are copied.
# Copying this separately prevents re-running npm install on every code change.
COPY package*.json ./
# Install production dependencies.
COPY . ./
RUN npm install
RUN npm install -g npm@10.9.0
# Run the web service on container startup.
CMD [ "npm", "start" ]
