# file: chunking_requests.py

from pydantic import BaseModel, <PERSON>
from typing import Dict

class ChunkStringRequest(BaseModel):
    """
    Request schema for chunking a string using the chunking API.
    Includes the string to chunk, strategy, and parameters.
    """
    string: str = Field(
        default="string_to_chunk",
        description="String to be chunked."
    )
    chunking_strategy: str = Field(
        default="recursive-character-text-splitter",
        description="Chunking strategy to apply."
    )
    parameters: Dict = Field(
        default_factory=dict,
        description="Optional parameters for the chunking strategy."
    )
