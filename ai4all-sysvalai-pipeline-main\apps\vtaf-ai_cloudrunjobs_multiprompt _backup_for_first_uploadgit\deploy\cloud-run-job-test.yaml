# Google Cloud Run Job 配置 - 对话测试版本
apiVersion: run.googleapis.com/v1
kind: Job
metadata:
  name: vtaf-ai-multiprompt-test
  labels:
    app: vtaf-ai-multiprompt
    version: test
spec:
  template:
    spec:
      template:
        spec:
          taskCount: 1
          parallelism: 1
          taskTimeout: 1800s  # 30 minutes timeout
          restartPolicy: OnFailure
          containers:
          - name: vtaf-ai-multiprompt-test
            image: gcr.io/valeo-cp2673-dev/vtaf-ai-multiprompt:latest
            command: ["python", "src/main_with_test.py"]
            env:
            # Google Cloud 项目配置
            - name: PROJECT_ID
              value: "valeo-cp2673-dev"
            - name: PROJECT_NUMBER
              value: "203210506053"
            - name: REGION
              value: "us-central1"
            - name: GOOGLE_CLOUD_PROJECT
              value: "valeo-cp2673-dev"
            
            # AI 模型配置
            - name: DEFAULT_MODEL
              value: "gemini-1.5-pro"
            - name: AI_MODEL
              value: "gemini-1.5-pro"
            
            # 测试模式配置
            - name: TEST_CONVERSATION_ONLY
              value: "true"  # 设置为 true 只运行对话测试
            
            # 任务配置（对话测试不需要，但保留以防万一）
            - name: TASK_TYPE
              value: "batch"
            - name: PROMPT_SHEET_URL
              value: "https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
            - name: TEST_SPEC_SHEET_URL
              value: "https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
            - name: PROMPT_SHEET_NAME
              value: "Class Data"
            
            # Google Sheets 配置
            - name: GOOGLE_SHEETS_ENABLED
              value: "true"
            
            # 日志配置
            - name: LOG_LEVEL
              value: "INFO"
            - name: DEBUG
              value: "false"
            
            resources:
              limits:
                cpu: "1"
                memory: "2Gi"
              requests:
                cpu: "0.5"
                memory: "1Gi"
