<template>
  <q-page class="bg-grey-3">
    <construction-banner></construction-banner>
  <div class="row">
  <div class="col-8">
    <!-- <insert-media></insert-media> -->
    <prompt-area></prompt-area>
    <div class="q-mt-md q-ml-md q-mr-md">
      <div class="row items-center q-gutter-md">
      <send-training-dataset-button></send-training-dataset-button>
      <send-batch-reqs-button></send-batch-reqs-button>
      <send-single-req-button></send-single-req-button>
      <archive-t-s-button></archive-t-s-button>
      <q-space />
      <submit-button></submit-button>
      </div>
    </div>
    <output-area></output-area>
    <div class="q-mt-md q-ml-md q-mr-md">
      <div class="row items-center q-gutter-sm">
      <send-ts-training-dataset-button></send-ts-training-dataset-button>
      <send-batch-t-cs-button></send-batch-t-cs-button>
      <send-single-t-c-button></send-single-t-c-button>
      <q-space />
      <load-log-button></load-log-button>
      <save-log-button></save-log-button>
      <clear-button></clear-button>
      </div>
    </div>
  </div>
  <div class="col-4">
    <logo-display></logo-display>
    <!-- <system-instruction-area></system-instruction-area>
    <set-system-prompt-button></set-system-prompt-button> -->
    <configuration-button></configuration-button>
    <gemini-model-dropdown></gemini-model-dropdown>
    <gemini-region-dropdown></gemini-region-dropdown>
    <gemini-temperature-slider></gemini-temperature-slider>
    <gemini-max-output-tokens-slider></gemini-max-output-tokens-slider>
    <plugin-button-groups></plugin-button-groups>
    <spreadsheet-link-text></spreadsheet-link-text>
  </div>
  </div>
  </q-page>
</template>

<script setup>
// import { storeToRefs } from "pinia";
import { useHomeStore } from "src/stores/home/<USER>";
// import InsertMedia from "src/components/Multiprompting/InsertMedia.vue"
import ConstructionBanner from "src/components/Multiprompting/ConstructionBanner.vue";
import ConfigurationButton from "src/components/Multiprompting/ConfigurationButton.vue";
import PromptArea from "src/components/Multiprompting/PromptArea.vue"
import SendTrainingDatasetButton from "src/components/Multiprompting/SendTrainingDatasetButton.vue"
import SendBatchReqsButton from "src/components/Multiprompting/SendBatchReqsButton.vue"
import SendSingleReqButton from "src/components/Multiprompting/SendSingleReqButton.vue"
import ArchiveTSButton from "src/components/Multiprompting/ArchiveTSButton.vue"
import SubmitButton from "src/components/Multiprompting/SubmitButton.vue"
import OutputArea from "src/components/Multiprompting/OutputArea.vue"
import SendTsTrainingDatasetButton from "src/components/Multiprompting/SendTsTrainingDatasetButton.vue"
import SendBatchTCsButton from "src/components/Multiprompting/SendBatchTCsButton.vue"
import SendSingleTCButton from "src/components/Multiprompting/SendSingleTCButton.vue"
import LoadLogButton from "src/components/Multiprompting/LoadLogButton.vue"
import SaveLogButton from "src/components/Multiprompting/SaveLogButton.vue"
import ClearButton from "src/components/Multiprompting/ClearButton.vue"
import LogoDisplay from "src/components/Multiprompting/LogoDisplay.vue"
// import SystemInstructionArea from "src/components/Multiprompting/SystemInstructionArea.vue"
// import SetSystemPromptButton from "src/components/Multiprompting/SetSystemPromptButton.vue"
import GeminiModelDropdown from "src/components/Multiprompting/GeminiModelDropdown.vue"
import GeminiRegionDropdown from "src/components/Multiprompting/GeminiRegionDropdown.vue"
import GeminiTemperatureSlider from "src/components/Multiprompting/GeminiTemperatureSlider.vue"
import GeminiMaxOutputTokensSlider from "src/components/Multiprompting/GeminiMaxOutputTokensSlider.vue"
import PluginButtonGroups from  "src/components/Multiprompting/PluginButtonGroups.vue"
import SpreadsheetLinkText from "src/components/Multiprompting/SpreadsheetLinkText.vue"
import { onMounted } from "vue";

const home_store = useHomeStore();
// const { main_email, first_letter_email } = storeToRefs(home_store);
const { get_user_email } = home_store;

onMounted(async () => {
  await get_user_email();
});
</script>
