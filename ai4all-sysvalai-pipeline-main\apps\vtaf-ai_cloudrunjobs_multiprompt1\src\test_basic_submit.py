#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试基础的 AI 对话提交功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_basic_ai_submit():
    """测试基础的 AI 提交功能"""
    print("🤖 测试基础 AI 对话提交功能")
    print("=" * 50)
    
    try:
        from services.ai_service import AIService
        
        # 初始化 AI 服务
        print("📝 初始化 AI 服务...")
        ai_service = AIService()
        print("✅ AI 服务初始化成功")
        
        # 测试简单对话
        test_prompts = [
            "你好，请回复'Hello World'",
            "请用一句话介绍你自己",
            "1+1等于多少？",
            "请生成一个简单的测试用例，包含Test Case ID和Test Objective两个字段"
        ]
        
        print(f"\n🔄 开始测试 {len(test_prompts)} 个提示词...")
        
        results = []
        
        for i, prompt in enumerate(test_prompts, 1):
            print(f"\n--- 测试 {i}/{len(test_prompts)} ---")
            print(f"📤 提示词: {prompt}")
            
            try:
                # 发送请求
                response = ai_service.generate_response(prompt)
                
                # 检查响应
                if response and not response.startswith("Error"):
                    print(f"✅ 响应成功 (长度: {len(response)} 字符)")
                    print(f"📥 响应内容: {response[:100]}{'...' if len(response) > 100 else ''}")
                    results.append(True)
                else:
                    print(f"❌ 响应失败: {response[:100] if response else 'No response'}")
                    results.append(False)
                    
            except Exception as e:
                print(f"❌ 请求异常: {e}")
                results.append(False)
        
        # 统计结果
        success_count = sum(results)
        total_count = len(results)
        
        print(f"\n{'=' * 50}")
        print(f"📊 测试结果统计:")
        print(f"✅ 成功: {success_count}/{total_count}")
        print(f"❌ 失败: {total_count - success_count}/{total_count}")
        print(f"📈 成功率: {success_count/total_count*100:.1f}%")
        
        return success_count > 0  # 至少有一个成功就算通过
        
    except Exception as e:
        print(f"❌ AI 服务测试失败: {e}")
        return False

def test_model_switching():
    """测试模型切换功能"""
    print(f"\n🔄 测试模型切换功能")
    print("=" * 50)
    
    try:
        from services.ai_service import AIService, GEMINI_MODELS
        
        ai_service = AIService()
        
        print(f"📋 可用的 Gemini 模型:")
        for i, model in enumerate(GEMINI_MODELS, 1):
            print(f"  {i}. {model}")
        
        # 测试切换到不同模型
        test_models = GEMINI_MODELS[:2]  # 只测试前两个模型
        
        for model in test_models:
            print(f"\n🔄 切换到模型: {model}")
            
            try:
                ai_service.change_model(model)
                print(f"✅ 模型切换成功")
                
                # 测试简单请求
                response = ai_service.generate_response("请说'模型切换测试成功'")
                if response and not response.startswith("Error"):
                    print(f"✅ 模型响应正常: {response[:50]}...")
                else:
                    print(f"❌ 模型响应异常: {response[:50] if response else 'No response'}...")
                    
            except Exception as e:
                print(f"❌ 模型切换失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型切换测试失败: {e}")
        return False

def test_token_counting():
    """测试 Token 计数功能"""
    print(f"\n🔢 测试 Token 计数功能")
    print("=" * 50)
    
    try:
        from services.ai_service import AIService
        
        ai_service = AIService()
        
        test_texts = [
            "Hello World",
            "这是一个中文测试文本",
            "This is a longer text to test token counting functionality with multiple words and sentences.",
            "请生成一个包含Test Case ID、Test Objective、Test Condition、Test Action、Test Expectation的测试用例"
        ]
        
        for text in test_texts:
            try:
                token_count = ai_service.count_tokens(text)
                print(f"📝 文本: {text[:30]}{'...' if len(text) > 30 else ''}")
                print(f"🔢 Token 数量: {token_count}")
                print()
            except Exception as e:
                print(f"❌ Token 计数失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Token 计数测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print(f"\n⚠️  测试错误处理")
    print("=" * 50)
    
    try:
        from services.ai_service import AIService
        
        ai_service = AIService()
        
        # 测试空提示词
        print("📝 测试空提示词...")
        response = ai_service.generate_response("")
        print(f"📥 空提示词响应: {response[:100] if response else 'No response'}")
        
        # 测试超长提示词（如果需要的话）
        print("\n📝 测试超长提示词...")
        long_prompt = "测试" * 1000  # 2000个字符
        response = ai_service.generate_response(long_prompt)
        print(f"📥 超长提示词响应: {'成功' if response and not response.startswith('Error') else '失败或错误'}")
        
        # 测试无效模型
        print("\n📝 测试无效模型...")
        try:
            response = ai_service.generate_response("测试", model="invalid-model")
            print(f"📥 无效模型响应: {response[:100] if response else 'No response'}")
        except Exception as e:
            print(f"📥 无效模型异常: {str(e)[:100]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 VTAF AI Multiprompt - 基础对话提交功能测试")
    print("=" * 60)
    
    tests = [
        ("基础 AI 对话提交", test_basic_ai_submit),
        ("模型切换功能", test_model_switching),
        ("Token 计数功能", test_token_counting),
        ("错误处理", test_error_handling)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 开始测试: {test_name}")
            result = test_func()
            results.append((test_name, result))
            print(f"{'✅ 通过' if result else '❌ 失败'}: {test_name}")
        except Exception as e:
            print(f"❌ 测试异常: {test_name} - {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'=' * 60}")
    print("🎯 基础对话功能测试总结")
    print(f"{'=' * 60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"总体结果: {passed}/{total} 测试通过")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed >= 1:  # 至少基础对话功能要通过
        print(f"\n🎉 基础对话提交功能可用！")
        if passed == total:
            print("💡 所有功能都正常，可以进行完整的批处理任务")
        else:
            print("💡 核心功能可用，部分高级功能可能需要调整")
    else:
        print(f"\n⚠️  基础对话功能不可用，请检查配置和网络连接")
    
    return passed >= 1

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
