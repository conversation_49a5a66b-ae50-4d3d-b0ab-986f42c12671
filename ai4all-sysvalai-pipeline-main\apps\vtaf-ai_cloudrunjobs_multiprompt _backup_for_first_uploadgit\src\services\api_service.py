import time
from typing import Dict, Any, List
from services.ai_service import AIService
from services.batch_processor import BatchProcessor
from services.sheet_manager import SheetManager
from utils.logger import get_logger
from configs.env import settings

logger = get_logger(__name__)

class MultiPromptAPIService:
    """多提示词API服务"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.sheet_manager = SheetManager()
        
    async def send_training_dataset(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """发送训练数据集"""
        try:
            # 对应前端 SendTrainingDatasetButton 功能
            logger.info("Processing training dataset request")
            
            # 连接到数据集表格
            if not self.sheet_manager.connect_sheet(config['dataset_sheet_url']):
                return {"status": "error", "message": "Failed to connect to dataset sheet"}
                
            # 读取训练数据
            dataset = self.sheet_manager.read_by_sheet_name(config['dataset_sheet_name'])
            
            return {
                "status": "success",
                "message": f"Training dataset loaded with {len(dataset)} records",
                "data": dataset
            }
            
        except Exception as e:
            logger.error(f"Training dataset error: {e}")
            return {"status": "error", "message": str(e)}
            
    async def send_batch_requests(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """批量请求处理"""
        try:
            # 对应前端 SendBatchReqsButton 功能
            logger.info("Processing batch requests")
            
            batch_processor = BatchProcessor(self.ai_service)
            batch_config = {
                'task_type': 'batch',
                'prompt_sheet': config['prompt_sheet_url'],
                'test_spec_sheet': config['test_spec_sheet_url'],
                'model': config.get('model', settings.DEFAULT_MODEL)
            }
            
            # 异步执行批处理
            batch_processor.run(batch_config)
            
            return {
                "status": "success",
                "message": "Batch processing started",
                "job_id": f"batch_{int(time.time())}"
            }
            
        except Exception as e:
            logger.error(f"Batch processing error: {e}")
            return {"status": "error", "message": str(e)}
            
    async def send_single_request(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """单个请求处理"""
        try:
            # 对应前端 SendSingleReqButton 功能
            logger.info("Processing single request")
            
            prompt = config.get('prompt', '')
            model = config.get('model', settings.DEFAULT_MODEL)
            
            if not prompt:
                return {"status": "error", "message": "Prompt is required"}
                
            # 生成响应
            response = self.ai_service.generate_response(prompt, model)
            
            return {
                "status": "success",
                "message": "Single request completed",
                "response": response
            }
            
        except Exception as e:
            logger.error(f"Single request error: {e}")
            return {"status": "error", "message": str(e)}