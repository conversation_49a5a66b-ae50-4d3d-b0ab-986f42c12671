#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重构后的Cloud Run Jobs
验证每个任务类型的功能是否正常工作
"""

import os
import sys
import time
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from utils.logger import get_logger
from services.job_router import JobRouter, get_available_job_types

logger = get_logger(__name__)

class JobTester:
    """任务测试器"""
    
    def __init__(self):
        self.test_results = {}
        
    def run_all_tests(self):
        """运行所有任务类型的测试"""
        logger.info("🧪 开始测试重构后的Cloud Run Jobs")
        logger.info("=" * 60)
        
        available_jobs = get_available_job_types()
        
        for job_type, description in available_jobs.items():
            logger.info(f"\n🔍 测试任务类型: {job_type}")
            logger.info(f"描述: {description}")
            
            success = self._test_job_type(job_type)
            self.test_results[job_type] = success
            
            if success:
                logger.info(f"✅ {job_type} 测试通过")
            else:
                logger.error(f"❌ {job_type} 测试失败")
        
        self._print_summary()
    
    def _test_job_type(self, job_type: str) -> bool:
        """测试特定的任务类型"""
        try:
            # 设置环境变量
            original_job_type = os.getenv('JOB_TYPE')
            os.environ['JOB_TYPE'] = job_type
            
            # 设置测试环境变量
            self._setup_test_environment(job_type)
            
            # 创建JobRouter并执行
            router = JobRouter()
            result = router.route_job()
            
            # 恢复原始环境变量
            if original_job_type:
                os.environ['JOB_TYPE'] = original_job_type
            else:
                os.environ.pop('JOB_TYPE', None)
            
            return result == 0
            
        except Exception as e:
            logger.error(f"测试 {job_type} 时发生异常: {e}")
            return False
    
    def _setup_test_environment(self, job_type: str):
        """为不同任务类型设置测试环境变量"""

        # 通用测试环境变量
        test_env = {
            'RUN_MODE': 'test',
            'PROJECT_ID': 'test-project',
            'REGION': 'us-central1',
            'AI_MODEL': 'gemini-1.5-flash',
        }
        
        # 根据任务类型设置特定环境变量
        if job_type == 'basic_submit':
            test_env.update({
                'PROMPT_TEXT': 'Test prompt for basic submit functionality',
                'OUTPUT_FILE': '/tmp/test_basic_submit.txt'
            })
            
        elif job_type in ['batch_processing', 'single_task']:
            test_env.update({
                'PROMPT_SHEET_URL': 'test-prompt-sheet-url',
                'TEST_SPEC_SHEET_URL': 'test-spec-sheet-url',
                'PROMPT_SHEET_NAME': 'TestPrompts'
            })
            
        elif job_type == 'testcase_archive':
            test_env.update({
                'PROMPT_SHEET_URL': 'test-prompt-sheet-url',
                'CASE_SHEET_URL': 'test-case-sheet-url',
                'PROMPT_SHEET_NAME': 'Prompt_Archive',
                'CASE_SHEET_NAME': 'TestCases',
                'USER_NAME': 'TestUser'
            })
            
        elif job_type in ['testscript_batch', 'testscript_single']:
            test_env.update({
                'PROMPT_SHEET_URL': 'test-prompt-sheet-url',
                'TEST_SPEC_SHEET_URL': 'test-spec-sheet-url',
                'HEAD_FILES_CONFIG': '{"module1": "header1.h", "module2": "header2.h"}',
                'HEAD_FILE_PROMPT': 'Test head file prompt'
            })
            
        elif job_type in ['email_send', 'email_back']:
            test_env.update({
                'TASK_SHEET_URL': 'test-task-sheet-url',
                'LAST_AI_RESPONSE': 'Test AI response for email',
                'EMAIL_TEMPLATE': 'Test email template: '
            })
            
        elif job_type in ['training_dataset', 'ts_training_dataset']:
            test_env.update({
                'DATASET_SHEET_URL': 'test-dataset-sheet-url',
                'DATASET_SHEET_NAME': 'TestTrainingData',
                'AUTO_SUBMIT': 'false'
            })
            
        elif job_type == 'system_instruction':
            test_env.update({
                'SYSTEM_INSTRUCTIONS': '["Test instruction 1", "Test instruction 2"]',
                'INSTRUCTION_MODE': 'replace'
            })
            
        elif job_type == 'file_operations':
            test_env.update({
                'FILE_OPERATION': 'list',
                'SOURCE_PATH': '/tmp',
                'FILE_PATTERN': '*.txt'
            })
        
        # 设置环境变量
        for key, value in test_env.items():
            os.environ[key] = value
    
    def _print_summary(self):
        """打印测试结果摘要"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 测试结果摘要")
        logger.info("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        failed_tests = total_tests - passed_tests
        
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过测试: {passed_tests}")
        logger.info(f"失败测试: {failed_tests}")
        logger.info(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        logger.info("\n详细结果:")
        for job_type, result in self.test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"  {job_type}: {status}")
        
        if failed_tests == 0:
            logger.info("\n🎉 所有测试都通过了!")
        else:
            logger.warning(f"\n⚠️  有 {failed_tests} 个测试失败，请检查相关服务")

def test_job_router():
    """测试JobRouter基本功能"""
    logger.info("🔧 测试JobRouter基本功能...")
    
    try:
        # 测试获取可用任务类型
        available_jobs = get_available_job_types()
        logger.info(f"可用任务类型数量: {len(available_jobs)}")
        
        # 测试JobRouter初始化
        router = JobRouter()
        logger.info(f"JobRouter初始化成功，当前任务类型: {router.job_type}")
        
        logger.info("✅ JobRouter基本功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ JobRouter基本功能测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 VTAF AI Multiprompt 重构测试工具")
    logger.info("=" * 60)
    
    # 测试JobRouter基本功能
    if not test_job_router():
        logger.error("JobRouter基本功能测试失败，退出")
        return 1
    
    # 运行所有任务类型测试
    tester = JobTester()
    tester.run_all_tests()
    
    # 根据测试结果返回退出码
    failed_count = sum(1 for result in tester.test_results.values() if not result)
    
    if failed_count == 0:
        logger.info("🎉 所有测试完成，重构成功!")
        return 0
    else:
        logger.error(f"💥 有 {failed_count} 个测试失败")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
