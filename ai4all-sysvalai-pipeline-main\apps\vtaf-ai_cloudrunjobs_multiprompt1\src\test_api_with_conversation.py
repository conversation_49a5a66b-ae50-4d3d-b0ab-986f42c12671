#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的API测试程序 - 模拟AI对话并测试三个按钮功能
"""

import requests
import json
import time
from datetime import datetime
from main import test_conversation_functionality


class APITester:
    def __init__(self, base_url="http://127.0.0.1:8080"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_health_check(self):
        """测试健康检查"""
        print("🔍 测试健康检查...")
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 健康检查成功: {data['message']}")
                return True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    def simulate_ai_conversation(self):
        """模拟AI对话，生成测试数据"""
        print("\n🤖 模拟AI对话...")
        
        conversations = [
            {
                "session_id": "test_session_001",
                "conversation": [
                    {"role": "user", "content": "你好，请帮我生成一个汽车测试用例"},
                    {"role": "assistant", "content": "好的！以下是一个汽车功能测试用例：\n\nTest Case ID: TC_001\nTest Objective: 验证车辆启动功能\nTest Condition: 车辆处于停车状态，钥匙在车内\nTest Action: 1. 踩下刹车踏板 2. 按下启动按钮\nTest Expectation: 发动机成功启动，仪表盘显示正常"},
                    {"role": "user", "content": "很好，再生成一个关于刹车系统的测试用例"},
                    {"role": "assistant", "content": "这里是刹车系统测试用例：\n\nTest Case ID: TC_002\nTest Objective: 验证紧急刹车功能\nTest Condition: 车辆以50km/h速度行驶\nTest Action: 用力踩下刹车踏板\nTest Expectation: 车辆在安全距离内完全停止，ABS系统正常工作"}
                ],
                "metadata": {
                    "timestamp": datetime.now().isoformat(),
                    "model": "gemini-1.5-pro",
                    "total_tokens": 150
                }
            },
            {
                "session_id": "test_session_002", 
                "conversation": [
                    {"role": "user", "content": "请解释什么是Vertex AI"},
                    {"role": "assistant", "content": "Vertex AI是Google Cloud的统一机器学习平台，提供端到端的ML工作流程，包括数据准备、模型训练、部署和监控功能。"}
                ],
                "metadata": {
                    "timestamp": datetime.now().isoformat(),
                    "model": "gemini-1.5-pro",
                    "total_tokens": 80
                }
            }
        ]
        
        return conversations
    
    def test_save_log_button(self, conversation_data):
        """测试Save Log按钮功能"""
        print(f"\n💾 测试Save Log按钮...")
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/logs/save",
                json=conversation_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 保存成功: {result['message']}")
                if 'filename' in result:
                    print(f"📁 文件名: {result['filename']}")
                return True, result.get('filename')
            else:
                print(f"❌ 保存失败: {response.status_code} - {response.text}")
                return False, None
                
        except Exception as e:
            print(f"❌ 保存异常: {e}")
            return False, None
    
    def test_load_log_list_button(self):
        """测试Load Log List按钮功能"""
        print(f"\n📋 测试Load Log List按钮...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/logs/list")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 获取日志列表成功")
                print(f"📁 找到 {len(result['files'])} 个日志文件:")
                
                for file_info in result['files']:
                    print(f"   - {file_info['filename']} ({file_info['size']} bytes, {file_info['modified']})")
                
                return True, result['files']
            else:
                print(f"❌ 获取列表失败: {response.status_code}")
                return False, []
                
        except Exception as e:
            print(f"❌ 获取列表异常: {e}")
            return False, []
    
    def test_load_specific_log(self, filename):
        """测试加载特定日志文件"""
        print(f"\n📖 测试加载日志文件: {filename}")
        
        try:
            response = self.session.get(f"{self.base_url}/api/logs/load?filename={filename}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 加载日志成功")
                print(f"💬 对话数量: {len(result['data']['conversation'])}")
                print(f"🔖 会话ID: {result['data'].get('session_id', 'N/A')}")
                return True, result['data']
            else:
                print(f"❌ 加载日志失败: {response.status_code}")
                return False, None
                
        except Exception as e:
            print(f"❌ 加载日志异常: {e}")
            return False, None
    
    def test_clear_button(self):
        """测试Clear按钮功能"""
        print(f"\n🗑️ 测试Clear按钮...")
        
        try:
            response = self.session.delete(f"{self.base_url}/api/logs/clear")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 清除成功: {result['message']}")
                if 'deleted_count' in result:
                    print(f"🗂️ 删除了 {result['deleted_count']} 个文件")
                return True
            else:
                print(f"❌ 清除失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 清除异常: {e}")
            return False

def main():
    """主测试函数"""
    print("🚀 VTAF AI Multiprompt - API功能完整测试")
    print("=" * 60)
    
    # 初始化测试器
    tester = APITester()
    
    # 测试结果统计
    test_results = []
    
    # 1. 健康检查
    result = tester.test_health_check()
    test_results.append(("健康检查", result))
    
    if not result:
        print("❌ 服务未启动，请先启动main.py")
        return
    
    # 2. 生成模拟对话数据
    conversations = test_conversation_functionality()
    print(f"✅ 生成了 {len(conversations)} 个模拟对话")
    
    # 3. 测试Save Log按钮 - 保存多个对话
    saved_files = []
    for i, conv in enumerate(conversations):
        print(f"\n--- 保存对话 {i+1} ---")
        success, filename = tester.test_save_log_button(conv)
        test_results.append((f"保存对话{i+1}", success))
        if filename:
            saved_files.append(filename)
        time.sleep(1)  # 避免文件名冲突
    
    # 4. 测试Load Log List按钮
    success, file_list = tester.test_load_log_list_button()
    test_results.append(("获取日志列表", success))
    
    # 5. 测试加载特定日志文件
    if saved_files:
        success, data = tester.test_load_specific_log(saved_files[0])
        test_results.append(("加载特定日志", success))
    
    # 6. 再次获取列表确认文件存在
    print(f"\n📊 再次确认日志文件...")
    tester.test_load_log_list_button()
    
    # 7. 测试Clear按钮（可选，会删除所有日志）
    print(f"\n⚠️ 是否测试Clear功能？(会删除所有日志文件)")
    user_input = input("输入 'yes' 继续测试Clear功能，其他任意键跳过: ")
    
    if user_input.lower() == 'yes':
        success = tester.test_clear_button()
        test_results.append(("清除日志", success))
        
        # 确认清除结果
        print(f"\n📊 清除后的日志列表:")
        tester.test_load_log_list_button()
    
    # 8. 测试结果汇总
    print(f"\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！三个按钮功能都正常工作！")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()