# vtaf-ai-client-sdk

A Python SDK for interacting with the VTAF-AI APIs, generated automatically from the OpenAPI specification and packaged for easy installation and use.

---

## Table of Contents
- [vtaf-ai-client-sdk](#vtaf-ai-client-sdk)
  - [Table of Contents](#table-of-contents)
  - [Folder Structure](#folder-structure)
  - [Creating the SDK](#creating-the-sdk)
  - [Packaging the SDK](#packaging-the-sdk)
    - [Steps to Package](#steps-to-package)
    - [1. Update Metadata](#1-update-metadata)
    - [2. Update Dependencies](#2-update-dependencies)
    - [3. Build the Package](#3-build-the-package)
    - [4. Locate the Built Packages](#4-locate-the-built-packages)
  - [Installing the SDK](#installing-the-sdk)
  - [Usage Example](#usage-example)

---

## Folder Structure

```plaintext
vtaf-ai-api-client
│
├── .ruff_cache              # Cache for ruff linter
├── dist                     # Distribution packages (wheel and tar.gz)
├── vtaf_ai_api_client       # Generated API client code
│   ├── api
│   │   ├── agents_v_1
│   │   ├── chunking_v_1
│   │   ├── default
│   │   ├── generations_v_1
│   │   ├── parsing_v_1
│   │   ├── utils_v_1
│   │   └── vector_db_v_1
│   └── models
└── vtaf_sdk                 # Wrapper SDK around generated client
```
## Creating the SDK

To generate or update the SDK from the OpenAPI spec, run:

```bash
openapi-python-client generate --url http://127.0.0.1:8000/vtaf-api/openapi.json --overwrite
```
Make sure your API server is running locally at the above URL or update the URL to your API endpoint.

---

## Packaging the SDK
This project uses [Poetry](https://python-poetry.org/) to manage dependencies and packaging. Here are the basics:

### Steps to Package

### 1. Update Metadata

- Edit the `pyproject.toml` file to update project metadata such as **authors** and **version**.

### 2. Update Dependencies

- Modify dependencies under `[tool.poetry.dependencies]` in `pyproject.toml` to reflect your project's requirements.

### 3. Build the Package

- Navigate to the `vtaf-ai-api-client` directory.
- Run the following command to build the package:
  ```bash
  python -m build 
  ```

### 4. Locate the Built Packages

- After building, the artifacts will be available in the `dist` directory. Typical output files include:

  - `vtaf_sdk-1.0.0-py3-none-any.whl`
  - `vtaf_sdk-1.0.0.tar.gz`

> **Note:** Poetry uses a single configuration file, `pyproject.toml`, to manage metadata, dependencies, and scripts, streamlining Python project management[2][4].

## Installing the SDK
Install the generated wheel package locally using pip: 
   ```bash 
   pip install vtaf_sdk-1.0.0-py3-none-any.whl
   ```
Alternatively, you can install directly from PyPI if published.

---
## Usage Example
Here's a minimal example of how to use the SDK after installation:
```python
from vtaf_sdk import VTAFClient

client = VTAFClient(
    base_url="https://cp2673.apps-dev.valeo.com/vtaf-api",
    audience="203210506053-jg7fke8teaejg9vo5697kvifghr16lmq.apps.googleusercontent.com",
    timeout=60,
)

parser_health = client.parsing.health_check()
print(parser_health)
```