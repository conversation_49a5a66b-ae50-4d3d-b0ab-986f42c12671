from typing import Optional


class ConfigSync:
    def __init__(self, project_id, dataset_id, google_sheet, bigquery, logger):
        self.project_id = project_id
        self.dataset_id = dataset_id
        self.google_sheet = google_sheet
        self.bigquery = bigquery
        self.logger = logger

    def update_functions(self, SHEET_NAME: str, ROW_INDEX: int, COLUMN_LETTER: str) -> Optional[bool]:
        """
        Updates the EUF_FEATURE_CONFIG_TABLE if a value is not found.

        Args:
            SHEET_NAME (str): Name of the Google Sheet.
            ROW_INDEX (int): Index of the row to fetch the value from.
            COLUMN_LETTER (str): Letter of the column to read.

        Returns:
            Optional[bool]: True if insertion was successful, False if not, None if nothing needed to be inserted.
        """
        try:
            # Read the sheet data
            sheet_data = self.google_sheet.read_sheet(SHEET_NAME, f'{COLUMN_LETTER}:{COLUMN_LETTER}')

            # Fetch the specific value from the row index
            euf_value = ', '.join(sheet_data[ROW_INDEX])
            if euf_value is None:
                self.logger.error(f"No value found at row {ROW_INDEX} in column {COLUMN_LETTER}")
                return False

            # Query to check if the value already exists
            get_feature_config_query = f"""
                SELECT * FROM `{self.project_id}.{self.dataset_id}.EUF_FEATURE_CONFIG_TABLE`
                WHERE value = '{euf_value}'
            """
            table_data, status = self.bigquery.execute_get_query(get_feature_config_query)

            if not status:
                self.logger.error("Failed to execute SELECT query.")
                return False

            if len(table_data) == 0:  # If the value doesn't exist, insert it
                insert_feature_config_query = f"""
                    INSERT INTO `{self.project_id}.{self.dataset_id}.EUF_FEATURE_CONFIG_TABLE` (uuid, category, value, relation)
                    VALUES (GENERATE_UUID(), '', '{euf_value}', '')
                """
                status = self.bigquery.execute_insert_query(insert_feature_config_query)

                if status:
                    self.logger.info(f"Successfully inserted EUF: {euf_value}")
                    return True
                else:
                    self.logger.error(f"Failed to insert EUF: {euf_value}")
                    return False
            else:
                self.logger.info(f"EUF value '{euf_value}' already exists in the table.")
                return None
        except IndexError:
            self.logger.error(f"Row index {ROW_INDEX} is out of range for the provided sheet data.")
            return False
        except Exception as e:
            self.logger.error(f"An error occurred: {str(e)}")
            return False

    def project_config(self, SHEET_NAME: str, ROW_INDEX: int, COLUMN_LETTER: str) -> Optional[bool]:
        """
        Updates the EUF_FEATURE_CONFIG_TABLE if a value is not found.

        Args:
            SHEET_NAME (str): Name of the Google Sheet.
            ROW_INDEX (int): Index of the row to fetch the value from.
            COLUMN_LETTER (str): Letter of the column to read.

        Returns:
            Optional[bool]: True if insertion was successful, False if not, None if nothing needed to be inserted.
        """
        try:
            # Read the sheet data
            sheet_data = self.google_sheet.read_sheet(SHEET_NAME, f'{COLUMN_LETTER}:{COLUMN_LETTER}')
            config_type = ', '.join(sheet_data[0]).lower()
            # Fetch the specific value from the row index
            config = ', '.join(sheet_data[ROW_INDEX])
            if config is None or not config_type:
                self.logger.error(f"No value found at row {ROW_INDEX} or 0 in column {COLUMN_LETTER}")
                return False
            category, config_value = config.split(":", 1)
            category = category.strip()
            config_value = config_value.strip()
            if not category or not config_value:
                self.logger.error("both category and value need to present")
                return False
            get_project_config_query = f"""
                SELECT * FROM `{self.project_id}.{self.dataset_id}.PROJECT_CONFIGURATION`
                WHERE LOWER(type) = '{config_type.lower()}' AND LOWER(category) = '{category.lower()}' 
                AND LOWER(value) = '{config_value.lower()}'
            """

            table_data, status = self.bigquery.execute_get_query(get_project_config_query)

            if not status:
                self.logger.error("Failed to execute SELECT query.")
                return False

            if len(table_data) == 0:  # If the value doesn't exist, insert it
                insert_project_config_query = f"""
                    INSERT INTO `{self.project_id}.{self.dataset_id}.PROJECT_CONFIGURATION` (category, value, relation, type, config_uuid)
                    VALUES ('{category.title()}', '{config_value.title()}', '', '{config_type}', GENERATE_UUID())
                """
                status = self.bigquery.execute_insert_query(insert_project_config_query)

                if status:
                    self.logger.info(f"Successfully inserted Feature: {config}")
                    return True
                else:
                    self.logger.error(f"Failed to insert EUF: {config}")
                    return False
            else:
                self.logger.info(f"EUF value '{config}' already exists in the table.")
                return None
        except IndexError:
            self.logger.error(f"Row index {ROW_INDEX} is out of range for the provided sheet data.")
            return False
        except Exception as e:
            self.logger.error(f"An error occurred: {str(e)}")
            return False

    def project_functions_relation(self, SHEET_NAME: str, ROW_INDEX: int, COLUMN_LETTER: str) -> Optional[bool]:
        """
        Updates the EUF_FEATURE_CONFIG_TABLE  relation.

        Args:
            SHEET_NAME (str): Name of the Google Sheet.
            ROW_INDEX (int): Index of the row to fetch the value from.
            COLUMN_LETTER (str): Letter of the column to read.

        Returns:
            Optional[bool]: True if insertion was successful, False if not, None if nothing needed to be inserted.
        """
        try:
            # Read the sheet data
            euf_sheet_data = self.google_sheet.read_sheet(SHEET_NAME, f'{COLUMN_LETTER}:{COLUMN_LETTER}')
            org_sheet_data = self.google_sheet.read_sheet(SHEET_NAME, f'A:A')

            try:
                org = ', '.join(org_sheet_data[ROW_INDEX])
            except IndexError:
                self.logger.error(
                    f"Row index {ROW_INDEX} is out of range for the provided sheet data in sheet '{SHEET_NAME}'.")
                return False
            try:
                if not len(euf_sheet_data[ROW_INDEX]):
                    euf_data_list = []
                else:
                    euf_data_str = euf_sheet_data[ROW_INDEX][0]
                    euf_data_list = [feature.strip() for feature in euf_data_str.split(',')]

            except IndexError:
                euf_data_list = []

            if not org:
                self.logger.error(
                    f"No value found at row {ROW_INDEX} in column {COLUMN_LETTER} of sheet '{SHEET_NAME}'.")
                return False

            try:
                bg, pg, pl = org.split("::", 2)
                bg = bg
                pg = pg
                pl = pl
            except ValueError:
                self.logger.error(f"Invalid org format '{org}' at row {ROW_INDEX}. Expected format: 'bg::pg::pl'.")
                return False

            get_organization_query = f"""
                SELECT org_uuid FROM `{self.project_id}.{self.dataset_id}.VALEO_ORGANIZATION`
                WHERE bg = '{bg}' AND pg = '{pg}' AND pl = '{pl}'
            """

            table_data, status = self.bigquery.execute_get_query(get_organization_query)

            if not status or not len(table_data):
                self.logger.error(f"Failed to retrieve org_uuid for organization '{org}' in sheet '{SHEET_NAME}'.")
                return False

            org_uuid = table_data['org_uuid'][0]

            existing_euf_query = f"""
                SELECT uuid
                FROM {self.project_id}.{self.dataset_id}.EUF_FEATURE_CONFIG_TABLE
                WHERE EXISTS (
                    SELECT 1
                    FROM UNNEST(SPLIT(relation, ',')) AS org_uuid
                    WHERE org_uuid = '{org_uuid}'
                );
            """
            table_data, status = self.bigquery.execute_get_query(existing_euf_query)

            if not status:
                self.logger.error(f"Failed to execute SELECT query for existing EUF data in sheet '{SHEET_NAME}'.")
                return False

            existing_euf_data_list = [row for row in table_data['uuid']]
            new_euf_data_list = []
            if euf_data_list:
                euf_values = ', '.join([f"'{euf}'" for euf in euf_data_list])

                new_euf_query = f"""
                    SELECT uuid 
                    FROM `{self.project_id}.{self.dataset_id}.EUF_FEATURE_CONFIG_TABLE`
                    WHERE value IN ({euf_values})
                """
                table_data, status = self.bigquery.execute_get_query(new_euf_query)

                if not status:
                    self.logger.error(f"Failed to retrieve new EUF data in sheet '{SHEET_NAME}'.")
                    return False

                new_euf_data_list = [row for row in table_data['uuid']]

            if not new_euf_data_list:
                remove_all_query = f"""
                    UPDATE `{self.project_id}.{self.dataset_id}.EUF_FEATURE_CONFIG_TABLE`
                    SET relation = (
                        SELECT STRING_AGG(org_uuid, ',')
                        FROM UNNEST(SPLIT(relation, ',')) org_uuid
                        WHERE org_uuid != '{org_uuid}'
                    )
                    WHERE EXISTS (
                        SELECT 1 FROM UNNEST(SPLIT(relation, ',')) org_uuid WHERE org_uuid = '{org_uuid}'
                    );
                """
                status = self.bigquery.execute_get_query(remove_all_query)

                if not status:
                    self.logger.error(
                        f"Failed to remove org_uuid '{org_uuid}' from all EUF_FEATURE_CONFIG_TABLE relations  in sheet '{SHEET_NAME}'.")
                    return False

                self.logger.info(
                    f"Successfully removed org_uuid '{org_uuid}' from all EUF_FEATURE_CONFIG_TABLE relations in sheet '{SHEET_NAME}'.")
                return True

            if not existing_euf_data_list:
                euf_uuid_list_str = ', '.join(f"'{euf_uuid}'" for euf_uuid in new_euf_data_list)
                update_project_config_relation = f"""
                                        UPDATE `{self.project_id}.{self.dataset_id}.EUF_FEATURE_CONFIG_TABLE`
                                        SET relation = IF(relation IS NULL OR relation = '', '{org_uuid}', CONCAT(relation, ',', '{org_uuid}'))
                                        WHERE uuid IN ({euf_uuid_list_str})
                                        AND NOT EXISTS (
                                            SELECT 1 FROM UNNEST(SPLIT(relation, ',')) org_uuid WHERE org_uuid = '{org_uuid}'
                                        );
                                    """
                status = self.bigquery.execute_get_query(update_project_config_relation)

                if not status:
                    self.logger.error(f"Failed to add org_uuid '{org_uuid}' to relation for UUID '{euf_uuid_list_str}'.")
                    return False

                self.logger.info(f"Successfully added org_uuid '{org_uuid}' to all relations to "
                                 f"EUF_FEATURE_CONFIG_TABLE in sheet '{SHEET_NAME}'.")
                return True
            uuids_to_add = set(new_euf_data_list) - set(existing_euf_data_list)
            uuids_to_remove = set(existing_euf_data_list) - set(new_euf_data_list)
            if uuids_to_add:
                update_project_config_relation = f"""
                        UPDATE `{self.project_id}.{self.dataset_id}.EUF_FEATURE_CONFIG_TABLE`
                        SET relation = IF(relation IS NULL OR relation = '', '{org_uuid}', CONCAT(relation, ',', '{org_uuid}'))
                        WHERE uuid IN ({', '.join([f"'{uuid}'" for uuid in uuids_to_add])})
                        AND NOT EXISTS (
                            SELECT 1 FROM UNNEST(SPLIT(relation, ',')) org_uuid WHERE org_uuid = '{org_uuid}'
                        );
                    """
                if not self.bigquery.execute_insert_query(update_project_config_relation):
                    self.logger.error(f"Bulk add failed for org_uuid '{org_uuid}' in EUF_FEATURE_CONFIG_TABLE.")
                    return False
            if uuids_to_remove:
                remove_query = f"""
                        UPDATE `{self.project_id}.{self.dataset_id}.EUF_FEATURE_CONFIG_TABLE`
                        SET relation = (
                            SELECT STRING_AGG(org, ',')
                            FROM UNNEST(SPLIT(relation, ',')) org
                            WHERE org != '{org_uuid}'
                        )
                        WHERE uuid IN ({', '.join([f"'{uuid}'" for uuid in uuids_to_remove])});
                    """
                if not self.bigquery.execute_insert_query(remove_query):
                    self.logger.error(f"Bulk remove failed for org_uuid '{org_uuid}' in EUF_FEATURE_CONFIG_TABLE.")
                    return False

            self.logger.info(
                f"Successfully updated EUF_FEATURE_CONFIG_TABLE relations for org_uuid '{org_uuid}' to  EUF_FEATURE_CONFIG_TABLE in sheet '{SHEET_NAME}'.")
            return True

        except Exception as e:
            self.logger.error(f"An unexpected error occurred: {str(e)}")
            return False

    def project_config_relation(self, SHEET_NAME: str, ROW_INDEX: int, COLUMN_LETTER: str) -> Optional[bool]:
        """
        Updates the PROJECT_CONFIGURATION  relation.

        Args:
            SHEET_NAME (str): Name of the Google Sheet.
            ROW_INDEX (int): Index of the row to fetch the value from.
            COLUMN_LETTER (str): Letter of the column to read.

        Returns:
            Optional[bool]: True if insertion was successful, False if not, None if nothing needed to be inserted.
        """
        try:
            # Read the sheet data
            project_config_data = self.google_sheet.read_sheet(SHEET_NAME, f'{COLUMN_LETTER}:{COLUMN_LETTER}')
            config_type = ', '.join(project_config_data[0]).lower()
            org_sheet_data = self.google_sheet.read_sheet(SHEET_NAME, f'A:A')
            if not config_type:
                self.logger.error(f"No value found at row  0 in column {COLUMN_LETTER}")
                return False
            try:
                org = ', '.join(org_sheet_data[ROW_INDEX])
            except IndexError:
                self.logger.error(
                    f"Row index {ROW_INDEX} is out of range for the provided sheet data in sheet '{SHEET_NAME}'.")
                return False
            try:
                if not len(project_config_data[ROW_INDEX]):
                    project_config_data_list = []
                else:
                    project_config_data_str = project_config_data[ROW_INDEX][0]
                    project_config_data_list = [feature.strip() for feature in project_config_data_str.split(',')]

            except IndexError:
                project_config_data_list = []

            if not org:
                self.logger.error(
                    f"No value found at row {ROW_INDEX} in column {COLUMN_LETTER} of sheet '{SHEET_NAME}'.")
                return False

            try:
                bg, pg, pl = org.split("::", 2)
                bg = bg
                pg = pg
                pl = pl
            except ValueError:
                self.logger.error(f"Invalid org format '{org}' at row {ROW_INDEX}. Expected format: 'bg::pg::pl'.")
                return False

            get_organization_query = f"""
                SELECT org_uuid FROM `{self.project_id}.{self.dataset_id}.VALEO_ORGANIZATION`
                WHERE bg = '{bg}' AND pg = '{pg}' AND pl = '{pl}'
            """

            table_data, status = self.bigquery.execute_get_query(get_organization_query)

            if not status or not len(table_data):
                self.logger.error(f"Failed to retrieve org_uuid for organization '{org}' in sheet '{SHEET_NAME}'.")
                return False

            org_uuid = table_data['org_uuid'][0]

            existing_project_config_query = f"""
                SELECT config_uuid
                FROM {self.project_id}.{self.dataset_id}.PROJECT_CONFIGURATION
                WHERE EXISTS (
                    SELECT 1
                    FROM UNNEST(SPLIT(relation, ',')) AS org_uuid
                    WHERE org_uuid = '{org_uuid}' AND LOWER(type) = '{config_type.lower()}'
                );
            """
            table_data, status = self.bigquery.execute_get_query(existing_project_config_query)

            if not status:
                self.logger.error(f"Failed to execute SELECT query for existing EUF data in sheet '{SHEET_NAME}'.")
                return False

            existing_project_config_data_list = [row for row in table_data['config_uuid']]
            new_project_config_data_list = []
            if project_config_data_list:
                project_config_pairs = []
                for project_config in project_config_data_list:
                    try:
                        category, config_value = project_config.split(":", 1)
                        project_config_pairs.append((category.strip().lower(), config_value.strip().lower()))
                    except ValueError:
                        self.logger.warning(f"Skipping invalid project config entry: {project_config}")

                # Build WHERE clauses like (category = 'x' AND value = 'y')
                conditions = " OR ".join(
                    f"(LOWER(category) = '{cat}' AND LOWER(value) = '{val}')" for cat, val in project_config_pairs
                )

                # Final query
                get_project_config_query = f"""
                    SELECT config_uuid, LOWER(category) as category, LOWER(value) as value
                    FROM `{self.project_id}.{self.dataset_id}.PROJECT_CONFIGURATION`
                    WHERE LOWER(type) = '{config_type.lower()}' AND ({conditions})
                """
                table_data, status = self.bigquery.execute_get_query(get_project_config_query)

                if not status:
                    self.logger.error(f"Failed to retrieve new Project Config data in sheet '{SHEET_NAME}'.")
                    return False
                new_project_config_data_list = list(table_data['config_uuid']) if not table_data.empty else []

            if not new_project_config_data_list:
                remove_all_query = f"""
                    UPDATE `{self.project_id}.{self.dataset_id}.PROJECT_CONFIGURATION`
                    SET relation = (
                        SELECT STRING_AGG(org_uuid, ',')
                        FROM UNNEST(SPLIT(relation, ',')) org_uuid
                        WHERE org_uuid != '{org_uuid}' AND LOWER(type) = '{config_type.lower()}'
                    )
                    WHERE EXISTS (
                        SELECT 1 FROM UNNEST(SPLIT(relation, ',')) org_uuid WHERE org_uuid = '{org_uuid}'
                         AND LOWER(type) = '{config_type.lower()}'
                    );
                """
                status = self.bigquery.execute_get_query(remove_all_query)

                if not status:
                    self.logger.error(
                        f"Failed to remove org_uuid '{org_uuid}' from all PROJECT_CONFIGURATION relations in sheet '{SHEET_NAME}'.")
                    return False

                self.logger.info(
                    f"Successfully removed org_uuid '{org_uuid}' from all PROJECT_CONFIGURATION relations in sheet '{SHEET_NAME}'.")
                return True

            if not existing_project_config_data_list:
                config_uuid_list_str = ', '.join(f"'{uuid}'" for uuid in new_project_config_data_list)
                update_project_config_relation = f"""
                        UPDATE `{self.project_id}.{self.dataset_id}.PROJECT_CONFIGURATION`
                        SET relation = IF(relation IS NULL OR relation = '', '{org_uuid}', CONCAT(relation, ',', '{org_uuid}'))
                        WHERE config_uuid IN ({config_uuid_list_str})
                        AND LOWER(type) = '{config_type.lower()}'
                        AND NOT EXISTS (
                            SELECT 1 FROM UNNEST(SPLIT(relation, ',')) org_uuid WHERE org_uuid = '{org_uuid}'
                             AND LOWER(type) = '{config_type.lower()}'
                        );
                    """
                status = self.bigquery.execute_get_query(update_project_config_relation)
                if not status:
                    self.logger.error(
                        f"Failed to add org_uuid '{org_uuid}' to PROJECT_CONFIGURATION relation for UUID '{config_uuid_list_str}'.")
                    return False

                self.logger.info(f"Successfully added org_uuid '{org_uuid}' to all PROJECT_CONFIGURATION relations in "
                                 f"sheet '{SHEET_NAME}'.")
                return True
            uuids_to_add = set(new_project_config_data_list) - set(existing_project_config_data_list)
            uuids_to_remove = set(existing_project_config_data_list) - set(new_project_config_data_list)
            if uuids_to_add:
                update_project_config_relation = f"""
                        UPDATE `{self.project_id}.{self.dataset_id}.PROJECT_CONFIGURATION`
                        SET relation = IF(relation IS NULL OR relation = '', '{org_uuid}', CONCAT(relation, ',', '{org_uuid}'))
                        WHERE config_uuid IN ({', '.join([f"'{uuid}'" for uuid in uuids_to_add])})
                        AND LOWER(type) = '{config_type.lower()}'
                        AND NOT EXISTS (
                            SELECT 1 FROM UNNEST(SPLIT(relation, ',')) org_uuid WHERE org_uuid = '{org_uuid}'
                             AND LOWER(type) = '{config_type.lower()}'
                        );
                    """
                if not self.bigquery.execute_insert_query(update_project_config_relation):
                    self.logger.error(f"Bulk add failed for org_uuid '{org_uuid}' in PROJECT_CONFIGURATION.")
                    return False
            if uuids_to_remove:
                remove_query = f"""
                        UPDATE `{self.project_id}.{self.dataset_id}.PROJECT_CONFIGURATION`
                        SET relation = (
                            SELECT STRING_AGG(org, ',')
                            FROM UNNEST(SPLIT(relation, ',')) org
                            WHERE org != '{org_uuid}' AND LOWER(type) = '{config_type.lower()}'
                        )
                        WHERE config_uuid IN ({', '.join([f"'{uuid}'" for uuid in uuids_to_remove])})
                         AND LOWER(type) = '{config_type.lower()}';
                    """
                if not self.bigquery.execute_insert_query(remove_query):
                    self.logger.error(f"Bulk remove failed for org_uuid '{org_uuid}' in PROJECT_CONFIGURATION.")
                    return False

            self.logger.info(
                f"Successfully updated PROJECT_CONFIGURATION relations for org_uuid '{org_uuid}' '{config_type}' in "
                f"sheet '{SHEET_NAME}'.")
            return True

        except Exception as e:
            self.logger.error(f"An unexpected error occurred: {str(e)}")
            return False
