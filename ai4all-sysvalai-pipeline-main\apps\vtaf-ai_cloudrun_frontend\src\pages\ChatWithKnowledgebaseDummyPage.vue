<template>
  <div>
<project-dropdown></project-dropdown>
<div v-if="response_loading" class="q-ml-md">
          <q-spinner-dots color="primary" size="40px" />
        </div>
<user-prompt></user-prompt>
<output-area></output-area>
</div>
</template>

<script setup>
import ProjectDropdown from 'src/components/Chat_Kb_dummy/ProjectDropdown.vue';
import UserPrompt from 'src/components/Chat_Kb_dummy/UserPrompt.vue';
import OutputArea from 'src/components/Chat_Kb_dummy/OutputArea.vue';
import { storeToRefs } from "pinia";
import { useHomeStore } from "src/stores/home/<USER>";
import { onMounted } from "vue";
import { useChatWithKbStore } from "src/stores/chat_with_kb_dummy-store";
import { QSpinnerDots } from "quasar";


const ai_testcase_config = useChatWithKbStore();
const { response_loading } = storeToRefs(ai_testcase_config);

const home_store = useHomeStore();
// const { main_email, first_letter_email } = storeToRefs(home_store);
const { get_user_email } = home_store;

onMounted(async () => {
  await get_user_email();
});
</script>
