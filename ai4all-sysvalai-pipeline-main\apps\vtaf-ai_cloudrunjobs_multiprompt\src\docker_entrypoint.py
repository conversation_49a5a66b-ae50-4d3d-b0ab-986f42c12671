#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Docker 容器启动脚本
"""

import os
import sys
import time
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from utils.logger import get_logger
from services.config_service import ConfigurationService

logger = get_logger(__name__)

def check_environment():
    """检查 Docker 环境配置"""
    logger.info("🐳 检查 Docker 环境配置...")
    
    required_env_vars = [
        'PROJECT_ID',
        'PROJECT_NUMBER',
        'REGION'
    ]
    
    missing_vars = []
    for var in required_env_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
        else:
            logger.info(f"✅ {var}: {value}")
    
    if missing_vars:
        logger.error(f"❌ 缺少必要的环境变量: {missing_vars}")
        return False
    
    # 检查认证文件
    auth_files = ['client_secret.json', 'service-account.json']
    auth_found = False
    
    for auth_file in auth_files:
        if os.path.exists(auth_file):
            logger.info(f"✅ 找到认证文件: {auth_file}")
            auth_found = True
            break
    
    if not auth_found:
        logger.warning("⚠️  未找到 Google Cloud 认证文件")
        logger.info("将尝试使用环境变量认证...")
    
    # 检查配置文件
    if os.path.exists('config.ini'):
        logger.info("✅ 找到配置文件: config.ini")
    else:
        logger.warning("⚠️  未找到 config.ini 文件，将使用环境变量配置")
    
    return True

def get_task_config():
    """获取任务配置"""
    config = {}
    
    # 优先从环境变量读取
    config['task_type'] = os.getenv('TASK_TYPE', 'batch')
    config['prompt_sheet'] = os.getenv('PROMPT_SHEET_URL')
    config['test_spec_sheet'] = os.getenv('TEST_SPEC_SHEET_URL')
    config['prompt_sheet_name'] = os.getenv('PROMPT_SHEET_NAME')
    config['model'] = os.getenv('AI_MODEL', os.getenv('DEFAULT_MODEL', 'gemini-1.5-pro'))
    
    # 如果环境变量中没有表格配置，尝试从配置文件读取
    if not config['prompt_sheet'] or not config['test_spec_sheet']:
        logger.info("从配置文件读取表格配置...")
        try:
            config_service = ConfigurationService()
            if config_service.config_exists():
                sheet_config = config_service.get_sheet_config_for_batch_task()
                if 'error' not in sheet_config:
                    config.update(sheet_config)
                    logger.info("✅ 从配置文件加载表格配置成功")
                else:
                    logger.error(f"配置文件错误: {sheet_config['error']}")
            else:
                logger.warning("配置文件不存在")
        except Exception as e:
            logger.error(f"读取配置文件失败: {e}")
    
    return config

def run_batch_task():
    """运行批处理任务"""
    logger.info("🚀 启动批处理任务...")
    
    try:
        from services.ai_service import AIService
        from services.batch_processor import BatchProcessor
        
        # 获取任务配置
        config = get_task_config()
        
        # 验证配置
        if not config.get('prompt_sheet') or not config.get('test_spec_sheet'):
            raise ValueError("缺少必要的表格配置 (prompt_sheet 或 test_spec_sheet)")
        
        logger.info(f"任务配置:")
        logger.info(f"  - 任务类型: {config['task_type']}")
        logger.info(f"  - 使用模型: {config['model']}")
        logger.info(f"  - 提示词表格: {config['prompt_sheet']}")
        logger.info(f"  - 测试规格表格: {config['test_spec_sheet']}")
        
        # 初始化服务
        ai_service = AIService()
        batch_processor = BatchProcessor(ai_service)
        
        # 执行批处理
        batch_processor.run(config)
        
        logger.info("✅ 批处理任务完成")
        return True
        
    except Exception as e:
        logger.exception(f"❌ 批处理任务失败: {e}")
        return False

def run_test_mode():
    """运行测试模式"""
    logger.info("🧪 启动测试模式...")
    
    try:
        from services.ai_service import AIService
        
        # 初始化 AI 服务
        ai_service = AIService()
        
        # 测试提示词
        test_prompt = "请生成一个简单的测试用例，包含以下字段：Test Case ID, Test Objective, Test Condition, Test Action, Test Expectation"
        
        logger.info("发送测试提示词...")
        
        # 获取响应
        response = ai_service.generate_response(test_prompt)
        
        logger.info("✅ AI 响应获取成功")
        logger.info(f"响应内容: {response[:200]}...")
        
        return True
        
    except Exception as e:
        logger.exception(f"❌ 测试模式失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🐳 VTAF AI Multiprompt Docker 容器启动")
    logger.info("=" * 60)
    
    # 检查环境
    if not check_environment():
        logger.error("环境检查失败，退出...")
        sys.exit(1)
    
    # 获取运行模式
    run_mode = os.getenv('RUN_MODE', 'batch')
    
    logger.info(f"运行模式: {run_mode}")
    
    success = False
    
    if run_mode == 'test':
        success = run_test_mode()
    elif run_mode == 'batch':
        success = run_batch_task()
    else:
        logger.error(f"未知的运行模式: {run_mode}")
        sys.exit(1)
    
    if success:
        logger.info("🎉 任务执行成功")
        
        # 如果是测试模式，保持容器运行
        if run_mode == 'test':
            logger.info("测试模式完成，保持容器运行...")
            while True:
                time.sleep(60)
                logger.info("容器运行中...")
    else:
        logger.error("💥 任务执行失败")
        sys.exit(1)

if __name__ == '__main__':
    main()
