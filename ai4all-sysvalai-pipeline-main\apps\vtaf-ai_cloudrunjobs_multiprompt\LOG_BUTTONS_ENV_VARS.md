# 日志按钮环境变量使用指南

## 概述

现在支持三个独立的环境变量来控制不同的日志按钮功能，每个环境变量对应一个具体的按钮操作。

## 环境变量

### 1. EXECUTE_SAVE_LOG
- **功能**: 触发 Save Log 按钮功能
- **值**: `true` 或 `false`
- **描述**: 保存对话记录到日志文件

### 2. EXECUTE_LOAD_LOG  
- **功能**: 触发 Load Log 按钮功能
- **值**: `true` 或 `false`
- **描述**: 加载并显示历史对话记录

### 3. EXECUTE_CLEAR_LOGS
- **功能**: 触发 Clear Logs 按钮功能
- **值**: `true` 或 `false`
- **描述**: 清除所有日志文件

## 辅助环境变量

### LOG_DATA
- **用于**: Save Log 功能
- **格式**: JSON字符串
- **描述**: 要保存的日志数据，如果不设置则使用默认示例数据

### LOG_FILENAME
- **用于**: Load Log 功能
- **格式**: 文件名字符串
- **描述**: 要加载的特定日志文件名，如果不设置则加载最新文件

## 使用示例

### 1. Google Cloud Run 环境变量设置

#### 保存日志
```yaml
env:
  - name: EXECUTE_SAVE_LOG
    value: "true"
  - name: SKIP_CONVERSATION_TEST
    value: "true"
  - name: EXECUTE_DEFAULT_BUTTONS
    value: "false"
  - name: LOG_DATA
    value: '{"message": "Cloud Run test", "source": "cloud_run"}'
```

#### 加载日志
```yaml
env:
  - name: EXECUTE_LOAD_LOG
    value: "true"
  - name: SKIP_CONVERSATION_TEST
    value: "true"
  - name: EXECUTE_DEFAULT_BUTTONS
    value: "false"
```

#### 清除日志
```yaml
env:
  - name: EXECUTE_CLEAR_LOGS
    value: "true"
  - name: SKIP_CONVERSATION_TEST
    value: "true"
  - name: EXECUTE_DEFAULT_BUTTONS
    value: "false"
```

### 2. gcloud CLI 命令

#### 保存日志
```bash
gcloud run services update your-service-name \
  --region us-central1 \
  --set-env-vars "EXECUTE_SAVE_LOG=true,SKIP_CONVERSATION_TEST=true,EXECUTE_DEFAULT_BUTTONS=false"
```

#### 加载日志
```bash
gcloud run services update your-service-name \
  --region us-central1 \
  --set-env-vars "EXECUTE_LOAD_LOG=true,SKIP_CONVERSATION_TEST=true,EXECUTE_DEFAULT_BUTTONS=false"
```

#### 清除日志
```bash
gcloud run services update your-service-name \
  --region us-central1 \
  --set-env-vars "EXECUTE_CLEAR_LOGS=true,SKIP_CONVERSATION_TEST=true,EXECUTE_DEFAULT_BUTTONS=false"
```

### 3. 本地测试

#### 保存日志
```bash
export EXECUTE_SAVE_LOG=true
export SKIP_CONVERSATION_TEST=true
export EXECUTE_DEFAULT_BUTTONS=false
export LOG_DATA='{"test": "local save test", "timestamp": "2024-01-01"}'
python src/main.py
```

#### 加载日志
```bash
export EXECUTE_LOAD_LOG=true
export SKIP_CONVERSATION_TEST=true
export EXECUTE_DEFAULT_BUTTONS=false
python src/main.py
```

#### 清除日志
```bash
export EXECUTE_CLEAR_LOGS=true
export SKIP_CONVERSATION_TEST=true
export EXECUTE_DEFAULT_BUTTONS=false
python src/main.py
```

## 执行优先级

如果同时设置多个日志按钮环境变量，执行优先级为：

1. **EXECUTE_SAVE_LOG** (最高优先级)
2. **EXECUTE_LOAD_LOG** 
3. **EXECUTE_CLEAR_LOGS** (最低优先级)

只有第一个为 `true` 的环境变量会被执行，其他会被忽略。

## 测试脚本

### 交互式测试
```bash
python test_individual_buttons.py
```

### 自动化测试
```bash
python run_test.py
```

## 日志文件位置

- **目录**: `logs/`
- **文件格式**: `multiprompt_log_YYYYMMDD_HHMMSS.json`
- **编码**: UTF-8

## 注意事项

1. **安全性**: 清除日志操作不可逆，请谨慎使用
2. **权限**: 确保应用有读写日志目录的权限
3. **存储**: 在 Google Cloud Run 中，日志文件存储在临时文件系统中
4. **并发**: 避免同时执行多个日志操作

## 故障排除

### 常见问题

1. **日志目录不存在**
   - 解决方案: 目录会在第一次保存时自动创建

2. **没有日志文件可加载**
   - 解决方案: 先执行保存操作创建日志文件

3. **权限错误**
   - 解决方案: 检查应用的文件系统权限

4. **JSON格式错误**
   - 解决方案: 验证 LOG_DATA 环境变量的JSON格式

### 调试方法

1. 查看应用日志输出
2. 检查环境变量设置
3. 验证日志目录权限
4. 使用测试脚本验证功能

## 更新历史

- **v1.0**: 初始版本，支持三个独立的日志按钮环境变量
- **v1.1**: 添加优先级控制和错误处理
- **v1.2**: 增加路径信息显示和详细日志
