#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GCP 环境简单对话测试
专门用于在 Google Cloud Run 上验证对话功能
"""

import os
import sys
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    """简单的 GCP 对话测试"""
    print("🌟 GCP 环境简单对话测试")
    print("=" * 50)
    
    try:
        # 1. 检查环境
        print("🌍 环境检查...")
        is_gcp = bool(os.getenv('K_SERVICE') or os.getenv('GOOGLE_CLOUD_PROJECT'))
        print(f"GCP 环境: {'是' if is_gcp else '否'}")
        print(f"项目 ID: {os.getenv('PROJECT_ID', 'Not set')}")
        print(f"区域: {os.getenv('REGION', 'Not set')}")
        
        # 2. 初始化 AI 服务
        print("\n🤖 初始化 AI 服务...")
        from services.ai_service import AIService
        
        ai_service = AIService()
        print("✅ AI 服务初始化成功")
        
        # 3. 测试简单对话
        print("\n💬 测试对话...")
        test_prompt = "请简短回答：你好，这是 GCP 测试"
        
        response = ai_service.generate_response(test_prompt)
        
        if response and not response.startswith("Error"):
            print(f"✅ 对话成功!")
            print(f"📤 提示词: {test_prompt}")
            print(f"📥 响应: {response}")
            print(f"📊 响应长度: {len(response)} 字符")
            
            # 4. 测试 Token 计数
            print("\n🔢 测试 Token 计数...")
            token_count = ai_service.count_tokens(test_prompt)
            print(f"Token 数量: {token_count}")
            
            print("\n🎉 GCP 环境对话功能正常!")
            return True
        else:
            print(f"❌ 对话失败: {response}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
