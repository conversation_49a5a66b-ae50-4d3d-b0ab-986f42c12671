<template>
      <q-header class="bg-white shadow-1">
        <q-toolbar>
          <q-btn flat @click="drawer = !drawer" round dense icon="menu" color="blue-9"/>
          <q-toolbar-title class="text-blue-9">Chat With Knowledgebase</q-toolbar-title>
          <!-- Pushes next content to the right -->
  <q-space />

  <!-- Right-aligned project text -->
  <div class="q-mr-md text-blue-10 text-subtitle1" v-if="selected_project_variant">
    Project for this conversation: {{ selected_project_variant }}
  </div>
        </q-toolbar>
      </q-header>

      <q-drawer
        v-model="drawer"
        show-if-above
        :width="300"
        :breakpoint="500"
        bordered
        :class="$q.dark.isActive ? 'bg-grey-9' : 'bg-grey-1'"
      >
        <q-scroll-area class="fit">
          <q-list class="q-pa-sm">
            <q-inner-loading :showing="isChatLoading">
              <q-spinner-ios color="blue-9" size="40px" />
            </q-inner-loading>
            <div class="static-options">
              <q-item clickable @click="goToNewChat()">
                <q-item-section avatar>
                  <q-icon name="add_circle_outline" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>New Chat</q-item-label>
                </q-item-section>
              </q-item>
              <q-item clickable @click="openSearchDialog">
                <q-item-section avatar>
                  <q-icon name="search" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Search Chats</q-item-label>
                </q-item-section>
              </q-item>
            </div>

            <div v-if="recentChats.length">
              <q-item-label header class="text-bold text-grey-7">Recent Chats</q-item-label>
              <template v-for="chat in recentChats" :key="chat.session_id">
                <q-item clickable @click="goToChat(chat.session_id)" :class="{ 'active-chat-item': chat.session_id === current_session_id }">
                  <q-item-section>
                    <q-item-label>{{ chat.title }}</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <div v-if="deletingChats.has(chat.session_id)">
                      <q-spinner-ios size="20px" color="grey-7" />
                    </div>
                    <div v-else>
                      <q-btn dense flat round icon="more_vert" @click.stop>
                        <q-menu>
                          <q-list>
                            <q-item clickable @click="deleteChat(chat.session_id)">
                              <q-item-section>Delete Chat</q-item-section>
                            </q-item>
                          </q-list>
                        </q-menu>
                      </q-btn>
                    </div>
                  </q-item-section>
                </q-item>
              </template>
              <q-separator />
            </div>

            <div v-if="olderChats.length">
              <q-item-label header class="text-bold text-grey-7">Older Chats</q-item-label>
              <template v-for="chat in olderChats" :key="chat.session_id">
                <q-item clickable @click="goToChat(chat.session_id)" :class="{ 'active-chat-item': chat.session_id === current_session_id }">
                  <q-item-section>
                    <q-item-label>{{ formatDate(chat.last_modified) }}</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-btn dense flat round icon="more_vert">
                      <q-menu>
                        <q-list>
                          <q-item clickable @click="deleteChat(chat.session_id)">
                            <q-item-section>Delete Chat</q-item-section>
                          </q-item>
                        </q-list>
                      </q-menu>
                    </q-btn>
                  </q-item-section>
                </q-item>
              </template>
            </div>

          </q-list>
        </q-scroll-area>
      </q-drawer>
      <q-dialog v-model="showProjectDialog" persistent>
  <q-card style="min-width: 400px">
    <q-card-section class="text-h6">
      Select Project for New Conversation
    </q-card-section>

    <q-card-section>
      <q-select
        filled
        dense
        use-input
        input-debounce="0"
        v-model="dummy_variant_id"
        :options="filteredOptions"
        @filter="filterFn"
        @update:model-value="handleSelect"
        emit-value
        map-options
        clearable
        label="Select Project"
        :loading="projects_dropdown_loading"
      />
    </q-card-section>

    <q-card-actions align="right">
      <q-btn flat label="Cancel" v-close-popup />
      <q-btn
        unelevated
        label="Start Chat"
        color="primary"
        @click="createChatSessionWithProject"
        :disable="!dummy_variant_id"
        :loading="start_chat_button_loading"
      />
    </q-card-actions>
  </q-card>
</q-dialog>
<q-dialog v-model="showSearchDialog" persistent>
  <q-card style="min-width: 400px; max-height: 90vh" class="q-pa-md column">
    <q-card-section class="text-h6">Search Chats by Project</q-card-section>

    <!-- Project Dropdown -->
    <q-select
      filled
      dense
      use-input
      input-debounce="0"
      v-model="search_variant_id"
      :options="filteredOptions"
      @filter="filterFn"
      @update:model-value="handleSearchSelect"
      emit-value
      map-options
      clearable
      label="Select Project"
      :loading="projects_dropdown_loading"
    />

    <!-- Matching Chats -->
    <div v-if="searchResults.length" class="q-mt-md">
      <q-list>
        <q-item
          v-for="chat in searchResults"
          :key="chat.session_id"
          clickable
          @click="goToChatFromSearch(chat.session_id)"
          class="q-mt-sm"
        >
          <q-item-section>
            <q-item-label>{{ chat.title }}</q-item-label>
            <q-item-label caption>{{ formatDate(chat.last_modified) }}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </div>
    <div v-else-if="search_variant_id" class="q-mt-md text-grey">
      No chats found for this project.
    </div>

    <q-card-actions align="right" class="q-mt-md">
      <q-btn flat label="Close" v-close-popup />
    </q-card-actions>
  </q-card>
</q-dialog>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { date } from 'quasar'
import { useChatWithKnowledgebaseStore } from 'src/stores/chat_with_kb/chat_with_knowledgebase_router';
import { storeToRefs } from "pinia";
import { QSpinnerIos } from 'quasar'
import ProjectDropdown from './ProjectDropdown.vue';
import { useRouter } from 'vue-router'

const router = useRouter()
const start_chat_button_loading = ref(false);

const chat_with_kbStore = useChatWithKnowledgebaseStore();
const { recentChats, olderChats, current_session_details, recent_chats, delete_session_id, current_session_id, isChatLoading, isConversationLoading,
  projects_variants_dropdown,
  selected_project_variant,
  selected_project_variant_uuid,
  projects_dropdown_loading,
 } = storeToRefs(chat_with_kbStore);

const { get_user_email, get_chats, delete_chat, create_session, get_projects } = chat_with_kbStore;

// State
const drawer = ref(false)
const showProjectDialog = ref(false);

const dummy_varinat = ref("");
const dummy_variant_id = ref("");

// Format ISO to readable date
const formatDate = (iso) => date.formatDate(iso, 'MMM D, YYYY - hh:mm A')

const deletingChats = ref(new Set())

// Delete a chat
const deleteChat = async (session_id) => {
  deletingChats.value.add(session_id)

  try {
    current_session_details.value = [];
    delete_session_id.value = session_id
    await delete_chat()
    selected_project_variant.value = "";
    selected_project_variant_uuid.value = "";
    await loadChats()
  } catch (err) {
    console.error('Error deleting chat:', err)
  } finally {
    deletingChats.value.delete(session_id)
    router.push(`/chat_with_knowledgebase`)
  }
}

// Load and group chats
const loadChats = async () => {
  isChatLoading.value=true;
  await get_chats();

  recentChats.value = []
  olderChats.value = []

  const now = new Date()
  for (const chat of recent_chats.value.sessions) {
    const lastMod = new Date(chat.last_modified)
    const diff = (now - lastMod) / (1000 * 60 * 60 * 24)
    if (diff < 7) {
      recentChats.value.push(chat)
    } else {
      olderChats.value.push(chat)
    }
  }
  isChatLoading.value=false;
}

const goToChat = (sessionId) => {
  current_session_id.value = sessionId;
  console.log('Navigating to session:', current_session_id.value)
  router.push(`/chat_with_knowledgebase/${current_session_id.value}`);
}
const createChatSessionWithProject = async () => {
  if (!dummy_variant_id.value) return;
  selected_project_variant.value = dummy_varinat.value;
  selected_project_variant_uuid.value = dummy_variant_id.value;
  start_chat_button_loading.value = true;
  current_session_details.value = [];
  await create_session(selected_project_variant_uuid.value);
  start_chat_button_loading.value=false;
  showProjectDialog.value = false;
  await loadChats();
  router.push(`/chat_with_knowledgebase/${current_session_id.value}`);
};

// const goToNewChat = async () => {
//   isConversationLoading.value = true;
//   current_session_details.value = [];
//   await create_session();
//   console.log('New Session Initiated')
//   isConversationLoading.value = false;
//   await loadChats();
//   // router.push('/chat_with_knowledgebase_trial')
//   router.push(`/chat_with_knowledgebase_trial/${current_session_id.value}`);
// }

const goToNewChat = async () => {
  dummy_varinat.value = "";
  dummy_variant_id.value = "";
  showProjectDialog.value = true;
};

const goToChatFromSearch = (sessionId) => {
  showSearchDialog.value = false;
  goToChat(sessionId);
};

onMounted (async () => {
  await get_user_email();
  projects_dropdown_loading.value = true;
  await get_projects();
  projects_dropdown_loading.value = false;
  await loadChats();
})

// Turn { key: uuid } into dropdown options
const projectsOptions = computed(() =>
  Object.entries(projects_variants_dropdown.value).map(([label, value]) => ({
    label,
    value,
  }))
);

// Filtering support
const filterQuery = ref("");
const filteredOptions = computed(() =>
  projectsOptions.value.filter((option) =>
    option.label.toLowerCase().includes(filterQuery.value.toLowerCase())
  )
);
const filterFn = (val, update) => {
  filterQuery.value = val;
  update();
};

// Save both UUID and key when selected
const handleSelect = (uuid) => {
  dummy_variant_id.value = uuid;
  const found = Object.entries(projects_variants_dropdown.value).find(
    ([key, val]) => val === uuid
  );
  dummy_varinat.value = found ? found[0] : null;
};

const showSearchDialog = ref(false);
const search_variant_id = ref("");
const searchResults = ref([]);

const openSearchDialog = () => {
  search_variant_id.value = "";
  searchResults.value = [];
  showSearchDialog.value = true;
};

const handleSearchSelect = (uuid) => {
  search_variant_id.value = uuid;

  // Find selected key
  const selectedKey = Object.entries(projects_variants_dropdown.value).find(
    ([key, val]) => val === uuid
  )?.[0];

  // Filter chats that belong to selected project
  if (selectedKey) {
    const allChats = [...recentChats.value, ...olderChats.value];
    searchResults.value = allChats.filter(chat =>
      chat.title.toLowerCase().includes(selectedKey.toLowerCase())
    );
  } else {
    searchResults.value = [];
  }
};
</script>

<style scoped>
.active-chat-item {
  background-color: #e0f2ff; /* light blue background */
  border-left: 4px solid #1976d2; /* Quasar blue-9 */
}
</style>
