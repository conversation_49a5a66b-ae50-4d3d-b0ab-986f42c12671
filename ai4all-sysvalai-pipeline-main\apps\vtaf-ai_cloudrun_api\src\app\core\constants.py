"""
config.py

Loads environment variables and defines application-wide constants such as
Google API scopes, service account details, and external service URLs.
"""

import os
import dotenv

# Load environment variables from .env file
dotenv.load_dotenv()

# Environment-based service account credentials
AI4ALL_SERVICE_ACCOUNT_EMAIL = os.getenv("AI4ALL_SERVICE_ACCOUNT_EMAIL")
AI4ALL_FRAMEWORK_CLIENT_ID = os.getenv("AI4ALL_FRAMEWORK_CLIENT_ID")
VTAF_SERVICE_ACCOUNT_EMAIL = os.getenv("VTAF_SERVICE_ACCOUNT_EMAIL")

QDRANT_IAP_CLIENT_ID = os.getenv("QDRANT_IAP_CLIENT_ID")
QDRANT_CLIENT_URL = os.getenv("QDRANT_CLIENT_URL")
QDRANT_CLIENT_PORT = os.getenv("QDRANT_PORT")
QDRANT_JWT = os.getenv("QDRANT_JWT")
QDRANT_COLLECTION_NAME = os.getenv("QDRANT_COLLECTION_NAME")

PROJECT_ID = os.getenv("PROJECT_ID")
PROJECT_NUMBER = os.getenv("PROJECT_NUMBER")
REGION = os.getenv("REGION", "europe-west1")
VERTEX_AI_MODEL = os.getenv("VERTEX_AI_MODEL", "gemini-2.0-flash")
VERTEX_EMBEDDING_MODEL = os.getenv("VERTEX_EMBEDDING_MODEL", "textembedding-gecko@001")
EMBEDDING_PARALLELISM = os.getenv("EMBEDDING_PARALLELISM", 1)

LANGFUSE_SECRET_KEY = os.getenv("LANGFUSE_SECRET_KEY")
LANGFUSE_PUBLIC_KEY = os.getenv("LANGFUSE_PUBLIC_KEY")
LANGFUSE_HOST = os.getenv("LANGFUSE_HOST", "https://cp2532.apps-dev.valeo.com/utils/langfuse/v3")

# External service URLs
PARSINGS_URL = "https://cp2532.apps-dev.valeo.com/data/prod/parsings"
CHUNKING_URL = "https://cp2532.apps-dev.valeo.com/data/prod/chunks"
GENERATIONS_URL = "https://cp2532.apps-dev.valeo.com/engine/prod/generations"

GOOGLE_CLOUD_SCOPES = [
    "https://www.googleapis.com/auth/cloud-platform"
]
