"""
embedding_factory.py

Provides a factory function to create a VertexAIEmbeddings instance for use
with LangChain, leveraging Google Cloud Vertex AI.

The embedding model is configured using project-specific constants such as
model name, region, and request parallelism.
"""

from langchain_google_vertexai import VertexAIEmbeddings

from app.core.constants import (
    PROJECT_ID,
    REGION,
    VERTEX_EMBEDDING_MODEL,
    EMBEDDING_PARALLELISM,
)


def create_vertex_embedding(credentials) -> VertexAIEmbeddings:
    """
    Creates and returns a VertexAIEmbeddings instance using the configured model and region.

    Args:
        credentials: Google Cloud credentials used to authenticate requests to Vertex AI.

    Returns:
        VertexAIEmbeddings: A configured embedding model instance compatible with Lang<PERSON>hain.
    """
    return VertexAIEmbeddings(
        model_name=VERTEX_EMBEDDING_MODEL,
        location=REGION,
        project=PROJECT_ID,
        request_parallelism=int(EMBEDDING_PARALLELISM),
        credentials=credentials,
    )
