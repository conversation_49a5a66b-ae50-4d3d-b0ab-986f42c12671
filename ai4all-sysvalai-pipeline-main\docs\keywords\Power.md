# Group {Power}
## Check_PowerSupply

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Check_PowerSupply.seq*


**Descriptions:** *This keyword is used the check the voltage and current of the given powersupply*


***Command Set:***

	Volt
		-Volt=Alias;Channel;Condition
	Amp
		-Amp=Alias;Channel;Condition


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Alias| Ring| AllowRTval=False,PS01,PS02,PS03,PS04| PS01|  
| Channel| Ring| AllowRTval=False,1,2| 1| specify the channel in this location
| Condition| String| NA| X>=10| Provide the condition for the reading comparision
X==10
X>=10
>=5 X <=10 

## Check_PowerSupplyTimeout

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Check_PowerSupplyTimeout.seq*


**Descriptions:** *This Keyword is used to poll for a specific current untill a timeout has occured. *


***Command Set:***

	Milliamp
		-Milliamp=Alias;Channel;Condition;Timout_sec


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Alias| Ring| AllowRTval=False,PS01,PS02,PS03,PS04| PS01| Specify the Alias of the powersupply, value obtained for NI Max
| Channel| Ring| AllowRTval=False,1,2| 1| The powersupply channel to set
| Condition| String| NA| X>=10| Provide the condition for the reading comparision
X==10
X>=10
>=5 X <=10 
| Timout_sec| Float| LE (<=)600.000000| 5| Set the timeout value in seconds

## Set_PowerSupply

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Set_PowerSupply.seq*


**Descriptions:** *This Keyword is used to control the voltage of power supply PL303QMD-P.*


***Command Set:***

	V-I
		-V-I=Alias;Volt;OVP;Amp;OCP;Channel;Output
	OnOFF
		-OnOFF=Alias;Channel;Output


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Alias| Ring| AllowRTval=False,PS01,PS02,PS03,PS04| PS01|  Specify the Alias of the powersupply, value obtained for NI Max
| Volt| Float| GELE (>=<=)0.000000,20.000000| 5| Voltage value to set
| OVP| Float| GELE (>=<=)0.000000,20.000000| 5|  Over Voltage Protection
| Amp| Float| GELE (>=<=)0.000000,5.000000| 0|  Current to set
| OCP| Float| GELE (>=<=)0.000000,10.000000| 0|  Over Current Protection
| Channel| Ring| AllowRTval=False,1,2| 1|  The powersupply channel to set
| Output| Ring| AllowRTval=False,ON,OFF| ON|  Set the channel output to ON \ OFF

