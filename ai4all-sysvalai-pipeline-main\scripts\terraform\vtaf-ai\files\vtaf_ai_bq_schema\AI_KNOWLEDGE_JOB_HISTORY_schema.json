[{"mode": "NULLABLE", "name": "user_email", "type": "STRING", "description": "user_email"}, {"mode": "NULLABLE", "name": "timestamp", "type": "TIMESTAMP", "description": "timestamp"}, {"mode": "NULLABLE", "name": "job_start_time", "type": "TIMESTAMP", "description": "job_start_time"}, {"mode": "NULLABLE", "name": "job_end_time", "type": "TIMESTAMP", "description": "job_end_time"}, {"mode": "NULLABLE", "name": "job_duration", "type": "STRING", "description": "job_duration"}, {"mode": "NULLABLE", "name": "bg", "type": "STRING", "description": "bg"}, {"mode": "NULLABLE", "name": "pg", "type": "STRING", "description": "pg"}, {"mode": "NULLABLE", "name": "pl", "type": "STRING", "description": "pl"}, {"mode": "NULLABLE", "name": "project", "type": "STRING", "description": "project"}, {"mode": "NULLABLE", "name": "project_uuid", "type": "STRING", "description": "project_uuid"}, {"mode": "NULLABLE", "name": "variant", "type": "STRING", "description": "variant"}, {"mode": "NULLABLE", "name": "variant_uuid", "type": "STRING", "description": "variant_uuid"}, {"mode": "NULLABLE", "name": "euf_feature", "type": "STRING", "description": "euf_feature"}, {"mode": "NULLABLE", "name": "euf_feature_uuid", "type": "STRING", "description": "euf_feature_uuid"}, {"mode": "NULLABLE", "name": "project_profile_uuid", "type": "STRING", "description": "project_profile_uuid"}, {"mode": "NULLABLE", "name": "status", "type": "STRING", "description": "status"}, {"mode": "NULLABLE", "name": "execution_status", "type": "STRING", "description": "execution_status"}, {"mode": "NULLABLE", "name": "user_upload", "type": "STRING", "description": "user_upload"}, {"mode": "NULLABLE", "name": "total_token", "type": "STRING", "description": "total_token"}, {"mode": "NULLABLE", "name": "prompt_token", "type": "STRING", "description": "prompt_token"}, {"mode": "NULLABLE", "name": "cached_prompt_token", "type": "STRING", "description": "cached_prompt_token"}, {"mode": "NULLABLE", "name": "completion_token", "type": "STRING", "description": "completion_token"}, {"mode": "NULLABLE", "name": "successfull_agent_request", "type": "STRING", "description": "successfull_agent_request"}, {"mode": "NULLABLE", "name": "execution_id", "type": "STRING", "description": "execution_id"}, {"mode": "NULLABLE", "name": "llm_model", "type": "STRING", "description": "llm_model"}, {"mode": "NULLABLE", "name": "llm_location", "type": "STRING", "description": "llm_location"}, {"mode": "NULLABLE", "name": "type", "type": "STRING", "description": "type"}, {"mode": "NULLABLE", "name": "result_file", "type": "STRING", "description": "result_file"}]