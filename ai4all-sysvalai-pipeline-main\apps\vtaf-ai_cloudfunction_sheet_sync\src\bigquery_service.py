from google.api_core.exceptions import BadRequest, NotFound, GoogleAPICallError, Conflict

from google.cloud import bigquery
from google.oauth2 import service_account
import time
import random


class BigqueryHandler:
    def __init__(self, project_id: str, logger, max_retries: int = 5):
        self.bq_client = bigquery.Client(project=project_id)
        self.logger = logger
        self.max_retries = max_retries

    def _run_query_with_retries(self, query, is_select=False):
        for attempt in range(self.max_retries):
            try:
                query_job = self.bq_client.query(query)
                result = query_job.result()
                if is_select:
                    return result.to_dataframe(), True
                else:
                    return True
            except Conflict as e:
                if "Could not serialize access" in str(e):
                    wait_time = 2 ** attempt + random.uniform(0.1, 0.5)
                    self.logger.warning(
                        f"Serialization conflict (Conflict). Retrying in {wait_time:.2f} seconds (attempt {attempt + 1}/{self.max_retries})...")
                    time.sleep(wait_time)
                    continue
                self.logger.error("Conflict error occurred:")
                self.logger.error(str(e))
                break
            except BadRequest as e:
                should_retry = any("Could not serialize access" in err.get("message", "") for err in e.errors)
                if should_retry:
                    wait_time = 2 ** attempt + random.uniform(0.1, 0.5)
                    self.logger.warning(
                        f"Serialization conflict (BadRequest). Retrying in {wait_time:.2f} seconds (attempt {attempt + 1}/{self.max_retries})...")
                    time.sleep(wait_time)
                    continue
                self.logger.error("Query failed due to invalid SQL syntax or other client error.")
                for error in e.errors:
                    self.logger.error(f"Error: {error['message']}")
                break
            except NotFound as e:
                self.logger.error("One or more resources were not found. Please check your project, dataset, or table "
                                  "name.")
                self.logger.error(f"Error: {str(e)}")
                break
            except GoogleAPICallError as e:
                self.logger.error("Query failed due to a network or server error.")
                self.logger.error(f"Error: {str(e)}")
                break
            except Exception as e:
                self.logger.error("An unexpected error occurred.")
                self.logger.error(f"Error: {str(e)}")
                break

        self.logger.error("Max retries reached or non-retryable error occurred.")
        return (None, False) if is_select else False

    def execute_insert_query(self, query):
        return self._run_query_with_retries(query, is_select=False)

    def execute_get_query(self, query):
        return self._run_query_with_retries(query, is_select=True)
