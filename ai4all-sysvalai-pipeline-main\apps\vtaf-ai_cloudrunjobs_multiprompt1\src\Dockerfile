FROM artf.alm.vnet.valeo.com/group-ai4all-docker-local/python:3.10

WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

# 设置默认环境变量 - 支持多种任务类型
ENV JOB_TYPE=batch_processing
ENV RUN_MODE=production

# Expose the port the app runs on (typically 5000 for Flask)
EXPOSE 5000
# Command to run the application - 使用重构后的主入口
CMD ["python", "main.py"]
