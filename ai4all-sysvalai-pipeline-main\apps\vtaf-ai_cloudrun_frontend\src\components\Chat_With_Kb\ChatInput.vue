<template>
  <div class="q-pa-lg bg-white row items-center" v-if="current_session_id">
    <q-input
      type="textarea"
      autogrow
      rounded
      outlined
      dense
      class="col-grow q-mr-sm input-area"
      bg-color="white"
      v-model="prompt_input"
      placeholder="Type a message"
      @keydown="handleKeydown"
    />
    <q-btn round flat icon="send" @click="sendMessage()" :disable="!selected_project_variant_uuid|| !prompt_input" />
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { useChatWithKnowledgebaseStore } from 'src/stores/chat_with_kb/chat_with_knowledgebase_router';
import { storeToRefs } from "pinia";
import { Dialog } from 'quasar';

const chat_with_kbStore = useChatWithKnowledgebaseStore();
const { selected_project_variant_uuid, prompt_input, prompt_response, current_session_details, current_session_id } = storeToRefs(chat_with_kbStore);

const { get_chat_response } = chat_with_kbStore;

function handleKeydown(e) {
  if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault();

    if (!selected_project_variant_uuid.value) {
      Dialog.create({
        title: 'Missing Project',
        message: 'Please select a project before sending a message.',
        ok: 'OK'
      });
      return;
    }

    if (!prompt_input.value) {
      Dialog.create({
        title: 'Empty Message',
        message: 'Please enter a message before sending.',
        ok: 'OK'
      });
      return;
    }

    sendMessage();
  }
}


function sendMessage() {
  // Your actual send logic here
  console.log('Send:', prompt_input.value);
  const userMessage = {
    role: 'user',
    parts: [{ type: 'text', data: prompt_input.value }]
  };
  const loadingModelMessage = {
    role: 'model',
    loading: true,
    parts: [{ type: 'text', data: '' }]
  };
  current_session_details.value.push(userMessage);
  current_session_details.value.push(loadingModelMessage);

  const currentIndex = chat_with_kbStore.current_session_details.length - 1;

  const query = prompt_input.value;
  prompt_input.value = '';
  prompt_response.value = '';
  get_chat_response(query).then(() => {
    current_session_details.value[currentIndex] = {
      role: 'model',
      parts: [{ type: 'text', data: prompt_response.value }]
    };
  });
}

</script>

<style scoped>
.input-area {
  max-height: 250px; /* Limit height */
  overflow-y: auto;  /* Allow scroll */
  resize: none;      /* Optional: prevent manual resize */
}
</style>
