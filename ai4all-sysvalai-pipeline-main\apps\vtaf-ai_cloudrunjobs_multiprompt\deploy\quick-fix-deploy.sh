#!/bin/bash
# 快速修复部署脚本

set -e

PROJECT_ID="valeo-cp2673-dev"
REGION="us-central1"
SERVICE_NAME="vtaf-ai-cloudrunjobs-multiprompt"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

echo "🔧 快速修复部署 VTAF AI Multiprompt"
echo "项目: ${PROJECT_ID}"
echo "区域: ${REGION}"

# 设置项目
gcloud config set project ${PROJECT_ID}

# 构建新的镜像
echo "🔨 构建修复后的镜像..."
cd ..
gcloud builds submit --tag ${IMAGE_NAME}:fixed .

# 更新 Cloud Run Job
echo "🚀 更新 Cloud Run Job..."
gcloud run jobs replace deploy/cloud-run-job.yaml --region=${REGION}

echo "✅ 修复部署完成!"
echo ""
echo "🧪 测试部署:"
echo "gcloud run jobs execute ${SERVICE_NAME} --region=${REGION}"
