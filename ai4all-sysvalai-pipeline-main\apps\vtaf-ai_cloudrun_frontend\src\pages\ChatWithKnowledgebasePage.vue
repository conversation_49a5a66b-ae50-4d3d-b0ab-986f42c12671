<template>
  <div>
    <q-layout view="hHh Lpr lff" container style="height: 90vh;">
      <chat-sidebar />
      <!-- Right main area -->
      <q-page-container>
      <create-new-chat></create-new-chat>
      <q-page class="q-pa-none column full-height">
        <!-- Chat Content -->
        <div class="col q-pa-md">
          <router-view />
        </div>
        <!-- Chat Input always at bottom -->
        <chat-input />
      </q-page>
    </q-page-container>
    <!-- <chat-input /> -->
     <error-dialog></error-dialog>
  </q-layout>
  </div>
</template>

<script setup>
import ChatSidebar from 'src/components/Chat_With_Kb/ChatSidebar.vue';
import CreateNewChat from 'src/components/Chat_With_Kb/CreateNewChat.vue';
import ChatInput from 'src/components/Chat_With_Kb/ChatInput.vue';
import ErrorDialog from 'src/components/Chat_With_Kb/ErrorDialog.vue';

import { onMounted, watch } from 'vue';
import { useChatWithKnowledgebaseStore } from 'src/stores/chat_with_kb/chat_with_knowledgebase_router';
import { useHomeStore } from "src/stores/home/<USER>";
import { storeToRefs } from "pinia";
import { useRoute } from 'vue-router';

const route = useRoute();

const home_store_config = useHomeStore();
const { main_email, first_letter_email } = storeToRefs(home_store_config);

const chat_with_kbStore = useChatWithKnowledgebaseStore();
const { user_email, current_session_details, delete_session_id, current_session_id } = storeToRefs(chat_with_kbStore);

const { jwt_token, get_user_email } = chat_with_kbStore;

onMounted(async () => {
  await get_user_email();
  await resetSessionData();
  main_email.value = user_email.value;
  first_letter_email.value = main_email.value.charAt(0).toUpperCase();
})

// Watch for route change back to parent
watch(() => route.fullPath, async (newPath) => {
  if (newPath === '/chat_with_knowledgebase') {
    await resetSessionData();
  }
});

async function resetSessionData() {
  current_session_details.value = [];
  delete_session_id.value = "";
  await jwt_token();
}
</script>

