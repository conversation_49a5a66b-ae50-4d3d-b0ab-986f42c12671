import json
import os

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

from configs.env import settings


class DriveUploader:
    SHARED_FOLDER_ID = settings.SHARED_FOLDER_ID

    def __init__(self, gcp_secret_manager):
        self.gcp_secret_manager = gcp_secret_manager
        self.drive_service, self.sheets_service = self._create_service()
        self.credentials = None

    def _create_service(self):
        try:
            vtaf_ai_sheet_oauth_file = "vtaf_ai_sheet_oauth.json"
            vtaf_ai_sheet_token_file = "vtaf_ai_sheet_token.json"
            vtaf_ai_sheet_oauth = json.loads(self.gcp_secret_manager.read_secret("vtaf-ai-sheet-oauth"))
            with open(vtaf_ai_sheet_oauth_file, "w") as temp_file:
                json.dump(vtaf_ai_sheet_oauth, temp_file)

            vtaf_ai_sheet_token = json.loads(self.gcp_secret_manager.read_secret("vtaf-ai-sheet-token"))
            with open(vtaf_ai_sheet_token_file, "w") as temp_file:
                json.dump(vtaf_ai_sheet_token, temp_file)
            if os.path.exists(vtaf_ai_sheet_token_file):
                self.credentials = Credentials.from_authorized_user_file(vtaf_ai_sheet_token_file,
                                                                         scopes=[
                                                                             'https://www.googleapis.com/auth/drive',
                                                                             'https://www.googleapis.com/auth/spreadsheets'
                                                                         ])
            # If there are no (valid) credentials available, let the user log in.
            if not self.credentials or not self.credentials.valid:
                if self.credentials and self.credentials.expired and self.credentials.refresh_token:
                    self.credentials.refresh(Request())
                else:
                    flow = InstalledAppFlow.from_client_secrets_file(
                        vtaf_ai_sheet_oauth_file, [
                            'https://www.googleapis.com/auth/drive',
                            'https://www.googleapis.com/auth/spreadsheets'
                        ]
                    )
                    self.credentials = flow.run_local_server(port=0)
                # Save the credentials for the next run
                with open(vtaf_ai_sheet_token_file, "w") as token:
                    token.write(self.credentials.to_json())
            return (build('drive', 'v3', credentials=self.credentials),
                    build('sheets', 'v4', credentials=self.credentials))
        except Exception as e:
            raise RuntimeError("❌ Failed to auth the Google Drive.")

    @staticmethod
    def flatten_json_list_to_sheet_data(json_list):
        if not json_list:
            return []

        headers = list(json_list[0].keys())

        def clean(val):
            if isinstance(val, str):
                return val.strip()  # keep \n for multiline
            return str(val)

        data = [headers]
        for obj in json_list:
            row = [clean(obj.get(key, "")) for key in headers]
            data.append(row)

        return data

    @staticmethod
    def sheet_data_to_dict_list(sheet_data):
        if not sheet_data or len(sheet_data) < 2:
            return []

        headers = sheet_data[0]
        rows = sheet_data[1:]

        dict_list = []
        for row in rows:
            row_dict = {headers[i]: row[i] if i < len(row) else "" for i in range(len(headers))}
            dict_list.append(row_dict)

        return dict_list

    def upload_json_directly_to_sheet(self, json_data: list[dict]):
        try:

            file_metadata = {
                'name': f"{settings.CLOUD_RUN_EXECUTION}_TestScript",
                'mimeType': 'application/vnd.google-apps.spreadsheet',
                'parents': [self.SHARED_FOLDER_ID]
            }

            file = self.drive_service.files().create(
                body=file_metadata,
                fields='id',
                supportsAllDrives=True
            ).execute()
            spreadsheet_id = file.get('id')

            # Step 2: Use Sheets API to populate the sheet
            values = self.flatten_json_list_to_sheet_data(json_data)

            self.sheets_service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range="Sheet1!A1",
                valueInputOption="RAW",
                body={"values": values}
            ).execute()
            print(f"Successfully to write JSON directly to Google Sheet: {spreadsheet_id}")

            return spreadsheet_id
        except Exception as e:
            print(f"❌ Failed to write JSON directly to Google Sheet: {str(e)}")
            raise RuntimeError(f"❌ Failed to write JSON directly to Google Sheet: {str(e)}")

    def read_full_sheet(self):
        try:
            spreadsheet_id = settings.FILE_NAME
            # Step 1: Get metadata to find the first sheet name
            metadata = self.sheets_service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
            first_sheet_name = metadata['sheets'][0]['properties']['title']

            # Step 2: Read data from the first sheet
            result = self.sheets_service.spreadsheets().values().get(
                spreadsheetId=spreadsheet_id,
                range=first_sheet_name,
                majorDimension='ROWS'
            ).execute()

            values = result.get("values", [])
            if not values:
                print("⚠️ Sheet is empty.")
                return []

            print(f"✅ Read data from sheet: '{first_sheet_name}'")

            test_script = self.sheet_data_to_dict_list(values)
            return test_script

        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return []

    @staticmethod
    def get_drive_file_url(file_id: str) -> str:
        return f"https://docs.google.com/spreadsheets/d/{file_id}"
