#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Single Task Service - 单个任务处理器
对应原始ai_generate.py中的do_single_task_send()方法
处理单个批处理任务
"""

import os
from typing import Dict, Any, Optional
from utils.logger import get_logger
from services.ai_service import AIService
from services.batch_processor import BatchProcessor
from services.config_service import ConfigurationService

logger = get_logger(__name__)

class SingleTaskService:
    """单个任务服务 - 处理单个批处理任务"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.config_service = ConfigurationService()
        
    def execute(self) -> int:
        """执行单个任务"""
        try:
            logger.info("🚀 Starting single task job")
            
            # 获取任务配置
            config = self._get_task_config()
            
            # 验证配置
            self._validate_config(config)
            
            # 设置为单个任务模式
            config['task_type'] = 'single'
            
            # 初始化批处理器（单个任务模式）
            batch_processor = BatchProcessor(self.ai_service)
            
            # 执行单个任务
            logger.info("Processing single task...")
            batch_processor.run(config)
            
            logger.info("✅ Single task job completed successfully")
            return 0
            
        except Exception as e:
            logger.exception(f"Single task job failed: {e}")
            return 1
    
    def _get_task_config(self) -> Dict[str, Any]:
        """获取任务配置"""
        config = {}
        
        # 从环境变量获取配置
        config['prompt_sheet'] = os.getenv('PROMPT_SHEET_URL')
        config['test_spec_sheet'] = os.getenv('TEST_SPEC_SHEET_URL')
        config['prompt_sheet_name'] = os.getenv('PROMPT_SHEET_NAME', 'Prompt_Single')
        config['model'] = os.getenv('AI_MODEL', 'gemini-1.5-flash')
        
        # 如果环境变量中没有配置，尝试从配置文件读取
        if not config['prompt_sheet'] or not config['test_spec_sheet']:
            try:
                if self.config_service.config_exists():
                    sheet_config = self.config_service.get_sheet_config_for_batch_task()
                    if 'error' not in sheet_config:
                        config.update(sheet_config)
                        logger.info("Configuration loaded from config.ini")
                    else:
                        logger.error(f"Config file error: {sheet_config['error']}")
            except Exception as e:
                logger.error(f"Failed to read config file: {e}")
        
        return config
    
    def _validate_config(self, config: Dict[str, Any]):
        """验证配置"""
        required_fields = ['prompt_sheet', 'test_spec_sheet']
        missing_fields = []
        
        for field in required_fields:
            if not config.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            raise ValueError(f"Missing required configuration for single task: {missing_fields}")
        
        logger.info("Single task configuration validation passed")
