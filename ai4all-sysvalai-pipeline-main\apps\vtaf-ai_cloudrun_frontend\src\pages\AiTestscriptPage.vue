<template>
  <div>
    <file-link-input></file-link-input>
  </div>
</template>

<script setup>
import FileLinkInput from 'src/components/AI_Testscript/FileLinkInput.vue';

import { useRunTsGenerationStore } from 'src/stores/ai_testscript/run-ts-generation-store';
import { useHomeStore } from "src/stores/home/<USER>";
import { onMounted } from "vue";
import { storeToRefs } from 'pinia';

const home_store_config = useHomeStore();
const { main_email, first_letter_email } = storeToRefs(home_store_config);

const ai_testscript_config = useRunTsGenerationStore();
const { user_email } = storeToRefs(ai_testscript_config);

const { get_user_email } = ai_testscript_config;

onMounted(async () => {
  await get_user_email();
  main_email.value = user_email.value;
  first_letter_email.value = main_email.value.charAt(0).toUpperCase();
})
</script>
