# Group {Flow_Controls}
## Break

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *Break.step*


**Descriptions:**


***Command Set:***

	Statement
		-Statement=


***Input Description***



## Else

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *Else.step*


**Descriptions:** **


***Command Set:***

	Statement
		-Statement=Variable;Condition;Value


***Input Description***



## End

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *End.step*


**Descriptions:** **


***Command Set:***

	Statement
		-Statement=


***Input Description***



## Goto

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *Goto.step*


**Descriptions:** **


***Command Set:***

	Statement
		-Statement=Test_Step


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Test_Step| Ring| AllowRTval=True,PC_1,Tp_1,eTp_1,RST_1,<Cleanup>| |  

## If

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *If.step*


**Descriptions:** **


***Command Set:***

	Statement
		-Statement=Test_Step;Test_Step_Condition


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Test_Step| Ring| AllowRTval=True,PC_1,TP_1,eTP_1,RST_1| |  
| Test_Step_Condition| Ring| AllowRTval=False,=="Passed",=="Failed"| |  

## IterationLog

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\IterationLog.seq*


**Descriptions:** *To be used inside Loop. Logs the Curent Iteration.*


***Command Set:***

	Counter
		-Counter=


***Input Description***



## Loop

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *Loop.step*


**Descriptions:** **


***Command Set:***

	Count
		-Count=Count
	Break
		-Break=Count;Test_Step;Break_Condition
	Infinite
		-Infinite=


***Input Description***



