import httpx
import asyncio
import logging

from app.dependencies.ai4all_auth import get_id_token

logger = logging.getLogger("vtaf")

async def fetch_with_retry(url, max_retries=5, base_delay=1, client=None):
    for attempt in range(1, max_retries + 1):
        try:
            id_token = await get_id_token(force=True)
            headers = {"Authorization": f"Bearer {id_token}"}
            response = await client.get(url, headers=headers, timeout=10)
            response.raise_for_status()  # Raise for HTTP errors (4xx, 5xx)
            return response
        except (httpx.RequestError, httpx.HTTPStatusError) as exc:
            logger.error(f"Attempt {attempt} failed: {exc}")
            if attempt == max_retries:
                raise  # No more retries, propagate exception
            delay = base_delay * 2 ** (attempt - 1)  # Exponential backoff: 1, 2, 4 seconds
            logger.info(f"Retrying health check in {delay} seconds...")
            await asyncio.sleep(delay)
