import datetime
import re
import traceback
from fastapi import APIRouter, HTTPException, Form
from utilities import get_bigquery_client, trigger_run_job, bigquery_insert_query
from configs.env import settings

router = APIRouter()
client = get_bigquery_client()

services_project_id = settings.SERVICES_PROJECT_ID
services_dataset_id = settings.SERVICES_DATASET_ID
ai_testscript_job = settings.TESTSCRIPT_GENERATION_JOB

def extract_sheet_id(url):
    match = re.search(r"/d/([a-zA-Z0-9-_]+)", url)
    return match.group(1) if match else None

@router.post("/generate_ts")
async def trigger_testscript_generation_job(user_email: str = Form(...), sheet_link: str = Form(...)):
    try:
        sheet_id = extract_sheet_id(url=sheet_link)
        env_vars = {
            "FILE_NAME": sheet_id
        }

        execution_id = trigger_run_job(job_name=ai_testscript_job, environment_variables=env_vars)
        current_timestamp = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ")

        execute_query = f"""
            INSERT INTO `{services_project_id}.{services_dataset_id}.TS_JOB_HISTORY` (
                user_email,
                timestamp,
                execution_id,
                execution_status,
                user_upload
            ) 
            VALUES (
                '{user_email}',
                '{current_timestamp}',
                '{execution_id}',
                'started',
                '{sheet_link}'             
            );
            """
        bigquery_insert_query(execute_query)
        # Response with file name and selected rows
        return {"message": "success", "execution_id": execution_id}
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)
    
@router.get("/get_current_jobs/{user_email}")
def get_current_jobs_history(user_email: str):
    try:
        fetch_query = f"SELECT timestamp, execution_id, execution_status FROM `{services_project_id}.{services_dataset_id}.TS_JOB_HISTORY` WHERE user_email = '{user_email}' ORDER BY Timestamp DESC"
        query = client.query(fetch_query)
        
        results = []
        for row in query.result():
            row_dict = dict(row)
            # Convert timestamp to a readable format
            if "timestamp" in row_dict and isinstance(row_dict["timestamp"], datetime.datetime):
                row_dict["timestamp"] = row_dict["timestamp"].strftime("%d %b %Y %I:%M:%S %p")  # Example: 23 Feb 2025 08:03:42 PM
            results.append(row_dict)
            # results.append(dict(row))  
        return results  # Return all rows
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)

