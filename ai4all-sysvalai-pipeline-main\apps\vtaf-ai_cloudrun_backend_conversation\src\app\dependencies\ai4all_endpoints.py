import traceback
from fastapi import HTTPException
from configs.env import settings

services_project_id = settings.SERVICES_PROJECT_ID

async def create_session_ai4all(id_token, ai4allclient):
    headers = {"Authorization": f"Bearer {id_token}"}
    try:
        response = await ai4allclient.post(
            "https://cp2532.apps-dev.valeo.com/engine/prod/conversations/v1alpha3",
            headers=headers,
            json={
                "title": "session_title"
            },
            timeout=60,
        )
        data = response.json()
        return data
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)
    
async def get_session_details_ai4all(id_token, ai4allclient, session_id):
    headers = {"Authorization": f"Bearer {id_token}"}
    try:
        response = await ai4allclient.get(
            f"https://cp2532.apps-dev.valeo.com/engine/prod/conversations/v1alpha3/{session_id}",
            headers=headers,
            timeout=60,
        )
        data = response.json()
        return data
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)
    
async def generate_ai4all(id_token, ai4allclient, result_data, session_id):
    headers = {"Authorization": f"Bearer {id_token}"}
    try:
        response = await ai4allclient.post(
            f"https://cp2532.apps-dev.valeo.com/engine/prod/conversations/v1alpha3/{session_id}/messages",
            headers=headers,
            timeout=60,
            json=result_data
        )
        data = response.json()
        return data
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)
    
async def delete_ai4all_session(id_token, ai4allclient, session_id):
    headers = {"Authorization": f"Bearer {id_token}"}
    try:
        response = await ai4allclient.delete(
            f"https://cp2532.apps-dev.valeo.com/engine/prod/conversations/v1alpha3/{session_id}",
            headers=headers,
            timeout=60,
        )
        return response
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)
    
async def ai4all_health_check(id_token, ai4allclient): 
    headers = {"Authorization": f"Bearer {id_token}"}
    try:
        response = await ai4allclient.get(
            f"https://cp2532.apps-dev.valeo.com/engine/prod/conversations/health",
            headers=headers,
            timeout=60,
        )
        print("ai4all status :", response.status_code)
        print("ai4all message :", response.text)
        return response
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        print(str(e))
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)