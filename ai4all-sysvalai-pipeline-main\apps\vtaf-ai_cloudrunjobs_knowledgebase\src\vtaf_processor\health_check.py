import time
import logging
import requests

from config import API_BASE

def check_health(service: str, headers: dict) -> bool:
    """Ping a health check endpoint and return True if healthy."""
    url = f"{API_BASE}/v1/{service}/health"
    for attempt in range(1, 4):
        try:
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                logging.info(f"{service.capitalize()} is healthy.")
                return True
        except Exception as e:
            logging.warning(f"Attempt {attempt} failed: {e}")
        time.sleep(10)

    logging.error(f"{service.capitalize()} health check failed after 3 attempts.")
    return False
