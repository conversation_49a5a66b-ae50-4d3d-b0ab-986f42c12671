# Group {VisionAnalyzer}
## Check_Image

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Check_Image.seq*


**Descriptions:** *This keyword is used to do the vision analysis operation. *


***Command Set:***

	Full
		-Full=GoldenImage;Score
	ROI-REF
		-ROI-REF=GoldenImage;ROI;Score
	Resolution
		-Resolution=X_Dim;Y_Dim;Tol_Dim
	Compare
		-Compare=GoldenImage;Score
	RGBPixelCount
		-RGBPixelCount=ROI;Pixel_Counts;R_Max;R_Min;G_Max;G_Min;B_Max;B_Min
	ROI-RGBA
		-ROI-RGBA=ROI;RGB;Tol;Pixel_Counts;BGCol;Alpha
	FMVSS
		-FMVSS=GoldenImage;Time


***Input Description***



## Check_Video

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Check_Video.seq*


**Descriptions:** **


***Command Set:***

	Framerate
		-Framerate=FPS;Tol


***Input Description***



## Check_Vision

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Check_Vision.seq*


**Descriptions:** *This Keywords checks for FPS and Frame counter using AVB display for iCAM3 project*


***Command Set:***

	FPS
		-FPS=FPS
	FrameCounter
		-FrameCounter=Frame_Counter;Delay
	SuperImposition
		-SuperImposition=Template


***Input Description***



