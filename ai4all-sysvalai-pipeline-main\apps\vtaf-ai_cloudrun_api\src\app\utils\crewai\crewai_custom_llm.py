from crewai import BaseLLM
from typing import Any, Dict, List, Optional, Union
import requests


def convert_list_of_contents(input_list):
    if not isinstance(input_list, list):
        raise ValueError("Expected a list of {'role', 'content'} objects.")

    return [
        {"role": "user", "parts": [{"type": "text", "data": item.get("content", "")}]}
        for item in input_list
    ]


class AI4ALL_LLM(BaseLLM):
    def __init__(
        self,
        model: str,
        api_key: str,
        endpoint: str,
        temperature: Optional[float] = None,
    ):
        # IMPORTANT: Call super().__init__() with required parameters
        super().__init__(model=model, temperature=temperature)

        self.api_key = api_key
        self.endpoint = endpoint

    def call(
        self,
        messages: Union[str, List[Dict[str, str]]],
        tools: Optional[List[dict]] = None,
        callbacks: Optional[List[Any]] = None,
        available_functions: Optional[Dict[str, Any]] = None,
    ) -> Union[str, Any]:
        """Call the LLM with the given messages."""

        messages = convert_list_of_contents(messages)

        payload = {
            "model_name": self.model,
            "contents": messages,
            # "temperature": self.temperature,
        }

        # if tools and self.supports_function_calling():
        #     payload["tools"] = tools

        response = requests.post(
            self.endpoint,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
            },
            json=payload,
            timeout=30,
        )
        response.raise_for_status()

        result = response.json()
        return result["parts"][0]["data"]

    def supports_function_calling(self) -> bool:
        """Override if your LLM supports function calling."""
        return True  # Change to False if your LLM doesn't support tools

    def get_context_window_size(self) -> int:
        """Return the context window size of your LLM."""
        return 8192  # Adjust based on your model's actual context window
