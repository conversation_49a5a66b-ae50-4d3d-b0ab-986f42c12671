<template>
  <div class="q-mt-md q-ml-md q-mr-md">
  <fieldset>
  <legend class="text-bold q-mb-md">Max Output Tokens</legend>
    <q-slider
      class="q-pa-sm"
      v-model="maxTokens"
      :min="1"
      :max="65535"
      label
      label-always
      color="primary"
    />
    <div class="text-right">{{ maxTokens }}</div>
</fieldset>
 </div>
</template>

<script setup>
import { ref } from 'vue';

const maxTokens = ref(655);
</script>
