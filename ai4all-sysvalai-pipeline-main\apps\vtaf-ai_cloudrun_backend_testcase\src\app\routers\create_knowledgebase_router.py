from fastapi import APIRouter, HTTPException, Form, UploadFile, File
from utilities import get_bigquery_client, generate_signed_url, upload_file_to_storage, trigger_run_job, bigquery_insert_query
from configs.env import settings
import datetime
import shutil
import traceback
import uuid
import os


router = APIRouter()
client = get_bigquery_client()

services_project_id = settings.SERVICES_PROJECT_ID
services_dataset_id = settings.SERVICES_DATASET_ID
services_bucket_name = settings.SERVICES_BUCKET_NAME
ai_testcase_job = settings.TESTCASE_GENERATION_JOB
knowledgebase_creation_job = settings.KNOWLEDGEBASE_CREATION_JOB

UPLOAD_DIR = "uploads"

@router.post("/valid_user_knowledge_base_creation")
def valid_user_knowledge_base(email : str = Form(...)):
    try:
        fetch_query = f"""SELECT EXISTS (
            SELECT 1 FROM `{services_project_id}.{services_dataset_id}.KEY_USERGROUP` WHERE user_email = '{email}'
        )"""
        query = client.query(query=fetch_query)

        result = False
        for row in query.result():
            result = row[0]
        return result
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)

@router.get("/get_profile_projects")
def get_profile_projects():
    try:
        query = f"""
            SELECT * FROM `{services_project_id}.{services_dataset_id}.PROJECT_TABLE`;
        """
        # Run query
        query_job = client.query(query)
        results = query_job.result()

        projects = []
        for row in results:
            projects.append(row)
        sorted_data = sorted(projects, key=lambda x: x["project"])
        return sorted_data
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)

@router.get("/get_tree_variant_details/{project}")
def get_tree_variant_details(project: str):
    try:
        fetch_query = f"SELECT DISTINCT variant FROM `{services_project_id}.{services_dataset_id}.PROJECT_PROFILE_TABLE` WHERE PROJECT = '{project}'"
        query = client.query(fetch_query)
        results = query.result()

        variant_details = []
        for row in results:
            variant_details.append(row['variant'])
        variant_details.sort()
        return variant_details
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)

@router.get("/get_tree_euf_features_details/{variant_data}")
def get_tree_euf_features_details(variant_data: str):
    try:
        data = variant_data.split(",")
        fetch_query = f"SELECT DISTINCT euf_feature FROM `{services_project_id}.{services_dataset_id}.PROJECT_PROFILE_TABLE` WHERE project = '{data[0]}' AND variant = '{data[-1]}'"
        query = client.query(fetch_query)
        results = query.result()

        euf_features_details = []
        for row in results:
            euf_features_details.append(row["euf_feature"])
        euf_features_details.sort()
        return euf_features_details
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)

@router.post('/get_search_knowledgebase_profile')
def get_search_knowledgebase_profile(project:str = Form(...), variant:str = Form(...), euf_feature:str = Form(...)):
    try:
        fetch_query = f"""
            SELECT user_email, execution_id, timestamp as created_time, status, execution_status, user_upload, result_file, type as file_type FROM `{services_project_id}.{services_dataset_id}.KNOWLEDGE_JOB_HISTORY` WHERE project = '{project}' AND variant = '{variant}' AND euf_feature = '{euf_feature}' ORDER BY timestamp
        """
        query = client.query(query=fetch_query)

        search_results = []
        for row in query.result():
            print(row)
            if row.result_file in ("NA", "",None):
                search_results.append({
                    "user_email": row.user_email,
                    "execution_id": row.execution_id,
                    "created_time": row.created_time,
                    "status": row.status,
                    "execution_status": row.execution_status,
                    "user_upload": generate_signed_url(row.user_upload),
                    "result_file": row.result_file,
                })
            else:
                search_results.append({
                    "user_email": row.user_email,
                    "execution_id": row.execution_id,
                    "created_time": row.created_time,
                    "status": row.status,
                    "execution_status": row.execution_status,
                    "user_upload": generate_signed_url(row.user_upload),
                    "result_file": generate_signed_url(row.result_file),
                })
            # elif row.execution_status == "completed" and row.result_file is not None:
        return search_results
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)
    
@router.post("/create_knowledgebase")
def create_knowledgebase(
    user_email: str = Form(...),
    file: UploadFile = File(...),
    project: str = Form(...),
    variant: str = Form(...),
    euf_feature: str = Form(...),
    file_type: str = Form(...),
):
    try:
        uid = str(uuid.uuid4())
        request_upload_dir = os.path.join(UPLOAD_DIR, uid)
        os.makedirs(request_upload_dir, exist_ok=True)

        # Save the uploaded file in the unique subdirectory
        file_path = os.path.join(request_upload_dir, file.filename)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        current_timestamp = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        # destination_file_name = file_path.split("\\")[-1]
        destination_file_name = file.filename
        destination_path = f"vtaf-ai/knowledgebase/user_uploads/{project}/{variant}/{euf_feature}/{file_type}/[{current_timestamp}]_{destination_file_name}"

        upload_file_to_storage(
            bucket_name=services_bucket_name,
            file_path=file_path,
            destination_blob_name=destination_path,
        )

        fetch_query = f"""
            SELECT bg, 
            pg, 
            pl, 
            project_uuid, 
            variant_uuid, 
            euf_feature_uuid, 
            uuid as project_profile_uuid from `{services_project_id}.{services_dataset_id}.PROJECT_PROFILE_TABLE` 
            WHERE project = '{project}' AND variant = '{variant}' AND euf_feature = '{euf_feature}'
        """

        bigquery_data = {}

        query = client.query(query=fetch_query)
        for row in query.result():
            bigquery_data['bg'] = row['bg']
            bigquery_data['pg'] = row['pg']
            bigquery_data['pl'] = row['pl']
            bigquery_data['project'] = project
            bigquery_data['project_uuid'] = row['project_uuid']
            bigquery_data['variant'] = variant
            bigquery_data['variant_uuid'] = row['variant_uuid']
            bigquery_data['euf_feature'] = euf_feature
            bigquery_data['euf_feature_uuid'] = row['euf_feature_uuid']
            bigquery_data['project_profile_uuid'] = row['project_profile_uuid']
        
        if file.filename.split(".")[-1] == ".dbc":
            file_type = "DBC File"
        env_vars = {
            "FILE_NAME" : destination_path,
            "PROJECT_PROFILE_UUID" : bigquery_data['project_profile_uuid'],
            "VTAF_AI_PROJECT_ID" : bigquery_data['project_uuid'],
            "VTAF_AI_PROJECT_VAR_ID" : bigquery_data['variant_uuid'],
            "VTAF_AI_EUF_FEA_UID" : bigquery_data['euf_feature_uuid'],
            "INPUT_TYPE" : file_type
        }
        execution_id = trigger_run_job(job_name=knowledgebase_creation_job, environment_variables=env_vars)
        execute_query = f"""
            INSERT INTO `{services_project_id}.{services_dataset_id}.KNOWLEDGE_JOB_HISTORY` (
                user_email,
                timestamp,
                bg,
                pg,
                pl,
                project,
                project_uuid,
                variant,
                variant_uuid,
                euf_feature,
                euf_feature_uuid,
                project_profile_uuid,
                execution_status,
                user_upload,
                execution_id,
                type
            )
            VALUES (
                '{user_email}',
                '{current_timestamp}',
                '{bigquery_data['bg']}',
                '{bigquery_data['pg']}',
                '{bigquery_data['pl']}',
                '{bigquery_data['project']}',
                '{bigquery_data['project_uuid']}',
                '{bigquery_data['variant']}',
                '{bigquery_data['variant_uuid']}',
                '{bigquery_data['euf_feature']}',
                '{bigquery_data['euf_feature_uuid']}',
                '{bigquery_data['project_profile_uuid']}',
                'started',
                '{destination_path}',
                '{execution_id}',
                '{file_type}'
            );
            """
        bigquery_insert_query(execute_query)

        shutil.rmtree(request_upload_dir, ignore_errors=True)

        # Response with file name and selected rows
        return {"message": "Job is running", "execution_id": execution_id}
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)


