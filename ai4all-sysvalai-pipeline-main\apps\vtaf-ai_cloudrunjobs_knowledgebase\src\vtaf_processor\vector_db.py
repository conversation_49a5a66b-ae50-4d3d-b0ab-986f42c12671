import requests
import logging

from config import API_BASE, COLLECTION_NAME

def add_chunks_to_vector_db(chunks: list, file_name: str, headers: dict):
    url = f"{API_BASE}/v1/vectordb/documents/add"

    for idx, chunk in enumerate(chunks):
        payload = {
            "content": chunk,
            "metadata": {
                "source": file_name,
                "chunk_index": idx
            },
            "collection_name": COLLECTION_NAME
        }

        try:
            response = requests.post(url, headers=headers, json=payload)
            if response.status_code == 200:
                logging.info(f"Chunk {idx} added to vector DB.")
        except Exception as e:
            logging.error(f"Error adding chunk {idx}: {e}")
