from fastapi import APIRouter, HTTPException, Form, UploadFile, File
from utilities import get_bigquery_client, generate_signed_url, upload_file_to_storage, trigger_run_job, bigquery_insert_query
from configs.env import settings
import datetime
import time
import traceback

router = APIRouter()
client = get_bigquery_client()

services_project_id = settings.SERVICES_PROJECT_ID
services_dataset_id = settings.SERVICES_DATASET_ID
services_bucket_name = settings.SERVICES_BUCKET_NAME
ai_testcase_job = settings.TESTCASE_GENERATION_JOB
knowledgebase_creation_job = settings.KNOWLEDGEBASE_CREATION_JOB

@router.get('/get_job_status/{execution_id}')
def get_job_status(execution_id: str):
    time.sleep(2)
    try:
        fetch_query = f"SELECT * FROM `{services_project_id}.{services_dataset_id}.TC_JOB_HISTORY` WHERE execution_id = '{execution_id}'"
        query = client.query(fetch_query)
        for row in query.result(timeout=30):
            print(row)
            # Convert row to dictionary
            row_dict = dict(row)
            print(row_dict)

            # If execution_status is "completed", generate signed URL
            if row_dict.get("execution_status") == "completed":
                output_csv_path = row_dict.get("output_csv")
                output_execution_id = row_dict.get("execution_id")
                output_execution_file = output_execution_id + "_TestCases.csv"
                signed_url_path = f"vtaf-ai/job_history/{ai_testcase_job}/{output_execution_id}/{output_execution_file}"
                if ".csv" in output_csv_path:
                    signed_url = generate_signed_url(signed_url_path)
                    row_dict["signed_url"] = signed_url
                    row_dict["drive_url"] = ""
                else:
                    row_dict["drive_url"] = f"https://docs.google.com/spreadsheets/d/{output_csv_path}"
                    row_dict["signed_url"] = ""

            print(row_dict)
            return row_dict  # Return job details (including signed URL if applicable)
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)
    
@router.get("/get_all_job_history/{user_email}")
def get_job_history(user_email: str):
    try:
        fetch_query = f"SELECT timestamp, execution_id, job_duration, status, execution_status FROM `{services_project_id}.{services_dataset_id}.TC_JOB_HISTORY` WHERE user_email = '{user_email}' ORDER BY Timestamp DESC"
        query = client.query(fetch_query)
        
        results = []
        for row in query.result():
            row_dict = dict(row)
            
            # Convert timestamp to a readable format
            if "timestamp" in row_dict and isinstance(row_dict["timestamp"], datetime.datetime):
                row_dict["timestamp"] = row_dict["timestamp"].strftime("%d %b %Y %I:%M:%S %p")  # Example: 23 Feb 2025 08:03:42 PM
            results.append(row_dict)  # Append each row as a dictionary to the list
        return results  # Return all rows
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)
    
@router.get("/get_current_jobs/{user_email}")
def get_current_jobs_history(user_email: str):
    try:
        fetch_query = f"SELECT timestamp, execution_id, execution_status FROM `{services_project_id}.{services_dataset_id}.TC_JOB_HISTORY` WHERE user_email = '{user_email}' AND (execution_status = 'running' OR execution_status = 'started') ORDER BY Timestamp DESC"
        query = client.query(fetch_query)
        
        results = []
        for row in query.result():
            row_dict = dict(row)
            # Convert timestamp to a readable format
            if "timestamp" in row_dict and isinstance(row_dict["timestamp"], datetime.datetime):
                row_dict["timestamp"] = row_dict["timestamp"].strftime("%d %b %Y %I:%M:%S %p")  # Example: 23 Feb 2025 08:03:42 PM
            results.append(row_dict)
            # results.append(dict(row))  
        return results  # Return all rows
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)