import os
import re
import time
import random
import gspread
from google.oauth2.service_account import Credentials
from gspread.exceptions import APIError
from configs.env import settings
from utils.logger import get_logger

logger = get_logger(__name__)

def extract_sheet_id(url):
    """
    从Google Sheets URL中提取表格ID
    Args:
        url: Google SpreadSheet URL 或 SpreadSheet ID
    Returns:
        spread_sheet_id
    """
    if len(url) < 45:
        return url
    spread_sheet_pattern = r'/spreadsheets/d/([a-zA-Z0-9_-]+)'

    # 从URL中匹配ID
    folder_match = re.search(spread_sheet_pattern, url)
    if folder_match:
        return folder_match.group(1)

    # 没有匹配到
    return None

class SheetManager:
    def __init__(self):
        self.gc = None
        self.spread_sheet = None
        self.spread_sheet_id = None
        self.sheet_names = []
        self.sheet_header_cache = {}
        self._initialize_client()

    def _initialize_client(self):
        """初始化 Google Sheets 客户端 - 支持多种认证方式"""
        try:
            if not settings.GOOGLE_SHEETS_ENABLED:
                logger.warning("Google Sheets is disabled")
                return

            # 认证优先级：
            # 1. Google Cloud 默认认证 (在 GCP 环境中)
            # 2. 硬编码服务账号信息
            # 3. 服务账号文件
            # 4. OAuth 文件

            # 1. 尝试 Google Cloud 默认认证
            if self._try_default_credentials():
                return

            # 2. 尝试硬编码服务账号
            if self._try_hardcoded_service_account():
                return

            # 3. 尝试服务账号文件
            if self._try_service_account_file():
                return

            # 4. 尝试 OAuth 文件
            if self._try_oauth_credentials():
                return

            raise Exception("All authentication methods failed")

        except Exception as e:
            logger.error(f"Failed to initialize Sheets client: {e}")
            raise

    def _try_default_credentials(self) -> bool:
        """尝试使用 Google Cloud 默认认证"""
        try:
            from google.auth import default

            # 检查是否在 Google Cloud 环境中
            credentials, project = default()

            # 添加必要的 scope
            if hasattr(credentials, 'with_scopes'):
                scopes = [
                    'https://spreadsheets.google.com/feeds',
                    'https://www.googleapis.com/auth/drive'
                ]
                credentials = credentials.with_scopes(scopes)

            self.gc = gspread.authorize(credentials)
            logger.info("Google Sheets client initialized with default credentials")
            return True

        except Exception as e:
            logger.debug(f"Default credentials failed: {e}")
            return False

    def _try_hardcoded_service_account(self) -> bool:
        """尝试使用硬编码服务账号信息"""
        try:
            if not (hasattr(settings, 'SERVICE_ACCOUNT_INFO') and settings.SERVICE_ACCOUNT_INFO):
                return False

            scope = [
                'https://spreadsheets.google.com/feeds',
                'https://www.googleapis.com/auth/drive'
            ]

            creds = Credentials.from_service_account_info(
                settings.SERVICE_ACCOUNT_INFO,
                scopes=scope
            )

            self.gc = gspread.authorize(creds)
            logger.info("Google Sheets client initialized with hardcoded service account")
            return True

        except Exception as e:
            logger.debug(f"Hardcoded service account failed: {e}")
            return False

    def _try_service_account_file(self) -> bool:
        """尝试使用服务账号文件"""
        try:
            if not os.path.exists(settings.SERVICE_ACCOUNT_FILE):
                return False

            scope = [
                'https://spreadsheets.google.com/feeds',
                'https://www.googleapis.com/auth/drive'
            ]

            creds = Credentials.from_service_account_file(
                settings.SERVICE_ACCOUNT_FILE,
                scopes=scope
            )

            self.gc = gspread.authorize(creds)
            logger.info("Google Sheets client initialized with service account file")
            return True

        except Exception as e:
            logger.debug(f"Service account file failed: {e}")
            return False

    def _try_oauth_credentials(self) -> bool:
        """尝试使用 OAuth 认证文件"""
        try:
            if not os.path.exists("./client_secret.json"):
                return False

            self.gc = gspread.oauth(
                credentials_filename="./client_secret.json",
                authorized_user_filename="authorized_user_1.json"
            )
            logger.info("Google Sheets client initialized with OAuth")
            return True

        except Exception as e:
            logger.debug(f"OAuth credentials failed: {e}")
            return False
            
    def connect_sheet(self, sheet_url):
        """连接到指定的表格"""
        try:
            self.spread_sheet_id = extract_sheet_id(sheet_url)
            self.spread_sheet = self.gc.open_by_key(self.spread_sheet_id)
            self.sheet_names = [worksheet.title for worksheet in self.spread_sheet.worksheets()]
            logger.info(f"Connected to sheet: {self.spread_sheet_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to sheet: {e}")
            return False

    def reconnect(self):
        """重新连接到表格（类似原始代码的reconnect方法）"""
        try:
            if self.gc and self.spread_sheet_id:
                # 尝试重新登录
                self.gc.login()
                # 重新打开表格
                self.spread_sheet = self.gc.open_by_key(self.spread_sheet_id)
        except Exception as e:
            logger.warning(f"Reconnect failed, reinitializing: {e}")
            # 如果重连失败，重新初始化
            try:
                self.spread_sheet = self.gc.open_by_key(self.spread_sheet_id)
            except Exception as init_error:
                logger.error(f"Failed to reinitialize sheet connection: {init_error}")
                raise
        
    def read_by_sheet_name(self, sheet_name):
        """读取指定工作表数据"""
        retries = 5
        for attempt in range(retries):
            try:
                self.reconnect()
                worksheet = self.spread_sheet.worksheet(sheet_name)
                return worksheet.get_all_records()
            except APIError as error:
                if '429' in str(error):
                    # 如果是 429 错误，等待一段时间后重试
                    wait_time = (2 ** attempt) + random.uniform(0, 1)
                    logger.warning(f"Hit rate limit, retrying in {wait_time} seconds...")
                    time.sleep(wait_time)
                else:
                    # 其他类型的错误，直接抛出
                    raise
            except Exception as e:
                logger.error(f"Error reading sheet {sheet_name}: {e}")
                raise

    def get_sheet_header(self, sheet_name):
        """获取工作表头部"""
        if sheet_name in self.sheet_header_cache:
            return self.sheet_header_cache[sheet_name]

        try:
            self.reconnect()
            worksheet = self.spread_sheet.worksheet(sheet_name)
            headers = worksheet.row_values(1)
            self.sheet_header_cache[sheet_name] = headers
            return headers
        except Exception as e:
            logger.error(f"Failed to get sheet header for {sheet_name}: {e}")
            return []
                
    def update_cell(self, sheet_name, row, col, value):
        """更新单元格"""
        try:
            self.reconnect()
            worksheet = self.spread_sheet.worksheet(sheet_name)
            worksheet.update_cell(row, col, value)
            logger.debug(f"Updated cell {sheet_name}[{row},{col}] = {value}")
        except Exception as e:
            logger.error(f"Failed to update cell: {e}")

    def read_cell(self, sheet_name, row, col):
        """读取指定单元格的值"""
        try:
            self.reconnect()
            worksheet = self.spread_sheet.worksheet(sheet_name)
            cell_value = worksheet.cell(row, col).value
            return cell_value
        except APIError as e:
            if e.response.status_code == 429:  # 处理 "Too Many Requests" 错误
                wait_time = int(e.response.headers.get('Retry-After', 60))
                logger.warning(f"API 请求过快，等待 {wait_time} 秒...")
                time.sleep(wait_time)
                return self.read_cell(sheet_name, row, col)  # 递归调用
            else:
                raise
        except Exception as e:
            logger.error(f"读取单元格 ({sheet_name}, {row}, {col}) 时出错: {e}")
            return None

    def add_row(self, sheet_name, row_data, table_range="A1"):
        """添加行数据"""
        try:
            self.reconnect()
            worksheet = self.spread_sheet.worksheet(sheet_name)
            worksheet.append_row(row_data, table_range=table_range)
            logger.debug(f"Added row to {sheet_name}")
        except Exception as e:
            logger.error(f"Failed to add row: {e}")

    def clear_sheet(self, sheet_name):
        """清空工作表"""
        try:
            self.reconnect()
            worksheet = self.spread_sheet.worksheet(sheet_name)
            worksheet.clear()
            logger.info(f"Cleared sheet: {sheet_name}")
        except Exception as e:
            logger.error(f"Failed to clear sheet: {e}")

    def create_sheet(self, sheet_name, rows=1000, cols=20):
        """创建新的工作表"""
        try:
            self.reconnect()
            if sheet_name in self.sheet_names:
                logger.info(f"Sheet {sheet_name} already exists")
                return self.sheet_names
            else:
                self.spread_sheet.add_worksheet(sheet_name, rows=rows, cols=cols)
                self.sheet_names.append(sheet_name)
                logger.info(f"Created new sheet: {sheet_name}")
                return self.sheet_names
        except Exception as e:
            logger.error(f"Failed to create sheet: {e}")
            return self.sheet_names

    def get_sheet_names(self):
        """获取所有工作表名称"""
        try:
            if not self.sheet_names:
                self.sheet_names = [ws.title for ws in self.spread_sheet.worksheets()]
            return self.sheet_names
        except Exception as e:
            logger.error(f"Failed to get sheet names: {e}")
            return []

    def check_sheet_permission(self, sheet_id=None):
        """检查表格权限"""
        try:
            target_id = sheet_id or self.spread_sheet_id
            if not target_id:
                return False

            # 尝试列出权限（需要适当的权限）
            permissions = self.gc.list_permissions(target_id)

            # 获取当前用户名
            current_user = os.getenv('USER', os.getenv('USERNAME', 'Unknown'))

            # 检查用户权限
            user_exists = False
            for item in permissions:
                if item.get("displayName") == current_user:
                    user_exists = True
                    role = item.get('role', '')
                    if role in ['writer', 'owner']:
                        return True
                    else:
                        return False

            # 如果用户不存在，尝试添加权限（需要owner权限）
            if not user_exists:
                try:
                    self.gc.insert_permission(
                        file_id=target_id,
                        value=current_user,
                        perm_type='user',
                        role='writer',
                        notify=False
                    )
                    return True
                except Exception as perm_error:
                    logger.warning(f"Failed to add permission: {perm_error}")
                    return False

        except Exception as e:
            logger.error(f"检查配置表权限失败: {e}")
            return False