#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入失败测试 - 直接尝试导入每个服务来检测依赖问题
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from utils.logger import get_logger

logger = get_logger(__name__)

class ImportFailureDetector:
    """导入失败检测器"""
    
    def __init__(self):
        self.import_results = {}
        self.dependency_issues = {}
        
    def test_all_imports(self):
        """测试所有导入"""
        logger.info("🔍 开始测试所有服务导入...")
        logger.info("=" * 60)
        
        # 定义要测试的服务模块
        service_modules = [
            'services.ai_service',
            'services.basic_submit_service',
            'services.batch_processor',
            'services.config_service',
            'services.deepseek_client',
            'services.gemini_client',
            'services.sheet_manager',
            'services.job_router',
            'services.email_send_service',
            'services.email_back_service',
            'services.file_operations_service',
            'services.single_task_service',
            'services.testcase_archive_service',
            'services.testscript_batch_service',
            'services.testscript_single_service',
            'services.training_dataset_service',
            'services.ts_training_dataset_service',
            'services.system_instruction_service',
        ]
        
        for module_name in service_modules:
            self._test_import(module_name)
        
        # 测试主要入口点
        main_modules = ['main', 'docker_entrypoint']
        for module_name in main_modules:
            if Path(f"{module_name}.py").exists():
                self._test_import(module_name)
        
        self._analyze_results()
    
    def _test_import(self, module_name: str):
        """测试单个模块导入"""
        logger.info(f"\n📦 测试导入: {module_name}")
        
        try:
            # 尝试导入模块
            module = __import__(module_name, fromlist=[''])
            logger.info(f"  ✅ 导入成功")
            self.import_results[module_name] = {
                'success': True,
                'error': None,
                'missing_deps': []
            }
            
        except ImportError as e:
            error_msg = str(e)
            logger.error(f"  ❌ 导入失败: {error_msg}")
            
            # 分析错误信息，提取缺失的依赖
            missing_deps = self._extract_missing_dependencies(error_msg)
            
            self.import_results[module_name] = {
                'success': False,
                'error': error_msg,
                'missing_deps': missing_deps
            }
            
        except Exception as e:
            logger.error(f"  ❌ 其他错误: {e}")
            self.import_results[module_name] = {
                'success': False,
                'error': str(e),
                'missing_deps': []
            }
    
    def _extract_missing_dependencies(self, error_msg: str) -> List[str]:
        """从错误信息中提取缺失的依赖"""
        missing_deps = []
        
        # 常见的导入错误模式
        patterns = [
            "No module named '",
            "cannot import name '",
            "ModuleNotFoundError: No module named '"
        ]
        
        for pattern in patterns:
            if pattern in error_msg:
                # 提取模块名
                start = error_msg.find(pattern) + len(pattern)
                end = error_msg.find("'", start)
                if end > start:
                    module_name = error_msg[start:end]
                    # 只取根模块名
                    root_module = module_name.split('.')[0]
                    if root_module not in missing_deps:
                        missing_deps.append(root_module)
        
        return missing_deps
    
    def _analyze_results(self):
        """分析测试结果"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 导入测试结果分析")
        logger.info("=" * 60)
        
        successful_imports = []
        failed_imports = []
        all_missing_deps = set()
        
        for module_name, result in self.import_results.items():
            if result['success']:
                successful_imports.append(module_name)
            else:
                failed_imports.append(module_name)
                all_missing_deps.update(result['missing_deps'])
        
        # 成功的导入
        logger.info(f"\n✅ 成功导入 ({len(successful_imports)} 个):")
        for module in successful_imports:
            logger.info(f"  📦 {module}")
        
        # 失败的导入
        if failed_imports:
            logger.error(f"\n❌ 失败导入 ({len(failed_imports)} 个):")
            for module in failed_imports:
                result = self.import_results[module]
                logger.error(f"  📦 {module}")
                logger.error(f"    错误: {result['error']}")
                if result['missing_deps']:
                    logger.error(f"    缺失依赖: {result['missing_deps']}")
        
        # 汇总所有缺失的依赖
        if all_missing_deps:
            logger.error(f"\n🚨 所有缺失的依赖 ({len(all_missing_deps)} 个):")
            for dep in sorted(all_missing_deps):
                logger.error(f"  📦 {dep}")
        
        # 生成依赖安装建议
        self._generate_install_suggestions(all_missing_deps)
    
    def _generate_install_suggestions(self, missing_deps: set):
        """生成安装建议"""
        logger.info("\n" + "=" * 60)
        logger.info("💡 依赖安装建议")
        logger.info("=" * 60)
        
        if not missing_deps:
            logger.info("🎉 没有缺失的依赖！")
            return
        
        # 依赖包映射
        package_mapping = {
            'openai': 'openai',
            'gspread': 'gspread',
            'google': 'google-cloud-storage google-cloud-aiplatform',
            'vertexai': 'google-cloud-aiplatform',
            'anthropic': 'anthropic',
            'requests': 'requests',
            'pandas': 'pandas',
            'numpy': 'numpy',
            'flask': 'Flask',
            'fastapi': 'fastapi',
            'uvicorn': 'uvicorn',
            'pydantic': 'pydantic',
            'sqlalchemy': 'SQLAlchemy',
            'redis': 'redis',
            'celery': 'celery',
            'pytest': 'pytest',
            'black': 'black',
            'flake8': 'flake8',
            'mypy': 'mypy'
        }
        
        logger.info("使用pip安装缺失的依赖:")
        pip_commands = []
        
        for dep in sorted(missing_deps):
            package = package_mapping.get(dep, dep)
            pip_command = f"pip install {package}"
            pip_commands.append(pip_command)
            logger.info(f"  {pip_command}")
        
        # 生成一条完整的安装命令
        if pip_commands:
            all_packages = []
            for dep in sorted(missing_deps):
                package = package_mapping.get(dep, dep)
                all_packages.extend(package.split())
            
            complete_command = f"pip install {' '.join(set(all_packages))}"
            logger.info(f"\n一次性安装所有依赖:")
            logger.info(f"  {complete_command}")
        
        # 写入requirements文件
        self._write_missing_requirements(missing_deps, package_mapping)
    
    def _write_missing_requirements(self, missing_deps: set, package_mapping: dict):
        """写入缺失的依赖到requirements文件"""
        try:
            requirements_file = Path("requirements_missing.txt")
            
            with open(requirements_file, 'w', encoding='utf-8') as f:
                f.write("# VTAF AI Multiprompt - 缺失的依赖\n")
                f.write("# 这些依赖需要安装才能正常运行所有功能\n\n")
                
                for dep in sorted(missing_deps):
                    packages = package_mapping.get(dep, dep).split()
                    for package in packages:
                        f.write(f"{package}\n")
            
            logger.info(f"\n📄 缺失依赖列表已写入: {requirements_file}")
            
        except Exception as e:
            logger.error(f"写入requirements文件时出错: {e}")

def main():
    """主函数"""
    logger.info("🔍 VTAF AI Multiprompt 导入失败检测工具")
    logger.info("=" * 60)
    
    detector = ImportFailureDetector()
    detector.test_all_imports()
    
    logger.info("\n🎉 导入测试完成!")

if __name__ == '__main__':
    main()
