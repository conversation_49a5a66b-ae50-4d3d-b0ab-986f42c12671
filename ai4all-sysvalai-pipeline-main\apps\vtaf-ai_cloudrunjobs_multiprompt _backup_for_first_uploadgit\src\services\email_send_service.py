#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Email Send Service - 邮件发送服务
对应原始ai_generate.py中的do_sendmail()方法
处理邮件发送任务
"""

import os
import time
from typing import Dict, Any, Optional
from utils.logger import get_logger
from services.ai_service import AIService
from services.sheet_manager import SheetManager
from services.config_service import ConfigurationService

logger = get_logger(__name__)

class EmailSendService:
    """邮件发送服务"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.sheet_manager = SheetManager()
        self.config_service = ConfigurationService()
        
    def execute(self) -> int:
        """执行邮件发送任务"""
        try:
            logger.info("🚀 Starting email send job")
            
            # 获取配置
            config = self._get_config()
            self._validate_config(config)
            
            # 连接到任务表格
            if not self.sheet_manager.connect_sheet(config['task_sheet']):
                raise Exception("Failed to connect to task sheet")
            
            # 处理邮件发送
            result = self._process_email_send(config)
            
            if result:
                logger.info("✅ Email send job completed successfully")
                return 0
            else:
                logger.error("❌ Email send job failed")
                return 1
            
        except Exception as e:
            logger.exception(f"Email send job failed: {e}")
            return 1
    
    def _get_config(self) -> Dict[str, Any]:
        """获取配置"""
        config = {}
        
        # 从环境变量获取
        config['task_sheet'] = os.getenv('TASK_SHEET_URL')
        config['last_ai_response'] = os.getenv('LAST_AI_RESPONSE', '')
        config['email_template'] = os.getenv('EMAIL_TEMPLATE', 'Please reply to this email: ')
        
        # 如果环境变量中没有配置，尝试从配置文件读取
        if not config['task_sheet']:
            try:
                if self.config_service.config_exists():
                    sheet_config = self.config_service.get_sheet_config_for_batch_task()
                    if 'error' not in sheet_config:
                        config['task_sheet'] = sheet_config.get('prompt_sheet')
                        logger.info("Task sheet configuration loaded from config.ini")
            except Exception as e:
                logger.error(f"Failed to read config file: {e}")
        
        return config
    
    def _validate_config(self, config: Dict[str, Any]):
        """验证配置"""
        required_fields = ['task_sheet']
        missing_fields = []
        
        for field in required_fields:
            if not config.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            raise ValueError(f"Missing required configuration for email send: {missing_fields}")
        
        # 检查是否有AI响应内容
        if not config.get('last_ai_response'):
            logger.warning("No AI response content provided for email")
        
        logger.info("Email send configuration validation passed")
    
    def _process_email_send(self, config: Dict[str, Any]) -> bool:
        """处理邮件发送"""
        try:
            # 1. 检查是否有AI响应内容
            last_ai_response = config.get('last_ai_response')
            if not last_ai_response or last_ai_response == "NoResponse":
                logger.error("No valid AI response to send via email")
                return False
            
            # 2. 更新Google Sheet中的任务状态
            self._update_task_sheet(config, last_ai_response)
            
            # 3. 等待Google Sheet更新（模拟原始逻辑）
            prompt_data = self._wait_for_sheet_update(config)
            
            if not prompt_data:
                logger.error("Failed to get updated prompt data from sheet")
                return False
            
            # 4. 生成邮件回复内容
            email_content = self._generate_email_content(config, prompt_data)
            
            # 5. 处理邮件回复（使用AI生成回复）
            response = self.ai_service.generate_response(email_content)
            
            if response.startswith("Error"):
                logger.error(f"Failed to generate email response: {response}")
                return False
            
            # 6. 记录邮件发送结果
            self._log_email_result(email_content, response)
            
            return True
            
        except Exception as e:
            logger.error(f"Email send processing failed: {e}")
            return False
    
    def _update_task_sheet(self, config: Dict[str, Any], ai_response: str):
        """更新任务表格"""
        try:
            # 更新任务表格的第2行第4列（D列）
            self.sheet_manager.update_cell("Task", 2, 4, ai_response)
            logger.info("Updated task sheet with AI response")
        except Exception as e:
            logger.error(f"Failed to update task sheet: {e}")
            raise
    
    def _wait_for_sheet_update(self, config: Dict[str, Any]) -> Optional[str]:
        """等待Google Sheet更新"""
        try:
            prompt_data = None
            # 等待最多60秒
            for _ in range(60):
                prompt_data = self.sheet_manager.read_cell("Task", 3, 4)
                if prompt_data:
                    break
                time.sleep(1)
            
            return prompt_data
            
        except Exception as e:
            logger.error(f"Failed to wait for sheet update: {e}")
            return None
    
    def _generate_email_content(self, config: Dict[str, Any], prompt_data: str) -> str:
        """生成邮件内容"""
        email_template = config.get('email_template', 'Please reply to this email: ')
        return f"{email_template}{prompt_data}"
    
    def _log_email_result(self, email_content: str, response: str):
        """记录邮件结果"""
        logger.info("=" * 50)
        logger.info("EMAIL SEND RESULT")
        logger.info("=" * 50)
        logger.info(f"Email content: {email_content[:200]}...")
        logger.info(f"AI response length: {len(response)} characters")
        logger.info(f"AI response: {response[:300]}...")
        logger.info("=" * 50)
