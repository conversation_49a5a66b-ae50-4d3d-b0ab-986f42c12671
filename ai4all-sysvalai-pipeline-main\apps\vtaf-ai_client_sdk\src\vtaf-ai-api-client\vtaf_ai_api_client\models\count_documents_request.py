from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar, Union, cast

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..types import UNSET, Unset

if TYPE_CHECKING:
    from ..models.count_documents_request_filter_type_0 import CountDocumentsRequestFilterType0


T = TypeVar("T", bound="CountDocumentsRequest")


@_attrs_define
class CountDocumentsRequest:
    """
    Attributes:
        collection_name (str):
        filter_ (Union['CountDocumentsRequestFilterType0', None, Unset]):
    """

    collection_name: str
    filter_: Union["CountDocumentsRequestFilterType0", None, Unset] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        from ..models.count_documents_request_filter_type_0 import CountDocumentsRequestFilterType0

        collection_name = self.collection_name

        filter_: Union[None, Unset, dict[str, Any]]
        if isinstance(self.filter_, Unset):
            filter_ = UNSET
        elif isinstance(self.filter_, CountDocumentsRequestFilterType0):
            filter_ = self.filter_.to_dict()
        else:
            filter_ = self.filter_

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "collection_name": collection_name,
            }
        )
        if filter_ is not UNSET:
            field_dict["filter"] = filter_

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.count_documents_request_filter_type_0 import CountDocumentsRequestFilterType0

        d = dict(src_dict)
        collection_name = d.pop("collection_name")

        def _parse_filter_(data: object) -> Union["CountDocumentsRequestFilterType0", None, Unset]:
            if data is None:
                return data
            if isinstance(data, Unset):
                return data
            try:
                if not isinstance(data, dict):
                    raise TypeError()
                filter_type_0 = CountDocumentsRequestFilterType0.from_dict(data)

                return filter_type_0
            except:  # noqa: E722
                pass
            return cast(Union["CountDocumentsRequestFilterType0", None, Unset], data)

        filter_ = _parse_filter_(d.pop("filter", UNSET))

        count_documents_request = cls(
            collection_name=collection_name,
            filter_=filter_,
        )

        count_documents_request.additional_properties = d
        return count_documents_request

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
