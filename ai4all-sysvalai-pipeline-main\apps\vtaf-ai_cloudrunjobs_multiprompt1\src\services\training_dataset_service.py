#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Training Dataset Service - 训练数据集服务
对应原始ai_generate.py中的load_init_data()方法
处理训练数据集加载任务
"""

import os
from typing import Dict, Any, List
from utils.logger import get_logger
from services.ai_service import AIService
from services.sheet_manager import SheetManager
from services.config_service import ConfigurationService

logger = get_logger(__name__)

class TrainingDatasetService:
    """训练数据集服务"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.sheet_manager = SheetManager()
        self.config_service = ConfigurationService()
        
    def execute(self) -> int:
        """执行训练数据集加载任务"""
        try:
            logger.info("🚀 Starting training dataset job")
            
            # 获取配置
            config = self._get_config()
            self._validate_config(config)
            
            # 连接到数据集表格
            if not self.sheet_manager.connect_sheet(config['dataset_sheet']):
                raise Exception("Failed to connect to dataset sheet")
            
            # 加载训练数据集
            dataset = self._load_training_dataset(config)
            
            # 处理数据集
            result = self._process_training_dataset(config, dataset)
            
            if result:
                logger.info("✅ Training dataset job completed successfully")
                return 0
            else:
                logger.error("❌ Training dataset job failed")
                return 1
            
        except Exception as e:
            logger.exception(f"Training dataset job failed: {e}")
            return 1
    
    def _get_config(self) -> Dict[str, Any]:
        """获取配置"""
        config = {}
        
        # 从环境变量获取
        config['dataset_sheet'] = os.getenv('DATASET_SHEET_URL')
        config['dataset_sheet_name'] = os.getenv('DATASET_SHEET_NAME', 'TrainingData')
        config['system_instruction_mode'] = os.getenv('SYSTEM_INSTRUCTION_MODE', 'update')  # update/append
        config['auto_submit'] = os.getenv('AUTO_SUBMIT', 'false').lower() == 'true'
        
        # 如果环境变量中没有配置，尝试从配置文件读取
        if not config['dataset_sheet']:
            try:
                if self.config_service.config_exists():
                    sheet_config = self.config_service.get_sheet_config_for_batch_task()
                    if 'error' not in sheet_config:
                        config['dataset_sheet'] = sheet_config.get('prompt_sheet')
                        logger.info("Dataset sheet configuration loaded from config.ini")
            except Exception as e:
                logger.error(f"Failed to read config file: {e}")
        
        return config
    
    def _validate_config(self, config: Dict[str, Any]):
        """验证配置"""
        required_fields = ['dataset_sheet', 'dataset_sheet_name']
        missing_fields = []
        
        for field in required_fields:
            if not config.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            raise ValueError(f"Missing required configuration for training dataset: {missing_fields}")
        
        logger.info("Training dataset configuration validation passed")
    
    def _load_training_dataset(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """加载训练数据集"""
        try:
            dataset_sheet_name = config['dataset_sheet_name']
            
            logger.info(f"Loading training dataset from sheet: {dataset_sheet_name}")
            
            # 读取数据集
            dataset = self.sheet_manager.read_by_sheet_name(dataset_sheet_name)
            
            if not dataset:
                logger.warning(f"No data found in sheet: {dataset_sheet_name}")
                return []
            
            logger.info(f"Loaded {len(dataset)} training records")
            return dataset
            
        except Exception as e:
            logger.error(f"Failed to load training dataset: {e}")
            raise
    
    def _process_training_dataset(self, config: Dict[str, Any], dataset: List[Dict[str, Any]]) -> bool:
        """处理训练数据集"""
        try:
            if not dataset:
                logger.warning("No training dataset to process")
                return True
            
            # 提取系统指令
            system_instructions = self._extract_system_instructions(dataset)
            
            if system_instructions:
                # 更新AI服务的系统指令
                self._update_system_instructions(config, system_instructions)
            
            # 如果启用自动提交，处理训练提示词
            if config.get('auto_submit'):
                self._process_training_prompts(dataset)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to process training dataset: {e}")
            return False
    
    def _extract_system_instructions(self, dataset: List[Dict[str, Any]]) -> List[str]:
        """从数据集中提取系统指令"""
        system_instructions = []
        
        try:
            for record in dataset:
                # 查找系统指令相关字段
                instruction = None
                
                # 尝试不同的字段名
                for field_name in ['system_instruction', 'instruction', 'system_prompt', 'prompt_system']:
                    if field_name in record and record[field_name]:
                        instruction = record[field_name].strip()
                        break
                
                if instruction and instruction not in system_instructions:
                    system_instructions.append(instruction)
            
            logger.info(f"Extracted {len(system_instructions)} system instructions")
            return system_instructions
            
        except Exception as e:
            logger.error(f"Failed to extract system instructions: {e}")
            return []
    
    def _update_system_instructions(self, config: Dict[str, Any], instructions: List[str]):
        """更新系统指令"""
        try:
            mode = config.get('system_instruction_mode', 'update')
            
            if mode == 'update':
                # 替换现有系统指令
                logger.info("Updating system instructions (replace mode)")
                self.ai_service.update_system_instruction(instructions)
            elif mode == 'append':
                # 追加到现有系统指令
                logger.info("Updating system instructions (append mode)")
                # 这里需要先获取现有指令，然后追加
                # 简化实现：直接更新
                self.ai_service.update_system_instruction(instructions)
            
            logger.info(f"System instructions updated with {len(instructions)} instructions")
            
        except Exception as e:
            logger.error(f"Failed to update system instructions: {e}")
            raise
    
    def _process_training_prompts(self, dataset: List[Dict[str, Any]]):
        """处理训练提示词（如果启用自动提交）"""
        try:
            logger.info("Processing training prompts with auto-submit")
            
            processed_count = 0
            
            for record in dataset:
                # 查找提示词字段
                prompt = None
                
                for field_name in ['prompt', 'question', 'input', 'text']:
                    if field_name in record and record[field_name]:
                        prompt = record[field_name].strip()
                        break
                
                if prompt:
                    try:
                        # 发送提示词到AI服务
                        response = self.ai_service.generate_response(prompt)
                        
                        if not response.startswith("Error"):
                            processed_count += 1
                            logger.debug(f"Processed training prompt: {prompt[:50]}...")
                        else:
                            logger.warning(f"Failed to process training prompt: {response}")
                            
                    except Exception as e:
                        logger.warning(f"Error processing training prompt: {e}")
            
            logger.info(f"Processed {processed_count} training prompts")
            
        except Exception as e:
            logger.error(f"Failed to process training prompts: {e}")
