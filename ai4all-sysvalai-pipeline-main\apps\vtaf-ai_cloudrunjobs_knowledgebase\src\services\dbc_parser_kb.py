import json
import logging
import os

import cantools

from configs.env import settings
from services.vtaf_api_clients import VtafApiClients

logger = logging.getLogger(__name__)


def load_dbc(project_detail: dict):
    try:
        dbc_file = os.path.join(settings.SOURCE_PATH, settings.FILE_NAME)
        db = cantools.database.load_file(dbc_file)
    except FileNotFoundError:
        logger.exception(f"The file '{dbc_file}' does not exist.")
        return f"The file '{dbc_file}' does not exist."
    except cantools.database.errors.ParseError as e:
        logger.exception(f"Failed to parse DBC file '{dbc_file}': {e}")
        return f"Failed to parse DBC file '{dbc_file}': {e}"
    except Exception as e:
        logger.exception(f"Unexpected error loading DBC file: {e}")
        return f"Unexpected error loading DBC file: {e}"

    try:
        messages = []
        for message in db.messages:
            msg_signals = [
                {
                    'name': sig.name,
                    'start_bit': sig.start,
                    'length': sig.length,
                    'scale': sig.scale,
                    'offset': sig.offset,
                    'unit': sig.unit or '',
                    'message_name': message.name
                }
                for sig in message.signals
            ]
            try:

                messages.append(json.dumps({
                    'name': message.name,
                    'id': message.frame_id,
                    'signals': msg_signals
                }, indent=4))
            except TypeError as e:
                logger.exception(f"Error serializing data: {e}")
                continue
    except Exception as e:
        logger.exception(f"Error extracting DBC contents: {e}")
        return f"Error extracting DBC contents: {e}"

    try:
        vtaf_apis = VtafApiClients()
        unhealthy_services = vtaf_apis.check_health("agents")
        if not unhealthy_services:
            logger.error(f"Health check failed for: {', '.join(unhealthy_services)}. Job aborted.")
            return f"Health check failed for: {', '.join(unhealthy_services)}. Job aborted."

        if messages:
            vtaf_apis.add_chunks_to_vector_db(messages, project_detail)

    except Exception as e:
        logger.exception(f"Error during service communication: {e}")
        return f"Error during service communication: {e}"

    logger.info("Job completed successfully.")
    return None
