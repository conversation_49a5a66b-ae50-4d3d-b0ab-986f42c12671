import base64
import requests
import logging

from config import API_BASE

def encode_file_to_base64(file_path: str) -> str:
    with open(file_path, "rb") as file:
        return base64.b64encode(file.read()).decode("utf-8")

def parse_file(file_path: str, headers: dict) -> list:
    file_content = encode_file_to_base64(file_path)
    url = f"{API_BASE}/v1/parsing/single-file-parsing"
    payload = {
        "file_name": file_path,
        "file_content": file_content,
        "parser": "auto",
        "output_format": ["text"]
    }

    try:
        response = requests.post(url, headers=headers, json=payload)
        if response.status_code == 200:
            logging.info("File parsed successfully.")
            return response.json().get("results", [])
    except Exception as e:
        logging.error(f"Parsing error: {e}")
    
    return []
