from collections.abc import Mapping
from typing import Any, <PERSON>Var, Union, cast

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..types import UNSET, Unset

T = TypeVar("T", bound="SummarizationRequest")


@_attrs_define
class SummarizationRequest:
    """Request schema for summarizing a given text input.

    Attributes:
        text (str): The input text to summarize.
        email (Optional[str]): Optional email address for notifications.
        job_id (Optional[str]): Optional job identifier for tracking.
        enable_tracing (Optional[bool]): Enable or disable tracing for this request.

        Attributes:
            text (Union[Unset, str]): Text content to summarize Default: ''.
            email (Union[None, Unset, str]): Optional email address for notifications
            job_id (Union[None, Unset, str]): Optional job identifier
            enable_tracing (Union[None, Unset, bool]): Optional flag to enable tracing for the request Default: True.
    """

    text: Union[Unset, str] = ""
    email: Union[None, Unset, str] = UNSET
    job_id: Union[None, Unset, str] = UNSET
    enable_tracing: Union[None, Unset, bool] = True
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        text = self.text

        email: Union[None, Unset, str]
        if isinstance(self.email, Unset):
            email = UNSET
        else:
            email = self.email

        job_id: Union[None, Unset, str]
        if isinstance(self.job_id, Unset):
            job_id = UNSET
        else:
            job_id = self.job_id

        enable_tracing: Union[None, Unset, bool]
        if isinstance(self.enable_tracing, Unset):
            enable_tracing = UNSET
        else:
            enable_tracing = self.enable_tracing

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update({})
        if text is not UNSET:
            field_dict["text"] = text
        if email is not UNSET:
            field_dict["email"] = email
        if job_id is not UNSET:
            field_dict["job_id"] = job_id
        if enable_tracing is not UNSET:
            field_dict["enable_tracing"] = enable_tracing

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        d = dict(src_dict)
        text = d.pop("text", UNSET)

        def _parse_email(data: object) -> Union[None, Unset, str]:
            if data is None:
                return data
            if isinstance(data, Unset):
                return data
            return cast(Union[None, Unset, str], data)

        email = _parse_email(d.pop("email", UNSET))

        def _parse_job_id(data: object) -> Union[None, Unset, str]:
            if data is None:
                return data
            if isinstance(data, Unset):
                return data
            return cast(Union[None, Unset, str], data)

        job_id = _parse_job_id(d.pop("job_id", UNSET))

        def _parse_enable_tracing(data: object) -> Union[None, Unset, bool]:
            if data is None:
                return data
            if isinstance(data, Unset):
                return data
            return cast(Union[None, Unset, bool], data)

        enable_tracing = _parse_enable_tracing(d.pop("enable_tracing", UNSET))

        summarization_request = cls(
            text=text,
            email=email,
            job_id=job_id,
            enable_tracing=enable_tracing,
        )

        summarization_request.additional_properties = d
        return summarization_request

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
