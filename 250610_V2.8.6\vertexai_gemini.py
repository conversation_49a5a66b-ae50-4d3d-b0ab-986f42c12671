#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <AUTHOR> Wen <PERSON>OU
# @Mail   : *******
# @Time   : 2024/05/27 11:16:39
# @Version: $Id$

import base64
import vertexai
from vertexai.preview import caching

from vertexai.generative_models import (
    GenerationConfig, 
    GenerativeModel, 
    ChatSession, 
    Part, 
    Image, 
    Content,
    SafetySetting,
    HarmCategory,
    HarmBlockThreshold
    )
from google.oauth2 import service_account

from google.api_core.exceptions import ServiceUnavailable,InvalidArgument
import datetime
from datetime import datetime as dt
from datetime import timezone as tz
from datetime import timedelta

# import deepseek_api as ds


service_account_info ={
  "type": "service_account",
  "project_id": "valeo-cp2673-dev",
  "private_key_id": "411461581923b2cbd0298a4ad435f3b295cda5d1",
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  "client_email": "*******",
  "client_id": "112942719303659873152",
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token",
  "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
  "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/************-compute%40developer.gserviceaccount.com",
  "universe_domain": "googleapis.com"
}

Gemini_model_list = [
    "gemini-2.5-pro-preview-05-06",
    "gemini-2.5-flash-preview-05-20",
    # "gemini-2.5-pro-exp-03-25",
    # "gemini-2.0-pro-exp-02-05",
    "gemini-2.0-flash-001",
    "gemini-2.0-flash-lite",
    # "gemini-2.0-flash-thinking-exp-01-21",
    "gemini-1.5-pro-002",  # Text, image, video, and audio
    "gemini-1.5-flash-002",  # pdf, Text, image, video, and audio
    # "gemini-1.0-pro",               # Text  ***The input system_instruction is not supported
    #"gemini-1.0-pro-vision"         # Image and text, ***The input system_instruction is not supported
]

region_list = [
    # "Columbus, Ohio (us-east5)",
    # "Dallas, Texas (us-south1)",
    "Iowa (us-central1)",
    # "Las Vegas, Nevada (us-west4)",
    # "Moncks Corner, South Carolina (us-east1)",
    # "Northern Virginia (us-east4)",
    # "Oregon (us-west1)",
    # "Montréal (northamerica-northeast1)",
    # "São Paulo, Brazil (southamerica-east1)",
    # "Belgium (europe-west1)",
    # "Finland (europe-north1)",
    # "Frankfurt, Germany (europe-west3)",
    # "London, United Kingdom (europe-west2)",
    # "Madrid, Spain (europe-southwest1)",
    # "Milan, Italy (europe-west8)",
    # "Netherlands (europe-west4)",
    # "Paris, France (europe-west9)",
    # "Warsaw, Poland (europe-central2)",
    # "Zürich, Switzerland (europe-west6)",
    # "Hong Kong, China (asia-east2)",
    # "Mumbai, India (asia-south1)",
    # "Seoul, Korea (asia-northeast3)",
    # "Singapore (asia-southeast1)",
    # "Sydney, Australia (australia-southeast1)",
    # "Taiwan (asia-east1)",
    # "Tokyo, Japan (asia-northeast1)",
    # "Dammam, Saudi Arabia (me-central2)",
    # "Doha, Qatar (me-central1)",
    # "Tel Aviv, Israel (me-west1)",
]

chat_model_list = [
    "Multiple rounds of prompt design (chat)",
    "single round (Prompt design)"
]

MIME_TYPE = [
    "application/pdf",
    "audio/mpeg",
    "audio/mp3",
    "audio/wav",
    "image/png",
    "image/jpeg",
    "text/plain",
    "video/mov",
    "video/mpeg",
    "video/mp4",
    "video/mpg",
    "video/avi",
    "video/wmv",
    "video/mpegps",
    "video/flv",
]

# Safety config
safety_config = [
    SafetySetting(
        category=HarmCategory.HARM_CATEGORY_HATE_SPEECH, 
        threshold=HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
    ),
    SafetySetting(
        category=HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT, 
        threshold=HarmBlockThreshold.BLOCK_ONLY_HIGH,
    ),
    SafetySetting(
        category=HarmCategory.HARM_CATEGORY_HARASSMENT, 
        threshold=HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
    ),
    SafetySetting(
        category=HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
        threshold=HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
    ),
]

# credentials = service_account.Credentials.from_service_account_info(
#     service_account_info)
credential = service_account.Credentials.from_service_account_file(
    "./service_account_info.json")

used_model = {"current_model": Gemini_model_list[0]}


# Initialize Vertex AI
vertexai.init(project=credential.project_id,
              location="us-central1", credentials=credential)

def Switch_region(region):
    vertexai.init(project=credential.project_id,
                  location=region, credentials=credential)

# Load the model
model = GenerativeModel(model_name=Gemini_model_list[0], system_instruction=[],safety_settings=safety_config)
chat = model.start_chat(response_validation=False)
config = GenerationConfig(max_output_tokens=2048,
                          temperature=0.5, top_p=1, top_k=32)


def change_model(model_name, system_instruction=[], chat_History=[]):
    global model, chat
    # if model_name == Gemini_model_list[2]:
    #     model = GenerativeModel(model_name=model_name,safety_settings=safety_config)
    #     chat = model.start_chat(
    #         response_validation=False, history=chat_History)
    # elif model_name in Gemini_model_list[:2]:
    #     model = GenerativeModel(model_name=model_name,
    #                             system_instruction=system_instruction,safety_settings=safety_config)
    #     chat = model.start_chat(history=chat_History)
    global used_model
    used_model = {"current_model": model_name}
    if model_name in Gemini_model_list:
        model = GenerativeModel(model_name=model_name,
                                system_instruction=system_instruction,safety_settings=safety_config)
        chat = model.start_chat(history=chat_History)
    elif model_name in ds.deepseek_model_list:
        pass
    else:
        from vertexai.preview.generative_models import GenerativeModel as GM
        cached_content = caching.CachedContent(cached_content_name=model_name)

        model = GM.from_cached_content(cached_content=cached_content)
        chat = model.start_chat()
        return cached_content.update_time,cached_content.expire_time
    
def get_used_model():
    return used_model["current_model"]

def update_system_instruction(system_instruction):
    global model
    model._system_instruction = system_instruction

def update_chat_history(chat_History):
    global chat
    chat._history = chat_History


def change_config(temperature, max_output_tokens):
    global config
    config = GenerationConfig(
        max_output_tokens=max_output_tokens, temperature=temperature, top_p=1, top_k=32)


def count_tokens(text):
    try:
        return model.count_tokens(text).total_tokens
    except Exception as e:
        print("count_tokens:" + str(e))
        return 0


def load_file_to_Part(filePathList, mime_type):
    if mime_type in MIME_TYPE:
        content = []
        for item in filePathList:
            if mime_type == "image/jpeg":
                content.append(Image.load_from_file(item))
            else:
                encoded_file = base64.b64encode(
                    open(item, "rb").read()).decode("utf-8")
                content.append(Part.from_data(
                    data=base64.b64decode(encoded_file), mime_type=mime_type))
        return content
    else:
        return None


def generate_text(prompt):
    response = model.generate_content(prompt, generation_config=config)
    return response.text


def get_chat_response(prompt: str) -> str:
    # text_response = []
    try:
        # responses = chat.send_message(
        #     prompt, stream=True, generation_config=config)
        # for chunk in responses:
        #     text_response.append(chunk.text)
        # return "".join(text_response)
        if get_used_model() in ds.deepseek_model_list:
            if isinstance(prompt, list):
                # return "deepseek model can not use file type prompt"
                responses = ds.get_response_with_file(get_used_model(), prompt[0], prompt[1])
                return responses
            else:
                responses = ds.get_response(get_used_model(), prompt)
                return responses
        else:
            responses = chat.send_message(
                    prompt, stream=False, generation_config=config)
            # print(responses.usage_metadata.total_token_count)
            # print(responses.candidates[0].finish_reason)
            return responses.text
    except ServiceUnavailable as e:
        # 如果出现 ServiceUnavailable 错误，捕获并处理它
        print(f"Service Unavailable error: {e}")
        # print(responses.candidates[0])
        return "The session timed out and disconnected. please submit the question again"
    except Exception as e:
        # 如果出现其他错误，捕获并处理它
        print(f"An error occurred: {e}")
        error = str(e).split("Safety ratings:")[0]
        # print(responses.candidates[0].finish_reason)
        # print(responses.candidates[0].finish_message)
        return f"The session was abnormally, \n{error}"

# print(get_chat_response("hello"))

# def create_cached_chat_session(system_instruction,contents) -> str:
#     cached_content = caching.CachedContent.create(
#         model_name="gemini-1.5-pro-002",
#         system_instruction=system_instruction,
#         contents=contents,
#         ttl=datetime.timedelta(minutes=60),
#         display_name="example-cache",
#     )
#     print(cached_content.name)

# def use_cached_chat_session(cache_id) -> str:
#     # global model,chat
#     from vertexai.preview.generative_models import GenerativeModel
#     cached_content = caching.CachedContent(cached_content_name=cache_id)

#     model = GenerativeModel.from_cached_content(cached_content=cached_content)
#     chat = model.start_chat()

def get_all_cached_sessions():
    cache_list = caching.CachedContent.list()
    # Access individual properties of a CachedContent object
    cacheId_list = {}
    if len(cache_list) == 0:
        # print("No cached content found.")
        return cacheId_list
    else:
        print(f"Found {len(cache_list)} cached content(s):")
        for cached_content in cache_list:
            print(f"disply name: {cached_content.display_name}")
            print(f"Cache '{cached_content.name}' for model '{cached_content.model_name}'")
            print(f"Last updated at: {cached_content.update_time}")
            print(f"Expires at: {cached_content.expire_time}")
            cacheId_list[cached_content.display_name] = cached_content.name
        return cacheId_list

def update_cached_session(cache_id, update_hours):
    cached_content = caching.CachedContent(cached_content_name=cache_id)
    # Option1: Update the context cache using TTL (Time to live)
    # cached_content.update(ttl=timedelta(hours=update_hours))
    # cached_content.refresh()

    # Option2: Update the context cache using specific time
    next_week_utc = dt.now(tz.utc) + timedelta(hours=update_hours)
    cached_content.update(expire_time=next_week_utc)
    cached_content.refresh()

    print(cached_content.expire_time)
    return cached_content.expire_time

def delete_cached_session(cache_id):
    cached_content = caching.CachedContent(cached_content_name=cache_id)
    cached_content.delete()
    print(f"Cache '{cached_content.name}' has been deleted.")
