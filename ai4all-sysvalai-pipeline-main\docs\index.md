# Welcome to VTAF Keyword Documnetation

**About VTAF**

Valeo Test Automation Framework is developed based on keyword driven testing. The test cases are placed in Doors using simple syncing mechanism, 

VTAF automatically generates test sequences from these keywords & Maintaining linkage to requirements.

On change of the requirements test sequence automatically updates.


## VTAF Architrcture

**Keyword driven Architecture**

![Alt text](icons/keyword_driven.png "a title")

[API Reference](../apps/vtaf-ai_cloudrun_api/api-reference/)
