import { ref } from "vue";
import { defineStore } from "pinia";
import { Notify, Loading } from "quasar";
import { ai_testcaseApi } from "src/boot/axios";

export const useProfileManagementStore = defineStore(
  "profile_management_store",
  () => {
    const user_email = ref("");
    const valid_user_for_create_knowledge_profile = ref(null);
    const pl_champion = ref([]);
    const profile_bg = ref("");
    const profile_pg = ref("");
    const profile_pl = ref("");
    const profile_selected_org_uuid = ref("");
    const profile_orgInputs = ref([]);
    const profile_functionality_inputs = ref([]);
    const profile_functions_dropdown = ref([]);

    const profile_selectedFunctionality = ref("");
    const profile_selectedFunctionalityUuid = ref("");

    const profile_algorithms_and_sensor_inputs = ref([]);
    const profile_algorithm_dropdown = ref([]);
    const profile_sensor_dropdown = ref([]);

    const profile_selectedSensors = ref({});
    const profile_selectedAlgorithms = ref({});

    const profile_projects_inputs = ref([]);
    const profile_projects_dropdown = ref([]);
    const profile_selected_project = ref("");
    const profile_selected_project_uuid = ref("");

    const profile_project_variants_inputs = ref([]);
    const profile_project_variants_dropdown = ref([]);
    const profile_selected_project_variant = ref("");
    const profile_selected_project_variant_uuid = ref("");

    //////////////////////////////////////////
    const isProfileDialogVisible = ref(false);
    const fetching_data_status = ref("To Fetch");
    const kb_history_table_loading = ref(false);

    const stepper_loading = ref(false);
    const initial_loading = ref(false);
    const create_profile_project_variant_loading = ref(false);
    const email_check_loading = ref(false);

    const panel = ref("create_project_profile");

    async function get_user_email() {
      try {
        const response = await ai_testcaseApi.get(`/user/get_user`);
        if (response.status === 200) {
          // Success
          const data = response.data.data;
          user_email.value = data;
          console.log(data);
        } else {
          handleError(response);
        }
      } catch (err) {
        console.log(err);
      }
    }

    async function is_user_valid_for_knowledge_profile_creation() {
      try {
        email_check_loading.value = true;
        fetching_data_status.value = "Fetching data...";
        const formData = new FormData();
        formData.append("email", user_email.value);
        formData.append("bg", profile_bg.value);
        formData.append("pg", profile_pg.value);
        formData.append("pl", profile_pl.value);
        const response = await ai_testcaseApi.post(
          `/profile/valid_user_knowledge_profile`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
        if (response.status == 200) {
          console.log("created");
          valid_user_for_create_knowledge_profile.value = response.data;

          email_check_loading.value = false;
        } else {
          throw new Error("Upload failed");
        }
      } catch (err) {
        handleFetchError(err);
      } finally {
        email_check_loading.value = false;
      }
    }

    async function get_champion() {
      try {
        email_check_loading.value = true;
        const formData = new FormData();
        formData.append("bg", profile_bg.value);
        formData.append("pg", profile_pg.value);
        formData.append("pl", profile_pl.value);
        const response = await ai_testcaseApi.post(
          `/profile/fetch_champions`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
        if (response.status == 200) {
          console.log("fetched");
          if (response.data) {
            pl_champion.value = response.data;
            isProfileDialogVisible.value = true;
          }
          email_check_loading.value = false;
        } else {
          throw new Error("Upload failed");
        }
      } catch (err) {
        handleFetchError(err);
      } finally {
        email_check_loading.value = false;
      }
    }

    async function get_org_inputs() {
      try {
        initial_loading.value = true;
        const response = await ai_testcaseApi.get(
          `/profile/get_organization_inputs`
        );
        profile_orgInputs.value = response.data;
      } catch (err) {
        handleFetchError(err);
      } finally {
        initial_loading.value = false;
      }
    }

    async function get_functionality_input() {
      try {
        stepper_loading.value = true;
        const formData = new FormData();
        formData.append("org_uuid", profile_selected_org_uuid.value);
        const response = await ai_testcaseApi.post(
          `/profile/get_functionality_inputs`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
        if (response.status == 200) {
          profile_functionality_inputs.value = response.data;
          profile_functions_dropdown.value =
            profile_functionality_inputs.value.flatMap((item) =>
              item.details.map((detail) => detail.value)
            );
          profile_functions_dropdown.value.sort();
          stepper_loading.value = false;
        } else {
          throw new Error("Upload failed");
        }
      } catch (error) {
        Notify.create({
          color: "negative",
          position: "bottom",
          message: error.message,
          icon: "report_problem",
        });
      } finally {
        fetching_data_status.value = "Completed";
        stepper_loading.value = false;
      }
    }

    async function get_algorithms_and_sensor_input() {
      try {
        stepper_loading.value = true;
        const formData = new FormData();
        formData.append("org_uuid", profile_selected_org_uuid.value);
        const response = await ai_testcaseApi.post(
          `/profile/get_algorithms_and_sensors`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
        if (response.status == 200) {
          profile_algorithms_and_sensor_inputs.value = response.data;
          profile_algorithm_dropdown.value = response.data.algorithms;
          profile_sensor_dropdown.value = response.data.sensors;
          stepper_loading.value = false;
          console.log(profile_algorithm_dropdown.value);
        } else {
          throw new Error("Upload failed");
        }
      } catch (error) {
        Notify.create({
          color: "negative",
          position: "bottom",
          message: error.message,
          icon: "report_problem",
        });
      } finally {
        stepper_loading.value = false;
      }
    }

    async function get_profile_projects() {
      try {
        initial_loading.value = true;
        const response = await ai_testcaseApi.get(
          `/profile/get_profile_projects`
        );
        profile_projects_inputs.value = response.data;
        profile_projects_dropdown.value = profile_projects_inputs.value.map(
          (p) => p.project
        );
        console.log(response.data);
      } catch (err) {
        handleFetchError(err);
      } finally {
        initial_loading.value = false;
      }
    }

    async function get_profile_project_variant() {
      try {
        create_profile_project_variant_loading.value = true;
        const response = await ai_testcaseApi.get(
          `/profile/get_profile_project_variant/${profile_selected_project_uuid.value}`
        );
        profile_project_variants_inputs.value = response.data;
        profile_project_variants_dropdown.value =
          profile_project_variants_inputs.value.map((v) => v.variant);
      } catch (err) {
        handleFetchError(err);
      } finally {
        create_profile_project_variant_loading.value = false;
      }
    }

    async function create_project_profile() {
      try {
        Loading.show();
        // Create FormData object
        const formData = new FormData();
        formData.append("user_email", user_email.value);
        formData.append("bg", profile_bg.value);
        formData.append("pg", profile_pg.value);
        formData.append("pl", profile_pl.value);
        formData.append(
          "features_or_functionality",
          profile_selectedFunctionality.value
        );
        formData.append(
          "features_or_functionality_uuid",
          profile_selectedFunctionalityUuid.value
        );
        formData.append(
          "selected_profile_project",
          profile_selected_project.value
        );
        formData.append(
          "selected_profile_project_uuid",
          profile_selected_project_uuid.value
        );
        formData.append(
          "selected_profile_project_variant",
          profile_selected_project_variant.value
        );
        formData.append(
          "selected_profile_project_variant_uuid",
          profile_selected_project_variant_uuid.value
        );
        // formData.append(
        //   "selected_algorithms",
        //   profile_selectedAlgorithms.value
        // );
        // formData.append("selected_inputs", profile_selectedInputs.value);

        // Ensure selected_algorithms and selected_inputs are JSON strings
        formData.append(
          "selected_algorithms",
          JSON.stringify(profile_selectedAlgorithms.value || [])
        );
        formData.append(
          "selected_inputs",
          JSON.stringify(profile_selectedSensors.value || [])
        );

        const response = await ai_testcaseApi.post(
          `/profile/create_project_profile`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
        if (response.data.status === "success") {
          console.log("created");
          Notify.create({
            color: "positive",
            position: "top",
            message: `Profile Created Successfully!!!`,
            icon: "check_circle_outline",
          });
          profile_bg.value = "";
          profile_pg.value = "";
          profile_pl.value = "";
          profile_selectedFunctionality.value = "";
          profile_selectedFunctionalityUuid.value = "";
          profile_selected_project.value = "";
          profile_selected_project_uuid.value = "";
          profile_selected_project_variant.value = "";
          profile_selected_project_variant_uuid.value = "";
          Loading.hide();
        } else if (response.data.status === "failed") {
          Notify.create({
            color: "warning",
            position: "top",
            message: response.data.message,
            icon: "warning",
            textColor: "black",
          });
          profile_bg.value = "";
          profile_pg.value = "";
          profile_pl.value = "";
          profile_selectedFunctionality.value = "";
          profile_selectedFunctionalityUuid.value = "";
          profile_selected_project.value = "";
          profile_selected_project_uuid.value = "";
          profile_selected_project_variant.value = "";
          profile_selected_project_variant_uuid.value = "";
          Loading.hide();
        } else {
          throw new Error("Upload failed");
        }
      } catch (err) {
        handleFetchError(err);
      } finally {
        Loading.hide();
      }
    }

    function handleFetchError(err) {
      if (err.response) {
        const errorMessage = err.response
          ? err.response.data.detail
          : err.message;
        const errorStatus = err.response.status;
        Notify.create({
          color: "negative",
          position: "bottom",
          message: `${errorStatus} : ${errorMessage}`,
          icon: "report_problem",
        });
      } else {
        Notify.create({
          color: "negative",
          position: "bottom",
          message: err.message,
          icon: "report_problem",
        });
      }
    }

    return {
      user_email,
      profile_orgInputs,
      pl_champion,
      profile_bg,
      panel,
      profile_pg,
      profile_pl,
      profile_selected_org_uuid,
      profile_functionality_inputs,
      profile_functions_dropdown,
      profile_selectedFunctionality,
      profile_selectedFunctionalityUuid,
      profile_algorithms_and_sensor_inputs,
      profile_algorithm_dropdown,
      profile_sensor_dropdown,
      profile_selectedSensors,
      profile_selectedAlgorithms,
      profile_projects_inputs,
      profile_projects_dropdown,
      profile_selected_project,
      profile_selected_project_uuid,
      profile_project_variants_inputs,
      profile_project_variants_dropdown,
      profile_selected_project_variant,
      profile_selected_project_variant_uuid,
      valid_user_for_create_knowledge_profile,
      isProfileDialogVisible,
      fetching_data_status,
      stepper_loading,
      initial_loading,
      create_profile_project_variant_loading,
      email_check_loading,
      get_user_email,
      is_user_valid_for_knowledge_profile_creation,
      get_champion,
      get_org_inputs,
      get_functionality_input,
      get_algorithms_and_sensor_input,
      get_profile_projects,
      get_profile_project_variant,
      create_project_profile,
    };
  }
);
