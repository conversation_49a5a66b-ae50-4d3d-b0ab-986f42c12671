# File: qdrant_requests.py

from pydantic import BaseModel
from typing import Dict, Any, Optional

class AddDocumentRequest(BaseModel):
    content: str
    metadata: Dict[str, Any]
    collection_name: str


class VectorSearchRequest(BaseModel):
    query: str
    collection_name: str
    limit: int = 3
    score_threshold: Optional[float] = 0.8
    filter: Optional[Dict[str, Any]] = None

class CountDocumentsRequest(BaseModel):
    collection_name: str
    filter: Optional[Dict[str, Any]] = None