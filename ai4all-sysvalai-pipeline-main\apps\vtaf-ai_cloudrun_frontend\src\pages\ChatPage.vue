<template>
  <chat-functions-input></chat-functions-input>
</template>

<script setup>
import ChatFunctionsInput from "src/components/Chat/ChatFunctionsInput.vue";
import { useHomeStore } from "src/stores/home/<USER>";
import { storeToRefs } from "pinia";
import { useChatStore } from "src/stores/chat/chat_store";
import { onMounted } from "vue";

const home_store_config = useHomeStore();
const { main_email, first_letter_email } = storeToRefs(home_store_config);

const chat_store = useChatStore();
const { user_email } = storeToRefs(chat_store);
const { get_user_email } = chat_store;

onMounted(async () => {
  await get_user_email();
  main_email.value = user_email.value;
  first_letter_email.value = main_email.value.charAt(0).toUpperCase();
});
</script>

<style>
table {
  border-collapse: collapse;
  width: 100%;
  margin-top: 10px;
  margin-bottom: 10px;
}

table,
th,
td {
  border: 1px solid #ccc;
}

th,
td {
  padding: 8px;
  text-align: left;
}

th {
  background-color: #f4f4f4;
}
</style>
