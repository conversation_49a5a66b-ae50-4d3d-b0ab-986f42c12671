const routes = [
  {
    path: "/",
    component: () => import("layouts/MainLayout.vue"),
    children: [
      { path: "", component: () => import("src/pages/IndexPage.vue") },
      {
        path: "/community",
        component: () => import("src/pages/CommunityPage.vue"),
      },
      {
        path: "/products",
        component: () => import("src/pages/ProductsPage.vue"),
      },
      {
        path: "/training",
        component: () => import("src/pages/TrainingPage.vue"),
      },
      {
        path: "/idea_exchange",
        component: () => import("src/pages/IdeaExchangePage.vue"),
      },
      // {
      //   path: "/documentation",
      //   component: () => import("pages/DocumentationPage.vue"),
      // },
      {
        path: "/chat",
        component: () => import("pages/ChatPage.vue"),
      },
      {
        path: "/chat_v2",
        component: () => import("pages/ChatPageV2.vue"),
      },
      // {
      //   path: "/ai_testcase",
      //   component: () => import("pages/AiTestcasePage.vue"),
      // },
      {
        path: "/ai_testcase",
        component: () => import("pages/AiTestcasePage.vue"),
        children: [
          {
            path: "",
            component: () => import("../components/AI_Testcase/AitcHome.vue"),
          },
          {
            path: "/ai_testcase/create_job",
            component: () => import("../components/AI_Testcase/OrgInput.vue"),
            props: true,
          },
          {
            path: "/ai_testcase/create_job/functionalities",
            component: () =>
              import("../components/AI_Testcase/FuncionalitiesInput.vue"),
            props: true,
          },
          {
            path: "/ai_testcase/create_job/functionalities/file_upload",
            component: () => import("../components/AI_Testcase/FileInput.vue"),
            props: true,
          },
          {
            path: "/ai_testcase/job_status/:execution_id",
            component: () => import("../components/AI_Testcase/JobStatus.vue"),
            props: true,
          },
          {
            path: "/ai_testcase/history",
            component: () => import("../components/AI_Testcase/AllHistory.vue"),
            props: true,
          },
          {
            path: "/ai_testcase/run_project_profile",
            component: () => import("pages/RunKnowledgebaseProfilePage.vue"),
          },
        ],
      },
      {
        path: "/project_profile",
        component: () => import("pages/ProjectProfilePage.vue"),
      },
      {
        path: "/multiprompting",
        component: () => import("pages/MultiPromptingPage.vue"),
      },
      {
        path: "/ai_testscript",
        component: () => import("pages/AiTestscriptPage.vue"),
      },
      {
        path: "/kb",
        component: () => import("pages/ChatWithKnowledgebaseDummyPage.vue"),
      },
      {
        path: "/chat_with_bigquery",
        component: () => import("src/pages/ChatWithBigqueryPage.vue"),
      },
      {
        path: "/chat_with_knowledgebase",
        component: () => import("src/pages/ChatWithKnowledgebasePage.vue"),
        children: [
          {
            path: "/chat_with_knowledgebase/:session_id",
            name: 'chat-session',
            component: () => import('src/components/Chat_With_Kb/ChatInterface.vue'),
            props: true,
          },
        ]
      },
    ],
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: "/:catchAll(.*)*",
    component: () => import("pages/ErrorNotFound.vue"),
  },
];

export default routes;
