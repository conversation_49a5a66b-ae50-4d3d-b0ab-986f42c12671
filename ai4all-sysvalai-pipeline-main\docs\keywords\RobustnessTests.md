# Group {RobustnessTests}
## Stress_Gears

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Stress_Gears.seq*


**Descriptions:** *Changing the gears and checking the view out for each gear activation*


***Command Set:***

	AutomaticGearType
		-AutomaticGearType=Iterations
	ManualGearType
		-ManualGearType=Iterations


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Iterations| String| NA| 1000|  Hold number of test iterations

## Stress_Power

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Stress_Power.seq*


**Descriptions:** *This Keyword will have all the power stress tests*


***Command Set:***

	9V
		-9V=Iterations
	13-5V
		-13-5V=Iterations
	16V
		-16V=Iterations
	Ranges
		-Ranges=Iterations;Volt


***Input Description***



