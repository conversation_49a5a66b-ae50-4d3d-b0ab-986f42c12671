#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script Batch Service - 批量测试脚本生成服务
对应原始ai_generate.py中的do_tc_batch_task()方法
处理批量测试脚本生成任务
"""

import os
from typing import Dict, Any, Optional
from utils.logger import get_logger
from services.ai_service import AIService
from services.sheet_manager import SheetManager
from services.config_service import ConfigurationService

logger = get_logger(__name__)

class TestScriptBatchService:
    """批量测试脚本生成服务"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.sheet_manager = SheetManager()
        self.config_service = ConfigurationService()
        self.case_count = 0
        self.error_count = 0
        
    def execute(self) -> int:
        """执行批量测试脚本生成任务"""
        try:
            logger.info("🚀 Starting test script batch job")
            
            # 获取配置
            config = self._get_config()
            self._validate_config(config)
            
            # 初始化头文件数据
            head_files = self._load_head_files(config)
            head_file_prompt = config.get('head_file_prompt', '')
            
            # 连接到表格
            if not self.sheet_manager.connect_sheet(config['prompt_sheet']):
                raise Exception("Failed to connect to prompt sheet")
            
            # 处理批量测试脚本生成
            self._process_batch_scripts(config, head_files, head_file_prompt)
            
            logger.info(f"✅ Test script batch completed. Cases: {self.case_count}, Errors: {self.error_count}")
            return 0
            
        except Exception as e:
            logger.exception(f"Test script batch job failed: {e}")
            return 1
    
    def _get_config(self) -> Dict[str, Any]:
        """获取配置"""
        config = {}
        
        # 从环境变量获取
        config['prompt_sheet'] = os.getenv('PROMPT_SHEET_URL')
        config['test_spec_sheet'] = os.getenv('TEST_SPEC_SHEET_URL')
        config['head_files_config'] = os.getenv('HEAD_FILES_CONFIG')  # JSON格式的头文件配置
        config['head_file_prompt'] = os.getenv('HEAD_FILE_PROMPT', '')
        config['model'] = os.getenv('AI_MODEL', 'gemini-1.5-flash')
        
        # 如果环境变量中没有配置，尝试从配置文件读取
        if not config['prompt_sheet'] or not config['test_spec_sheet']:
            try:
                if self.config_service.config_exists():
                    sheet_config = self.config_service.get_sheet_config_for_batch_task()
                    if 'error' not in sheet_config:
                        config.update(sheet_config)
                        logger.info("Configuration loaded from config.ini")
            except Exception as e:
                logger.error(f"Failed to read config file: {e}")
        
        return config
    
    def _validate_config(self, config: Dict[str, Any]):
        """验证配置"""
        required_fields = ['prompt_sheet', 'test_spec_sheet']
        missing_fields = []
        
        for field in required_fields:
            if not config.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            raise ValueError(f"Missing required configuration for test script batch: {missing_fields}")
        
        logger.info("Test script batch configuration validation passed")
    
    def _load_head_files(self, config: Dict[str, Any]) -> Dict[str, str]:
        """加载头文件配置"""
        head_files = {}
        
        try:
            # 从环境变量加载头文件配置
            head_files_config = config.get('head_files_config')
            if head_files_config:
                import json
                head_files = json.loads(head_files_config)
                logger.info(f"Loaded {len(head_files)} head files from config")
            else:
                logger.info("No head files configuration found, using empty config")
                
        except Exception as e:
            logger.warning(f"Failed to load head files config: {e}")
            
        return head_files
    
    def _process_batch_scripts(self, config: Dict[str, Any], head_files: Dict[str, str], head_file_prompt: str):
        """处理批量测试脚本生成"""
        # 获取所有工作表名称
        sheet_names = self.sheet_manager.get_sheet_names()
        
        last_module = None
        capl_contents = {}
        
        for sheet_name in sheet_names:
            if not sheet_name.startswith("Prompt_"):
                continue
                
            logger.info(f"Processing sheet: {sheet_name}")
            
            # 读取提示词数据
            prompts = self.sheet_manager.read_by_sheet_name(sheet_name)
            
            for prompt_data in prompts:
                try:
                    category = prompt_data.get('category', sheet_name)
                    prompt_text = prompt_data.get('prompt', '')
                    
                    if not prompt_text:
                        continue
                    
                    # 处理模块切换
                    module = self._extract_module_from_category(category)
                    if module != last_module:
                        last_module = module
                        # 处理头文件
                        self._process_head_file(module, head_files, head_file_prompt)
                    
                    # 生成测试脚本
                    response = self.ai_service.generate_response(prompt_text)
                    
                    if response.startswith("Error"):
                        self.error_count += 1
                        logger.error(f"Failed to generate script for prompt: {response}")
                        continue
                    
                    # 保存结果
                    self._save_script_result(prompt_data, response, capl_contents)
                    self.case_count += 1
                    
                except Exception as e:
                    self.error_count += 1
                    logger.error(f"Error processing prompt: {e}")
    
    def _extract_module_from_category(self, category: str) -> str:
        """从类别中提取模块名"""
        try:
            if "_" in category:
                return category.split("_")[1]
            return category
        except:
            return "default"
    
    def _process_head_file(self, module: str, head_files: Dict[str, str], head_file_prompt: str):
        """处理头文件"""
        try:
            head_file = head_files.get(module)
            if head_file:
                logger.info(f"Processing head file for module: {module}")
                # 这里可以添加头文件处理逻辑
                # 例如：将头文件内容添加到AI提示词中
                if head_file_prompt:
                    combined_prompt = f"{head_file}\n{head_file_prompt}"
                    self.ai_service.generate_response(combined_prompt)
        except Exception as e:
            logger.warning(f"Failed to process head file for module {module}: {e}")
    
    def _save_script_result(self, prompt_data: Dict[str, Any], response: str, capl_contents: Dict[str, str]):
        """保存脚本生成结果"""
        try:
            # 这里可以添加结果保存逻辑
            # 例如：解析CAPL脚本，保存到文件或数据库
            logger.info(f"Generated script for prompt: {prompt_data.get('category', 'Unknown')}")
            
            # 简单的结果记录
            category = prompt_data.get('category', 'Unknown')
            capl_contents[category] = response
            
        except Exception as e:
            logger.error(f"Failed to save script result: {e}")
