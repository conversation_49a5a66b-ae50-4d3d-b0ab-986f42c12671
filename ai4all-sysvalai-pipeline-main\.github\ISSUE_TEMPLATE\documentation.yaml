name: 📓 Documentation Request
description: Request new documentation or improvements to existing docs
title: "[DOCS] "
labels: ["documentation"]

body:
  - type: markdown
    attributes:
      value: |
        Thank you for helping us improve our documentation! Please fill out the form below.

  - type: dropdown
    id: request-type
    attributes:
      label: Request Type
      description: What kind of documentation change are you requesting?
      options:
        - 📚 New Documentation
        - ✏️ Update/Improve Existing Documentation
        - ❌ Remove Outdated Documentation
        - 🔎 Clarification Needed
        - 🔗 Broken Link
        - 🐞 Other Issue
    validations:
      required: true
    
  - type: input
    id: document-title
    attributes:
      label: Document Title/Section
      description: Title of the document or the specific section you're referring to (if applicable).
      placeholder: E.g., "User Guide - Installation"

  - type: textarea
    id: description
    attributes:
      label: Detailed Description
      description: Explain the issue or the missing information in detail. 
      placeholder: |
        E.g., "The installation instructions are missing steps for..." 
        or "The section on X is unclear because..."
    validations:
      required: true
      
  - type: textarea
    id: suggestions
    attributes:
      label: Suggestions (Optional)
      description: Offer any suggestions on how to improve the documentation.
      placeholder: "Consider adding examples, diagrams, or a troubleshooting guide."
  
  - type: dropdown
    id: doc-size
    attributes:
      label: Documentation Size
      description: |
        Estimate the size of fixing the bug.
        * **⚪ Not Defined:** I'm not sure of this info.
        * **🔵 XS:** Very small, <= 1 day. (2 Points)
        * **🟢 S** Small, ~ 2-3 days. (5 Points)
        * **🟡 M** Medium, <= 1 week. (10 Points)
        * **🟠 L** Large, ~ 7-8 days. (15 Points)
        * **🔴 XL:** Very large, <= 2 weeks. (20 Points)
      options:
        - ⚪ Not Defined
        - 🔵 XS (2 Points)
        - 🟢 S (5 Points)
        - 🟡 M (10 Points)
        - 🟠 L (15 Points)
        - 🔴 XL (20 Points)
    validations:
      required: true

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context (Optional)
      description: Provide any additional context, screenshots, or links that might be helpful.
