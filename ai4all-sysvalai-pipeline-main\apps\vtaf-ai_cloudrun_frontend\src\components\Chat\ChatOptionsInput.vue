<template>
  <q-card class="q-pa-md main_card">
    <div class="row">
      <div class="col-5">
        <label class="model_label">Select your language model</label>
        <q-select
          v-model="gemini_version"
          :options="gemini_version_options"
          dense
          outlined
          color="pink"
        />
        <div class="q-pa-md">
          <q-badge color="secondary"> Temperature: {{ temperature }} </q-badge>

          <q-slider
            v-model="temperature"
            :min="0"
            :max="2"
            :step="0.2"
            markers
          />
        </div>
        <div class="q-pa-md">
          <q-badge color="secondary">
            Max Output Tokens: {{ output_tokens }}
          </q-badge>

          <q-slider
            v-model="output_tokens"
            markers
            :min="0"
            :max="8192"
            :step="512"
            color="green"
          />
        </div>
      </div>
      <div class="col-7">
        <q-btn-toggle
          v-model="chat_type"
          style="margin-left: 5%; margin-bottom: 1rem"
          no-caps
          rounded
          unelevated
          toggle-color="positive"
          color="white"
          text-color="positive"
          :options="[
            { label: 'Single Chat', value: 'single' },
            { label: 'Multi Chat', value: 'multi' },
          ]"
        />
        <q-btn
          v-if="chat_type === 'multi' && messages.length > 0"
          color="positive"
          icon="close"
          label="Clear"
          style="margin-bottom: 1rem; margin-left: 3rem"
          @click="clearConversation"
        />
        <q-file
          outlined
          v-model="uploaded_prompt_file"
          label="upload prompt"
          style="margin-left: 5%"
        >
          <template v-slot:append>
            <q-icon name="upload"></q-icon>
          </template>
        </q-file>
        <q-card
          outlined
          class="q-px-sm"
          style="margin-left: 5%; margin-top: 1rem"
        >
          <q-card-section>
            <div class="label-prompts">Prompts</div>
            <q-input
              v-model="uploaded_prompt"
              filled
              type="textarea"
              autogrow
            />
          </q-card-section>
        </q-card>
      </div>
    </div>
    <div class="row">
      <q-card
        outlined
        class="q-px-sm"
        style="background-color: white; width: 100%; margin-top: 2rem"
      >
        <label class="model_response_label">
          <q-icon name="description"></q-icon>
          Document Chatbot</label
        >
        <!-- If prompt_loading is true, show spinner -->
        <div v-if="prompt_loading" class="q-ml-md">
          <q-spinner-dots color="positive" size="40px" />
        </div>
        <q-card-section>
          <div
            class="q-pa-md row justify-center"
            style="width: 100%; min-height: 35vh"
          >
            <div style="width: 100%; min-height: 35vh">
              <q-chat-message
                v-for="(message, index) in messages"
                :key="index"
                :name="message.name"
                :avatar="message.avatar"
                :text="[message.text]"
                text-html
                :sent="message.sent"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>
  </q-card>
  <div class="input_row">
    <q-card style="width: 100%">
      <div v-if="file_name && chat_type === 'single'" class="file-name">
        Selected File: {{ file_name }}
      </div>
      <q-card-section style="display: flex; align-items: center">
        <q-btn
          v-if="chat_type === 'single'"
          flat
          round
          icon="attach_file"
          @click="selectFile"
          color="positive"
        />
        <q-input
          v-model="prompt"
          type="textarea"
          placeholder="Enter text or file name"
          filled
          @keydown.enter="handleEnter"
          style="flex: 1; margin: 0 8px; overflow-y: auto; max-height: 200px"
          autogrow
        />
        <q-btn flat round icon="send" color="positive" @click="runAction" />
      </q-card-section>
    </q-card>
  </div>
</template>
<script setup>
import { storeToRefs } from "pinia";
import { useChatV2Store } from "src/stores/chat/chat_v2_store";
import { watch } from "vue";

const chat_v2_store = useChatV2Store();
const {
  gemini_version,
  uploaded_prompt_file,
  uploaded_prompt,
  prompt,
  uploaded_file,
  file_name,
  prompt_response,
  messages,
  responseMessage,
  temperature,
  output_tokens,
  chat_type,
  chat_hisory_clear,
  prompt_loading,
} = storeToRefs(chat_v2_store);

const { getPromptResponseStream, getMultiChatResponseStream } = chat_v2_store;

const gemini_version_options = ["gemini-1.5-flash", "gemini-1.5-pro"];
const selectFile = () => {
  const input = document.createElement("input");
  input.type = "file";
  input.onchange = (event) => {
    uploaded_file.value = event.target.files[0];
    if (uploaded_file) {
      file_name.value = uploaded_file.value.name; // Display the file name in the input box
    }
  };
  input.click(); // Programmatically click the input to open the file dialog
};

const runAction = async () => {
  if (chat_type.value == "single") {
    prompt_response.value = "";
    messages.value = [];
    responseMessage.value.text = "";
    prompt_loading.value = true;
    // Add user's prompt message
    messages.value.push({
      name: "me",
      avatar: "/src/assets/chat/user.png",
      text: prompt.value.replace(/\n/g, "<br>"),
      sent: true,
    });

    // Create a new response message object
    const newResponseMessage = {
      name: "VTAF.AI",
      avatar: "/src/assets/home/<USER>",
      text: "",
      sent: false,
    };

    // Add the new response message to the chat
    messages.value.push(newResponseMessage);

    // Set the responseMessage reference to the new object
    responseMessage.value = newResponseMessage;

    console.log("start_pushing");

    // Call the function to get the response with streaming
    await getPromptResponseStream();

    // Clear the prompt input
    prompt.value = "";
    file_name.value = "";
    uploaded_file.value = "";
  } else {
    prompt_response.value = "";

    // Add user's prompt message
    messages.value.push({
      name: "me",
      avatar: "https://cdn.quasar.dev/img/avatar1.jpg",
      text: prompt.value.replace(/\n/g, "<br>"),
      sent: true,
    });

    // Create a new response message object
    const newResponseMessage = {
      name: "VTAF.AI",
      avatar: "/src/assets/home/<USER>",
      text: "",
      sent: false,
    };

    // Add the new response message to the chat
    messages.value.push(newResponseMessage);

    // Set the responseMessage reference to the new object
    responseMessage.value = newResponseMessage;

    // Call the function to get the response with streaming
    await getMultiChatResponseStream();

    // Clear the prompt input
    prompt.value = "";
  }
};

const handleEnter = (event) => {
  console.log(event);

  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    runAction();
  }
};
const clearConversation = () => {
  chat_hisory_clear.value = true;
  messages.value = [];
};

// Watch for changes on chat_type
watch(chat_type, (newValue) => {
  // Clear messages whenever chat_type changes
  messages.value = [];
  console.log(`Switched to ${newValue} mode, messages cleared.`);
});
</script>
<style scoped>
.main_card {
  width: 90%;
  margin: auto;
  margin-top: 2rem;
  margin-bottom: 120px;
}
.model_label {
  display: block;
  margin-bottom: 1rem;
  background: rgb(186, 243, 186);
  padding: 5px;
  color: green;
  font-weight: bold;
}
.model_response_label {
  color: green;
  font-weight: bold;
  background: rgb(186, 243, 186);
  padding: 5px;
}
.chat-content {
  padding: 0.5em;
  background-color: #f4f4f8;
  border-radius: 5px;
  margin-bottom: 0.5em;
}
.label-prompts {
  margin-bottom: 1rem;
}
.input_row {
  display: flex;
  justify-content: center;
  margin: auto;
  margin-top: 1rem;
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 1000; /* Make sure it's above other elements */
}
.file-name {
  margin-left: 2rem;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-weight: bold;
  color: #4caf50;
}
</style>
