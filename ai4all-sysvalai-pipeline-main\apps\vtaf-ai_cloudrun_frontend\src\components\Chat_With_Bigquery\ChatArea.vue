<template>
  <q-page class="q-pa-md flex column justify-start">
    <div class="column q-gutter-md">

      <!-- User Prompt -->
      <div v-if="userPrompt" class="text-right">
        <q-chat-message
          :text="[userPrompt]"
          sent
          bg-color="light-blue-2"
          text-color="grey-10"
        />
      </div>

      <!-- Generated SQL Query -->
      <div v-if="generated_sql" class="q-pa-sm bg-light-green-3 text-grey-9 rounded-borders text-subtitle2">
        <div class="q-pa-md text-md text-bold">Generated Query</div>
        <div class="q-pa-md" style="white-space: pre-line">{{ generated_sql }}</div>
        <q-card class="q-pa-md bg-grey-2 text-grey-9" flat bordered>
          <q-card-section>
            <div style="white-space: pre-line">{{ bigquery_ai_response }}</div>
          </q-card-section>
        </q-card>
      </div>
      <div v-if="response_loading" class="row justify-center q-pa-md">
        <q-spinner-dots color="blue-10" size="30px" />
      </div>
    </div>

    <!-- Chat input -->
    <q-footer class="bg-white q-pa-sm shadow-2" elevated>
      <div class="row items-center">
        <q-input
          class="col-grow q-mr-sm"
          v-model="user_prompt"
          placeholder="Type your message..."
          outlined
          dense
          @keyup.enter="handleSend"
        />
        <q-btn icon="send" color="blue-10" round @click="handleSend" :disable="!selected_dataset || !user_prompt" />
      </div>
    </q-footer>
  </q-page>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useChatWithBigqueryStore } from "src/stores/chat/chat_with_bigquery-store";
import { storeToRefs } from "pinia";
import { QSpinnerDots } from 'quasar'

const chat_with_bigquery_config = useChatWithBigqueryStore();
const {
  selected_dataset,
  generated_sql,
  user_prompt,
  userPrompt,
  bigquery_ai_response,
  response_loading
} = storeToRefs(chat_with_bigquery_config);
const {
  query_bigquery
} = chat_with_bigquery_config;

async function handleSend() {
  if(selected_dataset.value=="" || selected_dataset.value==null || user_prompt.value==""){
      window.alert("please give all the details : Dataset, Prompt");
      return;
  }

  userPrompt.value = user_prompt.value;
  bigquery_ai_response.value = '';
  generated_sql.value = '';
  await query_bigquery();
  user_prompt.value = '';
}
</script>

<style scoped>
.q-page {
  overflow: hidden;
}
.q-footer {
  position: fixed;
  bottom: 0;
  width: 100%;
}
</style>
