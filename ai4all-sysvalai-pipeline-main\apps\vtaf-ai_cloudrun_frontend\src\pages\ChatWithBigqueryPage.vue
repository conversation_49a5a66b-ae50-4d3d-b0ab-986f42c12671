<template>
  <div class="q-mt-md" style="max-width: 70%; margin-left: 15%;border: 1px solid #ccc; border-radius: 8px; padding: 16px;">
    <div class="q-pa-md text-h6 text-blue-10">
      Chat With Bigquery
    </div>
    <dataset-lists></dataset-lists>
    <chat-area></chat-area>
  </div>
</template>

<script setup>
import DatasetLists from 'src/components/Chat_With_Bigquery/DatasetLists.vue';
import ChatArea from 'src/components/Chat_With_Bigquery/ChatArea.vue';
import { onMounted } from 'vue';
import { useChatWithBigqueryStore } from "src/stores/chat/chat_with_bigquery-store";
import { useHomeStore } from "src/stores/home/<USER>";
import { storeToRefs } from "pinia";

const chat_with_bigquery_config = useChatWithBigqueryStore();
const { user_email, selected_dataset, user_prompt, bigquery_ai_response, generated_sql, userPrompt} =
  storeToRefs(chat_with_bigquery_config);
const { get_user_email } = chat_with_bigquery_config

const home_store_config = useHomeStore();
const { main_email, first_letter_email } = storeToRefs(home_store_config);

onMounted(async () => {
  selected_dataset.value = "";
  user_prompt.value="";
  bigquery_ai_response.value="";
  generated_sql.value="";
  userPrompt.value="";
  await get_user_email();
  main_email.value = user_email.value;
  first_letter_email.value = main_email.value.charAt(0).toUpperCase();
  });
</script>
