# Group {Chamber Controls}
## Check_ThermalChamber

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Check_ThermalChamber.seq*


**Descriptions:** *Check the temperature of TestEquity E4*


***Command Set:***

	Temperature
		-Temperature=Alias;Condition


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Condition| String| NA| >=9 X <=10 |  Provide the condition for the reading comparision
X==10
X>=10
>=5 X <=10 | Required

## Set_ThermalChamber

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Set_ThermalChamber.seq*


**Descriptions:** *This keyword contains features to set the TestEquity E4 Thermal Chamber.*


***Command Set:***

	StartProfile
		-StartProfile=Profile_Num
	HoldProfile
		-HoldProfile=
	ResumeProfile
		-ResumeProfile=
	StopChamber
		-StopChamber=
	Temperature
		-Temperature=Temp_DegC
	WaitTill-Temp
		-WaitTill-Temp=Temp_DegC


***Input Description***



