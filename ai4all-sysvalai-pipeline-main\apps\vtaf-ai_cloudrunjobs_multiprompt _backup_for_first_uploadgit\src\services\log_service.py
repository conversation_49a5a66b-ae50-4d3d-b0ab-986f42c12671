from typing import Dict, Any, List
import json
import os
from datetime import datetime
from utils.logger import get_logger

logger = get_logger(__name__)
this_path = os.path.dirname(os.path.abspath(__file__))
path = os.path.normpath(os.path.join(this_path, "..","logs"))
class LogService:
    """日志管理服务"""
    
    def __init__(self):
        self.log_dir = path
        self.ensure_log_dir()
        
    def ensure_log_dir(self):
        """确保日志目录存在"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
            
    def save_log(self, log_data: Dict[str, Any]) -> Dict[str, Any]:
        """保存日志"""
        try:
            # 对应前端 SaveLogButton 功能
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"multiprompt_log_{timestamp}.json"
            filepath = os.path.join(self.log_dir, filename)
            
            # 添加时间戳
            log_data["timestamp"] = timestamp
            log_data["saved_at"] = datetime.now().isoformat()
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, indent=2, ensure_ascii=False)
                
            logger.info(f"Log saved to {filepath}")
            return {
                "status": "success", 
                "message": f"Log saved as {filename}",
                "filename": filename
            }
            
        except Exception as e:
            logger.error(f"Failed to save log: {e}")
            return {"status": "error", "message": str(e)}
            
    def load_log(self, filename: str = None) -> Dict[str, Any]:
        """加载日志"""
        try:
            # 对应前端 LoadLogButton 功能
            if not filename:
                # 获取最新的日志文件
                log_files = [f for f in os.listdir(self.log_dir) if f.endswith('.json')]
                if not log_files:
                    return {"status": "error", "message": "No log files found"}
                filename = sorted(log_files)[-1]
                
            filepath = os.path.join(self.log_dir, filename)
            
            if not os.path.exists(filepath):
                return {"status": "error", "message": f"Log file {filename} not found"}
                
            with open(filepath, 'r', encoding='utf-8') as f:
                log_data = json.load(f)
                
            logger.info(f"Log loaded from {filepath}")
            return {
                "status": "success",
                "message": f"Log loaded from {filename}",
                "data": log_data
            }
            
        except Exception as e:
            logger.error(f"Failed to load log: {e}")
            return {"status": "error", "message": str(e)}
            
    def get_log_list(self) -> Dict[str, Any]:
        """获取日志文件列表"""
        try:
            log_files = [f for f in os.listdir(self.log_dir) if f.endswith('.json')]
            log_files.sort(reverse=True)  # 最新的在前
            
            return {
                "status": "success",
                "files": log_files,
                "count": len(log_files)
            }
            
        except Exception as e:
            logger.error(f"Failed to get log list: {e}")
            return {"status": "error", "message": str(e)}
            
    def clear_logs(self) -> Dict[str, Any]:
        """清除日志"""
        try:
            # 对应前端 ClearButton 功能
            cleared_count = 0
            
            for filename in os.listdir(self.log_dir):
                if filename.endswith('.json'):
                    filepath = os.path.join(self.log_dir, filename)
                    os.remove(filepath)
                    cleared_count += 1
                    
            logger.info(f"Cleared {cleared_count} log files")
            return {
                "status": "success",
                "message": f"Cleared {cleared_count} log files"
            }
            
        except Exception as e:
            logger.error(f"Failed to clear logs: {e}")
            return {"status": "error", "message": str(e)}