from typing import Dict, Any
from threading import Lock
from langchain_community.utilities.sql_database import SQLDatabase
from langchain_community.agent_toolkits.sql.base import create_sql_agent
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from langchain.agents import AgentType
from langchain_google_vertexai import ChatVertexAI
from app.core.constants import PROJECT_ID
from langchain.callbacks.base import BaseCallbackHandler

# Configuration
project = PROJECT_ID
agent_cache: Dict[str, Any] = {}
cache_lock = Lock()

class SQLHandler(BaseCallbackHandler):
    def __init__(self):
        self.sql_result = []

    def on_agent_action(self, action, **kwargs):
        """Run on agent action. if the tool being used is sql_db_query,
         it means we're submitting the sql and we can 
         record it as the final sql"""

        if action.tool in ["sql_db_query_checker","sql_db_query"]:
            self.sql_result.append(action.tool_input)

def get_agent_executor_for_dataset(dataset: str) -> Any:
    with cache_lock:
        if dataset in agent_cache:
            return agent_cache[dataset]

        db = SQLDatabase.from_uri(f"bigquery://{PROJECT_ID}/{dataset}")

        llm = ChatVertexAI(
            model_name="gemini-2.0-flash",
            project=PROJECT_ID,
            temperature=0,
            max_output_tokens=2000,
        )

        toolkit = SQLDatabaseToolkit(db=db, llm=llm)

        agent_executor = create_sql_agent(
            llm=llm,
            toolkit=toolkit,
            verbose=False,
            top_k=1000,
            max_iterations=10,
            agent_type=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
        )

        agent_cache[dataset] = agent_executor
        return agent_executor

def run_query(query: str, dataset: str):
    handler = SQLHandler()
    agent_executor = get_agent_executor_for_dataset(dataset)
    response = agent_executor.invoke({"input": query}, {'callbacks':[handler]})
    response['generated_query'] = handler.sql_result[0]
    return response
