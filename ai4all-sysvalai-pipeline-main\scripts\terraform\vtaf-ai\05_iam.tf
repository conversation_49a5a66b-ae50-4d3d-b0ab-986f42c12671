#----------------------------------------------
#  Service Accounts
#----------------------------------------------
module "service_accounts" {
  source = "git::https://github-ri.vnet.valeo.com/GROUP-AI4ALL/ai4all-infra-terraform-modules.git//modules/management/iam/service-account?ref=main"

  project_id = var.project_id
  service_accounts = [
    {
      name         = var.devops_sa_name
      description  = "Service account for DevOps"
      display_name = "DevOps SA"
    },
    {
      name         = var.github_deployer_sa_name
      description  = "Service account for GitHub Deployer"
      display_name = "GitHub Deployer SA"
    },
    {
      name         = var.fw_dev_sa_name
      description  = "Service account for Framework User"
      display_name = "Framework User SA"
    }
  ]
}

#----------------------------------------------
#  Custom Roles
#----------------------------------------------

module "custom_roles" {
  source     = "git::https://github-ri.vnet.valeo.com/GROUP-AI4ALL/ai4all-infra-terraform-modules//modules/management/iam/custom-role?ref=main"
  depends_on = [module.service_accounts]
  project_id = var.project_id
  custom_roles = [
    {
      target_level = "project"
      target_id    = var.project_id
      role_id      = "vtafai.developer"
      version      = "v1.0"
      title        = "VTAF-AI Developer Role"
      description  = "A custom role for developers"
      stage        = "GA"
      permissions = [
        # Client Auth Config
        "clientauthconfig.clients.get",
        "clientauthconfig.clients.list",
        "oauthconfig.testusers.get",
        # IAM Service Accounts
        "iam.serviceAccounts.getIamPolicy",
        "iam.serviceAccounts.get",
        "iam.serviceAccounts.list",
        # Cloud Scheduler
        "cloudscheduler.jobs.get",
        "cloudscheduler.jobs.create",
        # Compute
        "compute.globalAddresses.get",
        "compute.sslPolicies.get",
        "compute.regionNetworkEndpointGroups.get",
        # Cloud function
        "cloudfunctions.functions.getIamPolicy",
      ]
      base_roles = [
        # Cloud Build
        "roles/serviceusage.serviceUsageConsumer",
        # The Project
        "roles/iam.roleViewer",
        # Serverless
        "roles/run.invoker",
        "roles/cloudfunctions.invoker",
        # Logging
        "roles/monitoring.viewer",
        "roles/logging.viewer",
        # Storage
        "roles/artifactregistry.reader",
        "roles/storage.objectCreator",
        # Datastore
        "roles/datastore.user",
        # IAP
        "roles/iap.httpsResourceAccessor",
        # LB
        "roles/compute.loadBalancerServiceUser",
        # Vertex AI
        "roles/aiplatform.user",
        # Analytics
        "roles/pubsub.subscriber",
        "roles/pubsub.publisher",
        # Cloud Build
        "roles/serviceusage.serviceUsageConsumer",
      ]
      excluded_permissions = [
        "resourcemanager.projects.list",
      ]
      members = []
    },
    {
      target_level = "project"
      target_id    = var.project_id
      role_id      = "vtafai.deployer"
      version      = "v1.0"
      title        = "VTAF-AI Deployer Role"
      description  = "A custom role for deployments"
      stage        = "GA"
      inherit_from = [
        "vtafai.developer"
      ]
      inherit_exclusion = true
      permissions = [
        # Client Auth Brand
        "clientauthconfig.brands.list",
        "clientauthconfig.brands.get",
        "clientauthconfig.brands.create",
        "clientauthconfig.brands.update",
        # Client Auth Config
        "clientauthconfig.clients.listWithSecrets",
        "clientauthconfig.clients.getWithSecret",
        "clientauthconfig.clients.update",
        "clientauthconfig.clients.create",
        "oauthconfig.verification.get",
        "oauthconfig.verification.update",
        "oauthconfig.testusers.update",
        # Resource Manager
        "resourcemanager.projects.getIamPolicy",
        # Compute LB
        "compute.sslPolicies.create",
        "compute.regionNetworkEndpointGroups.create",
        "compute.instanceGroupManagers.list",
        "compute.regions.list",
        "compute.zones.list",
        # Serverless
        "run.services.setIamPolicy",
        "run.jobs.setIamPolicy",
        "cloudfunctions.functions.setIamPolicy",
      ]
      base_roles = [
        # Serverless
        "roles/run.developer",
        "roles/cloudfunctions.developer",
        "roles/serviceusage.serviceUsageConsumer",
        # Storage
        "roles/artifactregistry.writer",
        "roles/storage.admin",
        # Datastore
        "roles/datastore.owner",
        # LB
        "roles/compute.loadBalancerAdmin",
        # IAP
        "roles/iap.admin",
        # Analytics
        "roles/pubsub.admin",
      ]
      excluded_permissions = [
        "storage.buckets.delete",
        "datastore.databases.delete",
        "datastore.backups.delete",
      ]
      members = []
    },
    {
      target_level      = "project"
      target_id         = var.project_id
      role_id           = "vtafai.endusers"
      version           = "v1.0"
      title             = "VTAF-AI End Users Role"
      description       = "A custom role for end users"
      stage             = "GA"
      inherit_exclusion = true
      permissions       = []
      base_roles = [
        # IAP
        "roles/iap.httpsResourceAccessor",
      ]
      excluded_permissions = []
      members              = []
    }
  ]
}


#----------------------------------------------
#  Roles Binding
#----------------------------------------------

resource "google_project_iam_binding" "roles_bindings" {
  depends_on = [module.custom_roles]
  for_each   = var.roles_bindings
  project    = var.project_id
  role       = "projects/${var.project_id}/roles/${each.key}"
  members    = each.value
}

#----------------------------------------------
#  SA Binding
#----------------------------------------------

resource "google_service_account_iam_binding" "sa_binding" {
  depends_on = [google_project_iam_binding.roles_bindings]

  for_each = var.sa_bindings

  service_account_id = each.key != "" ? "projects/${var.project_id}/serviceAccounts/${each.key}" : null
  role               = each.value.role
  members            = each.value.members
}