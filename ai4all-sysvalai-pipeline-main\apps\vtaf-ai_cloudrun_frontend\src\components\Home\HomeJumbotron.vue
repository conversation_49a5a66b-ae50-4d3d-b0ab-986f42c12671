<template>
  <!-- 'linear-gradient(to bottom, #228fc9,  #FFFFFF)', -->
  <div
    class="row"
    :style="{
      height: $q.screen.gt.sm ? '500px' : '600px',
      background: $q.screen.gt.sm
        ? 'linear-gradient(to bottom, #014a88, #000000)'
        : 'linear-gradient(to bottom, #014a88,  #000000)',
    }"
  >
    <div class="col" style="display: flex; align-items: center">
      <div
        style="
          text-align: center;
          max-width: 650px;
          margin-left: auto;
          margin-right: auto;
        "
      >
        <!-- big screen - laptop -->
        <div
          class="text-h3 text-bold text-white q-pb-lg q-pl-lg gt-sm animate-text heading_animation"
          style="text-align: left"
        >
          <!-- <q-img
            src="src/assets/home/<USER>"
            style="max-width: 20%; margin-right: 1rem"
          ></q-img> -->
          <p style="font-size: 90px">
            VTAF<span style="font-size: 140px">.AI</span>
          </p>
        </div>
        <!-- medium screen - laptop -->
        <div
          class="text-h3 text-bold text-white q-pb-lg q-pl-lg lt-md animate-text heading_animation"
          :style="{ textAlign: $q.screen.gt.sm ? 'left' : 'left' }"
        >
          <q-img
            src="src/assets/home/<USER>"
            style="max-width: 20%; margin-right: 1rem; margin-bottom: 1rem"
          ></q-img
          ><br />
          <p :style="{ fontSize: $q.screen.gt.xs ? '90px' : '50px' }">
            VTAF<span :style="{ fontSize: $q.screen.gt.xs ? '140px' : '70px' }"
              >.AI</span
            >
          </p>
        </div>
        <div class="animate-text">
          <div
            class="text-h6 text-bold q-pl-lg text-white q-pb-sm"
            :style="{ textAlign: $q.screen.gt.sm ? 'left' : 'left' }"
          >
            VTAF.AI test automation frameworks optimize efficiency and ensure
            comprehensive test coverage
          </div>
          <div
            class="text-h6 text-bold q-pl-lg text-white q-pb-lg"
            :style="{ textAlign: $q.screen.gt.sm ? 'left' : 'left' }"
          >
            VTAF.AI facilitate scalability by enabling easy addition of new
            tests
          </div>
        </div>
        <!-- <div
          class="q-pl-lg q-pr-lg"
          :style="{ textAlign: $q.screen.gt.sm ? 'left' : 'left' }"
        >
          <q-btn
            color="red"
            bg-color="white"
            label="Get Started"
            icon-right="navigate_next"
            style="margin-bottom: 2rem"
          >
          </q-btn>
        </div> -->
      </div>
    </div>
    <div
      class="col gt-sm"
      style="display: flex; align-items: center; height: 100%"
    >
      <!-- <q-img
        src="src/assets/home/<USER>"
        style="
          margin: 0 auto;
          width: 50%; /* Make the image fill the width of its parent */
          height: auto; /* Maintain the aspect ratio */
          object-fit: contain; /* Scale the image while preserving its aspect ratio to fit inside its parent */
        "
      ></q-img> -->
      <div class="circle">
        <img
          src="src/assets/home/<USER>"
          alt="Center Image"
          class="center"
        />
        <div class="rotating-image" style="--i: 0">
          <img src="src/assets/home/<USER>" alt="Image 1" />
        </div>
        <div class="rotating-image" style="--i: 1">
          <img src="src/assets/home/<USER>" alt="Image 2" />
        </div>
        <div class="rotating-image" style="--i: 2">
          <img src="src/assets/home/<USER>" alt="Image 3" />
        </div>
        <div class="rotating-image" style="--i: 3">
          <img src="src/assets/home/<USER>" alt="Image 4" />
        </div>
        <div class="rotating-image" style="--i: 4">
          <img src="src/assets/home/<USER>" alt="Image 5" />
        </div>
        <div class="rotating-image" style="--i: 5">
          <img src="src/assets/home/<USER>" alt="Image 6" />
        </div>
        <div class="rotating-image" style="--i: 6">
          <img src="src/assets/home/<USER>" alt="Image 1" />
        </div>
        <div class="rotating-image" style="--i: 7">
          <img src="src/assets/home/<USER>" alt="Image 2" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* .circle:hover .rotating-image {
  animation-play-state: paused;
} */
.circle {
  position: relative;
  width: 600px;
  height: 300px;
}

.center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 150px;
  height: 150px;
}

.rotating-image {
  position: absolute;
  width: 100%;
  height: 100%;
  animation: rotate 50s linear infinite;
}

.rotating-image img {
  position: absolute;
  width: 40px;
  height: 40px;
  top: 0;
  left: 50%;
  transform-origin: 0 150px;
  transform: rotate(calc(var(--i) * 45deg)) translateX(100px); /* 360/8 = 45 degrees */
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

.animate-text {
  opacity: 0;
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
  transform: translateY(-50px);
}

.animate-text.active {
  opacity: 1;
  transform: translateY(0);
}
.heading_animation p {
  color: transparent;
  line-height: 100px;
  -webkit-text-stroke: 1px #fff;
  background: url(src/assets/home/<USER>
  -webkit-background-clip: text;
  background-position: 0 0;
  animation: back 50s linear infinite;
}

@keyframes back {
  100% {
    background-position: 2000px 0;
  }
}
</style>

<script>
export default {
  mounted() {
    setTimeout(() => {
      const elements = document.querySelectorAll(".animate-text");
      elements.forEach((element) => {
        element.classList.add("active");
      });
    }, 500);
  },
};
</script>

<!-- <template>
  <div
    class="row"
    :style="{
      height: $q.screen.gt.sm ? '400px' : '500px',
      background: $q.screen.gt.sm
        ? 'linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(184, 44, 44, 0.5)), url(\'src/assets/Screenshot 2024-04-05 131617.png\')'
        : 'linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(184, 44, 44, 0.5)), url(\'src/assets/Screenshot 2024-04-05 131617.png\')',
      backgroundSize: 'cover',
      backgroundPosition: 'center',
    }"
  >
    <div class="col">
      <div
        style="
          text-align: center;
          max-width: 650px;
          margin-left: auto;
          margin-right: auto;
        "
      >
        <div
          class="text-h5 text-bold text-white q-mt-sm q-pt-sm q-pb-lg gt-sm animate-text"
        >
          <q-img
            src="src/assets/vtaf_logo.png"
            style="max-width: 20%; margin-right: 1rem"
          ></q-img>
          Valeo Test Automation Framework
        </div>
        <div
          class="text-h5 text-bold text-white q-mt-sm q-pt-sm q-pb-lg lt-md animate-text"
        >
          <q-img
            src="src/assets/vtaf_logo.png"
            style="max-width: 20%; margin-right: 1rem"
          ></q-img
          ><br />
          Valeo Test Automation Framework
        </div>
        <div class="animate-text">
          <div
            class="text-h6 text-white q-pl-lg q-pr-lg q-pb-sm"
            :style="{ textAlign: $q.screen.gt.sm ? 'left' : 'center' }"
          >
            VTAF test automation frameworks optimize efficiency and ensure
            comprehensive test coverage
          </div>
          <div
            class="text-h6 text-white q-pl-lg q-pr-lg q-pb-lg"
            :style="{ textAlign: $q.screen.gt.sm ? 'left' : 'center' }"
          >
            VTAF facilitate scalability by enabling easy addition of new tests
          </div>
        </div>
        <div
          class="q-pl-lg q-pr-lg"
          :style="{ textAlign: $q.screen.gt.sm ? 'left' : 'center' }"
        >
          <q-btn
            color="red"
            bg-color="white"
            label="Get Started"
            icon-right="navigate_next"
            style="margin-bottom: 2rem"
          >
          </q-btn>
        </div>
      </div>
    </div>
    <div class="col gt-sm">
      <q-img
        src="src/assets/home_jumbotron_image.png"
        style="margin: 0 auto; display: block; max-width: 45%; margin-top: 1rem"
      ></q-img>
    </div>
  </div>
</template>

<style scoped>
.animate-text {
  opacity: 0;
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
  transform: translateY(-50px);
}

.animate-text.active {
  opacity: 1;
  transform: translateY(0);
}
</style>

<script>
export default {
  mounted() {
    setTimeout(() => {
      const elements = document.querySelectorAll(".animate-text");
      elements.forEach((element) => {
        element.classList.add("active");
      });
    }, 500);
  },
};
</script> -->
