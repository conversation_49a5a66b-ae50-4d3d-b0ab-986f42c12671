# file: file_parsing_requests.py

from typing import List, Optional
from pydantic import BaseModel, Field

class ParseFileRequest(BaseModel):
    """
    Request schema for parsing a file using the parsing API.
    Includes base64-encoded file content, parser selection, and output format options.
    """
    file_name: str = Field(
        default="test/filepath.pdf",
        description="Name of the file to be parsed."
    )
    file_content: str = Field(
        default="base64_encoded_content_of_the_file",
        description="Base64 encoded content of the file."
    )
    parser: Optional[str] = Field(
        default="auto",
        description="Parser to use. Defaults to 'auto'."
    )
    output_format: List[str] = Field(
        default_factory=lambda: ["text", "images", "tables"],
        description="List of output formats to generate. E.g., 'text', 'images', 'tables'."
    )
