name:  Software Composition Analysis
on:
  workflow_dispatch:

jobs:
  BlackDuck-Analysis:
    runs-on: linux
    steps:
  
    - name: Checkout code
      uses: actions/checkout@v3

    - name: BlackDuck Analysis
      if: ${{ vars.PROJECT_NAME != '' }}
      run: java -jar /usr/local/detect-10.2.1-air-gap/detect-10.2.1.jar --blackduck.trust.cert=true --detect.cleanup=true --detect.source.path=. --blackduck.url=${{ vars.BLACKDUCK_URL }} --blackduck.api.token=${{ secrets.BLACKDUCK_TOKEN }} --detect.project.#name=${{ vars.PROJECT_NAME }} --detect.project.version.name=${{ github.base_ref }}

    - name: Skipping Analysis
      if: ${{ vars.PROJECT_NAME == '' }}
      run: |
        echo "### No **PROJECT_NAME** variable found! Skipping Analysis" >> $GITHUB_STEP_SUMMARY
        echo "To enable this, refer to our [Blackduck Guide](https://github-ri.vnet.valeo.com/pages/Documentation/RnI-Github-Enterprise/scablackduck/)" >> $GITHUB_STEP_SUMMARY

