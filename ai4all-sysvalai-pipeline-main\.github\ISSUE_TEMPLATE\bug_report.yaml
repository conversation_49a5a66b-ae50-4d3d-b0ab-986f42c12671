name: 🐞 Bug Report
description: Report a bug or issue you've encountered
title: "[BUG] "
labels: ["bug"]

body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to report a bug! To help us address the issue efficiently, please provide the following information:

  - type: checkboxes
    attributes:
      label: Search Before Reporting
      description: Please confirm you've searched existing issues to avoid duplicates.
      options:
        - label: I have searched existing issues and couldn't find a match.
          required: true

  - type: textarea
    id: current-behavior
    attributes:
      label: Current Behavior 🐛
      description: What is the problem you are facing? Describe the incorrect or unexpected behavior you observed.
    validations:
      required: true

  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected Behavior ✅
      description: What did you expect to happen instead?
    validations:
      required: true

  - type: textarea
    id: steps-to-reproduce
    attributes:
      label: Steps to Reproduce 🪜
      description: Please provide clear and concise steps to reproduce the bug.
      placeholder: |
        1. Go to...
        2. Click on...
        3. See error...
    validations:
      required: true

  - type: dropdown
    id: severity
    attributes:
      label: Severity 💥
      description: |
        How severe is the impact of this bug?
        * **⚪ Not Defined:** I'm not sure of this info.
        * **🔴 Critical:** Blocks core functionality, causes data loss, or poses a security risk.
        * **🟠 Major:** Significantly impairs functionality but has workarounds.
        * **🟡 Minor:** Inconvenience or cosmetic issue.
      options:
        - ⚪ Not Defined
        - 🔴 Critical
        - 🟠 Major
        - 🟡 Minor
    validations:
      required: true

  - type: dropdown
    id: priority
    attributes:
      label: Bug Priority ⚡
      description: |
        Select the priority level for this feature.
        * **⚪ Not Defined:** I'm not sure of this info.
        * **🔴 P1:** Highest priority, must be addressed immediately.
        * **🟠 P2:** High priority, should be addressed soon.
        * **🟡 P3:** Medium priority, can wait for a while.
        * **🟢 P4:** Low priority, can be addressed later.
        * **🔵 P5:** Lowest priority, may not be addressed at all.
      options:
        - ⚪ Not Defined
        - 🔴 P1
        - 🟠 P2
        - 🟡 P3
        - 🟢 P4
        - 🔵 P5
    validations:
      required: true
      
  - type: dropdown
    id: bug-size
    attributes:
      label: Bug Size
      description: |
        Estimate the size of fixing the bug.
        * **⚪ Not Defined:** I'm not sure of this info.
        * **🔵 XS:** Very small, <= 1 day. (2 Points)
        * **🟢 S** Small, ~ 2-3 days. (5 Points)
        * **🟡 M** Medium, <= 1 week. (10 Points)
        * **🟠 L** Large, ~ 7-8 days. (15 Points)
        * **🔴 XL:** Very large, <= 2 weeks. (20 Points)
      options:
        - ⚪ Not Defined
        - 🔵 XS (2 Points)
        - 🟢 S (5 Points)
        - 🟡 M (10 Points)
        - 🟠 L (15 Points)
        - 🔴 XL (20 Points)
    validations:
      required: true
    
  - type: dropdown
    id: reproducibility
    attributes:
      label: Reproducibility 🔄
      description: |
        How consistently can this bug be reproduced?
        * **Not Defined:** I'm not sure of this info.
        * **Always:** The bug happens every time the steps are followed.
        * **Sometimes:** The bug occurs intermittently or under specific conditions.
        * **Rarely:** The bug is difficult to reproduce and may be environment-specific.
        * **Unable to Reproduce:** You haven't been able to reproduce the bug reliably. 
      options:
        - Not Defined
        - Always
        - Sometimes
        - Rarely
        - Unable to Reproduce
    validations:
      required: true

  - type: textarea
    id: environment
    attributes:
      label: Environment 🌎
      description: Add information about the environment of this issue. (Specify package, version, os, ...)

  - type: textarea
    id: related-issues
    attributes:
      label: Related Issues
      description: List any other issues related to this bug.
      placeholder: |
        - #Issue 1
        - #Issue 2
        - #Issue 3

  - type: textarea
    id: anything-else
    attributes:
      label: Additional Context ➕
      description: Any other information that might be helpful (screenshots, console logs, etc.). Drag and drop files here or paste links.