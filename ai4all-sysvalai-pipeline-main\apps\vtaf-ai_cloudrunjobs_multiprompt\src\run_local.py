#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地运行脚本 - 用于开发和测试
"""

import os
import sys
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from configs.env import settings
from utils.logger import get_logger
from services.ai_service import AIService
from services.batch_processor import BatchProcessor
from services.config_service import ConfigurationService

logger = get_logger(__name__)

def load_config_from_file():
    """从配置文件加载配置"""
    config_service = ConfigurationService()
    
    # 检查配置文件是否存在
    if not config_service.config_exists():
        logger.error("config.ini 文件不存在，请先创建配置文件")
        logger.info("可以参考 config.ini.example 创建配置文件")
        return None
    
    # 获取表格配置
    sheet_config = config_service.get_sheet_config_for_batch_task()
    if 'error' in sheet_config:
        logger.error(f"配置文件错误: {sheet_config['error']}")
        return None
    
    return sheet_config

def run_batch_task():
    """运行批处理任务"""
    logger.info("=== 开始批处理任务 ===")
    
    # 从配置文件加载配置
    config = load_config_from_file()
    if not config:
        return False
    
    # 添加任务类型
    config['task_type'] = 'batch'
    config['model'] = os.getenv('AI_MODEL', settings.DEFAULT_MODEL)
    
    logger.info(f"使用模型: {config['model']}")
    logger.info(f"提示词表格: {config['prompt_sheet']}")
    logger.info(f"测试规格表格: {config['test_spec_sheet']}")
    
    try:
        # 初始化服务
        ai_service = AIService()
        batch_processor = BatchProcessor(ai_service)
        
        # 执行批处理
        batch_processor.run(config)
        
        logger.info("✅ 批处理任务完成")
        return True
        
    except Exception as e:
        logger.exception(f"❌ 批处理任务失败: {e}")
        return False

def run_single_task():
    """运行单个任务测试"""
    logger.info("=== 开始单个任务测试 ===")
    
    try:
        # 初始化 AI 服务
        ai_service = AIService()
        
        # 测试提示词
        test_prompt = "请生成一个简单的测试用例，包含以下字段：Test Case ID, Test Objective, Test Condition, Test Action, Test Expectation"
        
        logger.info(f"发送测试提示词: {test_prompt[:50]}...")
        
        # 获取响应
        response = ai_service.generate_response(test_prompt)
        
        logger.info("✅ AI 响应获取成功")
        logger.info(f"响应内容: {response[:200]}...")
        
        return True
        
    except Exception as e:
        logger.exception(f"❌ 单个任务测试失败: {e}")
        return False

def check_environment():
    """检查环境配置"""
    logger.info("=== 检查环境配置 ===")
    
    issues = []
    
    # 检查必要的环境变量
    required_env_vars = ['PROJECT_ID']
    for var in required_env_vars:
        value = getattr(settings, var, None)
        if not value or value == f"your-{var.lower().replace('_', '-')}":
            issues.append(f"环境变量 {var} 未正确设置")
        else:
            logger.info(f"✅ {var}: {value}")
    
    # 检查认证文件或硬编码认证
    auth_files = ['client_secret.json', 'service-account.json']
    auth_file_exists = False
    for file in auth_files:
        if os.path.exists(file):
            logger.info(f"✅ 找到认证文件: {file}")
            auth_file_exists = True
            break

    # 检查硬编码认证
    hardcoded_auth_available = False
    try:
        if hasattr(settings, 'SERVICE_ACCOUNT_INFO') and settings.SERVICE_ACCOUNT_INFO:
            logger.info("✅ 找到硬编码服务账号认证")
            hardcoded_auth_available = True
    except:
        pass

    if not auth_file_exists and not hardcoded_auth_available:
        issues.append("未找到 Google Cloud 认证文件或硬编码认证")
    
    # 检查配置文件
    if os.path.exists('config.ini'):
        logger.info("✅ 找到配置文件: config.ini")
    else:
        issues.append("未找到配置文件 config.ini")
    
    if issues:
        logger.error("❌ 环境检查发现问题:")
        for issue in issues:
            logger.error(f"  - {issue}")
        return False
    else:
        logger.info("✅ 环境检查通过")
        return True

def main():
    """主函数"""
    print("VTAF AI Multiprompt 本地运行工具")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        print("\n请先解决环境配置问题后再运行")
        return
    
    # 选择运行模式
    while True:
        print("\n请选择运行模式:")
        print("1. 运行批处理任务")
        print("2. 运行单个任务测试")
        print("3. 退出")
        
        choice = input("请输入选择 (1-3): ").strip()
        
        if choice == '1':
            success = run_batch_task()
            if success:
                print("\n🎉 批处理任务执行成功!")
            else:
                print("\n💥 批处理任务执行失败，请检查日志")
                
        elif choice == '2':
            success = run_single_task()
            if success:
                print("\n🎉 单个任务测试成功!")
            else:
                print("\n💥 单个任务测试失败，请检查日志")
                
        elif choice == '3':
            print("再见!")
            break
            
        else:
            print("无效选择，请重新输入")

if __name__ == '__main__':
    main()
