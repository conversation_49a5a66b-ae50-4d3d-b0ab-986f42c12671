<!-- <template>
  <div class="container" v-if="$q.screen.gt.sm">
    <q-carousel
      v-model="slide"
      transition-prev="scale"
      transition-next="scale"
      swipeable
      animated
      control-color="green"
      navigation
      padding
      arrows
      infinite
      class="bg-grey-3 text-white shadow-1 rounded-borders caro"
    >
      <q-carousel-slide
        v-for="(item, index) in slides"
        :key="index"
        :name="item.name"
        :class="`slide-${index + 1}`"
      >
        <div class="row">
          <div class="col-8 slide-left text-blue-10">
            <div class="image-slide-left">
              <q-icon name="format_quote" size="100px" color="green" />
            </div>
            {{ item.text }}
          </div>
          <div class="col-4 slide-right">
            <div class="image-slide-right bg-grey-4">
              <q-img :src="item.image" class="slide-image"></q-img>
            </div>
            <div class="slide-right-text text-blue-10">
              <span style="font-weight: bold">{{ item.title }}</span
              ><br />
              <span style="font-size: 20px; font-style: italic">{{
                item.subtitle
              }}</span>
            </div>
          </div>
        </div>
      </q-carousel-slide>
    </q-carousel>
  </div>
  <div class="small-container" v-else>
    <q-carousel
      v-model="slide"
      transition-prev="scale"
      transition-next="scale"
      swipeable
      animated
      control-color="green"
      navigation
      padding
      arrows
      infinite
      class="bg-grey-3 text-white shadow-1 rounded-borders small-caro"
    >
      <q-carousel-slide
        v-for="(item, index) in slides"
        :key="index"
        :name="item.name"
        :class="`slide-${index + 1}`"
      >
        <div class="row small-top-image">
          <q-icon name="format_quote" size="60px" color="green" />
        </div>
        <div class="row small-top-text text-blue-10">
          {{ item.text }}
        </div>
        <div class="row small-below-image bg-grey-4">
          <q-img :src="item.image" class="small-image"></q-img>
        </div>
        <div class="row small-below-text-1 text-blue-10">
          {{ item.title }}
        </div>
        <div class="row small-below-text-2 text-blue-10">
          {{ item.subtitle }}
        </div>
      </q-carousel-slide>
    </q-carousel>
  </div>
</template>

<script>
import { ref } from "vue";

export default {
  setup() {
    const slides = [
      {
        name: "style",
        text: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Aspernatur eum quas nisi mollitia quam earum quis tenetur minus rerum, ducimus fuga? Magni laboriosam nobis voluptate placeat optio possimus odit accusamus cupiditate quos consectetur aliquid inventore quibusdam, suscipit fugit hic et.",
        image: "src/assets/home/<USER>",
        title: "Lorem, ipsum dolor.",
        subtitle: "Lorem ipsum dolor sit.",
      },
      {
        name: "tv",
        text: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Aspernatur eum quas nisi mollitia quam earum quis tenetur minus rerum, ducimus fuga? Magni laboriosam nobis voluptate placeat optio possimus odit accusamus cupiditate quos consectetur aliquid inventore quibusdam, suscipit fugit hic et.",
        image: "src/assets/home/<USER>",
        title: "Lorem, ipsum dolor.",
        subtitle: "Lorem ipsum dolor sit.",
      },
      {
        name: "layers",
        text: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Aspernatur eum quas nisi mollitia quam earum quis tenetur minus rerum, ducimus fuga? Magni laboriosam nobis voluptate placeat optio possimus odit accusamus cupiditate quos consectetur aliquid inventore quibusdam, suscipit fugit hic et.",
        image: "src/assets/home/<USER>",
        title: "Lorem, ipsum dolor.",
        subtitle: "Lorem ipsum dolor sit.",
      },
      {
        name: "map",
        text: " ipsum dolor sit amet consectetur adipisicing elit. Aspernatur eum quas nisi mollitia quam earum quis tenetur minus rerum, ducimus fuga? Magni laboriosam nobis voluptate placeat optio possimus odit accusamus cupiditate quos consectetur aliquid inventore quibusdam, suscipit fugit hic et.",
        image: "src/assets/home/<USER>",
        title: "Lorem, ipsum dolor.",
        subtitle: "Lorem ipsum dolor sit.",
      },
    ];

    return {
      slide: ref("style"),
      slides,
    };
  },
};
</script>

<style scoped>
.container {
  width: 90%;
  margin-left: 5%;
  margin-right: 5%;
  margin-top: 5rem;
  margin-bottom: 5rem;
}
.slide-left {
  font-size: 22px;
  font-weight: bold;
}
.image-slide-left {
  margin-top: 1rem;
}
.image-slide-right {
  text-align: center;
  border-radius: 1rem;
  width: 60%;
  margin-left: 3rem;
  margin-top: 5rem;
  margin-bottom: 2rem;
}
.slide-right {
  font-size: 25px;
}
.slide-right-text {
  margin-left: 3rem;
}
.slide-image {
  width: 40%;
}
.small-container {
  width: 90%;
  margin-left: 5%;
  margin-right: 5%;
  margin-top: 5rem;
  margin-bottom: 5rem;
}
.small-below-image {
  margin-top: 2rem;
  width: 60%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 1rem;
}
.small-image {
  width: 40%;
}
.small-below-text-1 {
  margin-top: 1rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}
.small-below-text-2 {
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: center;
}
.small-caro {
  height: 450px;
}
@media screen and (max-width: 450px) {
  .small-caro {
    height: 550px;
  }
}
@media screen and (max-width: 1024px) and (min-width: 450px) {
  .small-caro {
    height: 475px;
  }
}
</style> -->

<template>
  <div class="html1" v-if="$q.screen.gt.sm">
    <section id="horizontal">
      <div class="container">
        <div class="horizontal__content">
          <div
            class="horizontal__item"
            v-for="(item, index) in slides"
            :key="index"
          >
            <q-card class="slide">
              <div class="slide_image_holder">
                <q-img :src="item.image" class="slide_image"></q-img>
              </div>
              <div class="slide_title text-green-10">
                <span style="font-weight: bold">{{ item.title }}</span
                ><br />
                <span style="font-size: 20px; font-style: italic">
                  {{ item.subtitle }}
                </span>
              </div>
              <div class="slide_quote_image">
                <q-icon name="format_quote" size="50px" color="green" />
              </div>
              <div class="slide_text text-green-10">
                <span style="font-size: 17px; font-style: italic">
                  {{ item.text }}
                </span>
              </div>
            </q-card>
          </div>
        </div>
      </div>
    </section>
  </div>
  <div class="small-container" v-else>
    <q-carousel
      v-model="slide"
      transition-prev="scale"
      transition-next="scale"
      swipeable
      animated
      control-color="green"
      navigation
      padding
      arrows
      infinite
      class="bg-grey-3 text-white shadow-1 rounded-borders small-caro"
    >
      <q-carousel-slide
        v-for="(item, index) in slides"
        :key="index"
        :name="item.name"
        :class="`slide-${index + 1}`"
      >
        <div class="row small-top-image">
          <q-icon name="format_quote" size="60px" color="green" />
        </div>
        <div class="row text-blue-10">
          {{ item.text }}
        </div>
        <div class="row small-below-image bg-grey-4">
          <q-img :src="item.image" class="small-image"></q-img>
        </div>
        <div class="row small-below-text-1 text-blue-10">
          {{ item.title }}
        </div>
        <div class="row small-below-text-2 text-blue-10">
          {{ item.subtitle }}
        </div>
      </q-carousel-slide>
    </q-carousel>
  </div>
</template>
<script setup>
import { useQuasar } from "quasar";
import { ref, onMounted, computed } from "vue";
import Lenis from "@studio-freight/lenis";
import { gsap } from "gsap";
const $q = useQuasar();
import ScrollTrigger from "gsap/ScrollTrigger";
const slide = ref("Slide 1");
const slides = [
  {
    name: "Slide 1",
    text: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Aspernatur eum quas nisi mollitia quam earum quis tenetur minus rerum, ducimus fuga? Magni laboriosam nobis voluptate placeat optio possimus odit accusamus cupiditate quos consectetur aliquid inventore quibusdam, suscipit fugit hic et.",
    image: "src/assets/home/<USER>",
    title: "Title 1",
    subtitle: "Subtitle 1",
  },
  {
    name: "Slide 2",
    text: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Aspernatur eum quas nisi mollitia quam earum quis tenetur minus rerum, ducimus fuga? Magni laboriosam nobis voluptate placeat optio possimus odit accusamus cupiditate quos consectetur aliquid inventore quibusdam, suscipit fugit hic et.",
    image: "src/assets/home/<USER>",
    title: "Title 2",
    subtitle: "Subtitle 2",
  },
  {
    name: "Slide 3",
    text: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Aspernatur eum quas nisi mollitia quam earum quis tenetur minus rerum, ducimus fuga? Magni laboriosam nobis voluptate placeat optio possimus odit accusamus cupiditate quos consectetur aliquid inventore quibusdam, suscipit fugit hic et.",
    image: "src/assets/home/<USER>",
    title: "Title 3",
    subtitle: "Subtitle 3",
  },
  {
    name: "Slide 4",
    text: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Aspernatur eum quas nisi mollitia quam earum quis tenetur minus rerum, ducimus fuga? Magni laboriosam nobis voluptate placeat optio possimus odit accusamus cupiditate quos consectetur aliquid inventore quibusdam, suscipit fugit hic et.",
    image: "src/assets/home/<USER>",
    title: "Title 4",
    subtitle: "Subtitle 4",
  },
  {
    name: "Slide 5",
    text: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Aspernatur eum quas nisi mollitia quam earum quis tenetur minus rerum, ducimus fuga? Magni laboriosam nobis voluptate placeat optio possimus odit accusamus cupiditate quos consectetur aliquid inventore quibusdam, suscipit fugit hic et.",
    image: "src/assets/home/<USER>",
    title: "Title 5",
    subtitle: "Subtitle 5",
  },
  {
    name: "Slide 6",
    text: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Aspernatur eum quas nisi mollitia quam earum quis tenetur minus rerum, ducimus fuga? Magni laboriosam nobis voluptate placeat optio possimus odit accusamus cupiditate quos consectetur aliquid inventore quibusdam, suscipit fugit hic et.",
    image: "src/assets/home/<USER>",
    title: "Title 6",
    subtitle: "Subtitle 6",
  },
  {
    name: "Slide 7",
    text: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Aspernatur eum quas nisi mollitia quam earum quis tenetur minus rerum, ducimus fuga? Magni laboriosam nobis voluptate placeat optio possimus odit accusamus cupiditate quos consectetur aliquid inventore quibusdam, suscipit fugit hic et.",
    image: "src/assets/home/<USER>",
    title: "Title 7",
    subtitle: "Subtitle 7",
  },
  {
    name: "Slide 8",
    text: "Lorem ipsum dolor sit amet consectetur adipisicing elit. Aspernatur eum quas nisi mollitia quam earum quis tenetur minus rerum, ducimus fuga? Magni laboriosam nobis voluptate placeat optio possimus odit accusamus cupiditate quos consectetur aliquid inventore quibusdam, suscipit fugit hic et.",
    image: "src/assets/home/<USER>",
    title: "Title 8",
    subtitle: "Subtitle 8",
  },
];
onMounted(() => {
  gsap.registerPlugin(ScrollTrigger);

  const lenis = new Lenis({
    duration: 1.2,
    easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
  });

  function raf(time) {
    lenis.raf(time);
    ScrollTrigger.update();
    requestAnimationFrame(raf);
  }

  requestAnimationFrame(raf);

  const section_2 = document.getElementById("horizontal");
  let box_items = gsap.utils.toArray(".horizontal__item");

  gsap.to(box_items, {
    xPercent: -100 * (box_items.length - 1),
    ease: "sine.out",
    scrollTrigger: {
      trigger: section_2,
      pin: true,
      scrub: 3,
      snap: 1 / (box_items.length - 1),
      end: "+=" + 1500,
    },
  });
});
</script>

<style scoped>
.html1 {
  scroll-behavior: initial;
  overflow: hidden;
  width: 100%;
  min-height: 100%;
  font-family: Slussen;
  font-size: 16px;
  font-weight: 400;
  background: #000;
  color: #fff;
}

h2 {
  font-size: 60px;
  font-weight: 900;
  line-height: 85%;
  border-left: 3px solid;
  padding: 25px;
  margin: 0;
}

h2 span {
  display: block;
}

.container {
  width: 95%;
  margin: auto;
}

section {
  padding: 50px 0;
}

.col {
  width: 50%;
}

#horizontal {
  padding: 100px 0;
}

.horizontal__content {
  display: flex;
}

.horizontal__item:not(:last-child) {
  margin-right: 50px;
}

.slide {
  min-width: 400px;
  min-height: 500px;
}

.slide_image {
  text-align: center;
  border-radius: 1rem;
  width: 30%;
  margin: 0 auto;
  margin-bottom: 1rem;
  margin-top: 1rem;
  margin-left: 3rem;
}

.slide_title {
  margin-left: 3rem;
}

.slide_quote_image {
  margin-left: 2.5rem;
}

.slide_text {
  padding-left: 3rem;
  padding-right: 3rem;
}
.parallax-section {
  position: relative;
  overflow: hidden;
}

.parallax-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("path_to_your_image.jpg"); /* Replace with your image */
  background-size: cover;
  background-position: center;
  z-index: -1;
  transform: translateZ(-1px) scale(2); /* Makes the background move slower */
}

/* Additional styling to ensure proper layout */
.container {
  position: relative;
  z-index: 1;
}

.small-container {
  width: 90%;
  margin-left: 5%;
  margin-right: 5%;
  margin-top: 5rem;
  margin-bottom: 5rem;
}
.small-below-image {
  margin-top: 2rem;
  width: 60%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 1rem;
}
.small-image {
  width: 40%;
}
.small-below-text-1 {
  margin-top: 1rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}
.small-below-text-2 {
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: center;
}
.small-caro {
  height: 450px;
}
@media screen and (max-width: 450px) {
  .small-caro {
    height: 550px;
  }
}
@media screen and (max-width: 1024px) and (min-width: 450px) {
  .small-caro {
    height: 475px;
  }
}
</style>
