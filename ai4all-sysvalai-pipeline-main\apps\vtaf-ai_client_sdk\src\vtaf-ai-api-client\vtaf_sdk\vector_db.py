from vtaf_ai_api_client.api.vector_db_v_1 import (
    add_document_v1_vectordb_documents_add_post,
    count_documents_v1_vectordb_documents_count_post,
    health_check_v1_vectordb_health_get,
    search_documents_v1_vectordb_documents_search_post,
)
from vtaf_ai_api_client.models import AddDocumentRequest, AddDocumentRequestMetadata, CountDocumentsRequest, VectorSearchRequest


class VectorDBAPI:
    def __init__(self, client):
        self.client = client

    def health_check(self):
        return health_check_v1_vectordb_health_get.sync(client=self.client).additional_properties

    def add_document(self, content: str, metadata: dict, collection_name: str):
        metadata_obj = AddDocumentRequestMetadata.from_dict(metadata)
        req = AddDocumentRequest(content=content, metadata=metadata_obj, collection_name=collection_name)
        return add_document_v1_vectordb_documents_add_post.sync(client=self.client, body=req)

    def search_documents(
        self, query: str, collection_name: str, limit: int = 3, score_threshold: float = 0.8, filter: dict = None
    ):
        req = VectorSearchRequest(
            query=query,
            collection_name=collection_name,
            limit=limit,
            score_threshold=score_threshold,
            filter=filter,
        )
        return search_documents_v1_vectordb_documents_search_post.sync(client=self.client, body=req)

    def count_documents(self, collection_name: str, filter: dict = None):
        req = CountDocumentsRequest(collection_name=collection_name, filter=filter)
        return count_documents_v1_vectordb_documents_count_post.sync(client=self.client, body=req)
