import { ref } from "vue";
import { defineStore } from "pinia";
import { Notify, Loading } from "quasar";
import { ai_testcaseApi } from "src/boot/axios";

export const useKnowledgebaseManagementStore = defineStore(
  "knowledgebase_management_store",
  () => {
    const user_email = ref("");

    const valid_user_for_create_knowledge_profile = ref(null);
    const pl_champion = ref([]);
    const isDialogVisible = ref(false);
    ////////////////////////////////////////
    const tree_projects = ref([]);
    const selected_project = ref("");
    const tree_variants = ref([]);
    const selected_variant = ref("");
    const tree_euf_features = ref([]);
    const selected_euf_features = ref("");
    const selectedFile_knowledgebase = ref(null);
    const knowledge_base_current_job_execution_id = ref("");
    const file_type_knowledgebase_options = ref([
      "Requirements - .csv/.xlsx",
      "Architecture - pdf",
      "High Level Design - pdf",
      "Low Level Design - pdf",
      "Data Dictionary - pdf",
      "DBC File - dbc",
      "Others... - pdf",
    ]);
    const selected_file_type = ref("");
    const selected_file_type_for_others = ref("");
    //////////////////////////////////////////
    const search_projects = ref([]);
    const selected_search_project = ref("");
    const search_variants = ref([]);
    const selected_search_variant = ref("");
    const search_euf_features = ref([]);
    const selected_search_euf_feature = ref("");
    const search_table_data = ref([]);

    //////////////////////////////////////////
    const kb_history_table_loading = ref(false);

    const panel = ref("create_project_profile");

    async function get_user_email() {
      try {
        const response = await ai_testcaseApi.get(`/user/get_user`);
        if (response.status === 200) {
          // Success
          const data = response.data.data;
          user_email.value = data;
          console.log(data);
        } else {
          handleError(response);
        }
      } catch (err) {
        console.log(err);
      }
    }

    async function is_user_valid_for_knowledge_profile_creation() {
      try {
        Loading.show();
        const formData = new FormData();
        formData.append("email", user_email.value);
        const response = await ai_testcaseApi.post(
          `/knowledgebase/valid_user_knowledge_base_creation`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
        if (response.status == 200) {
          console.log("created");
          valid_user_for_create_knowledge_profile.value = response.data;

          Loading.hide();
        } else {
          throw new Error("Upload failed");
        }
      } catch (err) {
        handleFetchError(err);
      } finally {
        Loading.hide();
      }
    }

    async function get_profile_projects() {
      try {
        Loading.show();
        const response = await ai_testcaseApi.get(
          `/knowledgebase/get_profile_projects`
        );
        const data = response.data;
        tree_projects.value = data.map((item) => item.project);
        search_projects.value = data.map((item) => item.project);
        console.log(data);
      } catch (err) {
        handleFetchError(err);
      } finally {
        Loading.hide();
      }
    }

    async function get_tree_variant_details() {
      try {
        Loading.show();
        const response = await ai_testcaseApi.get(
          `/knowledgebase/get_tree_variant_details/${selected_project.value}`
        );
        const data = response.data;
        tree_variants.value = data;
        console.log(data);
      } catch (err) {
        handleFetchError(err);
      } finally {
        Loading.hide();
      }
    }
    async function get_search_variant_details() {
      try {
        Loading.show();
        const response = await ai_testcaseApi.get(
          `/knowledgebase/get_tree_variant_details/${selected_search_project.value}`
        );
        const data = response.data;
        search_variants.value = data;
        console.log(data);
      } catch (err) {
        handleFetchError(err);
      } finally {
        Loading.hide();
      }
    }

    async function get_tree_euf_features_details() {
      try {
        Loading.show();
        const variant_data = [selected_project.value, selected_variant.value];
        const response = await ai_testcaseApi.get(
          `/knowledgebase/get_tree_euf_features_details/${variant_data}`
        );
        const data = response.data;
        tree_euf_features.value = data;
        console.log(data);
      } catch (err) {
        handleFetchError(err);
      } finally {
        Loading.hide();
      }
    }
    async function get_search_euf_features_details() {
      try {
        Loading.show();
        const variant_data = [
          selected_search_project.value,
          selected_search_variant.value,
        ];
        const response = await ai_testcaseApi.get(
          `/knowledgebase/get_tree_euf_features_details/${variant_data}`
        );
        const data = response.data;
        search_euf_features.value = data;
        console.log(data);
      } catch (err) {
        handleFetchError(err);
      } finally {
        Loading.hide();
      }
    }

    async function get_search_knowledgebase_profile() {
      try {
        // Loading.show();
        kb_history_table_loading.value = true;
        const formData = new FormData();
        formData.append("project", selected_search_project.value);
        formData.append("variant", selected_search_variant.value);
        formData.append("euf_feature", selected_search_euf_feature.value);

        const response = await ai_testcaseApi.post(
          `/knowledgebase/get_search_knowledgebase_profile`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
        if (response.status == 200) {
          console.log("created");
          search_table_data.value = response.data;
          // Loading.hide();
          kb_history_table_loading.value = false;
        } else {
          throw new Error("Upload failed");
        }
      } catch (err) {
        handleFetchError(err);
      } finally {
        // Loading.hide();
        kb_history_table_loading.value = false;
      }
    }

    async function createKnowledgebase() {
      Loading.show();
      if (!selectedFile_knowledgebase.value) {
        Loading.hide();
        return;
      }
      const formData = new FormData();
      formData.append("user_email", user_email.value);
      formData.append("file", selectedFile_knowledgebase.value);
      formData.append("project", selected_project.value);
      formData.append("variant", selected_variant.value);
      formData.append("euf_feature", selected_euf_features.value);
      if (
        selected_file_type.value === "Others... - pdf" &&
        selected_file_type_for_others.value != ""
      ) {
        formData.append(
          "file_type",
          selected_file_type_for_others.value.split("-")[0].trim()
        );
      } else if (
        selected_file_type.value === "Others... - pdf" &&
        selected_file_type_for_others.value === ""
      ) {
        window.alert("Please fill the others input box");
        Loading.hide();
        return;
      } else {
        formData.append(
          "file_type",
          selected_file_type.value.split("-")[0].trim()
        );
      }
      //////////////////////////////////////////////////////////
      // if(selected_file_type.value === "CSV or XLSX"){
      //   const changed_file_type = "Requirements";
      //   formData.append("file_type", changed_file_type);
      // } else {
      //   formData.append("file_type", selected_file_type.value);
      // }

      try {
        const response = await ai_testcaseApi.post(
          "/knowledgebase/create_knowledgebase",
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
        knowledge_base_current_job_execution_id.value =
          response.data.execution_id;
        console.log(knowledge_base_current_job_execution_id.value);
        if (response.status == 200) {
          console.log("uploaded");
          Notify.create({
            color: "positive",
            position: "top",
            message: `KnowledgeBase Created Successfully!!!`,
            icon: "check_circle_outline",
          });
          Loading.hide();
        } else {
          throw new Error("Upload failed");
        }
      } catch (error) {
        Notify.create({
          color: "negative",
          position: "bottom",
          message: error.message,
          icon: "report_problem",
        });
      } finally {
        Loading.hide();
      }
    }

    function handleFetchError(err) {
      if (err.response) {
        const errorMessage = err.response
          ? err.response.data.detail
          : err.message;
        const errorStatus = err.response.status;
        Notify.create({
          color: "negative",
          position: "bottom",
          message: `${errorStatus} : ${errorMessage}`,
          icon: "report_problem",
        });
      } else {
        Notify.create({
          color: "negative",
          position: "bottom",
          message: err.message,
          icon: "report_problem",
        });
      }
    }

    return {
      user_email,
      panel,
      tree_projects,
      selected_project,
      tree_variants,
      selected_variant,
      tree_euf_features,
      selected_euf_features,
      selectedFile_knowledgebase,
      knowledge_base_current_job_execution_id,
      selected_file_type,
      selected_file_type_for_others,
      file_type_knowledgebase_options,
      search_projects,
      selected_search_project,
      search_variants,
      selected_search_variant,
      search_euf_features,
      selected_search_euf_feature,
      search_table_data,
      valid_user_for_create_knowledge_profile,
      pl_champion,
      isDialogVisible,
      kb_history_table_loading,
      get_user_email,
      is_user_valid_for_knowledge_profile_creation,
      get_profile_projects,
      get_tree_variant_details,
      get_tree_euf_features_details,
      get_search_variant_details,
      get_search_euf_features_details,
      get_search_knowledgebase_profile,
      createKnowledgebase,
    };
  }
);
