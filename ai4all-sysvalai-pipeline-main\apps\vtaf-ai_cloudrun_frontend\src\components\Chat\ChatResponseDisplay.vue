<!-- <template>
  <div v-if="formattedResponses.length" class="wrapped-text">
    <div class="prompt-container">
      <div class="bg-grey-3 user_prompt">
        {{ prompt }}
      </div>
    </div>
    <template v-for="(block, index) in formattedResponses" :key="index">
      <template v-if="block.type === 'code'">
        <div class="code-box">
          <pre><code>{{ block.content }}</code></pre>
          <q-btn
            flat
            dense
            icon="content_copy"
            class="copy-btn"
            @click="copyToClipboard(block.content)"
            aria-label="Copy to clipboard"
          />
        </div>
      </template>
      <template v-else>
        <div v-html="block.content" />
      </template>
    </template>
  </div>
  <div v-if="prompt_banner_loading" class="q-ml-md">
    <q-spinner-dots color="primary" size="40px" />
  </div>
</template>

<script setup>
import { storeToRefs } from "pinia";
import { useChatStore } from "src/stores/chat/chat_store";
import { QSpinnerDots, QBtn } from "quasar";
import { ref, computed, watch } from "vue";
import { marked } from "marked";

const chat_store = useChatStore();
const { prompt_response, prompt_banner_loading, prompt } =
  storeToRefs(chat_store);

// Store parsed responses
const formattedResponses = ref([]);

watch(
  prompt_response,
  () => {
    parsePromptResponse(prompt_response.value);
  },
  { immediate: true }
);

function parsePromptResponse(response) {
  formattedResponses.value = [];
  if (!response) return;

  const lines = response.split("\n");
  let isCodeBlock = false;
  let currentCodeBlock = "";

  lines.forEach((line) => {
    if (line.trim().startsWith("```")) {
      if (isCodeBlock) {
        // End of a code block
        formattedResponses.value.push({
          type: "code",
          content: currentCodeBlock.trim(),
        });
        currentCodeBlock = "";
        isCodeBlock = false;
      } else {
        // Start of a code block
        isCodeBlock = true;
      }
    } else if (isCodeBlock) {
      currentCodeBlock += `${line}\n`;
    } else {
      // Add text (Markdown-enabled)
      formattedResponses.value.push({
        type: "text",
        content: marked(line),
      });
    }
  });

  if (isCodeBlock) {
    // Handle unclosed code block
    formattedResponses.value.push({
      type: "code",
      content: currentCodeBlock.trim(),
    });
  }
}

function copyToClipboard(content) {
  navigator.clipboard.writeText(content).then(() => {
    console.log("Copied to clipboard:", content);
  });
}
</script>

<style scoped>
.wrapped-text {
  margin: 0 auto;
  font-size: 1rem;
  max-width: 60%;
  padding-left: 2rem;
  padding-right: 2rem;
  word-wrap: break-word;
  overflow: auto;
}
.code-box {
  position: relative;
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  font-family: monospace;
  white-space: pre-wrap;
  overflow: auto;
}
.code-box pre {
  margin: 0;
  overflow-x: auto;
}
.copy-btn {
  position: absolute;
  top: 8px;
  right: 8px;
}
.inline-code {
  display: inline-block;
  background-color: #e0e0e0;
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: bold;
  font-family: monospace;
}
.user_prompt {
  max-width: 50%;
  padding: 10px;
  margin-bottom: 2rem;
  border-radius: 2rem;
}
.prompt-container {
  display: flex;
  justify-content: flex-end; /* Pushes child content to the right */
}
</style> -->

<template>
  <div class="wrapped-text">
    <template v-for="(chat, index) in chat_history" :key="index">
      <div class="prompt-container">
        <div class="bg-grey-3 user_prompt">
          {{ chat.prompt }}
        </div>
      </div>
      <template
        v-for="(block, blockIndex) in chat.formattedResponses"
        :key="blockIndex"
      >
        <template v-if="block.type === 'code'">
          <div class="code-box">
            <pre><code>{{ block.content }}</code></pre>
            <q-btn
              flat
              dense
              icon="content_copy"
              class="copy-btn"
              @click="copyToClipboard(block.content)"
              aria-label="Copy to clipboard"
            />
          </div>
        </template>
        <template v-else>
          <div v-html="block.content" />
        </template>
      </template>
    </template>
  </div>
  <div v-if="prompt_banner_loading" class="q-ml-md">
    <q-spinner-dots color="primary" size="40px" />
  </div>
</template>

<script setup>
import { storeToRefs } from "pinia";
import { useChatStore } from "src/stores/chat/chat_store";
import { QSpinnerDots, QBtn } from "quasar";
import { ref, watch } from "vue";
import { marked } from "marked";

const chat_store = useChatStore();
const { prompt_response, prompt_banner_loading, prompt } =
  storeToRefs(chat_store);

// Store the entire chat history
const chat_history = ref([]);

watch(
  prompt_response,
  () => {
    if (prompt_response.value) {
      addChatToHistory(prompt.value, prompt_response.value);
    }
  },
  { immediate: true }
);

function addChatToHistory(prompt, response) {
  const formattedResponses = parsePromptResponse(response);
  chat_history.value.push({ prompt, formattedResponses });
}

function parsePromptResponse(response) {
  const formattedResponses = [];
  if (!response) return formattedResponses;

  const lines = response.split("\n");
  let isCodeBlock = false;
  let currentCodeBlock = "";

  lines.forEach((line) => {
    if (line.trim().startsWith("```")) {
      if (isCodeBlock) {
        // End of a code block
        formattedResponses.push({
          type: "code",
          content: currentCodeBlock.trim(),
        });
        currentCodeBlock = "";
        isCodeBlock = false;
      } else {
        // Start of a code block
        isCodeBlock = true;
      }
    } else if (isCodeBlock) {
      currentCodeBlock += `${line}\n`;
    } else {
      // Add text (Markdown-enabled)
      formattedResponses.push({
        type: "text",
        content: marked(line),
      });
    }
  });

  if (isCodeBlock) {
    // Handle unclosed code block
    formattedResponses.push({
      type: "code",
      content: currentCodeBlock.trim(),
    });
  }

  return formattedResponses;
}

function copyToClipboard(content) {
  navigator.clipboard.writeText(content).then(() => {
    console.log("Copied to clipboard:", content);
  });
}
</script>

<style scoped>
.wrapped-text {
  margin: 0 auto;
  font-size: 1rem;
  max-width: 60%;
  padding-left: 2rem;
  padding-right: 2rem;
  word-wrap: break-word;
  overflow: auto;
}
.code-box {
  position: relative;
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  font-family: monospace;
  white-space: pre-wrap;
  overflow: auto;
}
.code-box pre {
  margin: 0;
  overflow-x: auto;
}
.copy-btn {
  position: absolute;
  top: 8px;
  right: 8px;
}
.user_prompt {
  max-width: 50%;
  padding: 10px;
  margin-bottom: 2rem;
  border-radius: 2rem;
}
.prompt-container {
  display: flex;
  justify-content: flex-end;
}
</style>
