#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI对话日志管理功能测试脚本
模拟AI对话，保存日志，加载日志并显示，最后清除日志
"""

import os
import sys
import json
import time
import random
from datetime import datetime
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

def generate_mock_conversation():
    """生成模拟AI对话数据"""
    conversations = [
        {
            "user": "你好，请帮我写一个Python函数来计算斐波那契数列",
            "assistant": "好的！我来帮你写一个计算斐波那契数列的Python函数：\n\n```python\ndef fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)\n```\n\n这是一个递归实现。如果你需要更高效的版本，我也可以提供迭代实现。",
            "timestamp": datetime.now().isoformat()
        },
        {
            "user": "能否提供一个迭代版本？性能更好一些",
            "assistant": "当然可以！这里是迭代版本，性能确实更好：\n\n```python\ndef fibonacci_iterative(n):\n    if n <= 1:\n        return n\n    \n    a, b = 0, 1\n    for _ in range(2, n + 1):\n        a, b = b, a + b\n    return b\n```\n\n这个版本的时间复杂度是O(n)，比递归版本的O(2^n)要好很多。",
            "timestamp": datetime.now().isoformat()
        },
        {
            "user": "谢谢！还有其他优化方法吗？",
            "assistant": "是的！还有几种优化方法：\n\n1. **矩阵快速幂法**（O(log n)）\n2. **记忆化递归**\n3. **生成器版本**（节省内存）\n\n如果你需要计算非常大的斐波那契数，矩阵快速幂法是最优的选择。需要我详细解释任何一种方法吗？",
            "timestamp": datetime.now().isoformat()
        }
    ]

    # 随机选择1-3个对话
    num_conversations = random.randint(1, len(conversations))
    selected_conversations = conversations[:num_conversations]

    return {
        "conversation_id": f"conv_{int(time.time())}_{random.randint(1000, 9999)}",
        "session_start": datetime.now().isoformat(),
        "total_exchanges": len(selected_conversations),
        "conversations": selected_conversations,
        "metadata": {
            "model": "claude-3-sonnet",
            "temperature": 0.7,
            "max_tokens": 2000,
            "test_mode": True
        }
    }

def display_conversation(conversation_data):
    """美化显示对话内容"""
    print("\n" + "="*80)
    print(f"📋 对话ID: {conversation_data.get('conversation_id', 'Unknown')}")
    print(f"🕐 开始时间: {conversation_data.get('session_start', 'Unknown')}")
    print(f"💬 对话轮数: {conversation_data.get('total_exchanges', 0)}")
    print("="*80)

    conversations = conversation_data.get('conversations', [])
    for i, conv in enumerate(conversations, 1):
        print(f"\n--- 对话轮次 {i} ---")
        print(f"� 用户: {conv.get('user', '')}")
        print(f"🤖 助手: {conv.get('assistant', '')}")
        print(f"⏰ 时间: {conv.get('timestamp', '')}")

    metadata = conversation_data.get('metadata', {})
    if metadata:
        print(f"\n📊 元数据:")
        for key, value in metadata.items():
            print(f"  {key}: {value}")
    print("="*80)

def get_log_directory_info():
    """获取日志目录信息"""
    try:
        from services.log_service import LogService
        log_service = LogService()

        # 获取绝对路径
        log_dir_abs = os.path.abspath(log_service.log_dir)

        return {
            "log_dir": log_service.log_dir,
            "log_dir_abs": log_dir_abs,
            "exists": os.path.exists(log_dir_abs)
        }
    except Exception as e:
        return {
            "log_dir": "logs",
            "log_dir_abs": os.path.abspath("logs"),
            "exists": False,
            "error": str(e)
        }

def simulate_ai_conversation_and_save():
    """模拟AI对话并保存日志"""
    print("🤖 模拟AI对话并保存日志")
    print("=" * 60)

    try:
        from services.job_router import JobRouter
        from services.log_service import LogService

        # 显示日志目录信息
        log_info = get_log_directory_info()
        print(f"📁 日志目录: {log_info['log_dir']}")
        print(f"📂 绝对路径: {log_info['log_dir_abs']}")
        print(f"📋 目录存在: {'是' if log_info['exists'] else '否'}")

        # 生成模拟对话数据
        print("\n📝 生成模拟AI对话数据...")
        conversation_data = generate_mock_conversation()

        # 显示生成的对话
        print("🎭 生成的对话内容:")
        display_conversation(conversation_data)

        # 预测保存的文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        predicted_filename = f"multiprompt_log_{timestamp}.json"
        predicted_filepath = os.path.join(log_info['log_dir_abs'], predicted_filename)

        print(f"\n💾 准备保存对话日志...")
        print(f"📄 预期文件名: {predicted_filename}")
        print(f"📍 预期路径: {predicted_filepath}")

        # 保存对话日志
        os.environ['JOB_TYPE'] = 'log_save'
        os.environ['RUN_MODE'] = 'production'  # 使用生产模式实际保存
        os.environ['LOG_DATA'] = json.dumps(conversation_data, ensure_ascii=False, indent=2)

        router = JobRouter()
        result = router.route_job()

        if result == 0:
            # 验证文件是否真的被创建
            if os.path.exists(predicted_filepath):
                file_size = os.path.getsize(predicted_filepath)
                print(f"✅ 对话日志保存成功！")
                print(f"📁 保存位置: {predicted_filepath}")
                print(f"📊 文件大小: {file_size} 字节")
            else:
                # 如果预测的路径不存在，尝试找到实际保存的文件
                log_service = LogService()
                log_files = [f for f in os.listdir(log_service.log_dir) if f.endswith('.json')]
                if log_files:
                    latest_file = sorted(log_files)[-1]
                    actual_filepath = os.path.join(log_info['log_dir_abs'], latest_file)
                    file_size = os.path.getsize(actual_filepath)
                    print(f"✅ 对话日志保存成功！")
                    print(f"📁 实际保存位置: {actual_filepath}")
                    print(f"📊 文件大小: {file_size} 字节")
                else:
                    print("✅ 对话日志保存成功！（但无法确定具体路径）")

            return conversation_data['conversation_id']
        else:
            print("❌ 对话日志保存失败！")
            return None

    except Exception as e:
        print(f"❌ 模拟对话保存异常: {e}")
        return None
    finally:
        # 清理环境变量
        for key in ['JOB_TYPE', 'LOG_DATA']:
            if key in os.environ:
                del os.environ[key]

def load_and_display_logs():
    """加载并显示日志"""
    print("\n📖 加载并显示日志")
    print("=" * 60)

    try:
        from services.job_router import JobRouter
        from services.log_service import LogService

        # 显示日志目录信息
        log_info = get_log_directory_info()
        print(f"📁 日志目录: {log_info['log_dir']}")
        print(f"📂 绝对路径: {log_info['log_dir_abs']}")

        # 首先获取日志列表
        print("\n📋 获取日志文件列表...")
        log_service = LogService()

        if os.path.exists(log_service.log_dir):
            log_files = [f for f in os.listdir(log_service.log_dir) if f.endswith('.json')]
            log_files.sort(reverse=True)  # 最新的在前

            if log_files:
                print(f"📄 找到 {len(log_files)} 个日志文件:")
                for i, filename in enumerate(log_files[:5], 1):  # 只显示前5个
                    filepath = os.path.join(log_info['log_dir_abs'], filename)
                    file_size = os.path.getsize(filepath) if os.path.exists(filepath) else 0
                    print(f"  {i}. {filename} ({file_size} 字节)")
                if len(log_files) > 5:
                    print(f"  ... 还有 {len(log_files) - 5} 个文件")

                # 加载最新的日志文件
                latest_file = log_files[0]
                latest_filepath = os.path.join(log_info['log_dir_abs'], latest_file)

                print(f"\n📂 加载最新的日志文件...")
                print(f"📄 文件名: {latest_file}")
                print(f"📍 完整路径: {latest_filepath}")

                os.environ['JOB_TYPE'] = 'log_load'
                os.environ['RUN_MODE'] = 'production'
                # 不设置LOG_FILENAME，让它自动加载最新的

                router = JobRouter()
                result = router.route_job()

                if result == 0:
                    print("✅ 日志加载成功！")

                    # 尝试读取并显示日志内容
                    try:
                        with open(latest_filepath, 'r', encoding='utf-8') as f:
                            log_data = json.load(f)

                        print(f"\n📋 日志内容预览:")
                        if 'conversation_id' in log_data:
                            print(f"  对话ID: {log_data['conversation_id']}")
                        if 'session_start' in log_data:
                            print(f"  开始时间: {log_data['session_start']}")
                        if 'conversations' in log_data:
                            print(f"  对话轮数: {len(log_data['conversations'])}")

                        # 显示完整对话内容
                        display_conversation(log_data)

                    except Exception as e:
                        print(f"⚠️ 无法预览日志内容: {e}")

                    return True
                else:
                    print("❌ 日志加载失败！")
                    return False
            else:
                print("📭 没有找到日志文件")
                return False
        else:
            print(f"📁 日志目录不存在: {log_info['log_dir_abs']}")
            return False

    except Exception as e:
        print(f"❌ 加载日志异常: {e}")
        return False
    finally:
        # 清理环境变量
        for key in ['JOB_TYPE', 'LOG_FILENAME']:
            if key in os.environ:
                del os.environ[key]

def clear_all_logs():
    """清除所有日志"""
    print("\n🗑️ 清除所有日志")
    print("=" * 60)

    try:
        from services.job_router import JobRouter
        from services.log_service import LogService

        # 显示日志目录信息
        log_info = get_log_directory_info()
        print(f"📁 日志目录: {log_info['log_dir']}")
        print(f"📂 绝对路径: {log_info['log_dir_abs']}")

        # 先统计要删除的文件
        log_service = LogService()
        if os.path.exists(log_service.log_dir):
            log_files = [f for f in os.listdir(log_service.log_dir) if f.endswith('.json')]

            if log_files:
                print(f"\n📄 找到 {len(log_files)} 个日志文件待删除:")
                for i, filename in enumerate(log_files, 1):
                    filepath = os.path.join(log_info['log_dir_abs'], filename)
                    file_size = os.path.getsize(filepath) if os.path.exists(filepath) else 0
                    print(f"  {i}. {filename} ({file_size} 字节)")
                    print(f"     路径: {filepath}")

                # 清除日志
                print(f"\n🧹 开始清除 {len(log_files)} 个日志文件...")
                os.environ['JOB_TYPE'] = 'log_clear'
                os.environ['RUN_MODE'] = 'production'

                router = JobRouter()
                result = router.route_job()

                if result == 0:
                    print("✅ 日志清除成功！")

                    # 验证文件是否真的被删除
                    remaining_files = [f for f in os.listdir(log_service.log_dir) if f.endswith('.json')] if os.path.exists(log_service.log_dir) else []
                    if not remaining_files:
                        print(f"🎯 确认：所有日志文件已从 {log_info['log_dir_abs']} 中删除")
                    else:
                        print(f"⚠️ 警告：仍有 {len(remaining_files)} 个文件未删除")

                    return True
                else:
                    print("❌ 日志清除失败！")
                    return False
            else:
                print("📭 没有找到需要清除的日志文件")
                return True
        else:
            print(f"📁 日志目录不存在: {log_info['log_dir_abs']}")
            return True

    except Exception as e:
        print(f"❌ 清除日志异常: {e}")
        return False
    finally:
        # 清理环境变量
        if 'JOB_TYPE' in os.environ:
            del os.environ['JOB_TYPE']

def interactive_menu():
    """交互式菜单"""
    print("\n" + "="*80)
    print("🎯 AI对话日志管理测试菜单")
    print("="*80)
    print("1. 🤖 模拟AI对话并保存日志")
    print("2. 📖 加载并显示日志")
    print("3. 📋 查看日志文件列表")
    print("4. 🗑️ 清除所有日志")
    print("5. 🔄 完整流程测试（对话→保存→加载→清除）")
    print("6. 🧪 通过main函数测试所有日志功能")
    print("0. 🚪 退出")
    print("="*80)

    while True:
        try:
            choice = input("\n请选择操作 (0-6): ").strip()

            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                simulate_ai_conversation_and_save()
            elif choice == '2':
                load_and_display_logs()
            elif choice == '3':
                list_log_files()
            elif choice == '4':
                if confirm_action("清除所有日志"):
                    clear_all_logs()
            elif choice == '5':
                full_workflow_test()
            elif choice == '6':
                test_via_main_function()
            else:
                print("❌ 无效选择，请输入 0-6")

        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 操作异常: {e}")

def list_log_files():
    """列出日志文件"""
    print("\n📋 查看日志文件列表")
    print("=" * 60)

    try:
        from services.job_router import JobRouter
        from services.log_service import LogService

        # 显示日志目录信息
        log_info = get_log_directory_info()
        print(f"📁 日志目录: {log_info['log_dir']}")
        print(f"📂 绝对路径: {log_info['log_dir_abs']}")
        print(f"📋 目录存在: {'是' if log_info['exists'] else '否'}")

        # 直接读取目录内容
        log_service = LogService()
        if os.path.exists(log_service.log_dir):
            log_files = [f for f in os.listdir(log_service.log_dir) if f.endswith('.json')]
            log_files.sort(reverse=True)  # 最新的在前

            if log_files:
                print(f"\n📄 找到 {len(log_files)} 个日志文件:")
                total_size = 0

                for i, filename in enumerate(log_files, 1):
                    filepath = os.path.join(log_info['log_dir_abs'], filename)

                    if os.path.exists(filepath):
                        file_size = os.path.getsize(filepath)
                        total_size += file_size

                        # 获取文件修改时间
                        mod_time = os.path.getmtime(filepath)
                        mod_time_str = datetime.fromtimestamp(mod_time).strftime("%Y-%m-%d %H:%M:%S")

                        print(f"  {i:2d}. {filename}")
                        print(f"      📍 路径: {filepath}")
                        print(f"      📊 大小: {file_size} 字节")
                        print(f"      🕐 修改时间: {mod_time_str}")
                        print()
                    else:
                        print(f"  {i:2d}. {filename} (文件不存在)")

                print(f"📊 总计: {len(log_files)} 个文件，总大小: {total_size} 字节")

                # 通过JobRouter执行日志列表功能
                print(f"\n🔧 通过JobRouter验证...")
                os.environ['JOB_TYPE'] = 'log_list'
                os.environ['RUN_MODE'] = 'production'

                router = JobRouter()
                result = router.route_job()

                if result == 0:
                    print("✅ JobRouter日志列表功能验证成功！")
                else:
                    print("❌ JobRouter日志列表功能验证失败！")
            else:
                print("\n📭 没有找到日志文件")
                print("💡 提示: 可以先运行 '模拟AI对话并保存日志' 来创建一些测试数据")
        else:
            print(f"\n📁 日志目录不存在: {log_info['log_dir_abs']}")
            print("💡 提示: 目录会在第一次保存日志时自动创建")

    except Exception as e:
        print(f"❌ 获取日志列表异常: {e}")
    finally:
        if 'JOB_TYPE' in os.environ:
            del os.environ['JOB_TYPE']

def confirm_action(action_name):
    """确认操作"""
    while True:
        confirm = input(f"⚠️ 确定要{action_name}吗？(y/n): ").strip().lower()
        if confirm in ['y', 'yes', '是']:
            return True
        elif confirm in ['n', 'no', '否']:
            return False
        else:
            print("请输入 y/n")

def full_workflow_test():
    """完整流程测试"""
    print("\n� 完整流程测试：对话→保存→加载→清除")
    print("=" * 80)

    # 步骤1: 模拟对话并保存
    print("步骤 1/4: 模拟AI对话并保存")
    conversation_id = simulate_ai_conversation_and_save()
    if not conversation_id:
        print("❌ 流程测试失败：无法保存对话")
        return

    input("\n按回车键继续到下一步...")

    # 步骤2: 加载并显示
    print("\n步骤 2/4: 加载并显示日志")
    if not load_and_display_logs():
        print("❌ 流程测试失败：无法加载日志")
        return

    input("\n按回车键继续到下一步...")

    # 步骤3: 查看日志列表
    print("\n步骤 3/4: 查看日志文件列表")
    list_log_files()

    input("\n按回车键继续到最后一步...")

    # 步骤4: 清除日志（可选）
    print("\n步骤 4/4: 清除日志（可选）")
    if confirm_action("清除所有日志"):
        clear_all_logs()
        print("✅ 完整流程测试完成！")
    else:
        print("✅ 完整流程测试完成（保留日志文件）！")

def test_via_main_function():
    """通过main函数测试所有日志功能"""
    print("\n🧪 通过main函数测试所有日志功能")
    print("=" * 60)

    # 设置环境变量来触发日志管理功能
    os.environ['RUN_MODE'] = 'production'
    os.environ['EXECUTE_LOG_FUNCTIONS'] = 'true'
    os.environ['SKIP_CONVERSATION_TEST'] = 'true'
    os.environ['EXECUTE_DEFAULT_BUTTONS'] = 'false'

    try:
        from main import main
        result = main()

        if result == 0:
            print("✅ main函数中的日志管理功能测试成功")
        else:
            print("❌ main函数中的日志管理功能测试失败")

    except Exception as e:
        print(f"❌ main函数测试异常: {e}")
    finally:
        # 清理环境变量
        for key in ['RUN_MODE', 'EXECUTE_LOG_FUNCTIONS', 'SKIP_CONVERSATION_TEST', 'EXECUTE_DEFAULT_BUTTONS']:
            if key in os.environ:
                del os.environ[key]

if __name__ == '__main__':
    print("🚀 AI对话日志管理功能测试")
    print("=" * 80)
    print("这个脚本可以帮你测试AI对话日志的完整生命周期：")
    print("• 模拟AI对话数据生成")
    print("• 保存对话日志到文件")
    print("• 加载和显示历史对话")
    print("• 管理和清理日志文件")
    print("=" * 80)

    # 启动交互式菜单
    interactive_menu()
