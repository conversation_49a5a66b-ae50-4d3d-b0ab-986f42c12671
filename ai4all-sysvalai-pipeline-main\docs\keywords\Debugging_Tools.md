# Group {Debugging Tools}
## ADB_Log

- [ ] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:**


**Descriptions:** *The ADB (Android Debug Bridge) keywords handle various debugging, device management, and communication tasks between a computer and an Android device*


***Command Set:***

	Start
		-Start=Alias
	Send
		-Send=Alias;Command
	Stop
		-Stop=
	Check
		-Check=Alias;Command;Response
	CheckLog
		-CheckLog=LogLookup


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Alias| String| NA| Xyz| A label or identifier for the specific Android device you are interacting with| Required
| Command| String| NA| Xyz|  The actual ADB command to be executed on the Android device. It could be any shell command| Required
| Response| String| NA| Xyz|  The expected output from the device after the command is executed. This allows verification that the device is responding correctly| Required
| LogLookup| String| NA| Xyz|   A string or keyword to search for in the device�s ADB log output. It could be an error message, event, or specific text you�re trying to track in the logs| Required

