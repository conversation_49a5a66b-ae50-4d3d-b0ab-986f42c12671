import requests
import logging

from config import API_BASE

def summarize_text(text: str, headers: dict) -> str:
    url = f"{API_BASE}/v1/agents/kb/summarize_text"
    payload = {"text": text}

    try:
        response = requests.post(url, headers=headers, json=payload)
        if response.status_code == 200:
            return response.json().get("raw", "")
    except Exception as e:
        logging.error(f"Summarization error: {e}")
    
    return ""
