import os
import yaml
from typing import List, Dict, Type
from pydantic import BaseModel, Field

from crewai import Agent, Crew, Task, LLM
from crewai.tools import BaseTool

from Utilities.qdrant_token_manager import QdrantTokenManager
from vtaf_agent.tools.qdrant_service import QdrantSearchTool
# Suppress warnings 
import warnings
import time

warnings.filterwarnings("ignore")

# Set environment variables
MODEL = "vertex_ai/gemini-2.0-flash"
LOCATION = "europe-west1"

INPUT = {
    "topic": "Automotive",
    "feature": "Functional Testing",
    "sensors": "Surround View System Camera and Ultasonic Sensor",
    "algorithms": "SAW2, PSMD, PED, 3DOD",
    "requirement": "Validate the Automate Braing System in different Speed Condition from 10 to 60",
    "project_uuid": "ab075cc2-57e1-493d-906a-aea42ab5d1fc",
    "variant_uuid": "870a64ca-b12e-4095-b684-c42ecc48d813",
    "euf_feature_uuid": "44fced86-adc6-486e-9f33-d2336736f3ce"
}
AGENTS_CONFIG_PATH = "vtaf_agent/config/agents.yaml"
TASKS_CONFIG_PATH = "vtaf_agent/config/tasks.yaml"



class TestAction(BaseModel):
    """Represents a single test action within a test case."""

    Precondition: str = Field(..., description="Precondition for the test case. Starting from one.")
    Testprocedure: str = Field(..., description="Steps to execute the test case. Starting from one.")
    Reset: str = Field(
        ..., description="Steps to reset the environment after the test. Starting from one."
    )


class TestCase(BaseModel):
    """Represents a single test case."""

    testcase_description: str = Field(..., description="Description of the test case.")
    test_action: TestAction = Field(..., description="Actions to perform during the test.")
    test_expectation: str = Field(...,
                                  description="Expected outcome of the test case. Should have one or more expectation step for the Testprocedure field in test_action.")
    testcase_creation_methodology: str = Field("", description="Methodology used to create the test case.")


class TestCases(BaseModel):
    """Represents a collection of test cases."""

    testcases: List[TestCase] = Field(..., description="List of test cases.")


def vtaf_crew( inputs=INPUT, model=MODEL, location=LOCATION,
               agents_config_path=AGENTS_CONFIG_PATH, tasks_config_path=TASKS_CONFIG_PATH,):
    try:
        llm = LLM(model=model, temperature=0, project=os.environ["SERVICES_PROJECT_ID"], vertex_location=location)

        # Input Model for Searching Requirements
        class SearchRequirementInput(BaseModel):
            """Schema for searching similar requirements using text input."""
            text: str = Field(..., description="Requirement text to find similar requirements.")
            limit: int = Field(5, description="Number of closest matches to retrieve.")

        # Tool for Searching Similar Requirements
        class SearchRequirementTool(BaseTool):
            name: str = "Requirement Similarity Search"
            description: str = "Finds similar requirements in Qdrant based on text input."
            args_schema: Type[BaseModel] = SearchRequirementInput

            def _run(self, text: str, limit: int = 5, score_threshold: float = 0.3) -> List[Dict]:
                # Convert text to vector
                filtered_results = qdrant_search_tool.searching_similar_requirements(text, limit, score_threshold)
                return filtered_results

        with open(agents_config_path, "r") as f:
            agents = yaml.safe_load(f)

        with open(tasks_config_path, "r") as f:
            tasks = yaml.safe_load(f)
        print(f"agents_config_path path: {agents_config_path}")
        print(f"tasks_config_path path: {tasks_config_path}")
        # Creating Agents
        system_test_engineer = Agent(config=agents["system_test_engineer"], verbose=False, llm=llm)
        quality_assurance_engineer = Agent(config=agents["quality_assurance_engineer"], verbose=False, llm=llm)
        # hardware_integration_specialist = Agent(config=agents["hardware_integration_specialist"], verbose=False,
        #                                         llm=llm)
        # system_architect = Agent(config=agents["system_architect"], verbose=False, llm=llm)
        # functional_tester = Agent(config=agents["functional_tester"], verbose=False, llm=llm)
        # integration_tester = Agent(config=agents["integration_tester"], verbose=False, llm=llm)
        # performance_tester = Agent(config=agents["performance_tester"], verbose=False, llm=llm)
        # valeo_subject_matter_expert = Agent(config=agents["valeo_subject_matter_expert"], verbose=False, llm=llm)
        # subject_matter_expert = Agent(config=agents["subject_matter_expert"], verbose=False, llm=llm)

        # Creating Tasks
        # answer_valeo_technical_questions_task = Task(config=tasks["answer_valeo_technical_questions"],
        #                                              agent=valeo_subject_matter_expert)
        # explain_valeo_standards_and_processes_task = Task(config=tasks["explain_valeo_standards_and_processes"],
        #                                                   agent=valeo_subject_matter_expert)
        # standards_and_compliance_clarification_task = Task(config=tasks["standards_and_compliance_clarification"],
        #                                                    agent=subject_matter_expert)
        # risk_analysis_support_task = Task(config=tasks["risk_analysis_support"], agent=subject_matter_expert)
        # testing_strategy_guidance_task = Task(config=tasks["testing_strategy_guidance"], agent=subject_matter_expert)
        # system_understanding_task = Task(config=tasks["system_understanding_task"], agent=system_test_engineer)
        requirements_review_task = Task(config=tasks["requirements_review_task"], agent=system_test_engineer, name="Requirements Review")
        test_case_draft_task = Task(config=tasks["test_case_draft_task"], agent=system_test_engineer, name="Test Case Draft")
        quality_assurance_task = Task(config=tasks["quality_assurance_task"], agent=quality_assurance_engineer, name="Quality Assurance")
        test_case_writing_task = Task(config=tasks["test_case_writing_task"], output_file="testcase.json",
                                      agent=system_test_engineer, output_json=TestCases, name="Test Case Writing")

        project_uuid, variant_uuid, euf_feature_uuid = inputs.get("project_uuid", ""), inputs.get("variant_uuid",
                                                                                                  ""), inputs.get(
            "euf_feature_uuid", "")

        if project_uuid and variant_uuid and euf_feature_uuid:
            qdrant_token_manager = QdrantTokenManager()
            QDRANT_JWT = qdrant_token_manager.get_current_jwt()
            qdrant_search_tool = QdrantSearchTool(QDRANT_JWT, project_uuid, variant_uuid, euf_feature_uuid)
            print("Qdrant Search Tool Initialized for Agent!")
            requirement_engineer = Agent(config=agents["requirement_engineer"], verbose=False, llm=llm,
                                         tools=[SearchRequirementTool()])
            requirement_clarification_task = Task(config=tasks["requirement_clarification_task"],
                                                  agent=requirement_engineer)
            tasks_flow = [
                requirements_review_task,
                requirement_clarification_task,
                test_case_draft_task,
                quality_assurance_task,
                test_case_writing_task,
            ]
        else:
            requirement_engineer = Agent(config=agents["requirement_engineer"], verbose=False, llm=llm)
            tasks_flow = [
                test_case_draft_task,
                quality_assurance_task,
                test_case_writing_task,
            ]

        # Create the crew
        crew = Crew(
            agents=[
                system_test_engineer,
                requirement_engineer,
                # hardware_integration_specialist,
                quality_assurance_engineer,
                # system_architect,
                # functional_tester,
                # integration_tester,
                # performance_tester,
                # valeo_subject_matter_expert,
                # subject_matter_expert,
            ],
            tasks=tasks_flow,
            verbose=False
        )

        # Run the crew
        result = crew.kickoff(inputs=inputs)
        return result, "Pass"
    except Exception as e:
        print(f"Error Occured in VTAF Agent Crew Kickoff: {e}")
        return None, f"Error Occured in VTAF Agent Crew Kickoff: {e}"


if __name__ == "__main__":
    vtaf_crew(model="vertex_ai/gemini-2.0-flash", location="us-central1")
