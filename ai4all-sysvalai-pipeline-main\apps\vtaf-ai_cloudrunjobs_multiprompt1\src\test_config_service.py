#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置服务功能
"""

import sys
import os
import tempfile
import shutil
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.config_service import ConfigurationService

def test_create_default_config():
    """测试创建默认配置文件"""
    print("=== 测试创建默认配置文件 ===")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    original_cwd = os.getcwd()
    
    try:
        os.chdir(temp_dir)
        
        config_service = ConfigurationService()
        
        # 测试创建默认配置
        result = config_service.create_default_config_file()
        print(f"创建默认配置文件: {'成功' if result else '失败'}")
        
        # 检查文件是否存在
        config_exists = config_service.config_exists()
        print(f"配置文件存在: {'是' if config_exists else '否'}")
        
        # 读取配置
        if config_exists:
            config = config_service.get_configuration()
            print(f"读取到的配置项数量: {len(config)}")
            print(f"默认数据集名称: {config.get('dataset_sheet_name', 'Not found')}")
        
        return result and config_exists
        
    finally:
        os.chdir(original_cwd)
        shutil.rmtree(temp_dir, ignore_errors=True)

def test_ini_config_parsing():
    """测试INI配置文件解析"""
    print("\n=== 测试INI配置文件解析 ===")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    original_cwd = os.getcwd()
    
    try:
        os.chdir(temp_dir)
        
        # 创建测试配置文件
        test_config_content = """[SheetConfig]
; auto submit req and write case to sheet
prompt_sheet = 1Q5MG1IDVEoiwTB-nTT0kLZlZ6jHJe3G3HrPiMPX7xJY
Dataset_sheet_name = Test_Training_Dataset
; The worksheet name for a single feeding request
prompt_sheet_name = Test_Prompt_Diag

[TestSpec_SheetConfig]
test_spec_sheet = 1pJLrK04seTAG8I3NH3jvF3IG5N-AUgpz5z-KYWLbzLU
TS_Dataset_sheet_name = Test_TS_Training_Dataset
TS_prompt_sheet_name = Test_TestSpec_Diag
"""
        
        with open("config.ini", "w", encoding="utf-8") as f:
            f.write(test_config_content)
        
        config_service = ConfigurationService()
        
        # 测试读取配置
        config = config_service.get_configuration()
        print(f"读取配置成功: {'是' if config else '否'}")
        print(f"Prompt Sheet: {config.get('prompt_sheet', 'Not found')}")
        print(f"Test Spec Sheet: {config.get('test_spec_sheet', 'Not found')}")
        print(f"Dataset Sheet Name: {config.get('dataset_sheet_name', 'Not found')}")
        
        # 测试批处理任务配置
        batch_config = config_service.get_sheet_config_for_batch_task()
        has_error = 'error' in batch_config
        print(f"批处理配置获取: {'失败 - ' + batch_config.get('error', '') if has_error else '成功'}")
        
        return config and not has_error
        
    finally:
        os.chdir(original_cwd)
        shutil.rmtree(temp_dir, ignore_errors=True)

def test_config_validation():
    """测试配置验证"""
    print("\n=== 测试配置验证 ===")
    
    config_service = ConfigurationService()
    
    # 测试有效配置
    valid_config = {
        "prompt_sheet": "1Q5MG1IDVEoiwTB-nTT0kLZlZ6jHJe3G3HrPiMPX7xJY",
        "test_spec_sheet": "1pJLrK04seTAG8I3NH3jvF3IG5N-AUgpz5z-KYWLbzLU",
        "prompt_sheet_name": "Test_Prompt"
    }
    
    validation_result = config_service.validate_sheet_config(valid_config)
    print(f"有效配置验证: {validation_result}")
    
    # 测试无效配置 - 缺少必要字段
    invalid_config = {
        "prompt_sheet": "",
        "test_spec_sheet": "1pJLrK04seTAG8I3NH3jvF3IG5N-AUgpz5z-KYWLbzLU"
    }
    
    validation_result2 = config_service.validate_sheet_config(invalid_config)
    print(f"无效配置验证: {validation_result2}")
    
    # 测试无效URL格式
    invalid_url_config = {
        "prompt_sheet": "invalid_url",
        "test_spec_sheet": "another_invalid_url"
    }
    
    validation_result3 = config_service.validate_sheet_config(invalid_url_config)
    print(f"无效URL配置验证: {validation_result3}")
    
    return validation_result == "valid" and validation_result2 != "valid" and validation_result3 != "valid"

def test_model_options():
    """测试模型选项获取"""
    print("\n=== 测试模型选项获取 ===")
    
    config_service = ConfigurationService()
    options = config_service.get_model_options()
    
    print(f"Gemini模型数量: {len(options.get('gemini_models', []))}")
    print(f"DeepSeek模型数量: {len(options.get('deepseek_models', []))}")
    print(f"区域数量: {len(options.get('regions', []))}")
    print(f"温度范围: {options.get('temperature_range', {})}")
    
    # 检查必要的选项
    required_keys = ['gemini_models', 'deepseek_models', 'regions', 'temperature_range', 'max_tokens_range']
    has_all_keys = all(key in options for key in required_keys)
    print(f"包含所有必要选项: {'是' if has_all_keys else '否'}")
    
    return has_all_keys and len(options.get('gemini_models', [])) > 0

def test_config_save_and_load():
    """测试配置保存和加载"""
    print("\n=== 测试配置保存和加载 ===")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    original_cwd = os.getcwd()
    
    try:
        os.chdir(temp_dir)
        
        config_service = ConfigurationService()
        
        # 测试保存配置
        test_config = {
            "prompt_sheet": "test_prompt_sheet_id",
            "test_spec_sheet": "test_spec_sheet_id",
            "prompt_sheet_name": "Test_Prompt_Sheet",
            "dataset_sheet_name": "Test_Dataset"
        }
        
        save_result = config_service.save_configuration(test_config)
        print(f"保存配置: {save_result.get('status', 'unknown')}")
        
        # 测试加载配置
        loaded_config = config_service.get_configuration()
        print(f"加载配置成功: {'是' if loaded_config else '否'}")
        
        # 验证保存的数据
        matches = (
            loaded_config.get('prompt_sheet') == test_config['prompt_sheet'] and
            loaded_config.get('test_spec_sheet') == test_config['test_spec_sheet'] and
            loaded_config.get('prompt_sheet_name') == test_config['prompt_sheet_name']
        )
        print(f"配置数据匹配: {'是' if matches else '否'}")
        
        return save_result.get('status') == 'success' and matches
        
    finally:
        os.chdir(original_cwd)
        shutil.rmtree(temp_dir, ignore_errors=True)

def run_all_tests():
    """运行所有测试"""
    print("开始运行配置服务功能测试...\n")
    
    tests = [
        ("创建默认配置", test_create_default_config),
        ("INI配置解析", test_ini_config_parsing),
        ("配置验证", test_config_validation),
        ("模型选项获取", test_model_options),
        ("配置保存加载", test_config_save_and_load)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result, None))
            print(f"✓ {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            results.append((test_name, False, str(e)))
            print(f"✗ {test_name}: 异常 - {e}")
    
    print(f"\n=== 测试总结 ===")
    passed = sum(1 for _, result, _ in results if result)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    for test_name, result, error in results:
        status = "✓ 通过" if result else f"✗ 失败{' - ' + error if error else ''}"
        print(f"  {test_name}: {status}")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
