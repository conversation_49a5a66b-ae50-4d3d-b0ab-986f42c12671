name: 🎯 Task
description: Track the details of a task
title: "[TASK] "
labels: ["task"]

body:
  - type: markdown
    attributes:
      value: |
        ## 🎯 Task Details

        Describe the task and its requirements.

  - type: textarea
    id: task-description
    attributes:
      label: 📝 Task Description
      description: Provide a detailed explanation of the task's goals and scope.
    validations:
      required: true

  - type: input
    id: parent-feature
    attributes:
      label: 🧩 Parent Feature
      description: Name or ID of the feature this task is related to.
      placeholder: "#Issue"
    validations:
      required: true

  - type: textarea
    id: additional-notes
    attributes:
      label: ➕ Additional Notes (Optional)
      description: Include any relevant details, resources, or links.