{"openapi": "3.1.0", "info": {"title": "VTAF AI API", "description": "API for VTAF AI", "version": "1.0.0"}, "servers": [{"url": "/vtaf-api"}], "paths": {"/": {"get": {"summary": "<Lambda>", "operationId": "_lambda___get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/v1/parsing/health": {"get": {"tags": ["Parsing - v1"], "summary": "Health Check API", "description": "Health check endpoint for the data parsing service", "operationId": "parsing_health_check", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Parsing Health Check"}}}}}}}, "/v1/parsing/supported-parsers": {"get": {"tags": ["Parsing - v1"], "summary": "Supported Parsers API", "description": "Supported parsers for the data parsing service", "operationId": "get_supported_parsers_v1_parsing_supported_parsers_get", "parameters": [{"name": "force", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Force"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/parsing/single-file-parsing": {"post": {"tags": ["Parsing - v1"], "summary": "Single File Parsing API", "description": "Parses a single file using the data parsing service", "operationId": "parse_single_file_v1_parsing_single_file_parsing_post", "parameters": [{"name": "force", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Force"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ParseFileRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/chunking/health": {"get": {"tags": ["Chunking - v1"], "summary": "Health Check API", "description": "Health check endpoint for the data chunking service", "operationId": "chunking_health_check", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Chunking Health Check"}}}}}}}, "/v1/chunking/supported-chunking-strategies": {"get": {"tags": ["Chunking - v1"], "summary": "Supported Chunking Strategies API", "description": "Supported chunking strategies for the data chunking service", "operationId": "get_supported_chunking_strategies_v1_chunking_supported_chunking_strategies_get", "parameters": [{"name": "force", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Force"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/chunking/single-string-chunking": {"post": {"tags": ["Chunking - v1"], "summary": "Single String Chunking API", "description": "Chunks a single string using the data chunking service", "operationId": "chunk_single_string_v1_chunking_single_string_chunking_post", "parameters": [{"name": "force", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Force"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChunkStringRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/agents/health": {"get": {"tags": ["Agents - v1"], "summary": "Health Check API", "description": "Health check endpoint for the Knowledgebase service", "operationId": "health_check_v1_agents_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Health Check V1 Agents Health Get"}}}}}}}, "/v1/agents/summarize_text": {"post": {"tags": ["Agents - v1"], "summary": "Summarize Text API", "description": "Summarizes text using the crewAI service", "operationId": "summarize_text_v1_agents_summarize_text_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SummarizationRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/agents/generate_test_script": {"post": {"tags": ["Agents - v1"], "summary": "Generate Test Script API", "description": "Generate Test Script using the crewAI service", "operationId": "generate_test_script_v1_agents_generate_test_script_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateTestScriptRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/agents/bigquery_extract": {"post": {"tags": ["Agents - v1"], "summary": "BigQuery Extract API", "description": "BigQuery Extract using the Agent service", "operationId": "demo_doors_extract_v1_agents_bigquery_extract_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/QueryRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/vectordb/health": {"get": {"tags": ["Vector DB - v1"], "summary": "Health Check API", "description": "Health check endpoint for the Knowledgebase service", "operationId": "health_check_v1_vectordb_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Health Check V1 Vectordb Health Get"}}}}}}}, "/v1/vectordb/documents/add": {"post": {"tags": ["Vector DB - v1"], "summary": "Add Document API", "description": "Add a document to the Qdrant vector database", "operationId": "add_document_v1_vectordb_documents_add_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddDocumentRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/vectordb/documents/search": {"post": {"tags": ["Vector DB - v1"], "summary": "Search Documents API", "description": "Search for documents in the Qdrant vector database", "operationId": "search_documents_v1_vectordb_documents_search_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VectorSearchRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/vectordb/documents/count": {"post": {"tags": ["Vector DB - v1"], "summary": "Count Documents API", "description": "Counts documents in the Qdrant vector database", "operationId": "count_documents_v1_vectordb_documents_count_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CountDocumentsRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/generations/health": {"get": {"tags": ["Generations - v1"], "summary": "Health Check API", "description": "Health check endpoint for the generations service", "operationId": "generator_health_check", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Generator Health Check"}}}}}}}, "/v1/generations/supported-models": {"get": {"tags": ["Generations - v1"], "summary": "Supported GenAI models API", "description": "Supported models for the generations service", "operationId": "get_supported_models_v1_generations_supported_models_get", "parameters": [{"name": "force", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Force"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/generations/rag": {"post": {"tags": ["Generations - v1"], "summary": "Retrieval Augmented Generation (RAG) API", "description": "Performs Retrieval Augmented Generation (RAG) using the generations service", "operationId": "rag_v1_generations_rag_post", "parameters": [{"name": "force", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Force"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerationRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1/utils/get_user": {"get": {"tags": ["Utils - v1"], "summary": "Get User API", "description": "Get User endpoint for the VTAF AI service", "operationId": "get_user_v1_utils_get_user_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"AddDocumentRequest": {"properties": {"content": {"type": "string", "title": "Content"}, "metadata": {"additionalProperties": true, "type": "object", "title": "<PERSON><PERSON><PERSON>"}, "collection_name": {"type": "string", "title": "Collection Name"}}, "type": "object", "required": ["content", "metadata", "collection_name"], "title": "AddDocumentRequest"}, "ChunkStringRequest": {"properties": {"string": {"type": "string", "title": "String", "description": "String to be chunked.", "default": "string_to_chunk"}, "chunking_strategy": {"type": "string", "title": "Chunking Strategy", "description": "Chunking strategy to apply.", "default": "recursive-character-text-splitter"}, "parameters": {"additionalProperties": true, "type": "object", "title": "Parameters", "description": "Optional parameters for the chunking strategy."}}, "type": "object", "title": "ChunkStringRequest", "description": "Request schema for chunking a string using the chunking API.\nIncludes the string to chunk, strategy, and parameters."}, "ContentPart": {"properties": {"type": {"type": "string", "title": "Type"}, "data": {"type": "string", "title": "Data"}, "mimetype": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Mimetype"}, "filename": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Filename"}}, "type": "object", "required": ["type", "data"], "title": "ContentPart"}, "CountDocumentsRequest": {"properties": {"collection_name": {"type": "string", "title": "Collection Name"}, "filter": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Filter"}}, "type": "object", "required": ["collection_name"], "title": "CountDocumentsRequest"}, "GenerateTestScriptRequest": {"properties": {"testcase": {"additionalProperties": true, "type": "object", "title": "Testcase", "description": "Structured test case to be converted into a test script"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email", "description": "Optional email address for notifications"}, "job_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Job Id", "description": "Optional job identifier"}, "enable_tracing": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Enable Tracing", "description": "Optional flag to enable tracing for the request", "default": true}}, "type": "object", "title": "GenerateTestScriptRequest", "description": "Request schema for generating a test script from a test case input.\n\nAttributes:\n    testcase (Dict): The input test case as a structured JSON object.\n    email (Optional[str]): Optional email address for notification.\n    job_id (Optional[str]): Optional job identifier.\n    enable_tracing (Optional[bool]): Whether tracing is enabled for the request."}, "GenerationConfig": {"properties": {"temperature": {"type": "number", "title": "Temperature"}, "top_k": {"type": "integer", "title": "Top K"}, "top_p": {"type": "number", "title": "Top P"}, "maximum_output_tokens": {"type": "integer", "title": "Maximum Output Tokens"}, "candidates_count": {"type": "integer", "title": "Candidates Count"}, "stop_sequences": {"items": {"type": "string"}, "type": "array", "title": "Stop Sequences"}}, "type": "object", "required": ["temperature", "top_k", "top_p", "maximum_output_tokens", "candidates_count", "stop_sequences"], "title": "GenerationConfig"}, "GenerationRequest": {"properties": {"model_name": {"type": "string", "title": "Model Name"}, "contents": {"items": {"$ref": "#/components/schemas/MessageContent"}, "type": "array", "title": "Contents"}, "stream": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Stream"}, "system_instruction": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "System Instruction"}, "generation_config": {"anyOf": [{"$ref": "#/components/schemas/GenerationConfig"}, {"type": "null"}]}, "tools_to_use": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tools To Use"}, "collections_names": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Collections Names"}, "rag_config": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON>"}}, "type": "object", "required": ["model_name", "contents"], "title": "GenerationRequest"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "MessageContent": {"properties": {"role": {"type": "string", "title": "Role"}, "parts": {"items": {"$ref": "#/components/schemas/ContentPart"}, "type": "array", "title": "Parts"}}, "type": "object", "required": ["role", "parts"], "title": "MessageContent"}, "ParseFileRequest": {"properties": {"file_name": {"type": "string", "title": "File Name", "description": "Name of the file to be parsed.", "default": "test/filepath.pdf"}, "file_content": {"type": "string", "title": "File Content", "description": "Base64 encoded content of the file.", "default": "base64_encoded_content_of_the_file"}, "parser": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "Parser to use. Defaults to 'auto'.", "default": "auto"}, "output_format": {"items": {"type": "string"}, "type": "array", "title": "Output Format", "description": "List of output formats to generate. E.g., 'text', 'images', 'tables'."}}, "type": "object", "title": "ParseFileRequest", "description": "Request schema for parsing a file using the parsing API.\nIncludes base64-encoded file content, parser selection, and output format options."}, "QueryRequest": {"properties": {"query": {"type": "string", "title": "Query"}, "dataset": {"type": "string", "title": "Dataset"}}, "type": "object", "required": ["query", "dataset"], "title": "QueryRequest"}, "SummarizationRequest": {"properties": {"text": {"type": "string", "title": "Text", "description": "Text content to summarize", "default": ""}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email", "description": "Optional email address for notifications"}, "job_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Job Id", "description": "Optional job identifier"}, "enable_tracing": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Enable Tracing", "description": "Optional flag to enable tracing for the request", "default": true}}, "type": "object", "title": "SummarizationRequest", "description": "Request schema for summarizing a given text input.\n\nAttributes:\n    text (str): The input text to summarize.\n    email (Optional[str]): Optional email address for notifications.\n    job_id (Optional[str]): Optional job identifier for tracking.\n    enable_tracing (Optional[bool]): Enable or disable tracing for this request."}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "VectorSearchRequest": {"properties": {"query": {"type": "string", "title": "Query"}, "collection_name": {"type": "string", "title": "Collection Name"}, "limit": {"type": "integer", "title": "Limit", "default": 3}, "score_threshold": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Score Threshold", "default": 0.8}, "filter": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Filter"}}, "type": "object", "required": ["query", "collection_name"], "title": "VectorSearchRequest"}}}}