#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试默认按钮功能脚本
验证main.py中的send_batch_reqs和send_single_req功能
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加src目录到Python路径
src_dir = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_dir))

def test_default_buttons():
    """测试默认按钮功能"""
    print("🧪 测试默认按钮功能")
    print("=" * 60)
    
    # 设置测试环境变量
    test_env = os.environ.copy()
    test_env.update({
        'EXECUTE_DEFAULT_BUTTONS': 'true',
        'SKIP_CONVERSATION_TEST': 'true',  # 跳过对话测试以专注于按钮功能
        'RUN_MODE': 'test',
        'PROJECT_ID': 'valeo-cp2673-dev',
        'REGION': 'europe-west1',
        'DEFAULT_MODEL': 'gemini-1.5-flash',
        
        # 为批量处理提供测试配置
        'PROMPT_SHEET_URL': 'https://docs.google.com/spreadsheets/d/test-batch-prompts',
        'TEST_SPEC_SHEET_URL': 'https://docs.google.com/spreadsheets/d/test-batch-specs',
        'PROMPT_SHEET_NAME': 'Prompt_Batch',
        
        # 为单个任务提供测试配置
        'PROMPT_TEXT': 'Test prompt for single request functionality',
        'OUTPUT_FILE': '/tmp/test_output.txt'
    })
    
    try:
        # 运行main.py
        result = subprocess.run(
            [sys.executable, 'src/main.py'],
            cwd=Path(__file__).parent,
            env=test_env,
            capture_output=True,
            text=True,
            timeout=120  # 2分钟超时
        )
        
        print("📤 执行结果:")
        print(f"返回码: {result.returncode}")
        print("\n📋 标准输出:")
        print(result.stdout)
        
        if result.stderr:
            print("\n⚠️ 标准错误:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("\n✅ 默认按钮功能测试成功!")
        else:
            print(f"\n❌ 默认按钮功能测试失败 (返回码: {result.returncode})")
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ 测试超时 (2分钟)")
        return False
    except Exception as e:
        print(f"❌ 测试执行异常: {e}")
        return False

def test_individual_job_types():
    """测试单个任务类型"""
    print("\n🔧 测试单个任务类型")
    print("=" * 60)
    
    job_types = ['batch_processing', 'single_task']
    results = {}
    
    for job_type in job_types:
        print(f"\n--- 测试 {job_type} ---")
        
        test_env = os.environ.copy()
        test_env.update({
            'JOB_TYPE': job_type,
            'EXECUTE_DEFAULT_BUTTONS': 'false',  # 不执行默认按钮，只执行指定任务
            'SKIP_CONVERSATION_TEST': 'true',
            'RUN_MODE': 'test',
            'PROJECT_ID': 'valeo-cp2673-dev',
            'REGION': 'europe-west1',
            'DEFAULT_MODEL': 'gemini-1.5-flash',
            
            # 配置参数
            'PROMPT_SHEET_URL': 'https://docs.google.com/spreadsheets/d/test',
            'TEST_SPEC_SHEET_URL': 'https://docs.google.com/spreadsheets/d/test',
            'PROMPT_TEXT': f'Test prompt for {job_type}',
        })
        
        try:
            result = subprocess.run(
                [sys.executable, 'src/main.py'],
                cwd=Path(__file__).parent,
                env=test_env,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            success = result.returncode == 0
            results[job_type] = success
            
            status = "✅ 成功" if success else "❌ 失败"
            print(f"{job_type}: {status}")
            
            if not success:
                print(f"错误输出: {result.stderr[:200]}...")
                
        except Exception as e:
            print(f"{job_type}: ❌ 异常 - {e}")
            results[job_type] = False
    
    return results

def test_environment_variables():
    """测试环境变量配置"""
    print("\n🔍 测试环境变量配置")
    print("=" * 60)
    
    # 测试不同的环境变量组合
    test_cases = [
        {
            'name': '默认配置',
            'env': {}
        },
        {
            'name': '跳过对话测试',
            'env': {'SKIP_CONVERSATION_TEST': 'true'}
        },
        {
            'name': '禁用默认按钮',
            'env': {'EXECUTE_DEFAULT_BUTTONS': 'false', 'SKIP_CONVERSATION_TEST': 'true'}
        },
        {
            'name': '仅对话测试',
            'env': {'TEST_CONVERSATION_ONLY': 'true'}
        }
    ]
    
    for test_case in test_cases:
        print(f"\n--- {test_case['name']} ---")
        
        test_env = os.environ.copy()
        test_env.update({
            'RUN_MODE': 'test',
            'PROJECT_ID': 'valeo-cp2673-dev',
            'REGION': 'europe-west1',
            'DEFAULT_MODEL': 'gemini-1.5-flash',
        })
        test_env.update(test_case['env'])
        
        try:
            result = subprocess.run(
                [sys.executable, 'src/main.py'],
                cwd=Path(__file__).parent,
                env=test_env,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            success = result.returncode == 0
            status = "✅ 成功" if success else "❌ 失败"
            print(f"结果: {status}")
            
        except subprocess.TimeoutExpired:
            print("结果: ⏰ 超时")
        except Exception as e:
            print(f"结果: ❌ 异常 - {e}")

def main():
    """主测试函数"""
    print("🚀 VTAF AI Multiprompt - 默认按钮功能测试")
    print("=" * 80)
    
    # 检查src目录是否存在
    if not src_dir.exists():
        print(f"❌ 源代码目录不存在: {src_dir}")
        return 1
    
    # 检查main.py是否存在
    main_py = src_dir / 'main.py'
    if not main_py.exists():
        print(f"❌ main.py文件不存在: {main_py}")
        return 1
    
    print(f"✅ 源代码目录: {src_dir}")
    print(f"✅ 主程序文件: {main_py}")
    
    # 执行测试
    try:
        # 1. 测试默认按钮功能
        button_success = test_default_buttons()
        
        # 2. 测试单个任务类型
        individual_results = test_individual_job_types()
        
        # 3. 测试环境变量配置
        test_environment_variables()
        
        # 生成测试报告
        print("\n" + "=" * 80)
        print("📊 测试结果总结")
        print("=" * 80)
        
        print(f"默认按钮功能测试: {'✅ 通过' if button_success else '❌ 失败'}")
        
        print("单个任务类型测试:")
        for job_type, success in individual_results.items():
            status = "✅ 通过" if success else "❌ 失败"
            print(f"  {job_type}: {status}")
        
        # 计算总体成功率
        total_tests = 1 + len(individual_results)
        passed_tests = (1 if button_success else 0) + sum(individual_results.values())
        success_rate = (passed_tests / total_tests) * 100
        
        print(f"\n总体测试结果: {passed_tests}/{total_tests} 通过 ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("🎉 测试总体通过!")
            return 0
        else:
            print("⚠️ 测试存在问题，需要检查")
            return 1
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
