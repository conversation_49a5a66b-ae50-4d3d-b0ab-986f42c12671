#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地环境设置脚本
"""

import os
import shutil
from pathlib import Path

def setup_environment():
    """设置本地环境"""
    print("🚀 VTAF AI Multiprompt 本地环境设置")
    print("=" * 50)
    
    src_dir = Path("src")
    
    # 1. 检查并创建 .env 文件
    env_file = src_dir / ".env"
    env_example = src_dir / ".env.example"

    print(f"检查文件: {env_example} (存在: {env_example.exists()})")
    print(f"检查文件: {env_file} (存在: {env_file.exists()})")
    
    if not env_file.exists() and env_example.exists():
        print("📝 创建 .env 文件...")
        shutil.copy(env_example, env_file)
        print(f"✅ 已创建 {env_file}")
        print("⚠️  请编辑 .env 文件，填入正确的配置信息")
    elif env_file.exists():
        print(f"✅ .env 文件已存在: {env_file}")
    else:
        print("❌ 未找到 .env.example 文件")
    
    # 2. 检查并创建 config.ini 文件
    config_file = src_dir / "config.ini"
    config_example = src_dir / "config.ini.example"
    
    if not config_file.exists() and config_example.exists():
        print("📝 创建 config.ini 文件...")
        shutil.copy(config_example, config_file)
        print(f"✅ 已创建 {config_file}")
        print("⚠️  请编辑 config.ini 文件，填入正确的 Google Sheets 信息")
    elif config_file.exists():
        print(f"✅ config.ini 文件已存在: {config_file}")
    else:
        print("❌ 未找到 config.ini.example 文件")
    
    # 3. 检查认证文件
    auth_files = ["client_secret.json", "service-account.json"]
    auth_found = False
    
    for auth_file in auth_files:
        auth_path = src_dir / auth_file
        if auth_path.exists():
            print(f"✅ 找到认证文件: {auth_path}")
            auth_found = True
            break
    
    if not auth_found:
        print("⚠️  未找到 Google Cloud 认证文件")
        print("   请将以下文件之一放入 src/ 目录:")
        print("   - client_secret.json (OAuth 认证)")
        print("   - service-account.json (服务账号认证)")
    
    # 4. 检查依赖
    requirements_file = src_dir / "requirements.txt"
    if requirements_file.exists():
        print(f"✅ 找到依赖文件: {requirements_file}")
        print("💡 运行以下命令安装依赖:")
        print(f"   cd {src_dir} && pip install -r requirements.txt")
    else:
        print("❌ 未找到 requirements.txt 文件")
    
    # 5. 创建日志目录
    log_dir = Path("logs")
    if not log_dir.exists():
        log_dir.mkdir()
        print(f"✅ 创建日志目录: {log_dir}")
    else:
        print(f"✅ 日志目录已存在: {log_dir}")
    
    print("\n" + "=" * 50)
    print("🎯 下一步操作:")
    print("1. 编辑 src/.env 文件，设置 Google Cloud 项目信息")
    print("2. 编辑 src/config.ini 文件，设置 Google Sheets 信息")
    print("3. 将 Google Cloud 认证文件放入 src/ 目录")
    print("4. 安装依赖: cd src && pip install -r requirements.txt")
    print("5. 运行测试: cd src && python run_local.py")

if __name__ == "__main__":
    setup_environment()
