import os

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from google.oauth2 import service_account


class GoogleSheetHandler:

    def __init__(self, spreadsheet_id, SCOPES, logger):
        self.credentials = None
        self.spreadsheet_id = spreadsheet_id
        self.SCOPES = SCOPES
        self.logger = logger
        self.drive_service = None
        self.service = None
        self.connect_to_drive()


    def connect_to_drive(self):
        try:
            self.drive_service = build('drive', 'v3')
            self.service = build('sheets', 'v4')
            self.logger.info(
                "Authenticated with Google Drive using service account. Spreadsheet and Document creation done.")

        except Exception as e:
            self.logger.error(f"Error connecting to Google Drive: {str(e)}")
            return None

    def read_sheet(self, sheet_name, cell_range="A:B"):
        try:
            data = self.service.spreadsheets().values().get(
                spreadsheetId=self.spreadsheet_id, range=f"{sheet_name}!{cell_range}"
            ).execute()
            return data.get("values", [])
        except Exception as e:
            self.logger.exception(f"Error reading sheet{sheet_name}: {str(e)}")
            raise
