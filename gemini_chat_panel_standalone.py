#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gemini AI 简化对话面板
只保留核心功能：对话框、模型选择、提交按钮
"""

import sys
import os
import json
import ssl
import time
import warnings
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                           QWidget, QTextEdit, QLineEdit, QPushButton, QComboBox, 
                           QLabel, QMessageBox)
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont, QTextCursor

# 禁用SSL警告
warnings.filterwarnings("ignore", category=DeprecationWarning)
if (not os.environ.get('PYTHONHTTPSVERIFY', '') and getattr(ssl, '_create_unverified_context', None)):
    ssl._create_default_https_context = ssl._create_unverified_context
    os.environ['GRPC_DNS_RESOLVER'] = 'native'

# Vertex AI相关导入
try:
    import vertexai
    from vertexai.generative_models import GenerativeModel, GenerationConfig
    from google.oauth2 import service_account
    VERTEXAI_AVAILABLE = True
except ImportError:
    VERTEXAI_AVAILABLE = False
    print("Vertex AI库未安装")

# DeepSeek API相关导入
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    print("requests库未安装")

class VertexAIManager:
    """Vertex AI管理器"""
    
    def __init__(self):
        self.model = None
        self.config = GenerationConfig(
            max_output_tokens=8192, 
            temperature=0.4, 
            top_p=1, 
            top_k=32
        )
        self.system_instruction = []
        self.project_id = "valeo-cp2673-dev"
        self.location = "us-central1"
        self.credential = None
        
        # 最新稳定的Gemini模型列表
        self.gemini_model_list = [
            "gemini-1.5-flash-002",
            "gemini-1.5-pro-002", 
            "gemini-2.0-flash-exp",
            "gemini-1.5-flash-8b",
            "gemini-1.0-pro"
        ]
        
        self.init_vertex_ai()
        
    def init_vertex_ai(self):
        """初始化Vertex AI"""
        try:
            # 设置环境变量解决SSL问题
            os.environ['GRPC_SSL_CIPHER_SUITES'] = 'HIGH+ECDSA'
            os.environ['GRPC_DNS_RESOLVER'] = 'native'
            os.environ['GOOGLE_API_USE_CLIENT_CERTIFICATE'] = 'false'
            
            # 加载服务账号凭据
            service_account_file = "./service_account_info.json"
            if os.path.exists(service_account_file):
                self.credential = service_account.Credentials.from_service_account_file(
                    service_account_file,
                    scopes=["https://www.googleapis.com/auth/cloud-platform"]
                )
                
                # 读取项目ID
                with open(service_account_file, 'r', encoding='utf-8') as f:
                    service_info = json.load(f)
                    self.project_id = service_info.get('project_id', self.project_id)
                
                # 初始化Vertex AI
                vertexai.init(
                    project=self.project_id,
                    location=self.location,
                    credentials=self.credential
                )
                
                # 创建默认模型
                self.change_model("gemini-1.5-flash-002")
                return True
            else:
                print("未找到service_account_info.json文件")
                return False
                
        except Exception as e:
            print(f"Vertex AI初始化失败: {e}")
            return False
            
    def change_model(self, model_name):
        """切换模型"""
        try:
            if model_name in self.gemini_model_list:
                self.model = GenerativeModel(
                    model_name=model_name,
                    generation_config=self.config,
                    system_instruction=self.system_instruction
                )
                return True
            else:
                print(f"不支持的模型: {model_name}")
                return False
        except Exception as e:
            print(f"模型切换失败: {e}")
            return False
            
    def get_chat_response(self, prompt):
        """获取聊天响应 - 添加重试机制"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if not self.model:
                    return "模型未初始化"
                    
                response = self.model.generate_content(prompt)
                
                if response.text:
                    return response.text
                else:
                    return "AI没有返回有效响应"
                    
            except Exception as e:
                error_msg = str(e)
                
                # 如果是网络或SSL问题，尝试重试
                if any(keyword in error_msg.lower() for keyword in ["ssl", "certificate", "503", "connect", "handshake"]):
                    if attempt < max_retries - 1:
                        print(f"连接失败，正在重试 ({attempt + 1}/{max_retries}): {error_msg}")
                        time.sleep(2)  # 等待2秒后重试
                        continue
                
                # 其他错误直接返回
                if "quota" in error_msg.lower():
                    return "配额已用完，请稍后再试"
                elif "safety" in error_msg.lower():
                    return "内容被安全过滤器阻止"
                elif "ssl" in error_msg.lower() or "certificate" in error_msg.lower():
                    return f"SSL证书验证失败，已重试{max_retries}次: {error_msg}"
                elif "503" in error_msg or "connect" in error_msg.lower():
                    return f"连接服务器失败，已重试{max_retries}次: {error_msg}"
                else:
                    return f"API调用失败: {error_msg}"
        
        return "多次重试后仍然失败，请稍后再试"

class DeepSeekManager:
    """DeepSeek API管理器"""
    
    def __init__(self):
        self.api_key = "sk-b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8"  # 请替换为真实API密钥
        self.base_url = "https://api.deepseek.com/v1/chat/completions"
        self.system_instruction = ""
        
        # DeepSeek模型列表
        self.deepseek_model_list = [
            "deepseek-chat", "deepseek-coder", "deepseek-r1:70b"
        ]
        
    def get_response(self, model_name, prompt):
        """获取DeepSeek响应"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            messages = []
            if self.system_instruction:
                messages.append({"role": "system", "content": self.system_instruction})
            messages.append({"role": "user", "content": prompt})
            
            data = {
                "model": model_name,
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 4000
            }
            
            response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    return result['choices'][0]['message']['content']
                else:
                    return "DeepSeek没有返回有效响应"
            else:
                return f"DeepSeek API错误: {response.status_code}"
                
        except Exception as e:
            return f"DeepSeek API调用失败: {str(e)}"

class ChatThread(QThread):
    """对话线程"""
    response_received = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, prompt, model_name, vertex_manager, deepseek_manager):
        super().__init__()
        self.prompt = prompt
        self.model_name = model_name
        self.vertex_manager = vertex_manager
        self.deepseek_manager = deepseek_manager
        
    def run(self):
        try:
            # 判断使用哪个AI服务
            if self.model_name in self.vertex_manager.gemini_model_list:
                response = self.vertex_manager.get_chat_response(self.prompt)
            elif self.model_name in self.deepseek_manager.deepseek_model_list:
                response = self.deepseek_manager.get_response(self.model_name, self.prompt)
            else:
                response = f"不支持的模型: {self.model_name}"
            
            if response:
                self.response_received.emit(response)
            else:
                self.error_occurred.emit("AI没有返回有效响应")
                
        except Exception as e:
            self.error_occurred.emit(f"API调用失败: {str(e)}")

class GeminiChatPanel(QMainWindow):
    """简化的Gemini AI对话面板"""
    
    def __init__(self):
        super().__init__()
        
        # 初始化AI管理器
        self.vertex_manager = VertexAIManager()
        self.deepseek_manager = DeepSeekManager()
        self.current_thread = None
        
        self.init_ui()
        self.setup_styles()
        self.check_authentication()
        
    def init_ui(self):
        """初始化简化的用户界面"""
        self.setWindowTitle("Gemini AI Chat - V2.8.6")
        self.setGeometry(100, 100, 800, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 顶部控制栏
        control_layout = QHBoxLayout()
        
        # AI模型选择
        model_label = QLabel("模型:")
        self.model_combo = QComboBox()
        all_models = self.vertex_manager.gemini_model_list + self.deepseek_manager.deepseek_model_list
        self.model_combo.addItems(all_models)
        self.model_combo.setCurrentText("gemini-1.5-flash-002")
        self.model_combo.currentTextChanged.connect(self.on_model_changed)
        
        control_layout.addWidget(model_label)
        control_layout.addWidget(self.model_combo, 1)
        control_layout.addStretch()
        
        main_layout.addLayout(control_layout)
        
        # 对话显示区
        self.chat_display = QTextEdit()
        self.chat_display.setReadOnly(True)
        self.chat_display.setPlaceholderText("对话将在这里显示...")
        font = QFont("Microsoft YaHei", 10)
        self.chat_display.setFont(font)
        main_layout.addWidget(self.chat_display, 1)
        
        # 输入区域
        input_layout = QHBoxLayout()
        
        self.input_field = QLineEdit()
        self.input_field.setPlaceholderText("输入您的问题...")
        self.input_field.returnPressed.connect(self.send_message)
        
        self.submit_button = QPushButton("Submit")
        self.submit_button.clicked.connect(self.send_message)
        self.submit_button.setMinimumHeight(40)
        
        input_layout.addWidget(self.input_field, 1)
        input_layout.addWidget(self.submit_button)
        
        main_layout.addLayout(input_layout)
        
    def setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 10px;
                background-color: white;
            }
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
            QComboBox {
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 8px;
                font-size: 14px;
            }
        """)
        
    def check_authentication(self):
        """检查认证状态"""
        try:
            service_account_file = "./service_account_info.json"
            if os.path.exists(service_account_file):
                with open(service_account_file, 'r', encoding='utf-8') as f:
                    service_info = json.load(f)
                    project_id = service_info.get('project_id', 'Unknown')
                
                if VERTEXAI_AVAILABLE:
                    self.add_message_to_chat("系统", f"✅ Vertex AI已连接 (项目: {project_id})", "#4CAF50")
                else:
                    self.add_message_to_chat("系统", "⚠️ Vertex AI库未安装", "#FF9800")
            else:
                self.add_message_to_chat("系统", "⚠️ 未找到认证文件", "#FF9800")
                
        except Exception as e:
            self.add_message_to_chat("系统", f"❌ 认证检查失败: {str(e)}", "#F44336")
            
    def on_model_changed(self, model_name):
        """模型切换处理"""
        try:
            if model_name in self.vertex_manager.gemini_model_list:
                success = self.vertex_manager.change_model(model_name)
                if success:
                    self.add_message_to_chat("系统", f"🔄 已切换到: {model_name}", "#2196F3")
                else:
                    self.add_message_to_chat("系统", f"❌ 模型切换失败", "#F44336")
            elif model_name in self.deepseek_manager.deepseek_model_list:
                self.add_message_to_chat("系统", f"🔄 已切换到: {model_name}", "#2196F3")
            else:
                self.add_message_to_chat("系统", f"❌ 不支持的模型", "#F44336")
                
        except Exception as e:
            self.add_message_to_chat("系统", f"❌ 切换失败: {str(e)}", "#F44336")
            
    def send_message(self):
        """发送消息"""
        user_input = self.input_field.text().strip()
        if not user_input:
            return
            
        # 禁用输入
        self.input_field.setEnabled(False)
        self.submit_button.setEnabled(False)
        self.submit_button.setText("生成中...")
        
        # 显示用户消息
        self.add_message_to_chat("用户", user_input, "#2196F3")
        self.input_field.clear()
        
        # 启动AI线程
        self.current_thread = ChatThread(
            user_input, 
            self.model_combo.currentText(),
            self.vertex_manager,
            self.deepseek_manager
        )
        self.current_thread.response_received.connect(self.on_response_received)
        self.current_thread.error_occurred.connect(self.on_error_occurred)
        self.current_thread.finished.connect(self.on_thread_finished)
        self.current_thread.start()
        
    def add_message_to_chat(self, sender, message, color="#333"):
        """添加消息到对话区"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        
        cursor = self.chat_display.textCursor()
        cursor.movePosition(QTextCursor.End)
        
        formatted_message = message.replace('<', '&lt;').replace('>', '&gt;').replace('\n', '<br>')
        
        bg_colors = {
            "用户": "#e3f2fd",
            "AI": "#f1f8e9", 
            "系统": "#fafafa"
        }
        bg_color = bg_colors.get(sender, "#ffffff")
        
        html_content = f"""
        <div style="margin: 8px 0; padding: 12px; border-radius: 8px; 
                    background-color: {bg_color}; border-left: 3px solid {color};">
            <div style="margin-bottom: 5px;">
                <b style="color: {color};">{sender}</b> 
                <span style="color: #666; font-size: 11px; float: right;">{timestamp}</span>
            </div>
            <div style="clear: both; color: #333; line-height: 1.5;">
                {formatted_message}
            </div>
        </div>
        """
        
        cursor.insertHtml(html_content)
        scrollbar = self.chat_display.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def on_response_received(self, response):
        """收到AI响应时的处理"""
        current_model = self.model_combo.currentText()
        
        # 根据模型类型显示不同的AI名称
        if current_model in self.vertex_manager.gemini_model_list:
            ai_name = "Gemini"
        elif current_model in self.deepseek_manager.deepseek_model_list:
            ai_name = "DeepSeek"
        else:
            ai_name = "AI"
            
        # 检查是否是错误响应
        if any(keyword in response.lower() for keyword in ["错误", "失败", "quota", "error"]):
            self.add_message_to_chat(ai_name, response, "#F44336")
        else:
            self.add_message_to_chat(ai_name, response, "#4CAF50")
            
    def on_error_occurred(self, error):
        """处理错误"""
        self.add_message_to_chat("系统", f"❌ {error}", "#F44336")
        
    def on_thread_finished(self):
        """线程完成后的处理"""
        self.input_field.setEnabled(True)
        self.submit_button.setEnabled(True)
        self.submit_button.setText("Submit")
        self.input_field.setFocus()
            
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.current_thread and self.current_thread.isRunning():
            self.current_thread.terminate()
            self.current_thread.wait()
        event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("Gemini AI Chat Panel - Simple")
    app.setApplicationVersion("2.8.6")
    
    # 创建并显示主窗口
    window = GeminiChatPanel()
    window.show()
    
    # 显示欢迎信息
    window.add_message_to_chat("系统", 
        "🎉 欢迎使用简化版Gemini AI对话面板！\n\n"
        "✨ 功能：\n"
        "• 支持最新Gemini模型\n"
        "• 支持DeepSeek API\n"
        "• 简洁的对话界面\n\n"
        "🚀 开始您的AI对话吧！", 
        "#4CAF50"
    )
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()