import fastapi
import logging
import async<PERSON>
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import ORJSONResponse
from crewai import LLM
from crewai.utilities.events import crewai_event_bus
from langfuse import Langfuse

from app.core.constants import PROJECT_ID, REGION, VERTEX_AI_MODEL
from app.dependencies.langfuse_client import get_langfuse
from app.utils.crewai.crew_event_handlers import register_crewai_event_handlers
from app.utils.langfuse.cleanup_trace_context import cleanup_trace_context_vars
from app.services.bigquery_agent_factory import run_query as bigquery_run_query
from app.services.summarizer_crew_factory import create_summarizer_crew
from app.services.test_script_crew_factory import create_test_script_crew
from app.routers.v1.schemas.bigquery_extract_requests import QueryRequest
from app.routers.v1.schemas.test_script_requests import GenerateTestScriptRequest
from app.routers.v1.schemas.summarization_requests import SummarizationRequest


logger = logging.getLogger("vtaf")
ai_agents_router = APIRouter(prefix="/agents")


@ai_agents_router.get(
    "/health",
    response_class=ORJSONResponse,
    description="Health check endpoint for the Knowledgebase service",
    summary="Health Check API",
    status_code=fastapi.status.HTTP_200_OK,
)
async def health_check() -> dict:
    """
    Health check endpoint for the Knowledgebase service.
    """
    logger.info("Knowledgebase Health check request received")
    return {
        "name": "Knowledgebase Service",
        "version": "v1",
        "status": "ok",
    }


@ai_agents_router.post(
    "/summarize_text",
    response_class=ORJSONResponse,
    description="Summarizes text using the crewAI service",
    summary="Summarize Text API",
    status_code=fastapi.status.HTTP_200_OK,
)
async def summarize_text(
    request: SummarizationRequest, langfuse: Optional[Langfuse] = Depends(get_langfuse)
):
    logger.info("Summarize text request received")

    async def run_summarization():
        llm = LLM(
            model=VERTEX_AI_MODEL,
            temperature=0,
            project=PROJECT_ID,
            vertex_location=REGION,
        )
        summarize_crew = create_summarizer_crew(request.email, llm)
        inputs = {"text_input": request.text, "crew_name": "Summarize Crew"}
        if request.job_id:
            inputs["job_id"] = request.job_id
        return await summarize_crew.kickoff_async(inputs=inputs)

    try:
        if request.enable_tracing and langfuse:
            with crewai_event_bus.scoped_handlers():
                register_crewai_event_handlers(langfuse)
                result = await run_summarization()
        else:
            result = await run_summarization()
    except Exception as exc:
        logger.error(f"Summarization failed: {exc}")
        raise HTTPException(status_code=500, detail="Failed to summarize text")
    finally:
        cleanup_trace_context_vars()

    return result


@ai_agents_router.post(
    "/generate_test_script",
    response_class=ORJSONResponse,
    description="Generate Test Script using the crewAI service",
    summary="Generate Test Script API",
    status_code=fastapi.status.HTTP_200_OK,
)
async def generate_test_script(
    request: GenerateTestScriptRequest, langfuse: Optional[Langfuse] = Depends(get_langfuse)
):
    logger.info("Generate Test Script request received")

    async def run_generate_test_script():
        llm = LLM(
            model=VERTEX_AI_MODEL,
            temperature=0,
            project=PROJECT_ID,
            vertex_location=REGION,
        )
        test_script_crew = create_test_script_crew(request.email, llm)
        inputs = {"TestCases": request.testcase, "crew_name": "TestScript Crew"}
        if request.job_id:
            inputs["job_id"] = request.job_id
        return await test_script_crew.kickoff_async(inputs=inputs)

    try:
        if request.enable_tracing and langfuse:
            with crewai_event_bus.scoped_handlers():
                register_crewai_event_handlers(langfuse)
                result = await run_generate_test_script()
        else:
            result = await run_generate_test_script()
    except Exception as exc:
        logger.error(f"Generate Test Script failed: {exc}")
        raise HTTPException(status_code=500, detail="Failed to generate test script")
    finally:
        cleanup_trace_context_vars()

    return result


@ai_agents_router.post(
    "/bigquery_extract",
    response_class=ORJSONResponse,
    description="BigQuery Extract using the Agent service",
    summary="BigQuery Extract API",
)
async def demo_doors_extract(request: QueryRequest):
    logger.info("BigQuery Extract request received")
    try:
        response = await asyncio.to_thread(bigquery_run_query, request.query, request.dataset)
        return response
    except Exception as exc:
        logger.error(f"Demo DOORS Extract failed: {exc}")
        raise HTTPException(status_code=500, detail="Failed to demo DOORS Extract")