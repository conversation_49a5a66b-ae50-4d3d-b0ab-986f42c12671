<template>
  <div
    v-if="renderedOutput"
    class="q-mt-md q-pa-md bg-grey-2"
    v-html="renderedOutput"
  ></div>
  </template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useChatWithKbStore } from "src/stores/chat_with_kb_dummy-store";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";
import { marked } from "marked";

const router = useRouter();

const ai_testcase_config = useChatWithKbStore();
const { output_area } =
  storeToRefs(ai_testcase_config);


// Convert markdown to HTML
const renderedOutput = computed(() => {
  return output_area.value ? marked.parse(output_area.value) : "";
});
</script>
