import os
import logging

from langfuse import <PERSON><PERSON>
from datetime import datetime
from crewai.utilities.events.base_event_listener import BaseEventListener
from crewai.utilities.events import (
    CrewKickoffStartedEvent,
    CrewKickoffCompletedEvent,
    CrewKickoffFailedEvent,
    AgentExecutionStartedEvent,
    AgentExecutionCompletedEvent,
    AgentExecutionErrorEvent,
    ToolUsageStartedEvent,
    ToolUsageFinishedEvent,
    ToolUsageErrorEvent,
)

logging.basicConfig(level=logging.INFO)

logger = logging.getLogger(__name__)
VERTEX_AI_MODEL = os.getenv("MODEL", "")


class LangfuseEventListener(BaseEventListener):
    def __init__(self, langfuse: Langfuse):
        super().__init__()
        self.langfuse = langfuse
        self.trace = None
        self.top_generation = None
        self.agent_span = None  # For the currently running task span
        self.tool_span = None

    def reset_state(self):
        self.trace = None
        self.top_generation = None
        self.agent_span = None
        self.tool_span = None


    def setup_listeners(self, bus):
        @bus.on(CrewKickoffStartedEvent)
        def on_crew_started(source, event):
            try:
                self.reset_state()
                self.trace = self.langfuse.trace(
                    name="Crew: Testcase generation",
                    user_id=source.name,
                    input=event.inputs,
                    session_id= os.getenv("CLOUD_RUN_EXECUTION", "") ,
                )
                self.top_generation = self.trace.generation(
                    name="Crew Execution",
                    input=event.inputs,
                    model=VERTEX_AI_MODEL,
                    metadata={"crew_name": event.crew_name},
                )
            except Exception as e:
                logger.error(f"Langfuse error on CrewKickoffStartedEvent: {e}")

        @bus.on(AgentExecutionStartedEvent)
        def on_agent_started(source, event):
            if not self.trace or not self.top_generation:
                logger.error("No active trace or top generation for agent start")
                return
            try:
                agent_work = f"T: [{event.task.name}]" if event.task.name else f"Tool: [{event.tools[0].name}]"
                self.agent_span = self.langfuse.span(
                    name=f"A: [{event.agent.role}] => {agent_work}",
                    input=event.task_prompt,
                    parent_observation_id=self.top_generation.id,
                    trace_id=self.trace.id,
                    metadata={
                        "agent_id": event.agent.id,
                        "task_id": event.task.id,
                        "task_name": event.task.name,
                        "agent_role": (event.agent.role or "").strip(),
                        "agent_goal": (event.agent.goal or "").strip(),
                        "agent_backstory": (event.agent.backstory or "").strip(),
                    },
                )
            except Exception as e:
                logger.error(f"Langfuse error on AgentExecutionStartedEvent: {e}")

        @bus.on(AgentExecutionCompletedEvent)
        def on_agent_completed(source, event):
            if self.agent_span:
                try:
                    self.agent_span.end(output=event.output or "")
                except Exception as e:
                    logger.error(f"Langfuse error on AgentExecutionCompletedEvent: {e}")
                self.agent_span = None

        @bus.on(CrewKickoffCompletedEvent)
        def on_crew_completed(source, event):
            if self.top_generation:
                try:
                    usage = {
                        "input": source.usage_metrics.prompt_tokens,
                        "output": source.usage_metrics.completion_tokens,
                        "cache_read_input_tokens": source.usage_metrics.cached_prompt_tokens,
                        "total": source.usage_metrics.total_tokens,
                    }
                    self.top_generation.end(
                        output=event.output.raw, usage_details=usage
                    )
                except Exception as e:
                    logger.error(
                        f"Langfuse error on CrewKickoffCompletedEvent (top_gen.end): {e}"
                    )

            if self.trace:
                try:
                    self.trace.update(
                        end_time=datetime.utcnow(),
                        output=event.output.raw,
                    )
                except Exception as e:
                    logger.error(
                        f"Langfuse error on CrewKickoffCompletedEvent (trace.update): {e}"
                    )
            self.reset_state()

        @bus.on(ToolUsageStartedEvent)
        def on_tool_started(source, event):
            if self.agent_span:
                try:
                    self.tool_span = self.langfuse.span(
                        name=f"{event.tool_name}",
                        input=event.tool_args,
                        parent_observation_id=self.agent_span.id,
                        trace_id=self.trace.id,
                    )
                except Exception as e:
                    logger.error(f"Langfuse error on ToolUsageStartedEvent: {e}")

        @bus.on(ToolUsageFinishedEvent)
        def on_tool_completed(source, event):
            if self.tool_span:
                try:
                    self.tool_span.end(output=event.output)
                except Exception as e:
                    logger.error(f"Langfuse error on ToolUsageFinishedEvent: {e}")
                self.tool_span = None


        @bus.on(ToolUsageErrorEvent)
        def on_tool_error(source, event):
            if self.tool_span:
                try:
                    self.tool_span.end(output=event.error, level="ERROR")
                except Exception as e:
                    logger.error(f"Langfuse error on ToolUsageErrorEvent: {e}")
                self.tool_span = None

        @bus.on(AgentExecutionErrorEvent)
        def on_agent_error(source, event):
            if self.agent_span:
                try:
                    self.agent_span.end(output=event.error, level="ERROR")
                except Exception as e:
                    logger.error(f"Langfuse error on AgentExecutionErrorEvent: {e}")
                self.agent_span = None
                

        @bus.on(CrewKickoffFailedEvent)
        def on_crew_failed(source, event):
            if self.top_generation:
                try:
                    self.top_generation.end(error=event.error, level="ERROR")
                except Exception as e:
                    logger.error(f"Langfuse error on CrewKickoffFailedEvent: {e}")
            self.reset_state()
            