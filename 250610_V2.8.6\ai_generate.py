# -*- coding: utf-8 -*-
import typing_extensions

# Form implementation generated from reading ui file 'connect_me.ui'
#
# Created by: PyQt5 UI code generator 5.11.3

# PyQt5中使用的基本控件都在PyQt5.QtWidgets模块中
from PyQt5.QtWidgets import QApplication, QFrame, QFileDialog, QMessageBox, QInputDialog
from PyQt5.QtCore import QStringListModel, QThread, pyqtSignal
from PyQt5 import QtCore
# 导入designer工具生成的UI模块
from AIGUI_0522 import Ui_Form

# 导入程序运行必须模块
import re
import sys
import time
import os
import ssl
import json
import glob
import win32api
import warnings
import markdown
import configparser
# import subprocess
from collections import deque

from vertexai_gemini import *
from deepseek_api import deepseek_model_list,ds_update_system_instruction
# from uploadToGoogleSheet import *
from DataProcessing import *
# from response_parser import *
from batch_task import BatchTask, CASE_HEADER_INDEX, GoogleSheetSinglePromptWithoutSavingDataSource
from test_script_task import TestScriptTask, TestScriptSinglePromptWithoutSavingDataSource,get_TS_save_number
from spread_sheet import *
from test_case_archive import *
from ppt_ai_translate import *

if (not os.environ.get('PYTHONHTTPSVERIFY', '') and getattr(ssl, '_create_unverified_context', None)):
    ssl._create_default_https_context = ssl._create_unverified_context

    os.environ['GRPC_DNS_RESOLVER'] = 'native'

warnings.filterwarnings("ignore", category=DeprecationWarning)


class GenerateTextThread(QThread):
    finished = pyqtSignal(str)
    inputToken = pyqtSignal(int)

    def __init__(self, prompt, chatModel):
        super().__init__()
        self.prompt = prompt
        self.chatModel = chatModel

    def run(self):
        tokens = count_tokens(self.prompt)
        if self.chatModel == 1:
            response = get_chat_response(self.prompt)
        else:
            response = generate_text(self.prompt)
        self.inputToken.emit(tokens)
        self.finished.emit(response)

class MyMainForm(QFrame, Ui_Form):
    send = pyqtSignal()

    def __init__(self, parent=None):
        super(MyMainForm, self).__init__(parent)
        self.setupUi(self)
        ai_model = QStringListModel()
        ai_model.setStringList(Gemini_model_list)
        self.comboBox.setModel(ai_model)

        regionList = QStringListModel()
        regionList.setStringList(region_list)
        self.comboBox_2.setModel(regionList)

        # ChatModel = QStringListModel()
        # ChatModel.setStringList(chat_model_list)
        # self.comboBox_3.setModel(ChatModel)

        # 添加登录按钮信号和槽。注意display函数不加小括号()
        self.pushButton.clicked.connect(self.display)
        # 添加清除按钮信号和槽。调用clearFocus函数
        self.pushButton_2.clicked.connect(self.clearFocus)
        self.pushButton_3.clicked.connect(self.change_system_instruction)
        self.pushButton_4.clicked.connect(self.selectFile)

        self.comboBox.currentIndexChanged.connect(self.change_AImodel)
        self.comboBox_2.currentIndexChanged.connect(self.change_region)
        # self.comboBox_3.currentIndexChanged.connect(self.change_chat_model)
        # self.comboBox_2.setCurrentIndex(19)

        self.horizontalSlider.valueChanged.connect(self.change_temperature)
        self.horizontalSlider_2.valueChanged.connect(self.change_max_tokens)
        self.horizontalSlider.setValue(5)
        self.change_temperature()

        # 保存，加载历史记录
        self.pushButton_5.clicked.connect(self.save_chat_history)
        self.pushButton_6.clicked.connect(self.load_chat_history)

        self.pushButton_7.clicked.connect(self.load_init_data)
        self.pushButton_8.clicked.connect(self.do_tc_archive)
        self.pushButton_9.clicked.connect(self.do_batch_task)
        self.pushButton_10.clicked.connect(self.do_single_task_send)

        # self.pushButton_11.clicked.connect(self.create_cached)
        # self.pushButton_12.clicked.connect(self.update_cached)
        # self.pushButton_13.clicked.connect(self.delete_cached)

        self.pushButton_14.clicked.connect(self.load_init_TS_dataset)

        self.pushButton_15.clicked.connect(self.do_tc_batch_task)
        self.pushButton_16.clicked.connect(self.do_tc_single_task_send)
        self.pushButton_17.clicked.connect(self.open_new_window_from_ui)
        # self.pushButton_17.setEnabled(False)
        self.pushButton_18.clicked.connect(self.do_sendmail)
        self.pushButton_19.clicked.connect(self.do_dmailback)

        self.generate_text_thread = None
        self.ButtonState = 0  # 初始状态
        self.update_button_color()

        self.filePathList = ""
        self.chatModel = 1
        self.tmpResponseCase = ""
        self.errorResponseCase = 0

        self.chatSessionRedords = []
        self.chatSessionRedordsNumber = 0
        self.history = []

        self.user_name = get_display_name()
        self.device_name = os.environ['COMPUTERNAME']
        self.inputTokens = 0
        self.outputTokens = 0
        self.acceptCase = 0
        self.requestCounter = 0
        self.CollectRequestCounter = True
        self.Version = "V2.8.6"
        self.setWindowTitle("AI Gemini " + self.Version)

        self.system_instruction_list = []
        self.autoSubmitFlag = False
        self.queue = deque()
        self.send.connect(self.autoSubmit)
        self.show_link()
        # self.reqSheet = SpreadSheet(self.prompt_sheet)
        # self.specSheet = SpreadSheet(self.test_spec_sheet)
        self.data_source = None
        self.tc_data_source = None
        self.batch_task = None
        self.tc_batch_task = None
        self.TS_HEADFile = {}
        self.TS_HEADFile_Prompt = ""

        self.cacheInfo = {}
        self.refresh_cached_model()
        self.last_ai_response = "NoResponse"

    def open_new_window_from_ui(self):
        print("按钮 (来自UI) 被点击，尝试打开新窗口 (来自UI)...")
        # if self.new_window_instance_ui is None or not self.new_window_instance_ui.isVisible():
        self.new_window_instance_ui = AI_Translate() # 传入父对象
        self.new_window_instance_ui.show()

    # enter关联submit按键点击
    def keyPressEvent(self, event):
        if event.key() == QtCore.Qt.Key_Return or event.key() == QtCore.Qt.Key_Enter:
            self.pushButton.click()
        else:
            super().keyPressEvent(event)

    def closeEvent(self, event):
        uploadData = [self.user_name, self.device_name, self.inputTokens,
                       self.outputTokens, self.acceptCase, get_now_time(), 
                       credential.service_account_email, self.Version, self.requestCounter,get_TS_save_number()]
        if (self.inputTokens + self.outputTokens) > 0:
            self.LogSheet = SpreadSheet("137NTLcEHD6wFUvwJgI4VmbGe7g4w1g4DZF4MsD_ZP7s")
            # write_google_log(sheet_name="records", full_data_array=[uploadData])
            self.LogSheet.add_row(sheet_name="records", row_data=uploadData)

    def update_button_color(self):
        if self.ButtonState in (0, 2):
            self.pushButton.setStyleSheet('''QPushButton {
                                                background-color: #E0F2F7; /* Light Blue */
                                                border: 1px solid #B0BEC5; /* Light Gray */
                                                border-radius: 5px;
                                                padding: 8px 16px;
                                                font-size: 14px;
                                                color: #37474F; /* Dark Gray */
                                            }

                                            QPushButton:hover {
                                                background-color: #CFD8DC; /* Lighter Gray */
                                                border: 1px solid #78909C; /* Gray */
                                            }

                                            QPushButton:pressed {
                                                background-color: #B0BEC5; /* Gray */
                                                border: 1px solid #546E7A; /* Darker Gray */
                                            }

                                            QPushButton:disabled {
                                                background-color: #ECEFF1; /* Very Light Gray */
                                                color: #90A4AE; /* Light Gray */
                                                border: 1px solid #CFD8DC; /* Light Gray */
                                            }''')
        elif self.ButtonState == 1:
            self.pushButton.setStyleSheet('''QPushButton {
                                                background-color: #DCEDC8; /* Light Green */
                                                border: none;
                                                border-radius: 4px;
                                                padding: 8px 8px;
                                                font-size: 14px;
                                                color: #2E7D32; /* Dark Green */
                                            }

                                            QPushButton:hover {
                                                background-color: #C8E6C9; /* Lighter Green */
                                            }

                                            QPushButton:pressed {
                                                background-color: #A5D6A7; /* Even Lighter Green */
                                            }

                                            QPushButton:disabled {
                                                background-color: #F1F8E9; /* Very Light Green */
                                                color: #81C784; /* Light Green */
                                            }''')
        # elif self.ButtonState == 2:
        #     self.pushButton.setStyleSheet("")

    def display(self):
        self.pushButton.setEnabled(False)
        self.pushButton.setText("Generating...")
        self.ButtonState = 1
        self.update_button_color()
        self.button_disable()
        prompt = self.textEdit.toPlainText()
        if prompt == "":
            QMessageBox.warning(self, "Error", "No input!")
            # self.textBrowser.setText("Not input")
            self.pushButton.setText("SUBMIT")
            self.pushButton.setEnabled(True)
            self.ButtonState = 2
            self.update_button_color()
            self.button_enable()
            return

        # if self.generate_text_thread and self.generate_text_thread.isRunning():
        #     # 如果有线程正在运行，等待线程完成并处理生成的文本
        #     return
        _prompt = ''
        if self.filePathList:
            if self.comboBox.currentText() in deepseek_model_list:
                _prompt = [prompt,self.filePathList]
            else:
                contents = load_file_to_Part(self.filePathList, self.fileType)
                contents.append(prompt)
                _prompt = contents
            self.filePathList = []
            self.lineEdit_2.clear()
        else:
            _prompt = prompt

        if self.chatModel:
            self.chatSessionRedords.insert(self.chatSessionRedordsNumber, {
                                           "messages": [{"role": "user", "content": prompt}]})

        self.textEdit.setText("")
        self.textBrowser.append('<h4>' + get_now_time() + '</h4>')
        # self.textBrowser.append('<pre><p>' + prompt + '</p><pre>')
        self.textBrowser.append('<pre><p>' + prompt.replace('<', '&lt;') + '</p><pre>')
        # self.inputTokens += count_tokens(prompt)
        self.generate_text_thread = GenerateTextThread(_prompt, self.chatModel)
        self.generate_text_thread.finished.connect(
            self.handle_generate_text_finished)
        self.generate_text_thread.inputToken.connect(self.update_input_tokens)
        self.generate_text_thread.start()
        # self.inputTokens += count_tokens(prompt)
        if self.CollectRequestCounter:
            self.requestCounter += 1

    def update_input_tokens(self, tokens):
        self.inputTokens += tokens

    def handle_generate_text_finished(self, response):
        self.textBrowser.append('<h4>' + get_now_time() + '</h4>')
        # self.textBrowser.append(
        #     '<pre><font color="green">AI: ' + '\n' + response.replace('<', '&lt;') + '</font><pre>')
        if response.startswith("The session"):
            self.errorResponseCase += 1
            self.textBrowser.append('<pre><font color="red">AI: ' + '\n' + response.replace('<', '&lt;') + '</font></pre>')
        else:
            self.textBrowser.append('<pre><font color="green">AI: ' + '\n' + response.replace('<', '&lt;') + '</font></pre>')
        #最后一次对话
        self.last_ai_response = response
        self.outputTokens += count_tokens(response)
        self.pushButton.setText("SUBMIT")
        self.pushButton.setEnabled(True)
        self.ButtonState = 2
        self.update_button_color()
        self.button_enable()
        if self.chatModel:
            self.chatSessionRedords[self.chatSessionRedordsNumber]["messages"].append(
                {"role": "model", "content": response.strip()})
            self.chatSessionRedordsNumber += 1
            self.tmpResponseCase = response
            self.history = chat.history
        if self.autoSubmitFlag:
            time.sleep(0.5)
            self.send.emit()

    def clearFocus(self):
        self.textBrowser.setText("")

    def change_system_instruction(self):
        self.system_instruction_list = []
        system_instruction = self.textEdit_2.toPlainText()
        self.system_instruction_list.append(system_instruction)
        # print(self.system_instruction_list)
        modelname = self.comboBox.currentText()
        # change_model(model_name=modelname,
        #              system_instruction=self.system_instruction_list, chat_History=self.history)
        if modelname in Gemini_model_list:
            update_system_instruction(self.system_instruction_list)
        elif modelname in deepseek_model_list:
            ds_update_system_instruction(system_instruction)
        self.textEdit_2.clear()
        self.textBrowser.append('<h4>' + get_now_time() + '</h4>')
        self.textBrowser.append(
            '<h4>' + "Set System Prompt Successful\n" + '</h4>')
        self.textBrowser.append('<pre><p>' + system_instruction + '</p><pre>')

    def selectFile(self):
        if self.comboBox.currentText() in deepseek_model_list:
            get_filenames_path, fileType = QFileDialog.getOpenFileName(self,
                                                                        "Select single file",
                                                                        os.getcwd(),
                                                                        "Document Files (*.pdf);;TXT Files (*.txt)")
            if fileType:
                self.lineEdit_2.setText(get_filenames_path)
                self.filePathList = get_filenames_path
        else:
            get_filenames_path, fileType = QFileDialog.getOpenFileNames(self,
                                                                        "Select multiple files",
                                                                        os.getcwd(),
                                                                        "Image Files (*.JPEG;*.JPG;*.PNG);;Document Files (*.pdf);;TXT Files (*.txt)")
            if fileType:
                filePath = str(';'.join(get_filenames_path))
                self.lineEdit_2.setText(filePath)
                # print(filePath)
                self.filePathList = filePath.split(";")
                if fileType == "Image Files (*.JPEG;*.JPG;*.PNG)":
                    self.fileType = "image/jpeg"
                elif fileType == "Document Files (*.pdf)":
                    self.fileType = "application/pdf"
                elif fileType == "TXT Files (*.txt)":
                    self.fileType = "text/plain"
                # print(self.filePathList)

    def change_AImodel(self):
        modelname = self.comboBox.currentText()
        # self.system_instruction_list = []
        # system_instruction = self.textEdit_2.toPlainText()
        # self.system_instruction_list.append(system_instruction)
        if modelname in Gemini_model_list:
            change_model(model_name=modelname,
                        system_instruction=self.system_instruction_list, chat_History=self.history)
            # self.pushButton_11.setEnabled(True)
            # self.pushButton_12.setEnabled(False)
            # self.pushButton_13.setEnabled(False)
            self.textBrowser.append('<h4>' + get_now_time() + '</h4>')
            self.textBrowser.append(
                '<h4>' + "Switch to " + modelname + " Model\n" + '</h4>')
            if modelname.startswith("gemini-2.5"):
                self.horizontalSlider_2.setMaximum(65535)
                self.horizontalSlider_2.setValue(65535)
                self.label_4.setText("65535")
            else:
                self.horizontalSlider_2.setMaximum(8192)
                self.horizontalSlider_2.setValue(8192)
                self.label_4.setText("8192")
        elif modelname in deepseek_model_list:
            change_model(model_name=modelname,
                        system_instruction=self.system_instruction_list, chat_History=self.history)
            # self.pushButton_11.setEnabled(False)
            # self.pushButton_12.setEnabled(False)
            # self.pushButton_13.setEnabled(False)
            self.textBrowser.append('<h4>' + get_now_time() + '</h4>')
            self.textBrowser.append(
                '<h4>' + "Switch to " + modelname + " Model\n" + '</h4>')
        else:
            cached_content = change_model(model_name=self.cacheInfo[modelname])
            # self.pushButton_11.setEnabled(False)
            # self.pushButton_12.setEnabled(True)
            # self.pushButton_13.setEnabled(True)
            self.textBrowser.append('<h4>' + get_now_time() + '</h4>')
            self.textBrowser.append(
                '<h4>' + "Switch to " + modelname + " Model\n" + '</h4>')
            self.textBrowser.append(f"Last updated at: {cached_content[0]}")
            self.textBrowser.append(f"Expires at: {cached_content[1]}")
       
        # self.textBrowser.setText(f"Switch to{modelname}Model\n")

        # if modelname == Gemini_model_list[2]:
        #     self.pushButton_4.setEnabled(False)
        #     self.lineEdit_2.setEnabled(False)
        #     self.lineEdit_2.clear()
        #     self.filePathList = []

        #     self.horizontalSlider.setMaximum(10)
        #     self.label_2.setText("1")
        #     self.horizontalSlider.setValue(5)

        #     self.textEdit_2.setEnabled(False)
        #     self.pushButton_3.setEnabled(False)
        # else:
        #     self.pushButton_4.setEnabled(True)
        #     self.lineEdit_2.setEnabled(True)

        #     self.horizontalSlider.setMaximum(20)
        #     self.label_2.setText("2")
        #     self.horizontalSlider.setValue(5)

        #     self.textEdit_2.setEnabled(True)
        #     self.pushButton_3.setEnabled(True)

    def change_region(self):
        regex = r'\((.*?)\)'
        location = self.comboBox_2.currentText()
        region = re.search(regex, location).group(1) 
        Switch_region(region)


    def change_temperature(self):
        temperature = self.horizontalSlider.value()
        max_tokens = self.horizontalSlider_2.value()
        self.textBrowser_3.setText(str(temperature / 10))
        self.textBrowser_2.setText(str(max_tokens))
        change_config(temperature / 10, max_output_tokens=max_tokens)

    def change_max_tokens(self):
        temperature = self.horizontalSlider.value()
        max_tokens = self.horizontalSlider_2.value()
        self.textBrowser_3.setText(str(temperature / 10))
        self.textBrowser_2.setText(str(max_tokens))
        change_config(temperature / 10, max_output_tokens=max_tokens)

    def change_chat_model(self):
        chatModel = self.comboBox_3.currentText()
        if chatModel == chat_model_list[0]:
            self.chatModel = 1
            self.textBrowser.setText(f"Switch to{chat_model_list[0]}Model\n")
        elif chatModel == chat_model_list[1]:
            self.chatModel = 0
            self.textBrowser.setText(f"Switch to{chat_model_list[1]}Model\n")
        else:
            self.chatModel = 1

    def save_chat_history(self):
        chat_text = self.textBrowser.toPlainText()

        # 获取用户输入的保存文件的路径和文件名
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Chat", "", "HTML Files (*.html);;Text Files (*.txt)")

        def save_chat_history_as_html(chat_text, file_path):
            # 使用markdown库和markdown.extensions.tables扩展将纯文本内容转换为HTML格式
            extensions = ['markdown.extensions.tables',
                          'markdown.extensions.fenced_code']
            html_content = markdown.markdown(chat_text, extensions=extensions)
            # 添加CSS样式
            css_styles = """
            <style>
                table {
                    border-collapse: collapse;
                }
                table, th, td {
                    border: 1px solid black;
                    padding: 5px;
                }             
            </style>
            """
            html_content = f"{css_styles}\n{html_content}"

            # #无框
            # extensions = ['markdown.extensions.tables', 'markdown.extensions.fenced_code']
            # html_content = markdown.markdown(chat_text, extensions=extensions)
            # 保存为HTML文件
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(html_content)

        try:
            if file_path:
                chat_txt = chat_text.replace('*', '')
                if file_path.endswith(".txt"):
                    # 保存为txt格式
                    with open(file_path, 'w', encoding='utf-8') as file:
                        file.write(chat_txt)
                    # print("Chat saved as txt successfully!")
                    QMessageBox.information(self, "info", "Chat saved as txt successfully!")
                elif file_path.endswith(".html"):
                    # 保存为HTML文件
                    save_chat_history_as_html(chat_text, file_path)
                    # print("Chat saved as HTML successfully!")
                    QMessageBox.information(self, "info", "Chat saved as HTML&jsonl successfully!")
                    with open(file_path.replace(".html", '.jsonl'), 'w', encoding='utf-8') as file1:
                        for item in self.chatSessionRedords:
                            json.dump(item, file1, ensure_ascii=False)
                            file1.write('\n')
                else:
                    print("Invalid file format selected!")
        except Exception as e:
            print("An error occurred while saving the chat:", str(e))

    def load_chat_history(self):
        # 获取用户选择的文本文件路径
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Load Chat", "", "Jsonl Files (*.jsonl)")
        if file_path:
            if file_path.endswith('.txt'):
                # 从文件中读取文本内容
                with open(file_path, 'r', encoding='utf-8') as file:
                    chat_text = file.read()
                # 将文本内容加载到textBrowser中
                self.textBrowser.setPlainText(chat_text)
                # print("Chat loaded successfully!")
            elif file_path.endswith('.jsonl'):
                self.history = []
                with open(file_path, 'r', encoding='utf-8') as file:
                    for line in file.readlines():
                        if line.startswith('{"messages"'):
                            # print(line)
                            data = json.loads(line)
                            self.history.append(Content(parts=[Part.from_text(
                                text=data["messages"][0]["content"])], role=data["messages"][0]["role"]))
                            self.history.append(Content(parts=[Part.from_text(
                                text=data["messages"][1]["content"])], role=data["messages"][1]["role"]))
                # modelname = self.comboBox.currentText()
                # self.system_instruction_list = []
                # system_instruction = self.textEdit_2.toPlainText()
                # self.system_instruction_list.append(system_instruction)
                # change_model(modelname, self.system_instruction_list, self.history)
                update_chat_history(self.history)
                self.textBrowser.setPlainText(
                    "Chat session records loaded successfully!")

    def load_init_data(self):
        if not self.get_config():
            return
        self.reqSheet = SpreadSheet(self.prompt_sheet)
        if self.Training_Dataset not in self.reqSheet.get_sheet_names():
            # self.reqSheet.create_new_sheet(self.Training_Dataset)
            QMessageBox.warning(self, "Error", "Training_Dataset not found!")
            return
        # from local jsonl file
        # file_path, _ = QFileDialog.getOpenFileName(
        #     self, "Load Data Set", "", "Jsonl Files (*.jsonl)")
        # if file_path:
        #     req_list = []
        #     try:
        #         with open(file_path, 'r', encoding='utf-8') as file:
        #             for line in file.readlines():
        #                 if line.startswith('{"messages"'):
        #                     data = json.loads(line)
        #                     text = data["messages"][0]["content"]
        #                     req_list.append(text)
        #                     self.queue.append(text)
        #         # print(req_list)
        #     except Exception as e:
        #         print(f"An error occurred: {e}")
        #         QMessageBox.warning(self, "Error", "jsonl file is not valid!\n"+e)
        #         return
        #     self.autoSubmitFlag = True
        #     self.textEdit.setText(self.queue.popleft())
        #     self.display()

        # from google sheet Training_Dataset
        keys_to_check = ['Components', 'Ttitle', 'Training_Prompt']
        dataset = self.reqSheet.read_by_sheet_name(self.Training_Dataset)
        self.system_instruction_list = []
        if not all(key in dataset[0] for key in keys_to_check):
            # print("有键不存在于字典中")
            QMessageBox.warning(self, "Error", "Training_Dataset is not valid!\n"+"Please check the Training_Dataset format!")
            return
        for line in dataset:
            if line["Training_Prompt"]:
                if line["Components"] == "System instructions":
                    self.system_instruction_list.append(line["Ttitle"]+":\n"+line["Training_Prompt"])
                    continue
                self.queue.append(line["Ttitle"]+":\n"+line["Training_Prompt"])
        modelname = self.comboBox.currentText()
        # change_model(model_name=modelname,
        #              system_instruction=self.system_instruction_list, chat_History=self.history)
        if modelname in Gemini_model_list:
            update_system_instruction(self.system_instruction_list)
        elif modelname in deepseek_model_list:
            if self.system_instruction_list:
                system_instruction = ''
                for item in self.system_instruction_list:
                    system_instruction += item + "\n"
                ds_update_system_instruction(system_instruction)
        self.textBrowser.append('<h4>' + get_now_time() + '</h4>')
        self.textBrowser.append(
            '<h4>' + "Set System Prompt Successful\n" + '</h4>')
        
        self.CollectRequestCounter = False
        self.autoSubmitFlag = True
        self.errorResponseCase = 0
        self.textEdit.setText(self.queue.popleft())
        self.display()

    def load_init_TS_dataset(self):
        if not self.get_config():
            return
        self.dateSetSheet = SpreadSheet(self.test_spec_sheet)
        if self.TS_Dataset_sheet_name not in self.dateSetSheet.get_sheet_names():
            # self.reqSheet.create_new_sheet(self.Training_Dataset)
            QMessageBox.warning(self, "Error", "TS_Dataset_sheet_name not found!")
            return
        # from google sheet Training_Dataset
        keys_to_check = ['Components', 'Ttitle', 'Training_Prompt']
        dataset = self.dateSetSheet.read_by_sheet_name(self.TS_Dataset_sheet_name)
        self.system_instruction_list = []
        if not all(key in dataset[0] for key in keys_to_check):
            # print("有键不存在于字典中")
            QMessageBox.warning(self, "Error", "Training_Dataset is not valid!\n"+"Please check the Training_Dataset format!")
            return
        for line in dataset:
            if line["Training_Prompt"]:
                if line["Components"] == "System instructions":
                    self.system_instruction_list.append(line["Ttitle"]+":\n"+line["Training_Prompt"])
                    continue
                if line["Components"] == "HeadFile":
                    if line["Ttitle"] == "Path":
                    #     file_path = line["Training_Prompt"].strip().split(";")
                    #     file_path1 = list(filter(None, file_path))
                    #     for item in file_path1:
                    #         try:
                    #             model = item.split("_")[0].strip()
                    #             directory = item.split("_")[1]
                    #             if not os.path.exists(directory):
                    #                 QMessageBox.warning(self, "Error", "HeadFile Path not found!\n"+item)
                    #                 return
                    #             else:
                    #                 # 获取指定目录下所有 .txt 文件的相对路径
                    #                 txt_files = glob.glob(os.path.join(directory, '*.txt'))
                    #                 # 将相对路径转换为绝对路径
                    #                 abs_paths = [os.path.abspath(file) for file in txt_files]
                    #                 self.TS_HEADFile[model] = abs_paths
                    #         except Exception as e:
                    #             print(e)
                    #             QMessageBox.warning(self, "Error", "HeadFile Path not found!\n"+item+"\n"+str(e))
                        continue
                    elif line["Ttitle"] == "Prompt":
                        try:
                            model = "Diag"
                            directory = "./API"
                            if not os.path.exists(directory):
                                QMessageBox.warning(self, "Error", "API folder not found!\n")
                                return
                            else:
                                # 获取指定目录下所有 .txt 文件的相对路径
                                txt_files = glob.glob(os.path.join(directory, '*.txt'))
                                # 将相对路径转换为绝对路径
                                abs_paths = [os.path.abspath(file) for file in txt_files]
                                self.TS_HEADFile[model] = abs_paths
                        except Exception as e:
                            print(e)
                            QMessageBox.warning(self, "Error", "HeadFile not found!\n"+"\n"+str(e))
                        self.TS_HEADFile_Prompt = line["Training_Prompt"]
                        continue
                self.queue.append(line["Ttitle"]+":\n"+line["Training_Prompt"])
        if self.system_instruction_list:
            modelname = self.comboBox.currentText()
            if modelname in Gemini_model_list:
                update_system_instruction(self.system_instruction_list)
            elif modelname in deepseek_model_list:
                if self.system_instruction_list:
                    system_instruction = ''
                    for item in self.system_instruction_list:
                        system_instruction += item + "\n"
                    ds_update_system_instruction(system_instruction)
            self.textBrowser.append('<h4>' + get_now_time() + '</h4>')
            self.textBrowser.append(
                '<h4>' + "Set System Prompt Successful\n" + '</h4>')
        # print(self.TS_HEADFile)
        self.CollectRequestCounter = False
        self.autoSubmitFlag = True
        self.errorResponseCase = 0
        self.textEdit.setText(self.queue.popleft())
        self.display()

    def autoSubmit(self):
        if len(self.queue) == 0:
            self.autoSubmitFlag = False
            self.CollectRequestCounter = True
            if self.errorResponseCase == 0:
                QMessageBox.information(self, "info", "Auto submit finished!")
            else:
                QMessageBox.information(self, "info", "Auto submit finished!\n"+str(self.errorResponseCase)+" cases failed!")
        else:
            self.textEdit.setText(self.queue.popleft())
            self.display()

    def create_cached_chat_session(self, cache_name: str, saveHours: int) -> str:
        contents = []
        for data in self.chatSessionRedords:
            contents.append(Content(parts=[Part.from_text(
                text=data["messages"][0]["content"])], role=data["messages"][0]["role"]))
            contents.append(Content(parts=[Part.from_text(
                text=data["messages"][1]["content"])], role=data["messages"][1]["role"]))
        try:
            cached_content = caching.CachedContent.create(
                model_name=self.comboBox.currentText(),
                system_instruction=self.system_instruction_list,
                contents=contents,
                ttl=datetime.timedelta(hours=saveHours),
                display_name=cache_name,
            )
            # print(cached_content.name)
            QMessageBox.information(self, "info", f"Cache '{cached_content.name}' for model '{cached_content.model_name}' \nExpires at: {cached_content.expire_time}")
            self.refresh_cached_model()
            uploadData = [self.user_name, "Create", str(cached_content.name),
                       str(cached_content.display_name), str(cached_content.model_name), str(cached_content.expire_time), get_now_time(), self.Version]
            self.LogSheet = SpreadSheet("137NTLcEHD6wFUvwJgI4VmbGe7g4w1g4DZF4MsD_ZP7s")
            self.LogSheet.add_row(sheet_name="record_cache", row_data=uploadData)
        except InvalidArgument as e:
            QMessageBox.information(self, "info", "Cache failed!\n"+str(e))

    def create_cached(self):
        if (self.inputTokens + self.outputTokens) > 32768:
            modelname = self.comboBox.currentText()
            if modelname in Gemini_model_list:
                text, ok=QInputDialog.getText(self, 'Text Input Dialog', 'Sava Cache Name:')
                if ok and text:
                    pass
                else:
                    return
                num, ok = QInputDialog.getInt(self, 'Sava Cache Hours', 'Sava Cache Hours:')
                if ok and num:
                    pass
                else:
                    return
                self.create_cached_chat_session(cache_name=text, saveHours=int(num))
            else:
                QMessageBox.information(self, "info", "The current model does not support caching!")
                return
        else:
            QMessageBox.information(self, "info", "Cache failed!\nInput tokens + output tokens must be greater than 32768")

    def update_cached(self):
        # print(self.chatSessionRedords)
        # print(chat.history)
        if self.comboBox.currentText() in Gemini_model_list:
            QMessageBox.information(self, "info", "This is a Gemini model, no need to update cache!")
            return
        else:
            num, ok = QInputDialog.getInt(self, 'Integer input dialog', 'Sava Cache Hours:')
            if ok and num:
                pass
            else:
                return
            modeName = self.comboBox.currentText()
            expire_time = update_cached_session(self.cacheInfo[modeName], int(num))
            # self.refresh_cached_model()
            QMessageBox.information(self, "info", f"Cache Model '{modeName}' \nExpires at: {expire_time}")
            # self.comboBox.setCurrentText(modeName)
            uploadData = [self.user_name, "Update", str(self.cacheInfo[modeName]),
                       modeName, "", str(expire_time),get_now_time(), self.Version]
            self.LogSheet = SpreadSheet("137NTLcEHD6wFUvwJgI4VmbGe7g4w1g4DZF4MsD_ZP7s")
            self.LogSheet.add_row(sheet_name="record_cache", row_data=uploadData)

    def delete_cached(self):
        modeName = self.comboBox.currentText()
        if modeName in Gemini_model_list:
            QMessageBox.information(self, "info", "This is a Gemini model, no need to delete cache!")
            return
        elif modeName in deepseek_model_list:
            QMessageBox.information(self, "info", "This is a Gemini model, no need to delete cache!")
            return
        else:
            reply = QMessageBox.question(self, "Question dialog box", "Do you want to continue deleting the currently used cached model?", QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                delete_cached_session(self.cacheInfo[modeName])
                self.refresh_cached_model()
                QMessageBox.information(self, "info", f"Cache '{modeName}' deleted!")
                uploadData = [self.user_name, "Deleted", str(self.cacheInfo[modeName]),
                       modeName, "", " ", get_now_time(), self.Version]
                self.LogSheet = SpreadSheet("137NTLcEHD6wFUvwJgI4VmbGe7g4w1g4DZF4MsD_ZP7s")
                self.LogSheet.add_row(sheet_name="record_cache", row_data=uploadData)

    def refresh_cached_model(self):
        all_cache_model = get_all_cached_sessions()
        if all_cache_model:
            
            new_model_list = Gemini_model_list + list(all_cache_model.keys()) + deepseek_model_list
            ai_model = QStringListModel()
            ai_model.setStringList(new_model_list)
            self.comboBox.setModel(ai_model)
            self.cacheInfo = all_cache_model
        else:
            ai_model = QStringListModel()
            ai_model.setStringList(Gemini_model_list+deepseek_model_list)
            self.comboBox.setModel(ai_model)

    @typing_extensions.deprecated("This method has been Deprecated")
    def parser_response_to_case(self): # Deprecated
        self.pushButton_8.setEnabled(False)
        if self.tmpResponseCase == "":
            QMessageBox.information(self, "info", "No response to parse!")
            self.pushButton_8.setEnabled(True)
            return
        try:
            # case_list = parse_response(self.tmpResponseCase)
            case_list = parse_test_cases(self.tmpResponseCase)
        except Exception as e:
            QMessageBox.information(self, "info", "Parse response failed!")
            self.pushButton_8.setEnabled(True)
            return
        if len(case_list)==0:
            QMessageBox.information(self, "info", "Parsing failed!!!\nThe response content does not meet the case standard")
            self.pushButton_8.setEnabled(True)
            return
        if not self.get_config():
            self.pushButton_8.setEnabled(True)
            return
        self.specSheet = SpreadSheet(self.test_spec_sheet)
        if self.case_sheet_name not in self.specSheet.get_sheet_names():
            self.specSheet.create_new_sheet(self.case_sheet_name)
        sheet_headers = self.specSheet.get_sheet_header(self.case_sheet_name)
        Invalid_case_number = 0
        for case in case_list:
            if len(case) != 6:
                # QMessageBox.information(self, "info", "Parsing failed!!!\nThe response content does not meet the case standard")
                # return
                Invalid_case_number += 1
                continue
            row = []
            for col in sheet_headers:
                if col in CASE_HEADER_INDEX:
                    row.append(case[CASE_HEADER_INDEX.index(col)])
                else:
                    row.append("")
            self.specSheet.add_row(self.case_sheet_name, row, table_range="A3")
            self.acceptCase += 1
        # QMessageBox.information(self, "info", "write case to google sheet successfully!")
        QMessageBox.information(self, "info", "write case to google sheet finished!\nvalid case number: "+str(len(case_list)-Invalid_case_number)+"\ninvalid case number: "+str(Invalid_case_number))
        self.pushButton_8.setEnabled(True)

    def handle_request(self, prompt):
        if self.chatModel:
            self.chatSessionRedords.insert(self.chatSessionRedordsNumber, {
                                           "messages": [{"role": "user", "content": prompt}]})

        self.textEdit.setText("")
        self.textBrowser.append('<h4>' + get_now_time() + '</h4>')
        # self.textBrowser.append('<pre><p>' + prompt + '</p><pre>')
        self.textBrowser.append('<pre><p>' + prompt.replace('<', '&lt;') + '</p><pre>')
        self.inputTokens += count_tokens(prompt)

    def handle_response(self, response):
        self.textBrowser.append('<h4>' + get_now_time() + '</h4>')
        if response.startswith("The session"):
            self.textBrowser.append('<pre><font color="red">AI: ' + '\n' + response.replace('<', '&lt;') + '</font></pre>')
        else:
            self.textBrowser.append(
                '<pre><font color="green">AI: ' + '\n' + response.replace('<', '&lt;') + '</font><pre>')
        self.outputTokens += count_tokens(response)
        if self.chatModel:
            self.chatSessionRedords[self.chatSessionRedordsNumber]["messages"].append(
                {"role": "model", "content": response.strip()})
            self.chatSessionRedordsNumber += 1
            self.tmpResponseCase = response

    def handle_batch_task_signal(self, message: str):
        """
        handle batch_task signal
        Args:
            message:

        Returns:

        """
        if message.startswith("send:"):
            self.handle_request(message[5:])
            if self.batch_task:
                data_source = self.batch_task.get_datasource()
                # cache the datasource for save step
                if isinstance(data_source, GoogleSheetSinglePromptWithoutSavingDataSource):
                    self.data_source = data_source
            if self.tc_batch_task:
                data_source = self.tc_batch_task.get_datasource()
                # cache the datasource for save step
                if isinstance(data_source, TestScriptSinglePromptWithoutSavingDataSource):
                    self.tc_data_source = data_source
        elif message.startswith("recv:"):
            self.handle_response(message[5:])
        elif message.startswith("case_count:"):
            case_count = int(message[11:])
            self.acceptCase += case_count
        elif message.startswith("error:"):
            # QMessageBox.warning(self, "Error", "Auto submit Filed!\n")
            QMessageBox.warning(self, "Error", message[6:])
        # elif message.startswith("configError:"):
        #     QMessageBox.warning(self, "Error", message)
        elif message.startswith("info:"):
            QMessageBox.information(self, "info", message[5:])
        elif message.startswith("response_error:"):
            errorResponseCase = int(message[15:])
            if errorResponseCase:
                QMessageBox.information(self, "info", "Auto submit finished!\n"+str(errorResponseCase)+" cases failed!")
        else:
            #todo  button on
            QMessageBox.warning(self, "info", "Auto submit finished!\n")
            self.pushButton.setText("SUBMIT")
            self.pushButton.setEnabled(True)
            self.ButtonState = 2
            self.update_button_color()
            self.button_enable()


    def do_batch_task(self):
        #todo button off
        self.pushButton.setEnabled(False)
        self.pushButton.setText("Generating...")
        self.ButtonState = 1
        self.update_button_color()
        self.button_disable()
        self.batch_task = BatchTask()
        self.batch_task.message_signal.connect(self.handle_batch_task_signal)
        self.batch_task.start()

    def do_tc_archive(self):
        #todo button off
        self.pushButton.setEnabled(False)
        self.pushButton.setText("Generating...")
        self.ButtonState = 1
        self.update_button_color()
        self.button_disable()
        
        self.tc_archive_task = TestCaseArchiveTask()
        self.tc_archive_task.message_signal.connect(self.handle_batch_task_signal)
        self.tc_archive_task.start()

    def do_sendmail(self):
        """当发送邮件按钮被点击时执行的操作."""
        #todo button off
        case_SpreadsheetId_file = r"./config.ini"
        if os.path.exists(case_SpreadsheetId_file):
            config = configparser.ConfigParser()
            config.read(case_SpreadsheetId_file)
            try:
                self.prompt_sheet = config.get('SheetConfig', 'prompt_sheet')
            except Exception as e:
                print(f"An error occurred: {e}")
                QMessageBox.warning(self, "Error", "config.ini is not valid!\n"+str(e))
                return False
        if self.last_ai_response != "NoResponse":
            self.pushButton.setEnabled(False)
            self.pushButton.setText("Generating...")
            self.button_disable()
            # SHEET_URL = "1bPXwiEWA5ZQ2Zfntg1AJYKmd5sUqvs0NIbaCqtpiFgY"  # 替换为你的 Google Sheets URL
            SHEET_URL = self.prompt_sheet
            SHEET_NAME = "Task"  # 替换为你的工作表名称
            ROW_TO_UPDATE = 2  # 要更新的行
            COLUMN_TO_UPDATE = 4  # 要更新的列
            NEW_VALUE = self.last_ai_response # 使用 last_ai_response 变量
            try:
                # 创建 SpreadSheet 对象
                spread_sheet_instance = SpreadSheet(SHEET_URL)
                # 更新单元格
                spread_sheet_instance.update_cell(SHEET_NAME, ROW_TO_UPDATE, COLUMN_TO_UPDATE, NEW_VALUE)
                spread_sheet_instance.update_cell(SHEET_NAME, ROW_TO_UPDATE, COLUMN_TO_UPDATE-1, "ready")
                QMessageBox.information(self, "info", "Google Sheet 更新成功！")
            except Exception as e:
                print(f"更新 Google Sheet 失败: {e}")
                QMessageBox.warning(self, "Error", f"更新 Google Sheet 失败: {e}")
            #todo  button on
            self.pushButton.setText("SUBMIT")
            self.pushButton.setEnabled(True)
            self.button_enable()
        else:
            QMessageBox.warning(self, "Error", "No AI generated responses!")
            # 或者其他处理方式，例如禁用发送按钮，或者设置一个默认值
            # self.pushButton.setText("SUBMIT")
            # self.pushButton.setEnabled(True)
            # self.button_enable()
            return  # 结束函数执行
        
    def do_dmailback(self):
        """回复邮件按钮点击后的操作"""
        try:
            # 1. 读取配置文件
            config = configparser.ConfigParser()
            config.read("./config.ini")
            prompt_sheet_url = config.get('SheetConfig', 'prompt_sheet')
            spread_sheet_instance = SpreadSheet(prompt_sheet_url)  # 创建 SpreadSheet 对象

            # 2. 更新 Google Sheet 状态为 "Collecting"
            spread_sheet_instance.update_cell("Task", 3, 3, "Collecting")
            
            
            # 3. 等待 Google Sheet 更新 (最多等待 60 秒)
            prompt_data = None  # 初始化 prompt_data
            for _ in range(60):  # 使用 for 循环代替 while 循环，更简洁
                prompt_data = spread_sheet_instance.read_cell("Task", 3, 4)
                if prompt_data:
                    break
                time.sleep(1)
            # self.emailColumn = spread_sheet_instance.read_cell("Task", 3, 1)
            # self.nameColumn = spread_sheet_instance.read_cell("Task", 3, 2)
            # self.ccColumn = spread_sheet_instance.read_cell("Task", 3, 5)
            # 5. 检查是否读取到提示文本
            if not prompt_data:
                QMessageBox.warning(self, "Error", "未找到符合条件的邮件或 Google Sheet 更新超时！")
                return  # 结束函数执行

            # 6. 调用 display 函数
            prompt_data = 'Please reply to this email'+ prompt_data
            self.textEdit.setText(prompt_data)
            self.display()
        except Exception as e:
            QMessageBox.warning(self, "Error", f"操作失败: {e}") # 统一处理异常



    @typing_extensions.deprecated("This method has been Deprecated")
    def do_single_task(self): # Deprecated
        #todo button off
        self.pushButton.setEnabled(False)
        self.pushButton.setText("Generating...")
        self.button_disable()
        self.batch_task = BatchTask(task_type="single")
        self.batch_task.message_signal.connect(self.handle_batch_task_signal)
        self.batch_task.start()

    def do_single_task_send(self):
        #todo button off
        self.pushButton.setEnabled(False)
        self.pushButton.setText("Generating...")
        self.ButtonState = 1
        self.update_button_color()
        self.button_disable()
        self.batch_task = BatchTask(task_type="single")
        self.batch_task.message_signal.connect(self.handle_batch_task_signal)
        self.batch_task.start()

    @typing_extensions.deprecated("This method has been Deprecated")
    def do_single_task_save(self):
        #todo button off
        if self.data_source:
            case_count = self.data_source.manual_save_result(self.tmpResponseCase)
            if case_count > 0:
                self.acceptCase += case_count
                QMessageBox.information(self, "Success", "{} cases saved!!\n".format(case_count))
            else:
                QMessageBox.warning(self, "Error", "No case saved!!\n")
        else:
            QMessageBox.warning(self, "Error", "There is no data source, you need to send a single requirement first!!\n")

    def do_tc_batch_task(self):
        #todo button off

        # if self.TS_HEADFile == None:
        #     QMessageBox.warning(self, "Error", "Please init Dateset!!\n")
        #     return

        self.pushButton.setEnabled(False)
        self.pushButton.setText("Generating...")
        self.ButtonState = 1
        self.update_button_color()
        self.button_disable()
        self.tc_batch_task = TestScriptTask(self.TS_HEADFile, self.TS_HEADFile_Prompt)
        self.tc_batch_task.message_signal.connect(self.handle_batch_task_signal)
        self.tc_batch_task.start()

    def do_tc_single_task_send(self):
        #todo button off
        self.pushButton.setEnabled(False)
        self.pushButton.setText("Generating...")
        self.ButtonState = 1
        self.update_button_color()
        self.button_disable()

        self.tc_batch_task = TestScriptTask(self.TS_HEADFile, self.TS_HEADFile_Prompt, task_type="single")
        self.tc_batch_task.message_signal.connect(self.handle_batch_task_signal)
        self.tc_batch_task.start()

    # def do_tc_single_task_save(self):
    #     #todo button off
    #     if self.tc_data_source:
    #         case_count = self.tc_data_source.manual_save_result(self.tmpResponseCase)
    #         if case_count > 0:
    #             self.acceptCase += case_count
    #             QMessageBox.information(self, "Success", "{} cases saved!!\n".format(case_count))
    #         else:
    #             QMessageBox.warning(self, "Error", "No case saved!!\n")
    #     else:
    #         QMessageBox.warning(self, "Error", "There is no data source, you need to send a single requirement first!!\n")




    def button_disable(self):
        self.pushButton_6.setEnabled(False)
        self.pushButton_7.setEnabled(False)
        self.pushButton_8.setEnabled(False)
        self.pushButton_9.setEnabled(False)
        self.pushButton_10.setEnabled(False)

        self.pushButton_14.setEnabled(False)
        self.pushButton_15.setEnabled(False)
        self.pushButton_16.setEnabled(False)
        # self.pushButton_17.setEnabled(False)
        self.pushButton_18.setEnabled(False)
        self.pushButton_19.setEnabled(False)
        

    def button_enable(self):
        self.pushButton_6.setEnabled(True)
        self.pushButton_7.setEnabled(True)
        self.pushButton_8.setEnabled(True)
        self.pushButton_9.setEnabled(True)
        self.pushButton_10.setEnabled(True)

        self.pushButton_14.setEnabled(True)
        self.pushButton_15.setEnabled(True)
        self.pushButton_16.setEnabled(True)
        # self.pushButton_17.setEnabled(True)
        self.pushButton_18.setEnabled(True)
        self.pushButton_19.setEnabled(True)

    def get_config(self):
        case_SpreadsheetId_file = r"./config.ini"
        if os.path.exists(case_SpreadsheetId_file):
            config = configparser.ConfigParser()
            config.read(case_SpreadsheetId_file)
            try:
                self.prompt_sheet = config.get('SheetConfig', 'prompt_sheet')
                self.Training_Dataset = config.get('SheetConfig', 'Dataset_sheet_name')
                self.test_spec_sheet = config.get('TestSpec_SheetConfig', 'test_spec_sheet')
                self.case_sheet_name = config.get('SheetConfig', 'prompt_sheet_name').replace("Prompt_","TestSpec_")
                self.TS_Dataset_sheet_name = config.get('TestSpec_SheetConfig', 'TS_Dataset_sheet_name')
                self.TS_prompt_sheet_name = config.get('TestSpec_SheetConfig', 'TS_prompt_sheet_name')

                self.label_9.setText(f"""
                    <html>
                    <body>
                        <p>
                            SpreadSheets:<br> 
                            <a href="https://docs.google.com/spreadsheets/d/{self.prompt_sheet}">PromptSpreadSheet</a>
                            AND 
                            <a href="https://docs.google.com/spreadsheets/d/{self.test_spec_sheet}">TestSpecSpreadSheet</a>
                        </p>
                    </body>
                    </html>
                """)
                self.label_9.setOpenExternalLinks(True)  # 允许点击链接

                if not check_config_spread_sheet_permission(self.prompt_sheet):
                    QMessageBox.warning(self, "Error", "prompt_sheet does not have write permission!!\n")
                    return False
                if not check_config_spread_sheet_permission(self.test_spec_sheet):
                    QMessageBox.warning(self, "Error", "test_spec_sheet does not have write permission!!\n")
                    return False
                return True
            except Exception as e:
                print(f"An error occurred: {e}")
                QMessageBox.warning(self, "Error", "config.ini is not valid!\n"+str(e))
                return False
        else:
            print("config.ini not found")
            self.pushButton_7.setEnabled(False)
            self.pushButton_8.setEnabled(False)
            self.pushButton_9.setEnabled(False)
            QMessageBox.warning(self, "Error", "config.ini not found!")
            return False
        
    def show_link(self):
        case_SpreadsheetId_file = r"./config.ini"
        if os.path.exists(case_SpreadsheetId_file):
            config = configparser.ConfigParser()
            config.read(case_SpreadsheetId_file)
            try:
                self.prompt_sheet = config.get('SheetConfig', 'prompt_sheet')
                self.test_spec_sheet = config.get('TestSpec_SheetConfig', 'test_spec_sheet')
                case_sheet_name = config.get('SheetConfig', 'prompt_sheet_name')

                self.label_9.setText(f"""
                    <html>
                    <body>
                        <p>
                            SpreadSheets:<br> 
                            <a href="https://docs.google.com/spreadsheets/d/{self.prompt_sheet}">PromptSpreadSheet</a>
                            AND 
                            <a href="https://docs.google.com/spreadsheets/d/{self.test_spec_sheet}">TestSpecSpreadSheet</a>
                        </p>
                    </body>
                    </html>
                """)
                self.label_9.setOpenExternalLinks(True)  # 允许点击链接
                self.pushButton_10.setToolTip(f"Currently using sheet: {case_sheet_name}")
            except Exception as e:
                print(f"An error occurred: {e}")
                QMessageBox.warning(self, "Error", "config.ini is not valid!\n"+str(e))
                return False

def get_now_time():
    return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())


def get_display_name():
    """获取当前用户的显示名称（全名）。"""
    try:
        name_format = win32api.NameDisplay
        user_name = win32api.GetUserNameEx(name_format)
        email_address = win32api.GetUserNameEx(win32api.NameUserPrincipal)
        return user_name
    except Exception as e:
        # print(f"获取用户全名失败: {e}")
        user_name = os.getlogin()
        return user_name
    



if __name__ == "__main__":
    # output = subprocess.check_output('tasklist', shell=True)
    # if "ai_generate.exe" in str(output):
    #     os.system("taskkill /im ai_generate.exe /f 2>nul >nul")
    # 固定的，PyQt5程序都需要QApplication对象。sys.argv是命令行参数列表，确保程序可以双击运行
    app = QApplication(sys.argv)
    # 创建窗口
    myWin = MyMainForm()
    # 将窗口控件显示在屏幕上
    myWin.show()
    # 程序运行，sys.exit方法确保程序完整退出。
    sys.exit(app.exec_())
