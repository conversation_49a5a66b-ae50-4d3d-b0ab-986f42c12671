from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar, Union, cast

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..types import UNSET, Unset

if TYPE_CHECKING:
    from ..models.vector_search_request_filter_type_0 import VectorSearchRequestFilterType0


T = TypeVar("T", bound="VectorSearchRequest")


@_attrs_define
class VectorSearchRequest:
    """
    Attributes:
        query (str):
        collection_name (str):
        limit (Union[Unset, int]):  Default: 3.
        score_threshold (Union[None, Unset, float]):  Default: 0.8.
        filter_ (Union['VectorSearchRequestFilterType0', None, Unset]):
    """

    query: str
    collection_name: str
    limit: Union[Unset, int] = 3
    score_threshold: Union[None, Unset, float] = 0.8
    filter_: Union["VectorSearchRequestFilterType0", None, Unset] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        from ..models.vector_search_request_filter_type_0 import VectorSearchRequestFilterType0

        query = self.query

        collection_name = self.collection_name

        limit = self.limit

        score_threshold: Union[None, Unset, float]
        if isinstance(self.score_threshold, Unset):
            score_threshold = UNSET
        else:
            score_threshold = self.score_threshold

        filter_: Union[None, Unset, dict[str, Any]]
        if isinstance(self.filter_, Unset):
            filter_ = UNSET
        elif isinstance(self.filter_, VectorSearchRequestFilterType0):
            filter_ = self.filter_.to_dict()
        else:
            filter_ = self.filter_

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "query": query,
                "collection_name": collection_name,
            }
        )
        if limit is not UNSET:
            field_dict["limit"] = limit
        if score_threshold is not UNSET:
            field_dict["score_threshold"] = score_threshold
        if filter_ is not UNSET:
            field_dict["filter"] = filter_

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.vector_search_request_filter_type_0 import VectorSearchRequestFilterType0

        d = dict(src_dict)
        query = d.pop("query")

        collection_name = d.pop("collection_name")

        limit = d.pop("limit", UNSET)

        def _parse_score_threshold(data: object) -> Union[None, Unset, float]:
            if data is None:
                return data
            if isinstance(data, Unset):
                return data
            return cast(Union[None, Unset, float], data)

        score_threshold = _parse_score_threshold(d.pop("score_threshold", UNSET))

        def _parse_filter_(data: object) -> Union["VectorSearchRequestFilterType0", None, Unset]:
            if data is None:
                return data
            if isinstance(data, Unset):
                return data
            try:
                if not isinstance(data, dict):
                    raise TypeError()
                filter_type_0 = VectorSearchRequestFilterType0.from_dict(data)

                return filter_type_0
            except:  # noqa: E722
                pass
            return cast(Union["VectorSearchRequestFilterType0", None, Unset], data)

        filter_ = _parse_filter_(d.pop("filter", UNSET))

        vector_search_request = cls(
            query=query,
            collection_name=collection_name,
            limit=limit,
            score_threshold=score_threshold,
            filter_=filter_,
        )

        vector_search_request.additional_properties = d
        return vector_search_request

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
