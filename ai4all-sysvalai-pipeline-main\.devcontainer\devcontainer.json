// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/ubuntu
{
	"name": "AI4ALL",
	// Or use a Dockerfile or Docker Compose file. More info: https://containers.dev/guide/dockerfile
	"image": "mcr.microsoft.com/devcontainers/base:noble",
	// Features to add to the dev container. More info: https://containers.dev/features.
	"features": {
		"ghcr.io/devcontainers/features/common-utils:2": {
			"configureZshAsDefaultShell": true
		},
		"ghcr.io/devcontainers/features/git:1": {},
		"ghcr.io/devcontainers/features/github-cli:1": {},
		"ghcr.io/devcontainers-contrib/features/curl-apt-get:1": {},
		"ghcr.io/devcontainers-contrib/features/wget-apt-get:1": {},
		"ghcr.io/devcontainers-contrib/features/copier:7": {},
		"ghcr.io/devcontainers-contrib/features/poetry:2": {},
		"ghcr.io/devcontainers-contrib/features/ruff:1": {},
		"ghcr.io/devcontainers/features/node:1": {},
		"ghcr.io/devcontainers/features/docker-in-docker:2": {},
		"ghcr.io/dhoeric/features/act:1": {},
		"ghcr.io/dhoeric/features/google-cloud-cli:1": {
			"installGkeGcloudAuthPlugin": true
		},
		"ghcr.io/devcontainers-contrib/features/jfrog-cli:1": {},
		"ghcr.io/devcontainers/features/terraform:1": {
			"version": "1.5.7"
		}
	},
	// Configure tool-specific properties.
	"customizations": {
		"vscode": {
			"extensions": [
				// AI Stuff
				"googlecloudtools.cloudcode",
				"VisualStudioExptTeam.intellicode-api-usage-examples",
				// Git Stuff
				"mhutchie.git-graph",
				"donjayamanne.githistory",
				"vivaxy.vscode-conventional-commits",
				// Python Stuff
				"ms-python.python",
				"ms-toolsai.jupyter",
				"charliermarsh.ruff",
				"njpwerner.autodocstring",
				"donjayamanne.python-environment-manager",
				// Generic Stuff
				"ms-azuretools.vscode-docker",
				"hashicorp.terraform",
				"redhat.vscode-xml",
				"redhat.vscode-yaml",
				"tamasfe.even-better-toml",
				"mechatroner.rainbow-csv",
				"mathematic.vscode-pdf",
				"shd101wyy.markdown-preview-enhanced",
				"bierner.markdown-mermaid",
				"mikestead.dotenv",
				"bruno-api-client.bruno",
				"42Crunch.vscode-openapi",
				// Web Stuff
				"dbaeumer.vscode-eslint",
				// "esbenp.prettier-vscode",
				"christian-kohler.npm-intellisense",
				"ritwickdey.LiveServer",
				"wix.vscode-import-cost",
				"formulahendry.auto-close-tag",
				"formulahendry.auto-rename-tag",
				"burkeholland.simple-react-snippets",
				// Prevented Stuff
				"-ms-python.autopep8",
				"-ms-azuretools.vscode-azureterraform"
			],
			"settings": {
				"extensions.autoUpdate": true,
				"dev.containers.workspaceMountConsistency": "consistent",
				"cloudcode.duetAI.project": "valeo-cp2384",
				"telemetry.telemetryLevel": "off",
				"redhat.telemetry.enabled": false,
				// "cloudcode.enableTelemetry": false, //Temp fix for bug in GCA
				"files.associations": {
					".env*": "dotenv"
				},
				"conventionalCommits.autoCommit": false,
				"[python]": {
					"editor.formatOnSave": true,
					"editor.defaultFormatter": "charliermarsh.ruff",
					"editor.codeActionsOnSave": {
						"source.organizeImports": "explicit"
					}
				},
				"notebook.formatOnSave.enabled": true,
				"notebook.codeActionsOnSave": {
					"source.organizeImports": "explicit"
				},
				"[jsonc]": {
					"editor.defaultFormatter": "vscode.json-language-features"
				},
				"[yaml]": {
					"editor.autoIndent": "full"
				},
				"[typescript]": {
					"editor.formatOnSave": true
				},
				"[javascript]": {
					"editor.formatOnSave": true
				},
				"[javascriptreact]": {
					"editor.formatOnSave": true
				}
			}
		}
	},
	// Sepcify minimum resource needs
	"hostRequirements": {
		"cpus": 4,
		"memory": "4gb",
		"storage": "32gb",
		"gpu": "optional"
	},
	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// "forwardPorts": [],
	// Use 'onCreateCommand', 'postCreateCommand' to run commands after the container is created.
	"onCreateCommand": {
		"default-apt-packages": "sudo apt update && sudo apt upgrade -y && sudo apt install -y build-essential libssl-dev zlib1g-dev libbz2-dev libreadline-dev libsqlite3-dev libncursesw5-dev xz-utils tk-dev libxml2-dev libxmlsec1-dev libffi-dev liblzma-dev git-lfs"
	},
	"postCreateCommand": {
		"git": "git config --global http.sslverify false && git lfs install",
		"pyenv": "curl https://pyenv.run | bash && echo 'export PYENV_ROOT=\"$HOME/.pyenv\"' | tee -a ~/.zshrc ~/.zprofile ~/.bashrc ~/.profile && echo 'command -v pyenv >/dev/null || export PATH=\"$PYENV_ROOT/bin:$PATH\"' | tee -a ~/.bashrc ~/.profile && echo '[[ -d $PYENV_ROOT/bin ]] && export PATH=\"$PYENV_ROOT/bin:$PATH\"' | tee -a ~/.zshrc ~/.zprofile && echo 'eval \"$(pyenv init -)\"' | tee -a ~/.zshrc ~/.zprofile ~/.bashrc ~/.profile && . ~/.profile && pyenv update && pyenv install 3.12 && pyenv rehash && pyenv global 3.12",
		"poetry": "poetry config virtualenvs.prefer-active-python true && poetry config virtualenvs.in-project true"
	}
	// Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
	// "remoteUser": "root"
}
