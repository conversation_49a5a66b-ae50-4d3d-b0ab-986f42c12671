<template>
  <q-card class="q-pa-md card-container">
    <!-- File Upload -->
    <q-file
      v-model="selectedFile"
      label="Upload CSV or XLSX"
      accept=".csv, .xlsx"
      @update:model-value="handleFileUpload"
      max-file-size="10485760"
      class="q-mb-md file-input"
      filled
      standout="bg-primary text-white"
      :disable="upload_file_loading"
    >
      <template v-slot:prepend>
        <q-icon name="attach_file" />
      </template>
    </q-file>

    <q-btn
      label="GENERATE TESTCASE"
      color="blue-10"
      :disable="
        !selectedFile || selectedRows.length === 0 || tableData.length === 0
      "
      @click="file_upload_button"
      class="q-mb-md generate-btn"
      :loading="upload_file_loading"
    />

    <!-- Data Table -->
    <q-table
      v-if="tableData.length"
      :rows="tableData"
      :columns="columns"
      row-key="id"
      flat
      bordered
      hide-pagination
      :rows-per-page-options="[0]"
      class="styled-table"
    >
      <!-- Header Checkbox -->
      <template v-slot:header="props">
        <q-tr :props="props" class="header-row">
          <q-th>
            <q-checkbox
              v-model="selectAll"
              @update:model-value="toggleSelectAll"
              :disable="upload_file_loading"
            />
          </q-th>
          <q-th v-for="col in props.cols" :key="col.name" class="table-header">
            {{ col.label }}
          </q-th>
        </q-tr>
      </template>

      <!-- Row Checkboxes -->
      <template v-slot:body="props">
        <q-tr :props="props" class="table-row">
          <q-td>
            <q-checkbox
              :model-value="selectedRows.includes(props.rowIndex)"
              @update:model-value="toggleRowSelection(props.rowIndex)"
              :disable="upload_file_loading"
            />
          </q-td>
          <q-td v-for="col in props.cols" :key="col.name" class="table-cell">
            {{ props.row[col.field] }}
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </q-card>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useQuasar } from "quasar";
import * as XLSX from "xlsx";
import Papa from "papaparse";
// import { useHomeStore } from "src/stores/home/<USER>";
import { useRunTcGenerationStore } from "src/stores/ai_testcase/run-tc-generation-store.js";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";

const ai_testcase_config = useRunTcGenerationStore();
const {
  user_email,
  selectedBG,
  selectedPG,
  selectedPL,
  algorithms_and_sensor_inputs,
  selectedAlgorithms,
  selectedSensors,
  selectedFunctionality,
  selectedFunctionalityUuid,
  selectedFile,
  tableData,
  columns,
  selectedRows,
  current_job_execution_id,
  upload_file_loading,
} = storeToRefs(ai_testcase_config);

const { get_user_email, uploadFile } = ai_testcase_config;

// const home_store_config = useHomeStore();
// const { main_email, first_letter_email } = storeToRefs(home_store_config);

const $q = useQuasar();
const router = useRouter();

onMounted(async () => {
  await get_user_email();
  // main_email.value = user_email.value;
  // first_letter_email.value = main_email.value.charAt(0).toUpperCase();
  if (!selectedBG.value) {
    router.push("/ai_testcase/create_job");
  } else {
    current_job_execution_id.value = "";
  }
});

const selectAll = computed({
  get: () =>
    selectedRows.value.length === tableData.value.length &&
    tableData.value.length > 0,
  set: (val) => toggleSelectAll(val),
});

const handleFileUpload = (file) => {
  if (!file) return;
  const reader = new FileReader();
  reader.onload = (event) => {
    const data = event.target.result;
    file.name.endsWith(".csv") ? parseCSV(data) : parseXLSX(data);
  };

  if (file.size > 10485760) {
    $q.notify({ type: "negative", message: "File size exceeds 10MB limit" });
    selectedFile.value = null;
  } else {
    reader.readAsBinaryString(file);
  }
};

const parseCSV = (data) => {
  Papa.parse(data, {
    header: true,
    skipEmptyLines: true,
    complete: (result) => {
      tableData.value = result.data;
      if (validateTableData()) {
        updateColumns(Object.keys(result.data[0]));
      }
    },
  });
};

const parseXLSX = (data) => {
  const workbook = XLSX.read(data, { type: "binary" });
  const sheetData = XLSX.utils.sheet_to_json(
    workbook.Sheets[workbook.SheetNames[0]]
  );
  tableData.value = sheetData;
  if (validateTableData()) {
    updateColumns(Object.keys(sheetData[0]));
  }
};

const updateColumns = (keys) => {
  columns.value = keys.map((key) => ({
    name: key,
    label: key,
    field: key,
    align: "left",
  }));
};

const toggleRowSelection = (index) => {
  selectedRows.value.includes(index)
    ? (selectedRows.value = selectedRows.value.filter((i) => i !== index))
    : selectedRows.value.push(index);
};

const toggleSelectAll = (val) => {
  selectedRows.value = val ? tableData.value.map((_, index) => index) : [];
};

const validateTableData = () => {
  const hasEmptyRow = tableData.value.some((row) =>
    Object.values(row).every(
      (cell) => cell === null || cell === undefined || cell === ""
    )
  );

  if (hasEmptyRow) {
    $q.notify({
      type: "negative",
      message:
        "The uploaded file contains empty rows. Please clean the file and try again.",
    });
    selectedFile.value = null;
    tableData.value = [];
    columns.value = [];
    return false;
  }
  return true;
};

const file_upload_button = async () => {
  await uploadFile();
  selectedBG.value = "";
  selectedPG.value = "";
  selectedPL.value = "";
  algorithms_and_sensor_inputs.value = [];
  selectedAlgorithms.value = [];
  selectedSensors.value = [];
  selectedFunctionality.value = "";
  selectedFunctionalityUuid.value = "";
  selectedFile.value = null;
  tableData.value = [];
  columns.value = [];
  selectedRows.value = [];
  router.push(`/ai_testcase/job_status/${current_job_execution_id.value}`);
};
</script>

<style scoped>
.card-container {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  max-width: 800px;
  margin: auto;
}

.file-input {
  width: 100%;
  border-radius: 8px;
}

.generate-btn {
  width: 100%;
  font-weight: bold;
  border-radius: 8px;
}

.styled-table {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.header-row {
  background-color: #f5f5f5;
  font-weight: bold;
}

.table-header {
  text-align: left;
  padding: 10px;
}

.table-row:nth-child(even) {
  background-color: #f9f9f9;
}

.table-cell {
  padding: 12px;
  text-align: left;
}
</style>
