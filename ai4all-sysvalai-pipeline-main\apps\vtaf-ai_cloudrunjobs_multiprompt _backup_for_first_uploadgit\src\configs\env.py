from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import Optional

class AppSettings(BaseSettings):
    # Google Cloud 配置
    PROJECT_ID: str = "valeo-cp2673-dev"
    PROJECT_NUMBER: Optional[str] = None
    REGION: str = "us-central1"
    
    # AI 模型配置
    VERTEX_AI_MODEL: str = "gemini-1.5-pro"
    VERTEX_EMBEDDING_MODEL: str = "text-embedding-004"
    DEFAULT_MODEL: str = "gemini-1.5-pro"

    # DeepSeek 配置
    DEEPSEEK_API_KEY: Optional[str] = None
    DEEPSEEK_BASE_URL: str = "http://10.168.44.171:3000/api"
    
    # Google Sheets 配置
    GOOGLE_SHEETS_ENABLED: bool = True
    SERVICE_ACCOUNT_FILE: str = "service-account.json"

    # 硬编码的服务账号信息（用于本地测试，云端环境中可选）
    SERVICE_ACCOUNT_INFO: Optional[dict] = None
    
    # 批处理配置
    BATCH_SIZE: int = 10
    MAX_RETRIES: int = 3
    RETRY_DELAY: int = 5
    
    # 生成配置
    MAX_OUTPUT_TOKENS: int = 2048
    TEMPERATURE: float = 0.5
    TOP_P: float = 1.0
    TOP_K: int = 32

    model_config = SettingsConfigDict(
        env_file=".env",
        case_sensitive=True,
        env_file_encoding='utf-8',
        extra='ignore'  # 忽略额外的环境变量
    )

def create_settings():
    """创建设置实例，支持不同环境"""
    import os

    # 检查是否在 Google Cloud 环境中
    is_cloud_env = any([
        os.getenv('K_SERVICE'),  # Cloud Run
        os.getenv('FUNCTION_NAME'),  # Cloud Functions
        os.getenv('GAE_SERVICE'),  # App Engine
        os.getenv('GOOGLE_CLOUD_PROJECT')  # 通用 GCP 环境
    ])

    if is_cloud_env:
        # 在云端环境中，优先使用默认认证，不使用硬编码服务账号
        return AppSettings()
    else:
        # 在本地环境中，使用硬编码的服务账号信息
        hardcoded_service_account = **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        return AppSettings(SERVICE_ACCOUNT_INFO=hardcoded_service_account)

# 创建全局设置实例
settings = create_settings()



# class AppSettings(BaseSettings):
#     # Google Cloud 配置
#     PROJECT_ID: str = "your-project-id"
#     PROJECT_NUMBER: str = "your-project-number"
#     REGION: str = "us-central1"
    
#     # AI 模型配置
#     VERTEX_AI_MODEL: str = "gemini-1.5-pro"
#     VERTEX_EMBEDDING_MODEL: str = "text-embedding-004"
#     DEFAULT_MODEL: str = "gemini-1.5-pro"
    
#     # DeepSeek 配置
#     DEEPSEEK_API_KEY: str = ""
#     DEEPSEEK_BASE_URL: str = "http://10.168.44.171:3000/api"
    
#     # Google Sheets 配置
#     GOOGLE_SHEETS_ENABLED: bool = True
#     SERVICE_ACCOUNT_FILE: str = "service-account.json"
    
#     # 批处理配置
#     BATCH_SIZE: int = 10
#     MAX_RETRIES: int = 3
#     RETRY_DELAY: int = 5
    
#     # 生成配置
#     MAX_OUTPUT_TOKENS: int = 2048
#     TEMPERATURE: float = 0.5
#     TOP_P: float = 1.0
#     TOP_K: int = 32

#     model_config = SettingsConfigDict(
#         env_file=".env",
#         case_sensitive=True
#     )

# settings = AppSettings()


