<template>
  <div class="q-pa-md">
    <div v-for="(message, index) in current_session_details" :key="index" class="q-mb-md">
      <div
        class="row items-start"
        :class="message.role === 'user' ? 'justify-end' : 'justify-start'"
      >
        <q-avatar size="32px" class="q-mr-sm" v-if="message.role === 'model'">
          <q-icon name="smart_toy" color="primary" />
        </q-avatar>
        <q-avatar size="32px" class="q-ml-sm" v-if="message.role === 'user'">
          <q-icon name="person" color="grey-8" />
        </q-avatar>

        <q-card class="chat-bubble" :class="message.role">
          <q-card-section class="text-body2">
            <div v-for="(part, i) in message.parts" :key="i">
              <!-- <div v-if="part.type === 'text'">
                {{ part.data }}
              </div> -->
              <div
                v-if="part.type === 'text'"
                v-html="message.role === 'model' ? renderMarkdown(part.data) : part.data"
                class="markdown-body"
              ></div>
              <!-- You can handle more types here -->
            </div>
            <div v-if="message.loading" class="row justify-center q-mt-sm">
              <q-spinner-dots size="24px" color="primary" />
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
    <q-inner-loading :showing="isConversationLoading">
      <q-spinner-puff size="50px" color="primary" />
    </q-inner-loading>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useChatWithKnowledgebaseStore } from 'src/stores/chat_with_kb/chat_with_knowledgebase_router';
import { storeToRefs } from 'pinia';
import { nextTick } from 'vue';
import MarkdownIt from 'markdown-it'
const md = new MarkdownIt({ html: true, linkify: true, breaks: true })

function renderMarkdown(text) {
  return md.render(text || '')
}

const route = useRoute()

const chat_with_kbStore = useChatWithKnowledgebaseStore();
const { current_session_id, chat_history, current_session_details, isConversationLoading } = storeToRefs(chat_with_kbStore);
const { get_user_email, get_session_conversation } = chat_with_kbStore;

const props = defineProps({
  session_id: {
    type: String,
    required: true,
  },
});

const loadSession = async (sessionId) => {
  isConversationLoading.value = true;
  current_session_details.value = [];
  current_session_id.value = sessionId;
  await get_session_conversation();
  isConversationLoading.value = false;
};

onMounted(async () => {
  if(route.params.session_id != ""){
    await get_user_email();
    console.log("route parames : ", route.params.session_id)
    current_session_id.value = route.params.session_id;
    console.log("current_session_id :", current_session_id.value)
    await loadSession(current_session_id.value);
  }
});

watch(
  () => route.params.session_id,
  async (newSessionId) => {
    await loadSession(newSessionId);
  }
);

watch(current_session_details, async () => {
  await nextTick();
  const container = document.querySelector('.q-pa-md');
  container.scrollTop = container.scrollHeight;
}, { deep: true });
</script>

<style scoped>
.chat-bubble {
  max-width: 70%;
  padding: 12px;
  border-radius: 12px;
  word-wrap: break-word;
  white-space: pre-wrap;
}
.chat-bubble.user {
  background-color: #e0f7fa;
  color: #7d9792;
}
.chat-bubble.model {
  background-color: #f1f8e9;
  color: #33691e;
}

.markdown-body :deep(*) {
  font-family: inherit;
  font-size: 14px;
  line-height: 1.6;
}

.markdown-body :deep(pre) {
  background-color: #f6f8fa;
  padding: 0.6em;
  border-radius: 6px;
  overflow-x: auto;
}

.markdown-body :deep(code) {
  background-color: #f0f0f0;
  padding: 2px 4px;
  border-radius: 4px;
  font-family: 'Fira Code', monospace;
}
</style>
