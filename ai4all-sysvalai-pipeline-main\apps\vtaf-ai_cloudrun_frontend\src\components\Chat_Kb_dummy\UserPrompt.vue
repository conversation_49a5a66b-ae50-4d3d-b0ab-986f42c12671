<template>
  <q-input
      v-model="user_prompt"
      type="textarea"
      placeholder="Enter Prompt here and click ENTER"
      filled
      @keydown.enter="handleEnter"
      style="flex: 1; margin: 0 8px; overflow-y: auto; max-height: 200px"
      autogrow
    />
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useChatWithKbStore } from "src/stores/chat_with_kb_dummy-store";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";

const router = useRouter();

const ai_testcase_config = useChatWithKbStore();
const { user_email, user_prompt, output } =
  storeToRefs(ai_testcase_config);
  const { get_response } = ai_testcase_config

const handleEnter = (event) => {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    get_response();
  }
};
</script>
