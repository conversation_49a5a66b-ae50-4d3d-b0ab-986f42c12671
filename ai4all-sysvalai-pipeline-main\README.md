# AI4ALL Folder Structure & Repository Template

## Table of Contents

- [AI4ALL Folder Structure \& Repository Templates](#ai4all-folder-structure--repository-templates)
  - [Table of Contents](#table-of-contents)
  - [Project Structure](#project-structure)
  - [Repository Naming Convention](#repository-naming-convention)
  - [Branching Strategy](#branching-strategy)
  - [Tags Naming Convention](#tags-naming-convention)
  - [Git Commits Messages Style](#git-commits-messages-style)
  - [Github Issues \& PR Templates](#github-issues--pr-templates)
    - [Issue Templates:](#issue-templates)
    - [Pull Request Template:](#pull-request-template)
  - [Apps \& Packages Templates:](#apps--packages-templates)
    - [Available Packages Templates:](#available-packages-templates)
    - [Available Apps Templates:](#available-apps-templates)
  - [Getting Started](#getting-started)
    - [Overview](#overview)
    - [Prerequisites](#prerequisites)
    - [Let's start by using the template repository](#lets-start-by-using-the-template-repository)
    - [Let's add a new app/package](#lets-add-a-new-apppackage)

## Project Structure

This project follows a structured layout to keep code organized and maintainable. Here's an overview of the main folders and their purposes:

- **`.github`:**  Contains GitHub-specific configuration files (workflows, issue templates, etc.).
- **`adrs`:**  Contains the Architecture Decision Rercords (ADRs) related to the whole repository.
- **`apps`:**  Houses the core application code (frontend, backend, or potentially multiple apps).
- **`data`:** Stores data files used by the applications (sample data, configuration files, etc.).
- **`docker`:** Contains Dockerfiles and related configuration for containerizing the project.
- **`docs`:** Holds project documentation (user guides, technical specifications, API references).
- **`examples`:** Provides example code snippets or small projects to demonstrate usage of the project's components or libraries.
- **`packages`:** Manages reusable code packages or libraries developed as part of the project.
- **`scripts`:**  Stores utility scripts for automating tasks like building, testing, deployment, etc.

## Repository Naming Convention

When creating a new repository it should follow the following naming convention  
```
ai4all-<pipeline_acronym>-<repository_name>
```

where:
- **`<pipeline_acronym>`:** is the acronym of your pipeline name, e.g. for software it is sw, for system it is sys, ...
- only small letters and numbers are used
- no spaces shall be used
- hypghens (-) are only used as shown as seperators, you can use underscore (\_) as seperator in your repoistory name or pipeline acronym

## Branching Strategy

This project utilizes a branching strategy with five long-lived branches to manage the software development lifecycle effectively:

- **`prod`:** This protected branch represents the production-ready codebase. Only pull requests from `uat` are merged into this branch. 
- **`uat`:**  This branch serves as the User Acceptance Testing environment. Only pull requests from `main` are merged into this branch for final testing before deployment to production.
- **`main` :**  The primary development branch where new features and bug fixes are integrated. Developers create feature branches off of `main` and create a pull request to merge them back after successful testing.
- **`prototype`:** This branch is dedicated to experimenting with new ideas and concepts. It allows for rapid prototyping and exploration without impacting the stability of other branches.
- **`feasibility`:** Used for initial investigations and proof-of-concept work.

Along with these 5 long-lived branches, the following supporting branches are also used:

- **`feature/xyz`, `feature/abc` :** These branches are created from `main` and are used to develop new features or enhancements. Once the feature is complete and tested, it is merged back into the `main` branch.
- **`enabler/xyz`, `enabler/abc` :** These branches are created from `main` and are used to develop an enabler (infra, scripts, docker files, ...). Once the enabler is complete and tested, it is merged back into the `main` branch.
- **`docs/xyz`, `docs/abc` :** These branches are created from `main` and are used to add documentation only. They should be used rarely as any feature or enabler should have documentation added with it. Once documentation is ready, it is merged back into the `main` branch.
- **`bugfix/xyz`, `bugfix/abc` :** These branches are created from `main` and are used to fix bugs or issues identified in the codebase. Once the bug is fixed and tested, it is merged back into the `main` branch.
- **`hotfix/xyz`, `hotfix/abc` :** These branches are created from `prod` or `uat` and are used to fix critical bugs or issues that need immediate attention in the production or uat environment. Once the hotfix is implemented and tested, it is merged back into the `prod` or `uat` and the `main` branches.

The following image represents the branching strategy  
[![](https://mermaid.ink/img/pako:eNqtVE9PpDAU_yrkJcQLImUGhB6NyZ42e9jEg-HSoR1oFErqI-ss4bvbDox2GOPorlxaHu_3Dx4doFRcAAXfHzzZSqTecFFJ_KFZV1_Ym4bJ9kaztqx_aS60qa3H0Rt9v2gPfUXrmatUTSNx2m_2AK_TinvKwmh02lTWonxQPXpW4gjXM5xhq6_AtoI9yY18lLib4SRaYJyWd5g_pWJCocJdJ2aNfAF4fX5G4LORsNfiyq5kFkxOM731fFmoEboS5zjecRPPbtJvUIw9ZBUt4I6EURhdbgSykBSwYDBT4RJYwmOcLt9Qs-Na4VY-X03L4QXG_2j5mOxjdwthxyc58umODXcJ7D_gpvso2OFbkO8IFp8xtlB2k52EOpnnTV9ZsFnIf86Py-R4iF-nBwIwnQbGzfE2WGgBWItGFEDNljP9UEAw1f9IjvVUzyLfxhgNnPWofu_aEijqXgTQd5yhuJWs0qwBumWPT6basRboAM9ASRyH64yQaJWukyRKkiyAHdB1EubXK5KR_Do1LatsDOCvUoYhCvM4TQ0kz01znGQkAMElKv1zOpX3h_Ne4n4PmHxo1Vf1rD--ALa63EM?type=png)](https://mermaid.live/edit#pako:eNqtVE9PpDAU_yrkJcQLImUGhB6NyZ42e9jEg-HSoR1oFErqI-ss4bvbDox2GOPorlxaHu_3Dx4doFRcAAXfHzzZSqTecFFJ_KFZV1_Ym4bJ9kaztqx_aS60qa3H0Rt9v2gPfUXrmatUTSNx2m_2AK_TinvKwmh02lTWonxQPXpW4gjXM5xhq6_AtoI9yY18lLib4SRaYJyWd5g_pWJCocJdJ2aNfAF4fX5G4LORsNfiyq5kFkxOM731fFmoEboS5zjecRPPbtJvUIw9ZBUt4I6EURhdbgSykBSwYDBT4RJYwmOcLt9Qs-Na4VY-X03L4QXG_2j5mOxjdwthxyc58umODXcJ7D_gpvso2OFbkO8IFp8xtlB2k52EOpnnTV9ZsFnIf86Py-R4iF-nBwIwnQbGzfE2WGgBWItGFEDNljP9UEAw1f9IjvVUzyLfxhgNnPWofu_aEijqXgTQd5yhuJWs0qwBumWPT6basRboAM9ASRyH64yQaJWukyRKkiyAHdB1EubXK5KR_Do1LatsDOCvUoYhCvM4TQ0kz01znGQkAMElKv1zOpX3h_Ne4n4PmHxo1Vf1rD--ALa63EM)

This strategy ensures a clear separation of concerns, promotes code stability, and facilitates a streamlined development workflow.

## Tags Naming Convention

When creating a tag for a specific app or package you should name your tag as follows:

> {appName or pkgName}/{[semVer](https://semver.org/)}

E.g.  
`myApp/1.0.0-beta1`  
`myPkg/2.0.1-rc2`

If you are creating a tag for the whole repository you can define the semVer directly.

## Git Commits Messages Style

You should always utilise [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/) to ensure clear and consistent commit messages.

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

- **`<type>`:** This indicates the type of change being made. The allowed types are:
    - **`feat`:** A new feature
    - **`fix`:** A bug fix
    - **`docs`:** Documentation only changes
    - **`style`:** Changes that do not affect the meaning of the code (white-space, formatting, missing semi-colons, etc)
    - **`refactor`:** A code change that neither fixes a bug nor adds a feature
    - **`perf`:** A code change that improves performance
    - **`test`:** Adding missing tests or correcting existing tests
    - **`chore`:** Changes to the build process or auxiliary tools and libraries such as documentation generation
    - **`revert`:** Reverts a previous commit
- **`[optional scope]`:** This specifies the scope of the change, such as the app or package that was affected.
- **`<description>`:** A brief, imperative tense description of the change.
- **`[optional body]`:** A more detailed explanation of the change, if necessary.
- **`[optional footer(s)]`:** One or more footers, such as `BREAKING CHANGE:` or `Closes #123`.
- `BREAKING CHANGE` can also be represented by writing `!` after `<type>[optional scope]`, e.g. `feat(pkg2)!: ...`

**Examples:**

```
feat(app1): add new endpoint for retrieving user data

This commit adds a new endpoint to the API that allows clients to retrieve user data.

BREAKING CHANGE: The format of the user data returned by the API has changed.
```

```
fix(pkg2): fix bug that caused the login form to be displayed incorrectly

This commit fixes a bug that caused the login form to be displayed incorrectly in some browsers.
```

```
docs(readme): update installation instructions

This commit updates the installation instructions in the README file.
```

## Github Issues & PR Templates

Included in this repository the following templates to be used in your project.

### Issue Templates:

- **`bug report` :** Use this template to report a bug or issue you've encountered in the project.
- **`documentation` :** Use this template to suggest improvements or additions to the project's documentation.
- **`enabler` :** Use this template for tasks or stories that are not user-facing features but are necessary to enable other features or improve the development process. This could include infrastructure work, refactoring, or technical debt reduction.
- **`feature` :** Use this template to create a new feature for the project.
- **`task` :** Use this template for small, well-defined tasks that represents a part of a full feature request.

### Pull Request Template:

- **`pull request` :** Use this template when submitting a pull request to the project. Provide a concise summary of your changes, link to the relevant issue, and outline any specific areas where you'd like feedback.

You can check them out from here: 

## Apps & Packages Templates:

Along side this repository template, we will use a tool called `Copier` to spawn folder structure for different `apps` and `packages` in our project.

> **What is `Copier` ?**  
> [`Copier`](https://copier.readthedocs.io/en/stable/) is a project templating tool that helps automate the creation of projects based on predefined templates.

### Available Packages Templates:
- **Generic Package:** Provides a basic structure for creating a generic package.
- **Python Poetry Package:** Offers a template for Python packages, leveraging the Poetry dependency manager and build system for streamlined development and packaging.
- **Node Package:** Sets up a foundation for developing Node.js packages.

### Available Apps Templates:
- **FastAPI Backend App:** Provides a starting point for building backend applications using the FastAPI framework in Python.
- **Python Script App:** Offers a simple structure for creating command-line Python scripts, ideal for automating tasks or performing data processing.
- **Notebook App:** Sets up an environment for interactive data analysis and exploration using Jupyter Notebooks, suitable for prototyping, visualization, and sharing insights.
- **Cloud Function App:** Provides a template for deploying serverless functions to GCP.
- **React Frontend App:** Offers a foundation for building dynamic and interactive user interfaces using the React JavaScript library.

**You can find the repository that finds these templates using this link:**  
[**Templates Library**](https://github-ri.vnet.valeo.com/GROUP-AI4ALL/ai4all-infra-templates_library)

**You can request a new template or submit a bug/improvement to an existing template from [here](https://github-ri.vnet.valeo.com/GROUP-AI4ALL/ai4all-infra-repo_template/issues).**  

## Getting Started

### Overview

We will check how we can create a new repository based on this template and how to use `Copier` to create a new app/package inside this repository.

### Prerequisites

**Make sure you have these applications before starting:**
- `Python 3.8+`
- `Git`
- `pipx`
- `Copier`: pipx install copier

### Let's start by using the template repository

Go to [AI4ALL GitHub](https://github-ri.vnet.valeo.com/GROUP-AI4ALL)

Choose to create a new repository:

![New Repository](https://github-ri.vnet.valeo.com/storage/user/289/files/395a8146-f73d-48d5-b17d-bd836f95c94c)

We should choose our template (GROUP-AI4ALL/ai4all-infra-repo_template) from here:

![Choosing the template repo](https://github-ri.vnet.valeo.com/storage/user/289/files/64f8936e-6482-4982-a933-54d47b8a60d6)

**But do not forget to include all branches**
![include all branches](https://github-ri.vnet.valeo.com/storage/user/289/files/7295d3d8-ea67-43bf-a654-c49255d80f15)

Select the name of the repository according to the convention 'ai4all-\<pipeline_acronym\>-\<repo_name\>':

![Choose name](https://github-ri.vnet.valeo.com/storage/user/289/files/4e4420de-63e8-4aa1-a542-015851f23616)

You can clone the newly created repository and overwrite the readme file.
![clone repo](https://github-ri.vnet.valeo.com/storage/user/289/files/9e17ae07-e9a6-471f-a507-9fcfec405769)

**And we're done !**

### Let's add a new app/package
Make sure you have `Copier` installed and you are in the main directory of your cloned project.  
Use this command to add a new app/package:

```shell
copier copy --trust https://github-ri.vnet.valeo.com/GROUP-AI4ALL/ai4all-infra-templates_library.git .
```

You will be prompted with the following question:

```shell 
Do you want to add an app or a package?: (app, package)
```
Then you'll be asked about the name, and type you want:

If you chose app:
```shell
Enter your app name.:
Specify your app type:
        - python-script
        - notebook
        - backend-fastapi
        - frontend-react
        - cloud-function
```
If you chose package:
```shell
Enter your package name.:
Specify your package type:
        - generic
        - python-poetry
        - node
```

This should be the final image to be:

![Final image](https://github.com/user-attachments/assets/e5084585-c150-413f-a1d9-34afb6aeffc8)

Then you will have your app/package!
