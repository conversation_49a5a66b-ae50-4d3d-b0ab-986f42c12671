[{"mode": "NULLABLE", "name": "user_email", "type": "STRING", "description": "user_email"}, {"mode": "NULLABLE", "name": "created_timestamp", "type": "TIMESTAMP", "description": "created_timestamp"}, {"mode": "NULLABLE", "name": "bg", "type": "STRING", "description": "bg"}, {"mode": "NULLABLE", "name": "pg", "type": "STRING", "description": "pg"}, {"mode": "NULLABLE", "name": "pl", "type": "STRING", "description": "pl"}, {"mode": "NULLABLE", "name": "project", "type": "STRING", "description": "project"}, {"mode": "NULLABLE", "name": "project_uuid", "type": "STRING", "description": "project_uuid"}, {"mode": "NULLABLE", "name": "variant", "type": "STRING", "description": "variant"}, {"mode": "NULLABLE", "name": "variant_uuid", "type": "STRING", "description": "variant_uuid"}, {"mode": "NULLABLE", "name": "euf_feature", "type": "STRING", "description": "euf_feature"}, {"mode": "NULLABLE", "name": "euf_feature_uuid", "type": "STRING", "description": "euf_feature_uuid"}, {"mode": "NULLABLE", "name": "sensors", "type": "JSON", "description": "sensors"}, {"mode": "NULLABLE", "name": "algorithms", "type": "JSON", "description": "algorithms"}, {"mode": "NULLABLE", "name": "uuid", "type": "STRING", "description": "uuid"}]