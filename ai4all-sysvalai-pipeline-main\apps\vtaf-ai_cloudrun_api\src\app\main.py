"""
main.py

Main entry point for the VTAF AI FastAPI application.
Initializes the FastAPI app and includes API routers.
"""

import os
# os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "valeo-cp2673-dev-6e4b62cfd776.json"
import asyncio
import httpx

from google.cloud import secretmanager
from contextlib import asynccontextmanager
from fastapi import FastAPI

from app.core.constants import PROJECT_NUMBER
from app.core.logging_config import setup_logging
from app.dependencies.langfuse_client import get_langfuse
from app.middleware.logging_middleware import RequestLoggingMiddleware
from app.routers.v1.parsing_router import parsing_router as v1_parsing_router
from app.routers.v1.chunking_router import chunking_router as v1_chunking_router
from app.routers.v1.ai_agents_router import ai_agents_router as v1_agents_router
from app.routers.v1.qdrant_router import qdrant_router as v1_qdrant_router
from app.routers.v1.generator_router import generations_router as v1_generations_router
from app.routers.v1.utils_router import utils_router as v1_utils_router

# Configure logging
logger = setup_logging()

@asynccontextmanager
async def lifespan(app: FastAPI):
    app.state.http_client = httpx.AsyncClient()
    yield
    await asyncio.to_thread(get_langfuse().flush) 
    await app.state.http_client.aclose()

# Create FastAPI application instance
app = FastAPI(
    title="VTAF AI API",
    description="API for VTAF AI",
    version="1.0.0",
    root_path="/vtaf-api",
    lifespan=lifespan,
)

app.add_middleware(RequestLoggingMiddleware)

app.get("/")(lambda: {"message":"Greetings from VTAF.AI API Services"})

# Register API routers with versioned prefixes
app.include_router(
    v1_parsing_router,
    prefix="/v1",
    tags=["Parsing - v1"],
)

app.include_router(
    v1_chunking_router,
    prefix="/v1",
    tags=["Chunking - v1"],
)

app.include_router(
    v1_agents_router,
    prefix="/v1",
    tags=["Agents - v1"],
)

app.include_router(
    v1_qdrant_router,
    prefix="/v1",
    tags=["Vector DB - v1"],
)

app.include_router(
    v1_generations_router,
    prefix="/v1",
    tags=["Generations - v1"],
)

app.include_router(
    v1_utils_router,
    prefix="/v1",
    tags=["Utils - v1"],
)

# def get_oauth_credentials_from_secret(secret_name="vtaf-ai-dev-credentials", project_id=PROJECT_NUMBER):
#     """Fetch the OAuth2 client secret JSON from Secret Manager."""
#     client = secretmanager.SecretManagerServiceClient()
#     secret_path = f"projects/{project_id}/secrets/{secret_name}/versions/latest"

#     response = client.access_secret_version(request={"name": secret_path})
#     return response.payload.data.decode("UTF-8")


# client_secrets = get_oauth_credentials_from_secret()
# with open("OAuthCredentials.json", "w") as temp_file:
#     temp_file.write(client_secrets)


# os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "OAuthCredentials.json"
