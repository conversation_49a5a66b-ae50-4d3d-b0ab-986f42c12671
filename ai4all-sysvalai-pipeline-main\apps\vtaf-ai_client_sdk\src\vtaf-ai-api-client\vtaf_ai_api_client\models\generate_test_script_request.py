from collections.abc import Mapping
from typing import TYPE_CHECKING, Any, TypeVar, Union, cast

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..types import UNSET, Unset

if TYPE_CHECKING:
    from ..models.generate_test_script_request_testcase import GenerateTestScriptRequestTestcase


T = TypeVar("T", bound="GenerateTestScriptRequest")


@_attrs_define
class GenerateTestScriptRequest:
    """Request schema for generating a test script from a test case input.

    Attributes:
        testcase (Dict): The input test case as a structured JSON object.
        email (Optional[str]): Optional email address for notification.
        job_id (Optional[str]): Optional job identifier.
        enable_tracing (Optional[bool]): Whether tracing is enabled for the request.

        Attributes:
            testcase (Union[Unset, GenerateTestScriptRequestTestcase]): Structured test case to be converted into a test
                script
            email (Union[None, Unset, str]): Optional email address for notifications
            job_id (Union[None, Unset, str]): Optional job identifier
            enable_tracing (Union[None, Unset, bool]): Optional flag to enable tracing for the request Default: True.
    """

    testcase: Union[Unset, "GenerateTestScriptRequestTestcase"] = UNSET
    email: Union[None, Unset, str] = UNSET
    job_id: Union[None, Unset, str] = UNSET
    enable_tracing: Union[None, Unset, bool] = True
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        testcase: Union[Unset, dict[str, Any]] = UNSET
        if not isinstance(self.testcase, Unset):
            testcase = self.testcase.to_dict()

        email: Union[None, Unset, str]
        if isinstance(self.email, Unset):
            email = UNSET
        else:
            email = self.email

        job_id: Union[None, Unset, str]
        if isinstance(self.job_id, Unset):
            job_id = UNSET
        else:
            job_id = self.job_id

        enable_tracing: Union[None, Unset, bool]
        if isinstance(self.enable_tracing, Unset):
            enable_tracing = UNSET
        else:
            enable_tracing = self.enable_tracing

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update({})
        if testcase is not UNSET:
            field_dict["testcase"] = testcase
        if email is not UNSET:
            field_dict["email"] = email
        if job_id is not UNSET:
            field_dict["job_id"] = job_id
        if enable_tracing is not UNSET:
            field_dict["enable_tracing"] = enable_tracing

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        from ..models.generate_test_script_request_testcase import GenerateTestScriptRequestTestcase

        d = dict(src_dict)
        _testcase = d.pop("testcase", UNSET)
        testcase: Union[Unset, GenerateTestScriptRequestTestcase]
        if isinstance(_testcase, Unset):
            testcase = UNSET
        else:
            testcase = GenerateTestScriptRequestTestcase.from_dict(_testcase)

        def _parse_email(data: object) -> Union[None, Unset, str]:
            if data is None:
                return data
            if isinstance(data, Unset):
                return data
            return cast(Union[None, Unset, str], data)

        email = _parse_email(d.pop("email", UNSET))

        def _parse_job_id(data: object) -> Union[None, Unset, str]:
            if data is None:
                return data
            if isinstance(data, Unset):
                return data
            return cast(Union[None, Unset, str], data)

        job_id = _parse_job_id(d.pop("job_id", UNSET))

        def _parse_enable_tracing(data: object) -> Union[None, Unset, bool]:
            if data is None:
                return data
            if isinstance(data, Unset):
                return data
            return cast(Union[None, Unset, bool], data)

        enable_tracing = _parse_enable_tracing(d.pop("enable_tracing", UNSET))

        generate_test_script_request = cls(
            testcase=testcase,
            email=email,
            job_id=job_id,
            enable_tracing=enable_tracing,
        )

        generate_test_script_request.additional_properties = d
        return generate_test_script_request

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
