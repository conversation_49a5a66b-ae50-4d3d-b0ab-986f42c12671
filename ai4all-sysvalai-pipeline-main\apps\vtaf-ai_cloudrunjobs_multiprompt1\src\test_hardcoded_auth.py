#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试硬编码认证功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_config_with_hardcoded_auth():
    """测试硬编码认证配置"""
    print("=== 测试硬编码认证配置 ===")
    
    try:
        from configs.env import settings
        
        print(f"✅ 配置加载成功")
        print(f"  - PROJECT_ID: {settings.PROJECT_ID}")
        print(f"  - 服务账号信息存在: {'是' if settings.SERVICE_ACCOUNT_INFO else '否'}")
        
        if settings.SERVICE_ACCOUNT_INFO:
            print(f"  - 服务账号项目ID: {settings.SERVICE_ACCOUNT_INFO.get('project_id', 'N/A')}")
            print(f"  - 服务账号邮箱: {settings.SERVICE_ACCOUNT_INFO.get('client_email', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_sheets_manager_with_hardcoded_auth():
    """测试使用硬编码认证的表格管理器"""
    print("\n=== 测试表格管理器硬编码认证 ===")
    
    try:
        from services.sheet_manager import SheetManager
        
        # 初始化表格管理器
        sheet_manager = SheetManager()
        print("✅ 表格管理器初始化成功")
        
        # 测试连接（使用一个测试表格ID）
        # 注意：这里使用一个公开的测试表格，或者你可以替换为实际的表格ID
        test_sheet_id = "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"  # Google 官方示例表格
        
        print(f"尝试连接测试表格: {test_sheet_id}")
        success = sheet_manager.connect_sheet(test_sheet_id)
        
        if success:
            print("✅ 表格连接成功")
            
            # 获取表格名称
            sheet_names = sheet_manager.get_sheet_names()
            print(f"✅ 获取到 {len(sheet_names)} 个工作表")
            if sheet_names:
                print(f"  - 第一个工作表: {sheet_names[0]}")
            
            return True
        else:
            print("❌ 表格连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 表格管理器测试失败: {e}")
        return False

def test_gemini_client_with_hardcoded_auth():
    """测试使用硬编码认证的Gemini客户端"""
    print("\n=== 测试Gemini客户端硬编码认证 ===")
    
    try:
        from services.gemini_client import GeminiClient
        
        # 初始化Gemini客户端
        print("初始化Gemini客户端...")
        gemini_client = GeminiClient()
        print("✅ Gemini客户端初始化成功")
        
        # 测试简单的响应生成
        test_prompt = "请说'Hello, World!'"
        print(f"发送测试提示词: {test_prompt}")
        
        response = gemini_client.get_response(test_prompt)
        print(f"✅ 获取响应成功: {response[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Gemini客户端测试失败: {e}")
        return False

def test_ai_service_integration():
    """测试AI服务集成"""
    print("\n=== 测试AI服务集成 ===")
    
    try:
        from services.ai_service import AIService
        
        # 初始化AI服务
        print("初始化AI服务...")
        ai_service = AIService()
        print("✅ AI服务初始化成功")
        
        # 测试响应生成
        test_prompt = "请生成一个简单的测试用例，包含Test Case ID和Test Objective"
        print("发送测试提示词...")
        
        response = ai_service.generate_response(test_prompt)
        print(f"✅ AI服务响应成功")
        print(f"响应内容: {response[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ AI服务测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔐 VTAF AI Multiprompt 硬编码认证测试")
    print("=" * 60)
    
    tests = [
        ("硬编码认证配置", test_config_with_hardcoded_auth),
        ("表格管理器认证", test_sheets_manager_with_hardcoded_auth),
        ("Gemini客户端认证", test_gemini_client_with_hardcoded_auth),
        ("AI服务集成", test_ai_service_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'=' * 60}")
    print("🎯 硬编码认证测试总结")
    print(f"{'=' * 60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"总体结果: {passed}/{total} 测试通过")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed == total:
        print(f"\n🎉 所有硬编码认证测试通过！")
        print("💡 现在可以使用完整的功能进行批处理任务了")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败")
        if passed >= 1:
            print("💡 部分功能可用，可以尝试使用可用的服务")
    
    return passed >= 2  # 至少要有配置和一个服务通过

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
