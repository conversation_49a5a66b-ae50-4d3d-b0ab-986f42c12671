#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple test for core functionality without external dependencies
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_data_processing_import():
    """Test data processing module import"""
    print("=== Testing Data Processing Import ===")
    
    try:
        from utils.data_processing import parse_test_cases, parse_test_script, fields
        print("✓ Data processing module imported successfully")
        print(f"✓ Fields defined: {len(fields)} fields")
        return True
    except Exception as e:
        print(f"✗ Failed to import data processing module: {e}")
        return False

def test_parse_test_cases_basic():
    """Test basic test case parsing"""
    print("\n=== Testing Basic Test Case Parsing ===")
    
    try:
        from utils.data_processing import parse_test_cases
        
        # Test with simple input
        test_input = """
Confidence Score: 85%

**Test Case ID**: TC_001
**Covered Requirement ID**: REQ_001
**Test Objective**: Test basic functionality
**Test Condition**: System is ready
**Test Action**: Execute test
**Test Expectation**: Test passes
"""
        
        result = parse_test_cases(test_input)
        
        # Check results
        confidence_score = result.get('Confidence Score', '')
        test_cases = result.get('Test Cases', [])
        
        print(f"✓ Confidence Score extracted: {confidence_score}")
        print(f"✓ Test cases found: {len(test_cases)}")
        
        if test_cases:
            first_case = test_cases[0]
            print(f"✓ First test case has {len(first_case)} fields")
            if len(first_case) >= 1:
                print(f"✓ Test Case ID: {first_case[0]}")
        
        success = confidence_score == '85' and len(test_cases) > 0
        print(f"✓ Basic parsing test: {'PASS' if success else 'FAIL'}")
        return success
        
    except Exception as e:
        print(f"✗ Basic parsing test failed: {e}")
        return False

def test_parse_test_script_basic():
    """Test basic test script parsing"""
    print("\n=== Testing Basic Test Script Parsing ===")
    
    try:
        from utils.data_processing import parse_test_script
        
        test_input = """
The confidence level is **90%** for this script.

```capl
testcase TC_001()
{
  write("Test case execution");
}
```
"""
        
        result = parse_test_script(test_input)
        
        confidence_score = result.get('Confidence Score', '')
        capl_script = result.get('Capl', '')
        
        print(f"✓ Confidence Score: {confidence_score}")
        print(f"✓ CAPL Script found: {'Yes' if capl_script else 'No'}")
        
        success = confidence_score and capl_script
        print(f"✓ Script parsing test: {'PASS' if success else 'FAIL'}")
        return success
        
    except Exception as e:
        print(f"✗ Script parsing test failed: {e}")
        return False

def test_edge_cases():
    """Test edge cases"""
    print("\n=== Testing Edge Cases ===")
    
    try:
        from utils.data_processing import parse_test_cases
        
        # Test empty input
        empty_result = parse_test_cases("")
        empty_ok = len(empty_result.get('Test Cases', [])) == 0
        print(f"✓ Empty input handling: {'PASS' if empty_ok else 'FAIL'}")
        
        # Test invalid input
        invalid_result = parse_test_cases("This is not a test case")
        invalid_ok = len(invalid_result.get('Test Cases', [])) == 0
        print(f"✓ Invalid input handling: {'PASS' if invalid_ok else 'FAIL'}")
        
        # Test confidence score only
        confidence_only = parse_test_cases("Confidence Score: 75%")
        confidence_ok = confidence_only.get('Confidence Score') == '75'
        print(f"✓ Confidence score extraction: {'PASS' if confidence_ok else 'FAIL'}")
        
        return empty_ok and invalid_ok and confidence_ok
        
    except Exception as e:
        print(f"✗ Edge cases test failed: {e}")
        return False

def test_logger_import():
    """Test logger module import"""
    print("\n=== Testing Logger Import ===")
    
    try:
        from utils.logger import get_logger
        logger = get_logger(__name__)
        print("✓ Logger module imported successfully")
        print("✓ Logger instance created")
        return True
    except Exception as e:
        print(f"✗ Logger import failed: {e}")
        return False

def test_file_structure():
    """Test file structure"""
    print("\n=== Testing File Structure ===")
    
    expected_files = [
        'utils/data_processing.py',
        'utils/logger.py',
        'services/config_service.py',
        'services/ai_service.py',
        'services/sheet_manager.py',
        'services/batch_processor.py',
        'configs/env.py',
        'main.py'
    ]
    
    missing_files = []
    existing_files = []
    
    for file_path in expected_files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
            print(f"✓ {file_path}: exists")
        else:
            missing_files.append(file_path)
            print(f"✗ {file_path}: missing")
    
    print(f"\nFile structure summary:")
    print(f"✓ Existing files: {len(existing_files)}")
    print(f"✗ Missing files: {len(missing_files)}")
    
    return len(missing_files) == 0

def run_simple_tests():
    """Run all simple tests"""
    print("VTAF AI Multiprompt - Simple Functionality Tests")
    print("=" * 60)
    
    tests = [
        ("File Structure Check", test_file_structure),
        ("Data Processing Import", test_data_processing_import),
        ("Logger Import", test_logger_import),
        ("Basic Test Case Parsing", test_parse_test_cases_basic),
        ("Basic Test Script Parsing", test_parse_test_script_basic),
        ("Edge Cases", test_edge_cases)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'=' * 60}")
    print("Test Summary")
    print(f"{'=' * 60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"Overall Result: {passed}/{total} tests passed")
    
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"  {test_name}: {status}")
    
    if passed == total:
        print(f"\n🎉 All tests passed! Core functionality is working.")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed.")
        return False

if __name__ == "__main__":
    success = run_simple_tests()
    sys.exit(0 if success else 1)
