<!-- <template>
  <div
    class="row"
    style="margin-top: 3rem; margin-left: 10%; margin-right: 10%"
  >
    <div
      v-for="(item, index) in cardData"
      :key="index"
      class="col-xs-12 col-sm-6 col-md-3 q-pr-lg q-mb-lg"
    >
      <q-card class="my-card text-white" :style="getCardStyle">
        <q-card-section>
          <div class="text-h6" style="text-align: center">{{ item.title }}</div>
          <div style="text-align: center">
            <q-avatar square size="42px"><img :src="item.image" /></q-avatar>
          </div>
        </q-card-section>

        <q-card-section class="q-pt-none" style="text-align: center">
          {{ item.description }}
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { useQuasar } from "quasar";
import { ref, computed } from "vue";
const $q = useQuasar();

const cardData = [
  {
    title: "LabVIEW",
    image: "src/assets/home/<USER>",
    description:
      "LabVIEW is a graphical programming environment that provides unique productivity accelerators for test system development.",
  },
  {
    title: "TestStand",
    image: "src/assets/home/<USER>",
    description:
      "TestStand is a test executive software used to develop test software for products which are produced by an enterprise.",
  },
  {
    title: "Python",
    image: "src/assets/home/<USER>",
    description:
      "Python is a general-purpose language, meaning it can be used to create variety of programs and isn't specialized for any problems.",
  },
  {
    title: "CAPL",
    image: "src/assets/home/<USER>",
    description:
      "CAPL is a procedural programming language similar to C developed by vector. Execution of blocks is controlled by events.",
  },
];

const getCardStyle = computed(() => {
  const cardStyle = {
    background: "radial-gradient(circle, #77befc 0%, #014a88 100%)",
    minHeight: "15rem",
    maxHeight: "15rem",
  };

  if ($q.screen.gt.sm && $q.screen.lt.lg) {
    cardStyle.minHeight = "18rem";
    cardStyle.maxHeight = "18rem";
  }

  return cardStyle;
});
</script>

<style scoped>
.my-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease,
    background-color 0.3s ease;
  box-shadow: none;
}

.my-card:hover {
  animation: pulse 1s infinite alternate, shadow 0.3s ease forwards,
    color-change 0.3s ease forwards;
}

@keyframes pulse {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.05);
  }
}

@keyframes shadow {
  from {
    box-shadow: none;
  }
  to {
    box-shadow: 20px 20px 20px rgba(0, 0, 0, 0.2);
  }
}
</style> -->

<!--##############################################################################################-->
<template>
  <div class="html1" v-if="$q.screen.gt.sm">
    <section id="vertical" class="parallax-section">
      <div class="parallax-background"></div>
      <div class="container">
        <div class="vertical__content">
          <div class="col col_left">
            <h2 class="vertical__heading">
              <span>ABOUT</span><span>VTAF</span><span>FEATURES</span>
            </h2>
          </div>
          <div class="col col_right">
            <div
              class="vertical__item"
              v-for="(item, index) in cardData"
              :key="index"
            >
              <q-card class="my-card" :style="getCardStyle">
                <q-card-section class="card-content">
                  <div class="text-h4" style="color: #84f593">
                    {{ item.title }}
                  </div>
                  <q-avatar square size="100px" class="card-avatar">
                    <img :src="item.image" />
                  </q-avatar>
                  <div class="card-description">{{ item.description }}</div>
                </q-card-section>
              </q-card>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
  <div v-else class="row" style="background-color: #000000">
    <div
      v-for="(item, index) in cardData"
      :key="index"
      class="col-xs-12 col-sm-6 col-md-3"
    >
      <q-card class="my-card text-white" :style="getCardStyle">
        <q-card-section>
          <div>
            <q-avatar square size="42px"><img :src="item.image" /></q-avatar>
          </div>
        </q-card-section>

        <q-card-section class="q-pt-none" style="text-align: left">
          {{ item.description }}
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { useQuasar } from "quasar";
import { onMounted, computed } from "vue";
import Lenis from "@studio-freight/lenis";
import { gsap } from "gsap";
const $q = useQuasar();
import ScrollTrigger from "gsap/ScrollTrigger";

const getCardStyle = computed(() => {
  const cardStyle = {
    background: "transparent",
    minHeight: "15rem",
    maxHeight: "15rem",
  };

  if ($q.screen.gt.sm && $q.screen.lt.lg) {
    cardStyle.minHeight = "18rem";
    cardStyle.maxHeight = "18rem";
  }

  return cardStyle;
});

const cardData = [
  {
    title: "LabVIEW",
    image: "src/assets/home/<USER>",
    description:
      "LabVIEW is a graphical programming environment that provides unique productivity accelerators for test system development.",
  },
  {
    title: "TestStand",
    image: "src/assets/home/<USER>",
    description:
      "TestStand is a test executive software used to develop test software for products which are produced by an enterprise.",
  },
  {
    title: "Python",
    image: "src/assets/home/<USER>",
    description:
      "Python is a general-purpose language, meaning it can be used to create variety of programs and isn't specialized for any problems.",
  },
  {
    title: "CAPL",
    image: "src/assets/home/<USER>",
    description:
      "CAPL is a procedural programming language similar to C developed by vector. Execution of blocks is controlled by events.",
  },
];

onMounted(() => {
  gsap.registerPlugin(ScrollTrigger);

  const lenis = new Lenis({
    duration: 1.2,
    easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
  });

  function raf(time) {
    lenis.raf(time);
    ScrollTrigger.update();
    requestAnimationFrame(raf);
  }

  requestAnimationFrame(raf);

  const section_1 = document.getElementById("vertical");
  const col_left = document.querySelector(".col_left");
  const timeln = gsap.timeline({ paused: true });

  timeln.fromTo(
    col_left,
    { y: 0 },
    { y: "170vh", duration: 1, ease: "none" },
    0
  );

  ScrollTrigger.create({
    animation: timeln,
    trigger: section_1,
    start: "top top",
    end: "bottom center",
    scrub: true,
  });
});
</script>

<style scoped>
.html1 {
  scroll-behavior: initial;
  overflow: hidden;
  width: 100%;
  min-height: 100%;
  font-family: Slussen;
  font-size: 16px;
  font-weight: 400;
  background: #000;
  color: #fff;
}

h2 {
  font-size: 60px;
  font-weight: 900;
  line-height: 85%;
  border-left: 3px solid #84f593;
  padding: 25px;
  margin: 0;
}

h2 span {
  display: block;
}

.container {
  width: 95%;
  margin: auto;
}

section {
  padding: 50px 0;
}

.col {
  width: 50%;
}

#vertical {
  height: 200vh;
  width: 100vw;
}

.vertical__content {
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.vertical__content .col_left {
  height: 100%;
}

.vertical__content .col.col_right {
  width: 40%;
}

.vertical__item:not(:last-child) {
  margin-bottom: 240px;
}

.my-card {
  background: transparent;
  min-height: 15rem;
  max-height: 15rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.card-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.card-avatar {
  margin-left: 20px;
  margin-right: 20px;
  transition: transform 0.8s ease;
}

.card-avatar:hover {
  transform: scale(1.1); /* Zoom in the avatar */
}

.card-description {
  flex-grow: 1;
  text-align: left;
  font-size: 20px;
}

/* Additional styling to ensure proper layout */
.container {
  position: relative;
  z-index: 1;
}

.my-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease,
    background-color 0.3s ease;
  box-shadow: none;
}

.my-card:hover {
  animation: pulse 1s infinite alternate, shadow 0.3s ease forwards,
    color-change 0.3s ease forwards;
}

@keyframes pulse {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.05);
  }
}

@keyframes shadow {
  from {
    box-shadow: none;
  }
  to {
    box-shadow: 20px 20px 20px rgba(0, 0, 0, 0.2);
  }
}
</style>
