from fastapi import APIRouter, Request, HTTPException
import traceback

router = APIRouter()

@router.get("/get_user")
def get_user(request: Request):
    try:
        # Check if the X-Goog-Authenticated-User-Email header is present
        user_email = request.headers.get("X-Goog-Authenticated-User-Email")

        if user_email:
            # Extract the email from the header
            email = user_email.split(":")[1]
        else:
            # Use a default email if the header is not present
            email = "<EMAIL>"

        response = {
            "data": email
        }

        return response
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        
        # Raise HTTPException with 500 status and detailed error
        raise HTTPException(status_code=500, detail=error_message)