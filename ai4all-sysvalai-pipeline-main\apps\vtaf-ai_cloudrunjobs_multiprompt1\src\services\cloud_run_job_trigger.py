#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cloud Run Job Trigger Service - 高稳定性的Cloud Run Jobs触发服务
负责接收前端请求并触发相应的Cloud Run Jobs
"""

import os
import json
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
from utils.logger import get_logger

# 可选导入Google Cloud SDK
try:
    from google.cloud import run_v2
    from google.cloud import logging as cloud_logging
    GOOGLE_CLOUD_AVAILABLE = True
except ImportError:
    GOOGLE_CLOUD_AVAILABLE = False
    run_v2 = None
    cloud_logging = None

logger = get_logger(__name__)

class CloudRunJobTrigger:
    """Cloud Run Job触发器 - 高稳定性设计"""
    
    def __init__(self):
        self.project_id = os.getenv('PROJECT_ID', 'valeo-cp2673-dev')
        self.region = os.getenv('REGION', 'europe-west1')
        self.google_cloud_available = GOOGLE_CLOUD_AVAILABLE

        if GOOGLE_CLOUD_AVAILABLE:
            self.client = run_v2.JobsClient()
            self.cloud_logger = self._setup_cloud_logging()
        else:
            self.client = None
            self.cloud_logger = None
            logger.warning("Google Cloud SDK not available, running in simulation mode")
        
        # 任务类型映射
        self.job_mapping = {
            'submit': 'basic_submit',
            'send_batch_reqs': 'batch_processing',
            'send_single_req': 'single_task',
            'archive_ts': 'testcase_archive',
            'send_batch_tcs': 'testscript_batch',
            'send_single_tc': 'testscript_single',
            'send_mail': 'email_send',
            'email_back': 'email_back',
            'send_training_dataset': 'training_dataset',
            'send_ts_training_dataset': 'ts_training_dataset',
            'set_system_prompt': 'system_instruction',
            'insert_media': 'file_operations',
        }
        
    def _setup_cloud_logging(self):
        """设置Cloud Logging"""
        if not GOOGLE_CLOUD_AVAILABLE:
            return None

        try:
            client = cloud_logging.Client(project=self.project_id)
            client.setup_logging()
            return client.logger('vtaf-ai-multiprompt-trigger')
        except Exception as e:
            logger.warning(f"Failed to setup cloud logging: {e}")
            return None
    
    def _log_to_cloud(self, level: str, message: str, extra_data: Dict = None):
        """记录到Cloud Logging"""
        try:
            if self.cloud_logger:
                log_entry = {
                    'message': message,
                    'timestamp': datetime.utcnow().isoformat(),
                    'level': level,
                    'service': 'vtaf-ai-multiprompt-trigger'
                }
                if extra_data:
                    log_entry.update(extra_data)
                
                if level == 'ERROR':
                    self.cloud_logger.log_struct(log_entry, severity='ERROR')
                elif level == 'WARNING':
                    self.cloud_logger.log_struct(log_entry, severity='WARNING')
                else:
                    self.cloud_logger.log_struct(log_entry, severity='INFO')
        except Exception as e:
            logger.warning(f"Failed to log to cloud: {e}")
    
    async def trigger_job(self, button_type: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """触发Cloud Run Job - 主要入口点"""
        request_id = self._generate_request_id()
        
        try:
            # 记录请求开始
            self._log_to_cloud('INFO', f'Job trigger request started', {
                'request_id': request_id,
                'button_type': button_type,
                'config_keys': list(config.keys())
            })
            
            logger.info(f"🚀 Triggering job for button: {button_type} (Request ID: {request_id})")
            
            # 验证按钮类型
            if button_type not in self.job_mapping:
                error_msg = f"Unknown button type: {button_type}"
                self._log_to_cloud('ERROR', error_msg, {'request_id': request_id})
                return {
                    'status': 'error',
                    'message': error_msg,
                    'request_id': request_id
                }
            
            job_type = self.job_mapping[button_type]
            
            # 构建任务配置
            job_config = self._build_job_config(job_type, config, request_id)
            
            # 执行任务
            result = await self._execute_cloud_run_job(job_type, job_config, request_id)
            
            # 记录结果
            self._log_to_cloud('INFO', f'Job trigger completed', {
                'request_id': request_id,
                'job_type': job_type,
                'status': result['status']
            })
            
            return result
            
        except Exception as e:
            error_msg = f"Job trigger failed: {str(e)}"
            self._log_to_cloud('ERROR', error_msg, {
                'request_id': request_id,
                'button_type': button_type,
                'error': str(e)
            })
            logger.exception(f"Job trigger failed for {button_type}: {e}")
            
            return {
                'status': 'error',
                'message': error_msg,
                'request_id': request_id
            }
    
    def _generate_request_id(self) -> str:
        """生成请求ID"""
        import uuid
        return f"req_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
    
    def _build_job_config(self, job_type: str, config: Dict[str, Any], request_id: str) -> Dict[str, Any]:
        """构建任务配置"""
        base_config = {
            'JOB_TYPE': job_type,
            'RUN_MODE': config.get('run_mode', 'production'),
            'REQUEST_ID': request_id,
            'PROJECT_ID': self.project_id,
            'REGION': self.region,
            'TIMESTAMP': datetime.utcnow().isoformat(),
        }
        
        # 根据任务类型添加特定配置
        if job_type == 'basic_submit':
            base_config.update({
                'PROMPT_TEXT': config.get('prompt_text', ''),
                'OUTPUT_FILE': f'/tmp/basic_submit_{request_id}.txt'
            })
            
        elif job_type in ['batch_processing', 'single_task']:
            base_config.update({
                'PROMPT_SHEET_URL': config.get('prompt_sheet', ''),
                'TEST_SPEC_SHEET_URL': config.get('test_spec_sheet', ''),
                'PROMPT_SHEET_NAME': config.get('prompt_sheet_name', 'Prompt_Batch'),
                'AI_MODEL': config.get('model', 'gemini-1.5-flash'),
                'TEMPERATURE': str(config.get('temperature', 0.7)),
                'MAX_OUTPUT_TOKENS': str(config.get('max_output_tokens', 8192))
            })
            
        elif job_type == 'testcase_archive':
            base_config.update({
                'PROMPT_SHEET_URL': config.get('prompt_sheet', ''),
                'CASE_SHEET_URL': config.get('case_sheet', ''),
                'PROMPT_SHEET_NAME': 'Prompt_Archive',
                'CASE_SHEET_NAME': 'TestCases',
                'USER_NAME': config.get('user_email', 'CloudRunUser')
            })
            
        elif job_type in ['testscript_batch', 'testscript_single']:
            base_config.update({
                'PROMPT_SHEET_URL': config.get('prompt_sheet', ''),
                'TEST_SPEC_SHEET_URL': config.get('test_spec_sheet', ''),
                'HEAD_FILES_CONFIG': json.dumps(config.get('head_files', {})),
                'HEAD_FILE_PROMPT': config.get('head_file_prompt', ''),
                'AI_MODEL': config.get('model', 'gemini-1.5-flash')
            })
            
        elif job_type in ['email_send', 'email_back']:
            base_config.update({
                'TASK_SHEET_URL': config.get('task_sheet', ''),
                'LAST_AI_RESPONSE': config.get('last_ai_response', ''),
                'EMAIL_TEMPLATE': config.get('email_template', 'Please reply to this email: ')
            })
            
        elif job_type in ['training_dataset', 'ts_training_dataset']:
            base_config.update({
                'DATASET_SHEET_URL': config.get('dataset_sheet', ''),
                'DATASET_SHEET_NAME': config.get('dataset_sheet_name', 'TrainingData'),
                'AUTO_SUBMIT': str(config.get('auto_submit', False)).lower()
            })
            
        elif job_type == 'system_instruction':
            base_config.update({
                'SYSTEM_INSTRUCTIONS': json.dumps(config.get('system_instructions', [])),
                'INSTRUCTION_MODE': config.get('instruction_mode', 'replace')
            })
            
        elif job_type == 'file_operations':
            base_config.update({
                'FILE_OPERATION': config.get('file_operation', 'upload'),
                'SOURCE_PATH': config.get('source_path', ''),
                'TARGET_PATH': config.get('target_path', ''),
                'FILE_PATTERN': config.get('file_pattern', '*')
            })
        
        return base_config
    
    async def _execute_cloud_run_job(self, job_type: str, job_config: Dict[str, Any], request_id: str) -> Dict[str, Any]:
        """执行Cloud Run Job"""
        job_name = f"vtaf-ai-{job_type.replace('_', '-')}"

        try:
            logger.info(f"📋 Executing Cloud Run Job: {job_name}")

            if not self.google_cloud_available:
                # 模拟模式
                logger.info("🎭 Running in simulation mode (Google Cloud SDK not available)")
                operation_name = f"projects/{self.project_id}/locations/{self.region}/operations/simulation-{request_id}"

                # 模拟执行延迟
                await asyncio.sleep(0.5)

                logger.info(f"✅ Job {job_name} simulated successfully")
                logger.info(f"🔗 Simulated operation name: {operation_name}")

                return {
                    'status': 'success',
                    'message': f'Job {job_name} simulated successfully (no Google Cloud SDK)',
                    'job_name': job_name,
                    'operation_name': operation_name,
                    'request_id': request_id,
                    'job_config': job_config,
                    'simulation_mode': True
                }

            # 真实模式
            # 构建执行请求
            execution_request = self._build_execution_request(job_name, job_config)

            # 执行任务
            operation = self.client.run_job(request=execution_request)

            logger.info(f"✅ Job {job_name} started successfully")
            logger.info(f"🔗 Operation name: {operation.name}")

            # 返回执行信息
            return {
                'status': 'success',
                'message': f'Job {job_name} started successfully',
                'job_name': job_name,
                'operation_name': operation.name,
                'request_id': request_id,
                'job_config': job_config,
                'simulation_mode': False
            }

        except Exception as e:
            error_msg = f"Failed to execute job {job_name}: {str(e)}"
            logger.error(error_msg)

            return {
                'status': 'error',
                'message': error_msg,
                'job_name': job_name,
                'request_id': request_id
            }
    
    def _build_execution_request(self, job_name: str, job_config: Dict[str, Any]):
        """构建执行请求"""
        parent = f"projects/{self.project_id}/locations/{self.region}"
        name = f"{parent}/jobs/{job_name}"
        
        # 构建环境变量
        env_vars = []
        for key, value in job_config.items():
            env_vars.append({
                'name': key,
                'value': str(value)
            })
        
        return {
            'parent': parent,
            'job': {
                'name': name,
                'spec': {
                    'template': {
                        'spec': {
                            'template': {
                                'spec': {
                                    'containers': [{
                                        'env': env_vars
                                    }]
                                }
                            }
                        }
                    }
                }
            }
        }
    
    async def get_job_status(self, operation_name: str) -> Dict[str, Any]:
        """获取任务状态"""
        try:
            # 这里可以添加获取任务状态的逻辑
            # 目前返回基本信息
            return {
                'status': 'running',
                'operation_name': operation_name,
                'message': 'Job is running'
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Failed to get job status: {str(e)}'
            }
    
    def get_available_jobs(self) -> Dict[str, str]:
        """获取可用的任务类型"""
        return {
            button: job_type for button, job_type in self.job_mapping.items()
        }
