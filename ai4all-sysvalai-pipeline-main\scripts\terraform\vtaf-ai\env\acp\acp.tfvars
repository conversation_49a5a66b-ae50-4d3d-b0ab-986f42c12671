##############################################################
# Project level Variables
##############################################################

project_id           = "valeo-cp2673-acp"
region               = "europe-west1"
zone                 = "europe-west1"
location             = "europe-west1"
environment          = "acp"
vtaf_ai_project      = "VTAF_AI"
vtaf_ai_service_name = "vtaf-ai"
bigquery_dataset_id  = "VTAF_AI_ACP"

##############################################################
# Artifact Registry Variables
##############################################################

repositories = [
  {
    repository_name        = "vtaf-ai-artifactory"
    description            = "Artifactory for vtaf-ai project"
    format                 = "docker"
    cleanup_policy_dry_run = false
  }
]

##############################################################
# Service accounts Variables
##############################################################

service_account_file  = "credentials_acp.json"
service_account_email = "<EMAIL>"

##############################################################
# Bucket Variables
##############################################################

buckets_config = {
  versioning = true
  bucket_viewers = [
    "serviceAccount:<EMAIL>",
  ]
  bucket_admins = [
    "serviceAccount:<EMAIL>",
  ]
  prefix        = "vtaf-ai"
  location      = "europe-west1"
  storage_class = "STANDARD"
}

buckets = [
  {
    bucket_name   = "config-sync-function-bucket"
    versioning    = false
    force_destroy = false
    bucket_labels = {
      "environment" = "acp"
      "service"     = "config-sync-function-source"
    }
  },
  {
    bucket_name   = "cloudbuild"
    versioning    = false
    force_destroy = false
    bucket_labels = {
      "environment" = "acp"
      "service"     = "cloudbuild"
    }
  },
  {
    bucket_name   = "agents"
    versioning    = false
    force_destroy = false
    bucket_labels = {
      "environment" = "acp"
      "service"     = "vtaf_ai_agents"
    }
  }
]
##############################################################
# Cloud Function
##############################################################

config_sheet_id = "1wwqr12-EimCavhnFzpd1hPV4jeovTaahKVYYdSTeTRM"

##############################################################
# VTAF.AI Knowledgebase Cloud Run Job Variables
##############################################################

vtaf_ai_cloudrunjobs_testcase_generation_image         = "vtaf_ai_cloudrunjobs_testcase_generation"
vtaf_ai_cloudrunjobs_testcase_generation_image_version = "latest"

vtaf_ai_cloudrunjobs_knowledgebase_image         = "vtaf_ai_cloudrunjobs_knowledgebase"
vtaf_ai_cloudrunjobs_knowledgebase_image_version = "latest"

vtaf_ai_cloudrunjobs_doors_embedding_image         = "vtaf_ai_cloudrunjobs_knowledgebase"
vtaf_ai_cloudrunjobs_doors_embedding_image_version = "latest"

vtaf_ai_cloudrunjobs_test_script_generation_image         = "vtaf_ai_cloudrunjobs_test_script_generation"
vtaf_ai_cloudrunjobs_test_script_generation_image_version = "latest"

kcrj_env_vars = [
  { name = "PROJECT_ID", value = "valeo-cp2673-acp" },
  { name = "QDRANT_CLIENT_URL", value = "https://db-cp2532.apps-dev.valeo.com/" },
  { name = "QDRANT_CLIENT_PORT", value = "443" },
  { name = "QDRANT_IAP_CLIENT_ID", value = "************-ju6c99h7a04akimhmljnrv3tp5usjhsu.apps.googleusercontent.com" },
  { name = "EMBEDDING_MODEL_NAME", value = "text-embedding-004" },
  { name = "EMBEDDING_LOCATION", value = "europe-west1" },
  { name = "EMBEDDING_PARALLELISM", value = "24" },
  { name = "COLLECTION_NAME", value = "SysVal_VTAF_AI_DataWarehouse_ACP" },
  { name = "SERVICE_ACCOUNT_EMAIL", value = "<EMAIL>" },
  { name = "OTEL_SDK_DISABLED", value = "True" },
  { name = "MODEL", value = "vertex_ai/gemini-2.0-flash" },
  { name = "FILE_NAME", value = "vtaf-ai/knowledgebase/user_uploads/Stellantis-A5U/PAM/Diagnostics/Requirements/[2025-03-19T17:21:42.962976Z]_SyRS_CMBS_AEB.xlsx" },
  { name = "BQ_DATA_SET", value = "VTAF_AI_ACP" },
  { name = "BQ_TABLE", value = "KNOWLEDGE_JOB_HISTORY" },
  { name = "PROJECT_PROFILE_UUID", value = "TEST_PROJECT_PROFILE_UUID" },
  { name = "SOURCE_PATH", value = "/mnt/vtaf-ai" },
  { name = "VTAF_AI_PROJECT_ID", value = "TEST_VTAF_AI_PROJECT_ID" },
  { name = "VTAF_AI_PROJECT_VAR_ID", value = "TEST_VTAF_AI_PROJECT_VAR_ID" },
  { name = "VTAF_AI_EUF_FEA_UID", value = "TEST_VTAF_AI_EUF_FEA_UID" },
  { name = "INPUT_TYPE", value = "Requirements" },
  { name = "BUCKET_NAME", value = "valeo-cp2673-acp-vtaf-ai-agents" },
  { name = "SERVICES_PROJECT_NUMBER", value = "************" },
  { name = "DOMAIN_NAME", value = "cp2673.apps-test.valeo.com" },
  { name = "CHUNKING_STRATEGY", value = "recursive-character-text-splitter" },
  { name = "ENABLE_SUMMARIZATION", value = "true" },
  { name = "VTAF_AI_CLIENT_ID", value = "************-6mscate7llc38o3600ki97egodnvbapc.apps.googleusercontent.com" }
]

tcrj_env_vars = [
  { name = "FILE_NAME", value = "feea3af3-225c-4f4a-98cf-23463ea3cfa5" },
  { name = "MODEL", value = "vertex_ai/gemini-2.0-flash" },
  { name = "LOCATION", value = "us-central1" },
  { name = "QDRANT_CLIENT_URL", value = "https://db-cp2532.apps-dev.valeo.com/" },
  { name = "QDRANT_CLIENT_PORT", value = "443" },
  { name = "QDRANT_IAP_CLIENT_ID", value = "************-ju6c99h7a04akimhmljnrv3tp5usjhsu.apps.googleusercontent.com" },
  { name = "EMBEDDING_MODEL_NAME", value = "text-embedding-004" },
  { name = "EMBEDDING_LOCATION", value = "europe-west1" },
  { name = "EMBEDDING_PARALLELISM", value = "24" },
  { name = "COLLECTION_NAME", value = "SysVal_VTAF_AI_DataWarehouse_ACP" },
  { name = "SERVICE_ACCOUNT_EMAIL", value = "<EMAIL>" },
  { name = "SERVICES_BUCKET_NAME", value = "valeo-cp2673-acp-vtaf-ai-agents" },
  { name = "SERVICES_PROJECT_ID", value = "valeo-cp2673-acp" },
  { name = "SERVICES_DATASET_ID", value = "VTAF_AI_ACP" },
  { name = "SERVICES_PROJECT_NUMBER", value = "************" },
  { name = "DOMAIN_NAME", value = "cp2673.apps-test.valeo.com" },
  { name = "OTEL_SDK_DISABLED", value = "False" },
  { name = "LANGFUSE_URL", value = "https://cp2532.apps-dev.valeo.com/utils/langfuse/v3" },
  { name = "SHARED_FOLDER_ID", value = "1TnTG29ElXAR9PRr0OsTAZbFtu-h10QSB" }

]

ecrj_env_vars = [
  { name = "COLLECTION_NAME", value = "SysVal_VTAF_AI_DataWarehouse_ACP" },
  { name = "PROJECT_ID", value = "valeo-cp2673-acp" },
  { name = "BQ_DATA_SET", value = "DOORS_Extraction_PROD" },
  { name = "BQ_TABLE", value = "CDA_20SystemReq" },
  { name = "DOMAIN_NAME", value = "cp2673.apps-test.valeo.com" },
  { name = "VTAF_AI_CLIENT_ID", value = "************-6mscate7llc38o3600ki97egodnvbapc.apps.googleusercontent.com" },
  { name = "Projects", value = "BDA_COM_BMW_SP2027_IPNEXT (1ST240444),BDA_COM_BMW_SP2025_IPNEXT (1ST211553),BDA_COM_GM_MY27_CCU_CHASSIS (1ST222242),BDA_COM_RNM_FACE_PCU (1ST220920)" }
]

tsrj_env_vars = [
  { name = "PROJECT_ID", value = "valeo-cp2673-acp" },
  { name = "FILE_NAME", value = "1IMwvSRhlZxIAduujVyNcfIyKWzZAby49H2JcFNmiQB4" },
  { name = "BQ_DATA_SET", value = "VTAF_AI_ACP" },
  { name = "BQ_TABLE", value = "TS_JOB_HISTORY" },
  { name = "SOURCE_PATH", value = "/mnt/vtaf-ai" },
  { name = "SERVICES_PROJECT_NUMBER", value = "************" },
  { name = "DOMAIN_NAME", value = "cp2673.apps-test.valeo.com" },
  { name = "VTAF_AI_CLIENT_ID", value = "************-6mscate7llc38o3600ki97egodnvbapc.apps.googleusercontent.com" },
  { name = "SHARED_FOLDER_ID", value = "1iY-sXuMsyBkSesK9AXS32prVn9_0eP1G" },
  { name = "MODEL", value = "vertex_ai/gemini-2.0-flash" },
  { name = "LOCATION", value = "europe-west1" },
  { name = "BUCKET_NAME", value = "valeo-cp2673-acp-vtaf-ai-agents" }

]

##############################################################
# cloud run Variables
##############################################################

services = [
  {
    service_name                  = "vtaf-ai-cloudrun-frontend"
    repository_name               = "vtaf-ai-artifactory"
    image_name                    = "vtaf_ai_cloudrun_frontend"
    image_tag                     = "latest"
    use_default_svc_account       = false
    service_account               = "<EMAIL>"
    cloud_run_deletion_protection = false
    timeout                       = "600s"
    max_instances                 = 1
    max_instance_request_concurrency = 40
    cpu_limit                        = "1000m"
    memory_limit                     = "2Gi"
    ports = {
      name           = "http1"
      container_port = 80
    }
    env_vars = {
      DOMAIN_NAME = "cp2673.apps-test.valeo.com"
    }
  },
  {
    service_name                  = "vtaf-ai-cloudrun-backend-testcase"
    repository_name               = "vtaf-ai-artifactory"
    image_name                    = "vtaf_ai_cloudrun_backend_testcase_generation"
    image_tag                     = "latest"
    cloud_run_deletion_protection = false
    use_default_svc_account       = false
    timeout                       = "600s"
    max_instances                 = 1

    max_instance_request_concurrency = 40
    cpu_limit                        = "1000m"
    memory_limit                     = "2Gi"
    service_account                  = "<EMAIL>"
    ports = {
      name           = "http1"
      container_port = 8080
    }
    env_vars = {
      SERVICES_BUCKET_NAME       = "valeo-cp2673-acp-vtaf-ai-agents",
      SERVICES_PROJECT_ID        = "valeo-cp2673-acp",
      SERVICES_DATASET_ID        = "VTAF_AI_ACP",
      SERVICES_ACCOUNT_EMAIL     = "<EMAIL>",
      TESTCASE_GENERATION_JOB    = "vtaf-ai-cloudrunjobs-testcase-generation"
      KNOWLEDGEBASE_CREATION_JOB = "vtaf-ai-cloudrunjobs-knowledgebase"
      TESTSCRIPT_GENERATION_JOB = "vtaf-ai-cloudrunjobs-test-script-generation"
    }
  },
  {
    service_name                  = "vtaf-ai-cloudrun-backend-chat"
    repository_name               = "vtaf-ai-artifactory"
    image_name                    = "vtaf_ai_cloudrun_backend_chat"
    image_tag                     = "latest"
    cloud_run_deletion_protection = false
    use_default_svc_account       = false
    timeout                       = "600s"
    max_instances                 = 1
    max_instance_request_concurrency = 40
    cpu_limit                        = "1000m"
    memory_limit                     = "2Gi"
    service_account                  = "<EMAIL>"
    ports = {
      name           = "http1"
      container_port = 8081
    }
    env_vars = {
      SERVICES_PROJECT_ID = "valeo-cp2673-acp",
      SERVICES_DATASET_ID = "VTAF_AI_ACP"
    }
  },
  {
    service_name                  = "vtaf-ai-cloudrun-api"
    repository_name               = "vtaf-ai-artifactory"
    image_name                    = "vtaf_ai_cloudrun_api"
    image_tag                     = "latest"
    cloud_run_deletion_protection = false
    use_default_svc_account       = false
    timeout                       = "600s"
    max_instances                 = 100
    min_instances                 = 1
    max_instance_request_concurrency = 40
    cpu_limit                        = "2000m"
    memory_limit                     = "4Gi"
    service_account                  = "<EMAIL>"
    ports = {
      name           = "http1"
      container_port = 8082
    }
     env_secret_vars = {
      LANGFUSE_PUBLIC_KEY = {
        secret  = "vtaf-ai-langfuse-public-key",
        version = "latest"
      },
      LANGFUSE_SECRET_KEY = {
        secret  = "vtaf-ai-langfuse-secret-key"
        version = "latest"
      }
    }
    env_vars = {
      PROJECT_ID                   = "valeo-cp2673-acp"
      REGION                       = "europe-west1"
      VERTEX_AI_MODEL              = "gemini-2.0-flash"
      VERTEX_EMBEDDING_MODEL       = "text-embedding-004"
      EMBEDDING_PARALLELISM        = "64"
      AI4ALL_SERVICE_ACCOUNT_EMAIL = "<EMAIL>"
      AI4ALL_FRAMEWORK_CLIENT_ID   = "************-aq5ghrumia4nu1e2hh1mu8u7rviuiq57.apps.googleusercontent.com"
      VTAF_SERVICE_ACCOUNT_EMAIL   = "<EMAIL>"
      QDRANT_IAP_CLIENT_ID         = "************-ju6c99h7a04akimhmljnrv3tp5usjhsu.apps.googleusercontent.com"
      QDRANT_CLIENT_URL            = "https://db-cp2532.apps-dev.valeo.com/"
      QDRANT_COLLECTION_NAME       = "SysVal_VTAF_AI_DataWarehouse_ACP"
      QDRANT_PORT                  = "443"
      QDRANT_JWT                   = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************.MPFT_Sq-4EaFSVWR3DF9pzO2yX-1K2k_t3EuU1u9wxQ"
      PROJECT_NUMBER               = "************"
      LANGFUSE_URL                 = "https://cp2532.apps-dev.valeo.com/utils/langfuse/v3"
    }
  },
    {
    service_name                  = "vtaf-ai-cloudrun-backend-conversation"
    repository_name               = "vtaf-ai-artifactory"
    image_name                    = "vtaf_ai_cloudrun_backend_conversation"
    image_tag                     = "latest"
    cloud_run_deletion_protection = false
    use_default_svc_account       = false
    service_account               = "<EMAIL>"
    timeout                       = "600s"
    max_instances                 = 1
    max_instance_request_concurrency = 40
    cpu_limit                        = "1000m"
    memory_limit                     = "2Gi"
    ports = {
      name           = "http1"
      container_port = 8083
    }
    env_vars = {
      SERVICES_PROJECT_ID="valeo-cp2673-acp",
      SERVICES_DATASET_ID="VTAF_AI_ACP",
      FIRESTORE_DATABASE_ID="vtaf-ai-chat-with-knowledge",
      FIRESTORE_SESSION_COLLECTION="vtaf-sessions",
      SERVICES_ACCOUNT_EMAIL="<EMAIL>",
      IAP_CLIENT_ID="************-jg7fke8teaejg9vo5697kvifghr16lmq.apps.googleusercontent.com",
      VTAF_API_URL="https://cp2673.apps-test.valeo.com",
      COLLECTION_NAME="SysVal_VTAF_AI_DataWarehouse_ACP"
    }
  },
]

vtaf_ai_artifactory_url    = "europe-west1-docker.pkg.dev"
vtaf_ai_artifactory_name   = "vtaf-ai-artifactory"
delete_contents_on_destroy = false

##############################################################
# IP-Address Variables
##############################################################


ip_addresses = [
  {
    name         = "cp2673-acp-pub-ip"
    address_type = "EXTERNAL"
    description  = "Internal IP address"
    global       = true
    labels = {
      environment = "acp"
    }
  }
]

##############################################################
# IAM Variables
##############################################################

devops_sa_name          = "devops-sa"
github_deployer_sa_name = "gh-cicd-sa"
fw_dev_sa_name          = "fw-dev-sa"

roles_bindings = {
  "vtafai.developer" = [
    "serviceAccount:<EMAIL>",
    "user:<EMAIL>",
    "group:<EMAIL>",

  ]
  "vtafai.deployer" = [
    "serviceAccount:<EMAIL>",
    "user:<EMAIL>",
  ]
  "vtafai.endusers" = [
    "group:<EMAIL>"
  ]
}

sa_bindings = {

}
##############################################################
# Load Balancer Variables
##############################################################


load_balancer_config = {
  iap_application_title = "VTAF-AI consent screen title"
  ip_address_name       = "cp2673-acp-pub-ip"
  ssl_domains           = ["cp2673.apps-test.valeo.com"]
  name                  = "cp2673-acp-lb"
  default_service       = "vtaf-ai-cloudrun-frontend"
  enable_ssl            = true
  main_client_id        = "************-6mscate7llc38o3600ki97egodnvbapc.apps.googleusercontent.com"
  labels                = { "environment" = "acp" }
  host_rules = [
    {
      hosts        = ["cp2673.apps-test.valeo.com"]
      path_matcher = "basic-matcher"
    }
  ]
  path_matchers = {
    "basic-matcher" = {
      default_service = "vtaf-ai-cloudrun-frontend"
      path_rules = [
        {
          paths   = ["/*", "/"]
          service = "vtaf-ai-cloudrun-frontend"
        },
        {
          paths   = ["/ai_testcase/*", "/ai_testcase"]
          service = "vtaf-ai-cloudrun-backend-testcase"
        },
        {
          paths   = ["/chat/*", "/chat"]
          service = "vtaf-ai-cloudrun-backend-chat"
        },
        {
          paths   = ["/vtaf-api/*", "/vtaf-api"]
          service = "vtaf-ai-cloudrun-api"
        },
        {
          paths   = ["/conversation/*", "/conversation"]
          service = "vtaf-ai-cloudrun-backend-conversation"
        }
      ]
    }
  }
  backend_services = {
    "vtaf-ai-cloudrun-frontend" = {
      service_type         = "cloud_run"
      enable_iap           = true
      use_main_client_id   = true
      iap_accessor_members = ["group:<EMAIL>"]
    },
    "vtaf-ai-cloudrun-backend-testcase" = {
      service_type         = "cloud_run"
      enable_iap           = true
      use_main_client_id   = true
      iap_accessor_members = ["group:<EMAIL>"]
    },
    "vtaf-ai-cloudrun-backend-chat" = {
      service_type         = "cloud_run"
      enable_iap           = true
      use_main_client_id   = true
      iap_accessor_members = ["group:<EMAIL>"]
    },
    "vtaf-ai-cloudrun-api" = {
      service_type         = "cloud_run"
      enable_iap           = true
      use_main_client_id   = true
      iap_accessor_members = ["group:<EMAIL>"]
    },
    "vtaf-ai-cloudrun-backend-conversation" = {
      service_type         = "cloud_run"
      enable_iap           = true
      use_main_client_id   = true
      iap_accessor_members = ["group:<EMAIL>"]
    }
  }
}
