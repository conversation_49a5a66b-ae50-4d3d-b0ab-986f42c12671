# Group {App Ctl}
## Check_Cam-VPI

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Check_Cam-VPI.seq*


**Descriptions:** *This keyword Check the VPI application Chimera Plugin. *


***Command Set:***

	ImgWidth
		-ImgWidth=Camera;Condition
	ImgHeight
		-ImgHeight=Camera;Condition
	ImgFPS
		-ImgFPS=Camera;Condition
	I2C-Simple
		-I2C-Simple=Response
	I2C-Bytes
		-I2C-Bytes=Response;StartIndex;Length
	I2C-Search
		-I2C-Search=Response
	I2C-Bit
		-I2C-Bit=Response;StartIndex
	I2C-Ascii
		-I2C-Ascii=Response
	EL-Bit
		-EL-Bit=Camera;Response;RowIndex;ColoumnIndex
	EL-Byte
		-EL-Byte=Camera;Response;RowIndex;ColoumnIndex
	CRC32
		-CRC32=
	I2C-DataValid
		-I2C-DataValid=
	I2C-Length
		-I2C-Length=Length
	I2C-EL-Byte
		-I2C-EL-Byte=Camera;RowIndex;ColoumnIndex;ByteLength;Comparison
	CyberSecurity-Unlock
		-CyberSecurity-Unlock=Mode;Status
	FTTI-Test
		-FTTI-Test=FTTIParameter;FTTICondition
	PixelClock
		-PixelClock=Camera;Condition


***Input Description***



## CommandLine

- [ ] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\CommandLine.seq*


**Descriptions:** *This keyword will call command line. *


***Command Set:***

	Call
		-Call=WaitCondition;TimeToWait;Expression;ReturnCondition


***Input Description***



## File_Action

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\File_Action.seq*


**Descriptions:** *To move file from source to destination*


***Command Set:***

	Copy
		-Copy=Source;Destination


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Source| String| NA| Xyz|  Define Source Path
| Destination| String| NA| Xyz|  Define Destination path

## PCAP_Log

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\PCAP_Log.seq*


**Descriptions:** *This keyword logs and controls PCAP VPI.*


***Command Set:***

	Open
		-Open=Source
	Close
		-Close=
	Start
		-Start=
	Stop
		-Stop=
	CheckPacketSize
		-CheckPacketSize=Condition


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Source| String| NA| Default|  
| Condition| String| NA| X>=10|  Provide the condition for the reading comparision
X==10
X>=10
>=5 X <=10 

## PLP

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\PLP.seq*


**Descriptions:** *This Keyword will open and close VPI application contating PLP plugins.*


***Command Set:***

	Open
		-Open=
	Close
		-Close=


***Input Description***



## PLP_Recorder

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\PLP_Recorder.seq*


**Descriptions:** *This Keyword will Start and Stop PLP recorder VPI plugin*


***Command Set:***

	Start
		-Start=
	Stop
		-Stop=
	ReceivedPackets
		-ReceivedPackets=Value
	DroppedPackets
		-DroppedPackets=Value
	ProbeDetails
		-ProbeDetails=ProbeID;ReceivedPacketsVal;DroppedPacketsVal


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Value| String| NA| X>=10|  Provide the condition for the reading comparision
X==10
X>=10
>=5 X <=10 
| ProbeID| String| NA| 179|  
| ReceivedPacketsVal| String| NA| X>=50|   Provide the condition for the reading comparision
X==10
X>=10
>=5 X <=10 
| DroppedPacketsVal| String| NA| X==0|  Provide the condition for the reading comparision
X==10
X>=10
>=5 X <=10 

## PLP_Validator

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\PLP_Validator.seq*


**Descriptions:** *This Keyword gets validates PCAP file using PLP_Validator tool*


***Command Set:***

	Start
		-Start=Timeout
	DroppedPackets
		-DroppedPackets=Packet_Type;Packet_Count
	CheckSanctionStatus
		-CheckSanctionStatus=Status


***Input Description***



## Set_Cam-VPI

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Set_Cam-VPI.seq*


**Descriptions:** *This keyword control the VPI application Chimera Plugin. *


***Command Set:***

	ConnectCamera
		-ConnectCamera=Camera
	RequestFrame
		-RequestFrame=Camera
	StreamMode
		-StreamMode=Camera;StreamMode
	CameraPower
		-CameraPower=Camera;Power
	I2C
		-I2C=Camera;I2CMode;Type;DeviceAddress;ResponseSize;AnyAddressHex;NoOfRegisters;Payload
	SnapImage
		-SnapImage=Camera
	Open
		-Open=
	Close
		-Close=
	I2C-EEPROM
		-I2C-EEPROM=Type;NoOfRegisters;Payload
	I2C-PMIC
		-I2C-PMIC=Type;NoOfRegisters;Payload
	I2C-SER
		-I2C-SER=Type;NoOfRegisters;Payload
	I2C-Imager
		-I2C-Imager=Type;NoOfRegisters;Payload
	StandardComm
		-StandardComm=Command;DataInput;NumberOfBytes
	DisConnectCamera
		-DisConnectCamera=Camera
	CyberSecurity-Unlock
		-CyberSecurity-Unlock=Mode
	CyberSecurity-R-Params
		-CyberSecurity-R-Params=Mode;value
	FTTI-Test
		-FTTI-Test=FTTIParameter;FTTIValue
	I2C-Request
		-I2C-Request=OtherAddressHex;Port;ExpectedResponseByte;DeviceAddresses;TxInputString
	SendI2C
		-SendI2C=Type;AnyAddressHex;DataInput;SlaveAddress;CommChannel
	VCMeasurement
		-VCMeasurement=Camera;VCID
	ResetDes
		-ResetDes=Camera


***Input Description***



## Variables

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Variables.seq*


**Descriptions:** *To create dynamic variables*


***Command Set:***

	Create
		-Create=Name;Value


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Name| String| NA| Xyz|  Name of the Variable to create
| Value| String| NA| Xyz|  Define multiple values seperated by ( ,). 
e.g. P,N,R

## Vosstrex_Interface

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Vosstrex_Interface.seq*


**Descriptions:** **


***Command Set:***

	Open
		-Open=
	Close
		-Close=
	Control
		-Control=Command;Data
	StartScene
		-StartScene=
	StopScene
		-StopScene=
	Position
		-Position=X-Position;Y-Position;Origin
	Gear
		-Gear=Gear
	LoadScene
		-LoadScene=Scene
	Speed
		-Speed=Speed
	LaunchPlugin
		-LaunchPlugin=Plugin
	SteeringWheelAngle
		-SteeringWheelAngle=SteeringWheelAngle
	CustomExport
		-CustomExport=
	AddObstacle
		-AddObstacle=Model;Position;GyroData;Size;Target;ObstacleSpeed;ID
	RemoveObstacle
		-RemoveObstacle=ID
	ModifyObstacle
		-ModifyObstacle=Position;GyroData;Size;ID;Position_En;GyroData_En;Size_En


***Input Description***



