import logging
import fastapi

from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import ORJSONResponse


logger = logging.getLogger("vtaf")
utils_router = APIRouter(prefix="/utils")

@utils_router.get(
    "/get_user",
    response_class=ORJSONResponse,
    description="Get User endpoint for the VTAF AI service",
    summary="Get User API",
    status_code=fastapi.status.HTTP_200_OK,
)
def get_user(request: Request):
    try:
        user_email = request.headers.get("X-Goog-Authenticated-User-Email")
        if user_email:
            email = user_email.split(":")[1]
        else:
            email = "<EMAIL>"
            
        return {"data": email}
    except Exception as e:
        logger.error(f"Exception occurred: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve user information")