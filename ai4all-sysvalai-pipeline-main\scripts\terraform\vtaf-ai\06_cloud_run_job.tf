# This Cloud Run Job Used To Create Embedding in Qdrant DB
resource "google_cloud_run_v2_job" "vtaf_ai_embedding_generation_job" {
  name                = "${var.vtaf_ai_service_name}-cloudrunjobs-knowledgebase"
  location            = var.location
  deletion_protection = false
  template {
    template {
      containers {
        image = "${var.vtaf_ai_artifactory_url}/${var.project_id}/${var.vtaf_ai_artifactory_name}/${var.vtaf_ai_cloudrunjobs_knowledgebase_image}:${var.vtaf_ai_cloudrunjobs_knowledgebase_image_version}"
        dynamic "env" {
          for_each = var.kcrj_env_vars
          content {
            name  = env.value.name
            value = env.value.value
          }
        }
        volume_mounts {
          name       = "vtaf_ai_agents"
          mount_path = "/mnt/vtaf-ai"
        }
        resources {
          limits = {
            memory = "4Gi"
            cpu    = "2"
          }
        }
      }
      volumes {
        name = "vtaf_ai_agents"
        gcs {
          bucket = "${var.project_id}-vtaf-ai-agents"
        }
      }
      timeout         = "86400s" # 1 Days
      max_retries     = 0
      service_account = var.service_account_email

    }
  }
}

# This Cloud Run Job Used To Create Manual Testcases
resource "google_cloud_run_v2_job" "vtaf_ai_tc_generation_job" {
  name                = "${var.vtaf_ai_service_name}-cloudrunjobs-testcase-generation"
  location            = var.location
  deletion_protection = false
  template {
    template {
      containers {
        image = "${var.vtaf_ai_artifactory_url}/${var.project_id}/${var.vtaf_ai_artifactory_name}/${var.vtaf_ai_cloudrunjobs_testcase_generation_image}:${var.vtaf_ai_cloudrunjobs_testcase_generation_image_version}"
        dynamic "env" {
          for_each = var.tcrj_env_vars
          content {
            name  = env.value.name
            value = env.value.value
          }
        }
        volume_mounts {
          name       = "vtaf_ai_agents"
          mount_path = "/mnt/vtaf-ai"
        }
        resources {
          limits = {
            memory = "4Gi"
            cpu    = "2"
          }
        }
      }
      volumes {
        name = "vtaf_ai_agents"
        gcs {
          bucket = "${var.project_id}-vtaf-ai-agents"
        }
      }
      timeout         = "86400s" # 1 Days
      max_retries     = 0
      service_account = var.service_account_email

    }
  }
}

# This Cloud Run Job Used To Create Manual Testcases
resource "google_cloud_run_v2_job" "vtaf_ai_doors_embedding" {
  name                = "${var.vtaf_ai_service_name}-doors-embedding"
  location            = var.location
  deletion_protection = false
  template {
      task_count      = 12
      parallelism     = 0
    template {

      containers {
        image = "${var.vtaf_ai_artifactory_url}/${var.project_id}/${var.vtaf_ai_artifactory_name}/${var.vtaf_ai_cloudrunjobs_doors_embedding_image}:${var.vtaf_ai_cloudrunjobs_doors_embedding_image_version}"
        dynamic "env" {
          for_each = var.ecrj_env_vars
          content {
            name  = env.value.name
            value = env.value.value
          }
        }
        resources {
          limits = {
            memory = "4Gi"
            cpu    = "2"
          }
        }
      }

      timeout         = "86400s" # 1 Days
      max_retries     = 0
      service_account = var.service_account_email

    }
  }
}

# This Cloud Run Job Used To Create Manual Testcases
resource "google_cloud_run_v2_job" "vtaf_ai_ts_generation_job" {
  name                = "${var.vtaf_ai_service_name}-cloudrunjobs-test-script-generation"
  location            = var.location
  deletion_protection = false
  template {
    template {
      containers {
        image = "${var.vtaf_ai_artifactory_url}/${var.project_id}/${var.vtaf_ai_artifactory_name}/${var.vtaf_ai_cloudrunjobs_test_script_generation_image}:${var.vtaf_ai_cloudrunjobs_test_script_generation_image_version}"
        dynamic "env" {
          for_each = var.tsrj_env_vars
          content {
            name  = env.value.name
            value = env.value.value
          }
        }
        volume_mounts {
          name       = "vtaf_ai_agents"
          mount_path = "/mnt/vtaf-ai"
        }
        resources {
          limits = {
            memory = "4Gi"
            cpu    = "2"
          }
        }
      }
      volumes {
        name = "vtaf_ai_agents"
        gcs {
          bucket = "${var.project_id}-vtaf-ai-agents"
        }
      }
      timeout         = "86400s" # 1 Days
      max_retries     = 0
      service_account = var.service_account_email

    }
  }
}

# This Cloud Run Job Used For Multiprompting
resource "google_cloud_run_v2_job" "vtaf_ai_multiprompting_job" {
  name                = "${var.vtaf_ai_service_name}-cloudrunjobs-multiprompt"
  location            = var.location
  deletion_protection = false
  template {
    template {
      containers {
        image = "${var.vtaf_ai_artifactory_url}/${var.project_id}/${var.vtaf_ai_artifactory_name}/${var.vtaf_ai_cloudrunjobs_multiprompt_image}:${var.vtaf_ai_cloudrunjobs_multiprompt_image_version}"
        dynamic "env" {
          for_each = var.mpcrj_env_vars
          content {
            name  = env.value.name
            value = env.value.value
          }
        }
        resources {
          limits = {
            memory = "4Gi"
            cpu    = "2"
          }
        }
      }
    
      timeout         = "86400s" # 1 Days
      max_retries     = 0
      service_account = var.service_account_email

    }
  }
}