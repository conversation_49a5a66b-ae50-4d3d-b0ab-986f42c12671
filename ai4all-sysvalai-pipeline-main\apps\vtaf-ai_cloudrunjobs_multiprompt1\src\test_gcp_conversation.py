#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 GCP 环境中的 AI 对话功能
模拟 main.py 的执行流程
"""

import os
import sys
import json
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def simulate_gcp_environment():
    """模拟 GCP 环境变量"""
    print("🌍 模拟 GCP 环境设置...")
    
    # 设置 GCP 环境标识
    os.environ['K_SERVICE'] = 'vtaf-ai-cloudrunjobs-multiprompt'
    os.environ['GOOGLE_CLOUD_PROJECT'] = 'valeo-cp2673-dev'
    os.environ['PROJECT_ID'] = 'valeo-cp2673-dev'
    os.environ['REGION'] = 'us-central1'
    
    # 设置任务配置（模拟真实的表格配置）
    os.environ['TASK_TYPE'] = 'batch'
    os.environ['AI_MODEL'] = 'gemini-1.5-pro'
    
    # 模拟表格配置（你需要替换为实际的表格URL）
    os.environ['PROMPT_SHEET_URL'] = 'https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms'
    os.environ['TEST_SPEC_SHEET_URL'] = 'https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms'
    os.environ['PROMPT_SHEET_NAME'] = 'Class Data'
    
    print("✅ GCP 环境变量设置完成")

def test_main_initialization():
    """测试 main.py 的初始化流程"""
    print("\n📝 测试 main.py 初始化流程...")
    
    try:
        # 导入 main 模块的函数
        from main import get_task_config, validate_config, log_environment_info
        
        # 1. 测试环境信息记录
        print("1️⃣ 测试环境信息记录...")
        log_environment_info()
        
        # 2. 测试配置获取
        print("\n2️⃣ 测试任务配置获取...")
        config = get_task_config()
        print(f"配置获取成功: {json.dumps({k: v for k, v in config.items() if k != 'model'}, indent=2)}")
        print(f"使用模型: {config['model']}")
        
        # 3. 测试配置验证
        print("\n3️⃣ 测试配置验证...")
        validate_config(config)
        print("✅ 配置验证通过")
        
        return config
        
    except Exception as e:
        print(f"❌ main.py 初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_ai_service_in_gcp():
    """测试在 GCP 环境中的 AI 服务"""
    print("\n🤖 测试 GCP 环境中的 AI 服务...")
    
    try:
        from services.ai_service import AIService
        
        # 初始化 AI 服务
        print("初始化 AI 服务...")
        ai_service = AIService()
        print("✅ AI 服务初始化成功")
        
        # 测试对话功能
        test_prompts = [
            "你好，请回复'GCP测试成功'",
            "请生成一个简单的测试用例，包含Test Case ID和Test Objective",
            "1+1等于多少？请简短回答"
        ]
        
        print(f"\n🔄 开始测试 {len(test_prompts)} 个对话...")
        
        results = []
        for i, prompt in enumerate(test_prompts, 1):
            print(f"\n--- 对话测试 {i}/{len(test_prompts)} ---")
            print(f"📤 提示词: {prompt}")
            
            try:
                response = ai_service.generate_response(prompt)
                
                if response and not response.startswith("Error"):
                    print(f"✅ 响应成功 (长度: {len(response)} 字符)")
                    print(f"📥 响应内容: {response[:100]}{'...' if len(response) > 100 else ''}")
                    results.append(True)
                else:
                    print(f"❌ 响应失败: {response[:100] if response else 'No response'}")
                    results.append(False)
                    
            except Exception as e:
                print(f"❌ 对话异常: {e}")
                results.append(False)
        
        success_count = sum(results)
        total_count = len(results)
        
        print(f"\n📊 对话测试结果:")
        print(f"✅ 成功: {success_count}/{total_count}")
        print(f"📈 成功率: {success_count/total_count*100:.1f}%")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ AI 服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_processor_initialization():
    """测试批处理器初始化"""
    print("\n⚙️ 测试批处理器初始化...")
    
    try:
        from services.ai_service import AIService
        from services.batch_processor import BatchProcessor
        
        # 初始化服务
        ai_service = AIService()
        batch_processor = BatchProcessor(ai_service)
        
        print("✅ 批处理器初始化成功")
        
        # 测试配置处理
        config = {
            'task_type': 'batch',
            'prompt_sheet': os.getenv('PROMPT_SHEET_URL'),
            'test_spec_sheet': os.getenv('TEST_SPEC_SHEET_URL'),
            'prompt_sheet_name': os.getenv('PROMPT_SHEET_NAME'),
            'model': 'gemini-1.5-pro'
        }
        
        print("✅ 批处理器配置准备完成")
        print("⚠️  注意: 实际批处理需要有效的 Google Sheets 配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 批处理器测试失败: {e}")
        return False

def test_sheets_connection():
    """测试 Google Sheets 连接"""
    print("\n📊 测试 Google Sheets 连接...")
    
    try:
        from services.sheet_manager import SheetManager
        
        sheet_manager = SheetManager()
        print("✅ SheetManager 初始化成功")
        
        # 测试连接到示例表格
        test_sheet_id = "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
        success = sheet_manager.connect_sheet(test_sheet_id)
        
        if success:
            print("✅ Google Sheets 连接成功")
            sheet_names = sheet_manager.get_sheet_names()
            print(f"📋 找到 {len(sheet_names)} 个工作表")
            return True
        else:
            print("❌ Google Sheets 连接失败")
            return False
            
    except Exception as e:
        print(f"❌ Sheets 连接测试失败: {e}")
        return False

def cleanup_environment():
    """清理测试环境变量"""
    test_vars = [
        'K_SERVICE', 'GOOGLE_CLOUD_PROJECT', 'PROJECT_ID', 'REGION',
        'TASK_TYPE', 'AI_MODEL', 'PROMPT_SHEET_URL', 'TEST_SPEC_SHEET_URL', 'PROMPT_SHEET_NAME'
    ]
    
    for var in test_vars:
        if var in os.environ:
            del os.environ[var]

def main():
    """主测试函数"""
    print("🚀 GCP 环境 AI 对话功能测试")
    print("=" * 60)
    
    try:
        # 1. 模拟 GCP 环境
        simulate_gcp_environment()
        
        # 2. 测试项目
        tests = [
            ("main.py 初始化", test_main_initialization),
            ("AI 对话服务", test_ai_service_in_gcp),
            ("批处理器初始化", test_batch_processor_initialization),
            ("Google Sheets 连接", test_sheets_connection)
        ]
        
        results = []
        
        for test_name, test_func in tests:
            try:
                print(f"\n🧪 开始测试: {test_name}")
                result = test_func()
                results.append((test_name, result))
                print(f"{'✅ 通过' if result else '❌ 失败'}: {test_name}")
            except Exception as e:
                print(f"❌ 测试异常: {test_name} - {e}")
                results.append((test_name, False))
        
        # 总结
        print(f"\n{'=' * 60}")
        print("🎯 GCP 环境测试总结")
        print(f"{'=' * 60}")
        
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        print(f"总体结果: {passed}/{total} 测试通过")
        
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        if passed >= 2:  # 至少要有初始化和AI对话通过
            print(f"\n🎉 GCP 环境中的 AI 对话功能可用！")
            print("💡 main.py 应该能在 Google Cloud 上正常执行对话任务")
        else:
            print(f"\n⚠️  GCP 环境测试未完全通过")
            print("💡 建议检查认证和网络配置")
        
        return passed >= 2
        
    finally:
        # 清理环境
        cleanup_environment()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
