# This Dataset And Tables Used By VTAF.AI

module "bigquery_vtaf_ai" {
  source  = "terraform-google-modules/bigquery/google"
  version = "~> 9.0"

  dataset_id                 = var.bigquery_dataset_id
  dataset_name               = var.bigquery_dataset_id
  description                = "core resources used by ${var.bigquery_dataset_id}"
  project_id                 = var.project_id
  location                   = "EU"
  delete_contents_on_destroy = false
  access = [
    {
      "role" : "roles/bigquery.dataOwner",
      "special_group" : "projectOwners"
    },
    {
      "role" : "roles/bigquery.dataOwner",
      "user_by_email" : "<EMAIL>"
    },
    {
      "role" : "roles/bigquery.dataOwner",
      "user_by_email" : "<EMAIL>"
    },
    {
      "role" : "roles/bigquery.dataOwner",
      "user_by_email" : "<EMAIL>"
    },
    {
      "role" : "roles/bigquery.dataOwner",
      "user_by_email" : "<EMAIL>"
    },
    {
      "role" : "roles/bigquery.dataOwner",
      "user_by_email" : "${var.project_id}@appspot.gserviceaccount.com"
    },
    {
      "role" : "roles/bigquery.dataOwner",
      "user_by_email" : var.service_account_email
    },
    {
      "role" : "roles/bigquery.dataOwner",
      "user_by_email" : "vtaf-ai-terraform@${var.project_id}.iam.gserviceaccount.com"
    },
    {
      "role" : "roles/bigquery.dataOwner",
      "user_by_email" : "<EMAIL>"
    },
    {
      "role" : "roles/bigquery.dataOwner",
      "user_by_email" : "<EMAIL>"
    },
    {
      "role" : "roles/bigquery.dataOwner",
      "user_by_email" : "<EMAIL>"
    }
  ]
  tables = [
    {
      table_id = "KNOWLEDGE_JOB_HISTORY",
      schema   = file("./files/vtaf_ai_bq_schema/AI_KNOWLEDGE_JOB_HISTORY_schema.json"),
      time_partitioning = {
        type                     = "DAY",
        field                    = "timestamp",
        require_partition_filter = false,
        expiration_ms            = null,
      },
      range_partitioning = null,
      expiration_time    = null,
      clustering         = ["project", "variant", "euf_feature", "user_email"],
      labels = {
        env      = var.environment
        billable = "true"
        owner    = "valeo"
      }
    },
    {
      table_id = "TC_JOB_HISTORY",
      schema   = file("./files/vtaf_ai_bq_schema/AI_TC_JOB_HISTORY_schema.json"),
      time_partitioning = {
        type                     = "DAY",
        field                    = "timestamp",
        require_partition_filter = false,
        expiration_ms            = null,
      },
      range_partitioning = null,
      expiration_time    = null,
      clustering         = ["user_email"],
      labels = {
        env      = var.environment
        billable = "true"
        owner    = "valeo"
      }
    },
    {
      table_id           = "EUF_FEATURE_CONFIG_TABLE",
      schema             = file("./files/vtaf_ai_bq_schema/EUF_FEATURE_CONFIG_TABLE_schema.json"),
      time_partitioning  = null
      range_partitioning = null,
      expiration_time    = null,
      clustering         = [],
      labels = {
        env      = var.environment
        billable = "true"
        owner    = "valeo"
      }
    },
    {
      table_id           = "PROJECT_CONFIGURATION",
      schema             = file("./files/vtaf_ai_bq_schema/PROJECT_CONFIGURATION_schema.json"),
      time_partitioning  = null
      range_partitioning = null,
      expiration_time    = null,
      clustering         = [],
      labels = {
        env      = var.environment
        billable = "true"
        owner    = "valeo"
      }
    },
    {
      table_id           = "PROJECT_KNOWLEDGE_BASE",
      schema             = file("./files/vtaf_ai_bq_schema/PROJECT_KNOWLEDGE_BASE_schema.json"),
      time_partitioning  = null
      range_partitioning = null,
      expiration_time    = null,
      clustering         = [],
      labels = {
        env      = var.environment
        billable = "true"
        owner    = "valeo"
      }
    },
    {
      table_id = "PROJECT_PROFILE_TABLE",
      schema   = file("./files/vtaf_ai_bq_schema/PROJECT_PROFILE_TABLE_schema.json"),
      time_partitioning = {
        type                     = "DAY",
        field                    = "created_timestamp",
        require_partition_filter = false,
        expiration_ms            = null,
      },
      range_partitioning = null,
      expiration_time    = null,
      clustering         = ["project", "variant", "euf_feature"],
      labels = {
        env      = var.environment
        billable = "true"
        owner    = "valeo"
      }
    },
    {
      table_id           = "PROJECT_TABLE",
      schema             = file("./files/vtaf_ai_bq_schema/PROJECT_TABLE_schema.json"),
      time_partitioning  = null
      range_partitioning = null,
      expiration_time    = null,
      clustering         = ["project"],
      labels = {
        env      = var.environment
        billable = "true"
        owner    = "valeo"
      }
    },
    {
      table_id           = "PROJECT_VARIANT_TABLE",
      schema             = file("./files/vtaf_ai_bq_schema/PROJECT_VARIANT_TABLE_schema.json"),
      time_partitioning  = null
      range_partitioning = null,
      expiration_time    = null,
      clustering         = [],
      labels = {
        env      = var.environment
        billable = "true"
        owner    = "valeo"
      }
    },
    {
      table_id           = "KEY_USERGROUP",
      schema             = file("./files/vtaf_ai_bq_schema/VTAF_KEY_USERGROUP_schema.json"),
      time_partitioning  = null
      range_partitioning = null,
      expiration_time    = null,
      clustering         = [],
      labels = {
        env      = var.environment
        billable = "true"
        owner    = "valeo"
      }
    },
    {
      table_id           = "VALEO_ORGANIZATION",
      schema             = file("./files/vtaf_ai_bq_schema/VTAF_ORGANIZATION_schema.json"),
      time_partitioning  = null
      range_partitioning = null,
      expiration_time    = null,
      clustering         = [],
      labels = {
        env      = var.environment
        billable = "true"
        owner    = "valeo"
      }
    },
      {
      table_id           = "MULTIPROMPTING_CONFIG",
      schema             = file("./files/vtaf_ai_bq_schema/MULTIPROMPTING_CONFIG_schema.json"),
      time_partitioning  = null
      range_partitioning = null,
      expiration_time    = null,
      clustering         = [],
      labels = {
        env      = var.environment
        billable = "true"
        owner    = "valeo"
      }
    },
      {
      table_id           = "TS_JOB_HISTORY",
      schema             = file("./files/vtaf_ai_bq_schema/AI_TS_JOB_HISTORY_schema.json"),
      time_partitioning = {
        type                     = "DAY",
        field                    = "timestamp",
        require_partition_filter = false,
        expiration_ms            = null,
      },
      range_partitioning = null,
      expiration_time    = null,
      clustering         = ["user_email"],
      labels = {
        env      = var.environment
        billable = "true"
        owner    = "valeo"
      }
    }
  ]
  dataset_labels = {
    env      = var.environment
    billable = "true"
    owner    = "valeo"
    name     = "vtaf_ai_dev"
  }
}
