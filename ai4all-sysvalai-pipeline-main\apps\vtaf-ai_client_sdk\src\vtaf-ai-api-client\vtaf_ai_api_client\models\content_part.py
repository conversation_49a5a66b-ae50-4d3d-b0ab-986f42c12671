from collections.abc import Mapping
from typing import Any, <PERSON>Var, Union, cast

from attrs import define as _attrs_define
from attrs import field as _attrs_field

from ..types import UNSET, Unset

T = TypeVar("T", bound="ContentPart")


@_attrs_define
class ContentPart:
    """
    Attributes:
        type_ (str):
        data (str):
        mimetype (Union[None, Unset, str]):
        filename (Union[None, Unset, str]):
    """

    type_: str
    data: str
    mimetype: Union[None, Unset, str] = UNSET
    filename: Union[None, Unset, str] = UNSET
    additional_properties: dict[str, Any] = _attrs_field(init=False, factory=dict)

    def to_dict(self) -> dict[str, Any]:
        type_ = self.type_

        data = self.data

        mimetype: Union[None, Unset, str]
        if isinstance(self.mimetype, Unset):
            mimetype = UNSET
        else:
            mimetype = self.mimetype

        filename: Union[None, Unset, str]
        if isinstance(self.filename, Unset):
            filename = UNSET
        else:
            filename = self.filename

        field_dict: dict[str, Any] = {}
        field_dict.update(self.additional_properties)
        field_dict.update(
            {
                "type": type_,
                "data": data,
            }
        )
        if mimetype is not UNSET:
            field_dict["mimetype"] = mimetype
        if filename is not UNSET:
            field_dict["filename"] = filename

        return field_dict

    @classmethod
    def from_dict(cls: type[T], src_dict: Mapping[str, Any]) -> T:
        d = dict(src_dict)
        type_ = d.pop("type")

        data = d.pop("data")

        def _parse_mimetype(data: object) -> Union[None, Unset, str]:
            if data is None:
                return data
            if isinstance(data, Unset):
                return data
            return cast(Union[None, Unset, str], data)

        mimetype = _parse_mimetype(d.pop("mimetype", UNSET))

        def _parse_filename(data: object) -> Union[None, Unset, str]:
            if data is None:
                return data
            if isinstance(data, Unset):
                return data
            return cast(Union[None, Unset, str], data)

        filename = _parse_filename(d.pop("filename", UNSET))

        content_part = cls(
            type_=type_,
            data=data,
            mimetype=mimetype,
            filename=filename,
        )

        content_part.additional_properties = d
        return content_part

    @property
    def additional_keys(self) -> list[str]:
        return list(self.additional_properties.keys())

    def __getitem__(self, key: str) -> Any:
        return self.additional_properties[key]

    def __setitem__(self, key: str, value: Any) -> None:
        self.additional_properties[key] = value

    def __delitem__(self, key: str) -> None:
        del self.additional_properties[key]

    def __contains__(self, key: str) -> bool:
        return key in self.additional_properties
