import { handleError, ref } from "vue";
import { defineStore } from "pinia";
import { Notify, Loading } from "quasar";
import { ai_testcaseApi } from "src/boot/axios";

export const useRunKbTcGenerationStore = defineStore("run_kb_tc_store", () => {
  const user_email = ref("");

  const projects_list = ref([]);
  const variants_list = ref([]);
  const euf_features_list = ref([]);
  const selected_project = ref("");
  const selected_variant = ref("");
  const selected_euf_feature = ref("");
  const selected_file = ref(null);
  const sensors_and_algorithms = ref([]);
  const table_data = ref([]);
  const columns = ref([]);
  const selected_rows = ref([]);
  const current_execution_id = ref("");
  const project_loading = ref(false);
  const variant_loading = ref(false);
  const euf_feature_loading = ref(false);
  const sensors_and_algorithms_loading = ref(false);
  const upload_file_loading = ref(false);

  const agent_yaml_content = ref(null);
  const task_yaml_content = ref(null);

  const is_yaml_edited = ref(false);
  const is_yaml_saved = ref(false);

  async function get_user_email() {
    try {
      const response = await ai_testcaseApi.get(`/user/get_user`);
      if (response.status === 200) {
        // Success
        const data = response.data.data;
        user_email.value = data;
        console.log(data);
      } else {
        handleError(response);
      }
    } catch (err) {
      console.log(err);
    }
  }

  async function get_profile_projects() {
    try {
      project_loading.value = true;
      const response = await ai_testcaseApi.get(`/run_kb/get_profile_projects`);
      const data = response.data;
      projects_list.value = data.map((item) => item.project);
      console.log(data);
    } catch (err) {
      handleFetchError(err);
    } finally {
      project_loading.value = false;
    }
  }
  async function get_variant_details() {
    try {
      variant_loading.value = true;
      const response = await ai_testcaseApi.get(
        `/run_kb/get_tree_variant_details/${selected_project.value}`
      );
      const data = response.data;
      variants_list.value = data;
      console.log(data);
    } catch (err) {
      handleFetchError(err);
    } finally {
      variant_loading.value = false;
    }
  }
  async function get_euf_features_details() {
    try {
      euf_feature_loading.value = true;
      const variant_data = [selected_project.value, selected_variant.value];
      const response = await ai_testcaseApi.get(
        `/run_kb/get_tree_euf_features_details/${variant_data}`
      );
      const data = response.data;
      euf_features_list.value = data;
      console.log(data);
    } catch (err) {
      handleFetchError(err);
    } finally {
      euf_feature_loading.value = false;
    }
  }

  async function get_knowledgebase_profile() {
    try {
      sensors_and_algorithms_loading.value = true;
      const formData = new FormData();
      formData.append("project", selected_project.value);
      formData.append("variant", selected_variant.value);
      formData.append("euf_feature", selected_euf_feature.value);

      const response = await ai_testcaseApi.post(
        `/run_kb/get_knowledgebase_profile`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      if (response.status == 200) {
        console.log("fetched");
        sensors_and_algorithms.value = response.data;
        sensors_and_algorithms_loading.value = false;
      } else {
        throw new Error("Upload failed");
      }
    } catch (err) {
      handleFetchError(err);
    } finally {
      sensors_and_algorithms_loading.value = false;
    }
  }

  async function upload_file_with_knowledgebase() {
    try {
      upload_file_loading.value = true;
      if (!selected_file.value) {
        Loading.hide();
        return;
      }

      const formData = new FormData();
      formData.append("user_email", user_email.value);
      formData.append("p_uuid", sensors_and_algorithms.value[0].project_uuid);
      formData.append("v_uuid", sensors_and_algorithms.value[0].variant_uuid);
      formData.append(
        "ef_uuid",
        sensors_and_algorithms.value[0].euf_feature_uuid
      );
      formData.append("business_group", sensors_and_algorithms.value[0].bg);
      formData.append("product_group", sensors_and_algorithms.value[0].pg);
      formData.append("product_line", sensors_and_algorithms.value[0].pl);
      formData.append("project", sensors_and_algorithms.value[0].project);
      formData.append("variant", sensors_and_algorithms.value[0].variant);
      formData.append(
        "euf_feature",
        sensors_and_algorithms.value[0].euf_feature
      );
      formData.append("file", selected_file.value);
      formData.append("selected_rows", JSON.stringify(selected_rows.value));
      formData.append("features", selected_euf_feature.value);
      formData.append(
        "algorithms",
        JSON.stringify(sensors_and_algorithms.value[0].algorithms)
      );
      formData.append(
        "sensors",
        JSON.stringify(sensors_and_algorithms.value[0].sensors)
      );
      formData.append(
        "agent_content",
        JSON.stringify(agent_yaml_content.value)
      );
      formData.append("task_content", JSON.stringify(task_yaml_content.value));
      formData.append("is_yaml_edited", is_yaml_saved.value);
      const response = await ai_testcaseApi.post(
        `/run_kb/upload_file_knowledgebase`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      if (response.status == 200) {
        console.log("uploaded");
        current_execution_id.value = response.data.execution_id;
        upload_file_loading.value = false;
      } else {
        throw new Error("Upload failed");
      }
    } catch (err) {
      handleFetchError(err);
    } finally {
      upload_file_loading.value = false;
    }
  }

  async function get_yaml_contents() {
    try {
      const formData = new FormData();
      formData.append("functionality", selected_euf_feature.value);
      formData.append(
        "sensors",
        JSON.stringify(sensors_and_algorithms.value[0].sensors)
      );
      formData.append(
        "algorithms",
        JSON.stringify(sensors_and_algorithms.value[0].algorithms)
      );

      const response = await ai_testcaseApi.post(
        `/run_kb/get_yaml_content`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      if (response.status == 200) {
        console.log("fetched");
        agent_yaml_content.value = response.data.agent_yaml;
        task_yaml_content.value = response.data.task_yaml;
        console.log(agent_yaml_content.value);
        console.log(task_yaml_content.value);
      } else {
        throw new Error("Upload failed");
      }
    } catch (err) {
      handleFetchError(err);
    } finally {
    }
  }

  function handleFetchError(err) {
    if (err.response) {
      const errorMessage = err.response
        ? err.response.data.detail
        : err.message;
      const errorStatus = err.response.status;
      Notify.create({
        color: "negative",
        position: "bottom",
        message: `${errorStatus} : ${errorMessage}`,
        icon: "report_problem",
      });
    } else {
      Notify.create({
        color: "negative",
        position: "bottom",
        message: err.message,
        icon: "report_problem",
      });
    }
  }

  return {
    user_email,
    projects_list,
    variants_list,
    euf_features_list,
    selected_project,
    selected_variant,
    selected_euf_feature,
    selected_file,
    sensors_and_algorithms,
    table_data,
    columns,
    selected_rows,
    current_execution_id,
    project_loading,
    variant_loading,
    euf_feature_loading,
    sensors_and_algorithms_loading,
    upload_file_loading,
    agent_yaml_content,
    task_yaml_content,
    is_yaml_edited,
    is_yaml_saved,
    get_user_email,
    get_profile_projects,
    get_variant_details,
    get_euf_features_details,
    get_knowledgebase_profile,
    upload_file_with_knowledgebase,
    get_yaml_contents,
  };
});
