#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试云端配置加载
"""

import os
import sys
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_environment_detection():
    """测试环境检测"""
    print("🌍 环境检测测试")
    print("=" * 50)
    
    # 检查环境变量
    cloud_env_vars = {
        'K_SERVICE': os.getenv('K_SERVICE'),
        'FUNCTION_NAME': os.getenv('FUNCTION_NAME'),
        'GAE_SERVICE': os.getenv('GAE_SERVICE'),
        'GOOGLE_CLOUD_PROJECT': os.getenv('GOOGLE_CLOUD_PROJECT'),
        'PROJECT_ID': os.getenv('PROJECT_ID'),
        'REGION': os.getenv('REGION')
    }
    
    print("环境变量:")
    for key, value in cloud_env_vars.items():
        status = "✅" if value else "❌"
        print(f"  {status} {key}: {value or 'Not set'}")
    
    # 判断环境类型
    is_cloud_env = any([
        os.getenv('K_SERVICE'),
        os.getenv('FUNCTION_NAME'),
        os.getenv('GAE_SERVICE'),
        os.getenv('GOOGLE_CLOUD_PROJECT')
    ])
    
    print(f"\n环境类型: {'☁️  Google Cloud' if is_cloud_env else '💻 本地环境'}")
    return is_cloud_env

def test_config_loading():
    """测试配置加载"""
    print("\n📝 配置加载测试")
    print("=" * 50)
    
    try:
        # 模拟云端环境
        os.environ['K_SERVICE'] = 'test-service'
        
        from configs.env import settings
        
        print("✅ 配置加载成功")
        print(f"  - PROJECT_ID: {settings.PROJECT_ID}")
        print(f"  - REGION: {settings.REGION}")
        print(f"  - DEFAULT_MODEL: {settings.DEFAULT_MODEL}")
        print(f"  - SERVICE_ACCOUNT_INFO: {'存在' if settings.SERVICE_ACCOUNT_INFO else '不存在'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理环境变量
        if 'K_SERVICE' in os.environ:
            del os.environ['K_SERVICE']

def test_pydantic_validation():
    """测试 Pydantic 验证"""
    print("\n🔍 Pydantic 验证测试")
    print("=" * 50)
    
    try:
        from configs.env import AppSettings
        
        # 测试最小配置
        minimal_config = AppSettings(
            PROJECT_ID="test-project",
            REGION="us-central1"
        )
        
        print("✅ 最小配置验证成功")
        print(f"  - PROJECT_ID: {minimal_config.PROJECT_ID}")
        print(f"  - REGION: {minimal_config.REGION}")
        
        # 测试完整配置
        full_config = AppSettings(
            PROJECT_ID="test-project",
            PROJECT_NUMBER="123456789",
            REGION="us-central1",
            DEEPSEEK_API_KEY="test-key"
        )
        
        print("✅ 完整配置验证成功")
        
        return True
        
    except Exception as e:
        print(f"❌ Pydantic 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 Google Cloud 配置测试")
    print("=" * 60)
    
    tests = [
        ("环境检测", test_environment_detection),
        ("配置加载", test_config_loading),
        ("Pydantic 验证", test_pydantic_validation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'=' * 60}")
    print("🎯 测试总结")
    print(f"{'=' * 60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"总体结果: {passed}/{total} 测试通过")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
