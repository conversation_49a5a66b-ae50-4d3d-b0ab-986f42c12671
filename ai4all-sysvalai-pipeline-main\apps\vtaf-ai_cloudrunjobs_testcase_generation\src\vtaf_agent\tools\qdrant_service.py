import json
import logging
import os
import time
# Suppress warnings
import warnings

import google.auth
import google.auth.transport.requests
import google.auth.transport.requests
import jwt
import requests
from google.auth import impersonated_credentials
from google.auth.transport.requests import Request
from langchain_google_vertexai import VertexAIEmbeddings
from langchain_qdrant import QdrantVectorStore
from qdrant_client import QdrantClient, models

warnings.filterwarnings("ignore")

# Initialize logging
logging.basicConfig(level=logging.INFO)

SCOPES = ["https://www.googleapis.com/auth/cloud-platform"]
SERVICE_ACCOUNT_EMAIL = os.environ['SERVICE_ACCOUNT_EMAIL']
COLLECTION_NAME = os.getenv("COLLECTION_NAME")


class QdrantSearchTool:
    def __init__(self, QDRANT_JWT, project_uuid, variant_uuid, euf_feature_uuid):
        # Load environment variables
        required_env_vars = [
            "SERVICES_PROJECT_ID", "QDRANT_CLIENT_URL", "QDRANT_CLIENT_PORT", "QDRANT_IAP_CLIENT_ID",
            "EMBEDDING_MODEL_NAME", "LOCATION",
            "COLLECTION_NAME", "SERVICE_ACCOUNT_EMAIL"
        ]

        for var in required_env_vars:
            if not os.getenv(var):
                logging.error(f"Environment variable {var} is missing.")
                raise EnvironmentError(f"Missing required environment variable: {var}")

        # Assign environment variables to constants
        self.VTAF_AI_PROJECT_ID = project_uuid
        self.VTAF_AI_PROJECT_VAR_ID = variant_uuid
        self.VTAF_AI_EUF_FEA_UID = euf_feature_uuid
        self.PROJECT = os.getenv("SERVICES_PROJECT_ID")
        self.QDRANT_CLIENT_URL = os.getenv("QDRANT_CLIENT_URL")
        self.QDRANT_CLIENT_PORT = os.getenv("QDRANT_CLIENT_PORT")
        self.QDRANT_IAP_CLIENT_ID = os.getenv("QDRANT_IAP_CLIENT_ID")
        self.EMBEDDING_MODEL_NAME = os.getenv("EMBEDDING_MODEL_NAME")
        self.LOCATION = os.getenv("LOCATION")
        self.COLLECTION_NAME = os.getenv("COLLECTION_NAME")
        self.QDRANT_JWT = QDRANT_JWT
        self.EMBEDDING_PARALLELISM = int(os.getenv("EMBEDDING_PARALLELISM", 1))  # Default to 1 if missing
        self.SERVICE_ACCOUNT_EMAIL = SERVICE_ACCOUNT_EMAIL

        # Token cache initialization
        self.cached_token = None
        self.token_expiration_time = None

        # Initialize clients
        self.service_account_credentials = self.get_impersonated_credentials(self.SERVICE_ACCOUNT_EMAIL)
        self.client = QdrantClient(
            url=self.QDRANT_CLIENT_URL,
            port=self.QDRANT_CLIENT_PORT,
            api_key=self.QDRANT_JWT,
            auth_token_provider=self.get_qdrant_token,
            timeout=30,
        )
        self.embedding = VertexAIEmbeddings(
            model_name=self.EMBEDDING_MODEL_NAME,
            project=self.PROJECT,
            location=self.LOCATION,
            request_parallelism=self.EMBEDDING_PARALLELISM,
            credentials=self.service_account_credentials,
        )
        self.vector_store = QdrantVectorStore(
            client=self.client,
            collection_name=self.COLLECTION_NAME,
            embedding=self.embedding
        )
        self.embedding_function = VertexAIEmbeddings(
            model_name=self.EMBEDDING_MODEL_NAME,
            location=self.LOCATION,
            credentials=self.service_account_credentials,
        )

    def get_impersonated_credentials(self, target_principal: str,
                                     lifetime: int = 300) -> google.auth.credentials.Credentials:
        """Get impersonated credentials for service account."""
        try:

            # Use default credentials as source
            source_credentials, _ = google.auth.default(scopes=["https://www.googleapis.com/auth/cloud-platform"])

            # Refresh if necessary
            source_credentials.refresh(Request())

            target_credentials = impersonated_credentials.Credentials(
                source_credentials=source_credentials,
                target_principal=target_principal,
                target_scopes=["https://www.googleapis.com/auth/cloud-platform"],
                lifetime=lifetime,
            )
            return target_credentials
        except Exception as e:
            logging.error(f"Error getting impersonated credentials: {e}")
            raise

    def get_impersonated_id_token(self, target_credentials: google.auth.credentials.Credentials,
                                  target_audience: str) -> str:
        """Retrieve the impersonated ID token."""
        try:
            credentials = impersonated_credentials.IDTokenCredentials(
                target_credentials=target_credentials,
                target_audience=target_audience,
                include_email=True,
            )
            request = google.auth.transport.requests.Request()
            credentials.refresh(request)
            return credentials.token
        except Exception as e:
            logging.error(f"Error fetching ID token: {e}")
            raise

    def get_qdrant_token(self) -> str:
        """Get Qdrant ID token, cache it and refresh only when expired."""
        if self.cached_token is None or time.time() >= self.token_expiration_time:
            try:
                token = self.get_impersonated_id_token(
                    self.service_account_credentials,
                    os.getenv("QDRANT_IAP_CLIENT_ID"),
                )
                self.cached_token = token
                # Set token expiration to 1 hour (or whatever the token lifetime is)
                self.token_expiration_time = time.time() + 3600  # 1 hour
                logging.info("Qdrant ID Token retrieved and cached successfully.")
            except Exception as e:
                logging.error(f"Error getting Qdrant token: {e}")
                raise

        return self.cached_token

    def searching_similar_requirements(self, requirement: str, limit: int, score_threshold: float):
        """Delete existing points in Qdrant based on a list of identifiers."""
        try:
            relevant_docs = []
            if self.VTAF_AI_PROJECT_ID == 'd88f5e15-7ec8-4623-8559-994cc26e8fbc' and self.VTAF_AI_PROJECT_VAR_ID == '7dd83e09-5da5-4b8c-b065-1d283ae5458d':
                metadata_filter = models.Filter(
                    must=[
                        models.FieldCondition(
                            key="metadata.project_uuid", match=models.MatchValue(value=self.VTAF_AI_PROJECT_ID)
                        ), models.FieldCondition(
                            key="metadata.variant_uuid", match=models.MatchValue(value=self.VTAF_AI_PROJECT_VAR_ID)
                        ), models.FieldCondition(
                            key="metadata.euf_feature_uuid", match=models.MatchValue(value=self.VTAF_AI_EUF_FEA_UID)
                        )
                    ]
                )
            else:
                metadata_filter = models.Filter(
                    should=[
                        models.FieldCondition(
                            key="metadata.project_uuid", match=models.MatchValue(value=self.VTAF_AI_PROJECT_ID)
                        ), models.FieldCondition(
                            key="metadata.variant_uuid", match=models.MatchValue(value=self.VTAF_AI_PROJECT_VAR_ID)
                        ), models.FieldCondition(
                            key="metadata.euf_feature_uuid", match=models.MatchValue(value=self.VTAF_AI_EUF_FEA_UID)
                        )
                    ]
                )

            relevant_collection_points = self.vector_store.similarity_search_with_score(
                query=requirement,
                k=limit,
                filter=metadata_filter,
                score_threshold=0.7
            )

            if relevant_collection_points:
                relevant_collection_docs = [
                    {
                        "page_content": doc.page_content,
                        "metadata": doc.metadata,
                        "score": f"{score:3f}"
                    }
                    for doc, score in relevant_collection_points
                ]
                print(relevant_collection_docs)
                relevant_docs.append(
                    relevant_collection_docs
                )
                return relevant_docs
            return []

        except Exception as e:
            logging.error(f"Error Searching similar requirements in bulk with : {requirement} - {e}")
            raise
