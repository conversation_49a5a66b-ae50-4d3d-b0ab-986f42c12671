/*@!Encoding:1252*/
/*autoTest_Diag_API.cin                         */
/*Version 2.0,   Author: <PERSON><PERSON><PERSON>, 2020/03/22 */
/*Status: Released                              */

/*Variables need Configuration */
variables
{
  /*Config - for Diag*/
  char <PERSON>cu<PERSON>argert[64] = "Geely_Gen2_PAS3";
  diagRequest  Geely_Gen2_PAS3.*  pasDiagReqst;        // 0x7A5   0x7DF
  diagResponse Geely_Gen2_PAS3.*  pasDiagRespd;        // 0x7AD
  
  byte maskDTC[12] = {0x9b, 0x58, 0x13,0x9b, 0x57, 0x12,0x9b, 0x57, 0x13,0x9b, 0x58, 0x12};                // masked DTC
  
  /*For Multi Bus Switch */
  char   ecuBus[64]    = "SafetyCANFD4";            // fill ECU network name
  char   castleBus[64] = "Castle5";                 // Fill Castle5 network name
  char   vs6Bus[64]    = "Virtual_VS6";             // VS6 Virtual Bus
  char   ecuBus2[64]   = "ChassisCAN1";            // fill ECU network 2 name, applicable if ECU has 2 channels
  const  ecuBusChn     = 1;                         // ECU Bus is on CAN1
  const  ecuBusChn2    = 2;                         // ECU channel 2
  const  castleBusChn  = 3;                         // Castle Bus is on CAN3
  const  vs6BusChn     = 4;                         // VS6 Bus is on CAN4
  /*End*/
  
  enum  bool {true=1, false=0};
  const cApplicationTimeoutMs = 5000;
  const msgTimeoutMs          = 500;
  const byte maskDTC_flag = 1;
  word          printDiag = 1;                              // to Print Diag Object
  /*End*/
  
  /*CanCom Test - Error Frames*/
  dword errorMsgCnt1;                         // error frame counter for CAN1
  dword errorMsgCnt2;                         // error frame counter for CAN2
  /*End*/

  /*CanCom Test - Sending user defined Msg*/
  msTimer msgTimer1;
  msTimer msgTimer2;
  msTimer msgTimer3;
  message *userMsg1;
  message *userMsg2;
  message *userMsg3;
  dword   msgCnt1, msgCnt2, msgCnt3;        // msg frame counter
  dword   maxCnt1, maxCnt2, maxCnt3;        // msg frames to send
  /*End*/
  
  /*CanCom Test - Background check*/
  dword ChkId_Sig[16];           // check id for signals
  dword ChkId_Sys[16];           // check id for sysvariables
  dword ChkCnt_Sig = 0;          // <= 15, max 16 check ids
  dword ChkCnt_Sys = 0;
  const ChkCnt_Max = 16;
  /*End*/
  
  
  /*Generic - For Report Print*/
  char  gTestIdStr[10];                                   // Test step ID for test report
  word  gTestStepIndex = 0;
  word  gSubStepIndex  = 0;
  char  gResultString[1024];                              // String for temporary test step result outputs
  char  txtRptPath[64] = "Test\\TestReport\\txtReport";   // relateive path for txt report path
  dword txtRptHandle;                                     // txt report handle
  dword txtIdx;                                           // case index in txt report
  /*End*/
  
  /**/
  struct msgComp {
    word  idx;
    dword msgID;
    byte  msgLen;
    word  msgCyle;       // ms, 0-not cycle msg
    word  minCyle;       // ms, min msg cycle,  >20ms 10%, <= 20ms 20%
    word  maxCyle;       // ms, max msg cycle,
    byte  msgCRC;        // 0x1 - msg has CRC, 0x0-msg has no CRC
    
  };
  /**/
  
  /*Config -  for SA unlock */
  byte return_key[3] = {0x00,0x00,0x00};               // return key for unlock
  byte return_keySA61[4] = {0x00,0x00,0x00,0x00};      // return key for unlock
  char sendkey[64];
  char key1[8];
  char key2[8];
  char key3[8];
  char key4[8];
  
  struct all_support_DTC {
    char DtcList[129][8];
    byte DtcChked[129];
    word DtcNumber;
  };
  
  struct all_support_DTC all_fs11_DTC = {
    {  
      "11C800","503448","504444","912B87","929C12","929C14","929C35","929C96","929D12","929D14","929D35","929D96","92BD11","92BD13","939611",
      "939613","939711","939713","93F312","93F314","93F335","93F396","93F412","93F414","93F435","93F496","948511","948513",
      "9B3612","9B3614","9B3635","9B3696","9B3812","9B3814","9B3835","9B3896","9B4012","9B4014","9B4035","9B4096","9B4212",
      "9B4214","9B4235","9B4296","9B4412","9B4414","9B4435","9B4496","9B4612","9B4614","9B4635","9B4696","9B4812","9B4814",
      "9B4835","9B4896","9B5012","9B5014","9B5035","9B5096","9B5687","9B5711","9B5712","9B5713","9B5811","9B5812","9B5813",
      "D00200","D02268","D10687","D10D87","D12A87","D15500","D15600","D16887","D16C87","D34587","D44187","D44787","D44987",
      "D44C87","D45187","D45287","D45487","DC5086","DC5088","DC7A01","DC7A02","DC7A88","E10156","E30055","E30056","E30057",
      "E40057","EE0068","EE0168","EE0368","EE0468","EF0048","EF0148","EF0429","EF0792","EF0992","EF0A46","EF0C46",
      "EF0D04","EF0E4B","EF0F92","EF1092","EF1192","EF124B","EF134B","EF144B","EF154B","EF1629","EF1729","EF1829","EF1996",
      "EF1A96","EF1B96","EF1C96","EF1D96","EF1E96","EF2076","EF2176","EF2276","EF2376","F00362","EF2456"
   },
    {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
      0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
    },
    129
  };
  
  /*Variable for TOE control*/
  msTimer clearBut;                                    // timer to clear button push
  /*End*/
  //NM and Diag 
  msTimer VH6501_Start1;
  msTimer VH6501_Start2;
  msTimer VH6501_Stop1;
  msTimer VH6501_Stop2; 
 word CCPara1[73]={0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0A,0x0B,0x0C,0x0D,0x0E,0x0F,0x10,0x80,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x8A,0x8B,0x8C,0x8D,0x8E,0x8F,0x90,0xA1,0xA2,0xA3,0xA4,0xA5,0xA6,0xA7,0xA8,0xA9,0xAA,0xAB,0xAC,0xAD,0xAE,0xAF,0xB0,0xB1,0xB2,0xB3,0xB4,0xB5,0xB6,0xB7,0xB8,0xB9,0xBA,0xBB,0xBC,0xBD,0xBE,0xBF,0xE0,0xE1,0xE2,0xE3,0xE4,0xE5,0xE6,0xE7,0xE8,0xE9};
  word CCPara8[2]={0x01,0x02};
  word CCPara9[33]={0x01, 0x02,	0x03,	0x04,	0x05,	0x06,	0x07,	0x08,	0x09,	0x0A,	0x0B,	0x0C,	0x0D,0xA1,0xA2,0xA3,0x7F,0x80,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0x8A,0x8B,0x8C,0x8D,0x8E,0x8F};
  word CCPara10[2]={0x01,0x02};
  word CCPara13[6]={0x01,0x02,0x03,0x04,0x80,0x81};
  word CCPara17[73]={0x01, 0x02,	0x03,	0x04,	0x05,	0x06,	0x07,	0x08,	0x09,	0x0A,	0x0B,	0x0C,	0x0D,	0x0E,	0x0F,	0x10,	0x11,	0x12,	0x13,	0x14,	0x15,	0x16,	0x17,	0x18,	0x19,	0x1A,	0x1B,	0x1C,	0x1D,	0x1E,	0x1F,	0x20,	0x21,	0x22,	0x23,	0x24,	0x25,	0x26,	0x27,0x80,	0x81,	0x82,	0x83,	0x84,	0x85,	0x86,	0x87,	0x88,	0x89,	0x8A,	0x8B,	0x8C,0x8D,0x8E,0x8F,0x90,0x91,0x92,0x93,0x94,0x95,0x96,0x97,0x98,0x99,0x9A,0x9B,0x9C,0x9D,0x9E,0x9F,0xA0,0xFD};
  word CCPara18[102]={0x28,	0x29,	0x2A,	0x2B,	0x2C,	0x2D,	0x2E,	0x2F,	0x30,	0x31,	0x32,	0x33,	0x34,	0x35,	0x36,	0x37,	0x38,	0x39,	0x3A,	0x3B,	0x3C,	0x3D,	0x3E,	0x3F,	0x40,	0x41,	0x42,	0x43,	0x44,	0x45,	0x46,	0x47,	0x48,	0x49,	0x4A,	0x4B,	0x4C,	0x4D,	0x4E,	0x4F,	0x50,	0x51,	0x52,	0x53,	0x54,	0x55,	0x56,	0x57,	0x58,	0x59,	0x5A,	0x5B,	0x5C,	0x5D,	0x5E,	0x5F,	0x60,	0x61,	0x62,	0x63,	0x64,	0x65,	0x66,	0x67,	0x68,	0x69,	0x6A,	0x6B,	0x6C,	0x6D,	0x6E,	0x6F,	0x70,	0x71,	0x72,	0x73,	0x74,	0x75,	0x76,	0x77,	0x78,	0x79,	0x7A,	0x7B,	0x7C,	0x7D,	0x7E,	0x7F,	0x80,	0x81,	0x82,	0x83,	0x84,	0x85,	0x86,	0x87,	0x88,	0x89,	0x8A,	0x8B,	0x8C,	0x90};
  word CCPara23[9]={0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09};
  word CCPara53[2]={0x01,0x02};
  word CCPara58[5]={0x01,0x02,0x03,0x80,0x81};
  word CCPara77[20]={0x01,0x02,0x03,0x04,0x05,0x80,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0x8A,0x8B,0x8C,0x8D,0x8E};
  word CCPara100[4]={0x01,0x02,0x03,0x80};
  word CCPara139[5]={0x01,0x02,0x03,0x04,0x80};
  word CCPara140[2]={0x01,0x02};
  word CCPara142[16]={0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0A,0x80,0x81,0x82,0x83,0x84,0x85};
  word CCPara146[10]={0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0A};
  word CCPara152[6]={0x01,0x02,0x03,0x04,0x80,0x81};
  word CCPara153[4]={0x01,0x02,0x03,0x04};
  word CCPara154[25]={0x01,	0x02,	0x03,	0x04,	0x05,	0x06,	0x07,	0x08,	0x09,	0x0A,	0x0B,	0x0C,0x0D,	0x0E,	0x0F,0x10,	0x11,	0x12,0x80,	0x81,	0x82,	0x83,	0x84,	0x85,0x86};
  word CCPara156[7]={0x01,0x02,0x3,0x80,0x81,0x82,0x83};
  word CCPara157[9]={0x01,0x02,0x3,0x4,0x80,0x81,0x82,0x83,0x84};
  word CCPara161[35]={0x01, 0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0A,0x0B,0x0C,0x0D,0x0E,0x0F,0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,0x18,0x19,0x1A,0x1B,0x1C,0x1D,0x1E,0x1F,0x20,0x80,0x81,0x82};
  word CCPara177[10]={0x01,0x02,0x03,0x04,0x05,0x6,0x80,0x81,0x83,0x84};
  word CCPara184[8]={0x01,0x02,0x03,0x04,0x80,0x81,0x82,0x83};
  word CCPara197[42]={0x01,	0x02,	0x03,	0x04,	0x05,	0x06,	0x07,	0x08,	0x09,	0x0A,	0x0B,	0x0C,	0x0D,	0x0E,	0x0F,	0x10,	0x11,	0x12,	0x13,	0x14,	0x15,	0x16,	0x17,	0x18,	0x19,	0x1A,	0x1B,	0x1C,	0x1D,	0x1E,	0x1F,	0x20,	0x21,	0x22,	0x23,	0x24,	0x25,0x80,0x81,0x82,0x83,0x84};
  word CCPara211[8]={0x01,0x02,0x03,0x04,0x05,0x06,0x80,0x81};
  word CCPara220[18]={0x01,	0x02,	0x03,	0x04,	0x05,	0x06,	0x07,	0x08,	0x09,	0x0A,	0x0B,	0x0C,	0x0D,	0x0E,	0x0F,0x80,0x81,0x82};
  word CCPara316[11]={0x01,0x02,0x03,0x04,0x05,0x06,0x80,0x81,0x82,0x83,0x84};
  word CCPara401[3]={0x01,0x02,0x03};
  word CCPara409[4]={0x01,0x02,0x03,0x04};
  word CCPara479[3]={0x01,0x02,0x80};
  word CCPara483[4]={0x01,0x02,0x03,0x04};
  word CCPara484[2]={0x01,0x02};
  word CCPara494[12]={0x01,0x02,0x03,0x04,0x80,0x81,0x82,0x83,0x84,0x85,0x86,0x87};
  word CCPara495[2]={0x01,0x02};
  word CCPara500[4]={0x01,0x02,0x80,0x81};
  word CCPara540[2]={0x01,0x02};
  word CCPara565[3]={0x01,0x02,0x03};
  word CCPara614[2]={0x01,0x02};
  word CCPara618[2]={0x01,0x02};
  word CCPara628[3]={0x01,0x02,0x03};
  word CCPara639[7]={0x01,0x02,0x03,0x04,0x05,0x06,0x07};
  word CCPara640[7]={0x01,0x02,0x03,0x04,0x05,0x06,0x07};
  word CCPara648[4]={0x01,0x02,0x03,0x04};

}

/// <tstf_DTC>
/**/
testfunction tstf_Diag_Chk_allReportedDTC()
{
  func_Diag_Chk_allReportedDTC();
}


int func_Diag_Chk_allReportedDTC()
{
  word i,j,k;
  long Ret;
//  long index;
//  long cmp;
//  long tempDTC;
  word lenObtain;
  byte respObtain[519];
//  char tempObtain[20];
//  char tempstr1[20];
//  char tempstr2[20];
//  char tempstr3[20];
  
  char repDtcString[129][16];
  char temp[8];
  long chkFlag;
  
  setTestId(1);
  if (1 != testWaitForDiagResponse(pasDiagReqst, 2000) ) 
  {          
   snprintf(gResultString, elcount(gResultString), "Valid response missing or received too late!");
   testStepFail(gTestIdStr, gResultString);
   return -1;
  }
  else
  {
    //testWaitForTimeout(1500);
    for (i=0; i< all_fs11_DTC.DtcNumber; i++)      // clear checked DTC flag
    {
      all_fs11_DTC.DtcChked[i] = 0x0;
    }
    
    for (i=0; i< 124; i++)                        // clear Dtc string
    {
      repDtcString[i][0] = '\0';
    }
    
    
   testStepPass(gTestIdStr, "Response received successfully.");
   if(printDiag != 0) TestReportWriteDiagResponse(pasDiagReqst);                     // write response to report
   
   Ret =diagGetLastResponseCode (pasDiagReqst);
   
   incTestStepId();
   if(Ret == -1)     // positive response
   {     
     testStepPass(gTestIdStr,"Positive Response is Received.");
     
     diagGetLastResponse(pasDiagRespd);
     lenObtain = diagGetPrimitiveData(pasDiagRespd,respObtain,elCount(respObtain));
    
     incTestStepId();
    
      for (i=3; i<lenObtain; i++)          
      {                                                    //3 4 5 6 , 7 8 9 10, 11 12 13 14, 
        j= (i-3)/4;
        snprintf(temp, elCount(temp), "%02X", respObtain[i]);
        strncat(repDtcString[j], temp, elCount(repDtcString));
      }
      
      
      //testStepPass(gTestIdStr,"");
    
      if( j != all_fs11_DTC.DtcNumber-1)
      {
        testStepFail(gTestIdStr, "Reveived DTC number [%d] is not equal to [%d].", j+1, all_fs11_DTC.DtcNumber);
      }
   
      for (i=0; i< all_fs11_DTC.DtcNumber; i++)
      {
        for(k=0;k<=j;k++)
        {
          //chkFlag = str_match_regex(repDtcString[k], all_fs11_DTC.DtcList[i]);       // 11 22 33 44 55 66
          chkFlag = str_replace(repDtcString[k], all_fs11_DTC.DtcList[i], all_fs11_DTC.DtcList[i]);
          if(chkFlag == 0x1)
          {
            all_fs11_DTC.DtcChked[i] = 0x1;
            break;
          }
        }
      }
      
      for(k=0;k<=j;k++)
      {
        setTestId(k);
        testStepWarning(gTestIdStr,"%s\n",repDtcString[k]);
      }
    
      for (i=0; i< all_fs11_DTC.DtcNumber; i++)
      {
        if( all_fs11_DTC.DtcChked[i] == 0x1)
        {
          testStepPass(gTestIdStr, "DTC [0x%s] is Detected.", all_fs11_DTC.DtcList[i]);
        }
        else if( all_fs11_DTC.DtcChked[i] == 0x0)
        {
          testStepFail(gTestIdStr, "DTC [0x%s] is not Detected.", all_fs11_DTC.DtcList[i]);
        }
        else{}
      }
      
  
    }
    else
    {
      testStepFail(gTestIdStr, "Negative response is received.");
    }
  }
  return 1;
}



// Set and increment test step ID for test report
updateTestIdStr()
{
  if(gSubStepIndex ==0 ){
    snprintf(gTestIdStr, elcount(gTestIdStr), "%d", gTestStepIndex);
  }
  else{
    snprintf(gTestIdStr, elcount(gTestIdStr), "%d.%d", gTestStepIndex, gSubStepIndex);
  }
}

setTestId(word tsIndex)
{
  gTestStepIndex = tsIndex;
  gSubStepIndex = 0;
  updateTestIdStr();
}

incTestStepId()
{
  gTestStepIndex++;
  gSubStepIndex = 0;
  updateTestIdStr();  
}

incSubStepId()
{
  gSubStepIndex++;
  updateTestIdStr();  
}

/*convert string to data bytes, and return bytes number*/
long stringToBytes(char diagString[], byte diagBytes[] )
{
  word  ret;
  char  strCopy[8];
  dword strLength;
  word  byteLength;
  dword singleValue;
  word  i, j;
  
  str_replace(diagString," ","");           // remove blank space
  
  strLength = strlen(diagString);           // get diag command char s
  
  if(strLength%2 !=0){                      // wrong diag string
    return -1;
  }
  if(strLength == 0){                       // or empty string
    return 0;
  }
  
  for(i=j=0;i<strLength;i+=2)
  {
    strncpy (strCopy, "0x", elcount(strCopy));
    substr_cpy_off(strCopy, 2, diagString, i, 2, elcount(strCopy)); 
    
    strtoul(strCopy, singleValue);           //str to int64
    
    diagBytes[j] = singleValue;
    //write("%d. %s-%x-%x",i, strCopy,diagBytes[j], singleValue); 
    j++;
  }
  
  ret = strLength>>1;
  
  return ret;
}

/// <tstf_Security_Unlock>
/* To UnlockSA Levelx by physical addressing*/
/* Para1: level{0x1, 0x3, 0x11, 0x49}       */
/* Para2: addr{0-phy, 1-func addressing}    */
testfunction tstf_Diag_UnlockSA_Lx(byte level, byte addr)
{
  func_Diag_Request_Seed(level, addr);            
  func_Diag_AwaitSeed_CalcKey(level);
  func_Diag_SendKey(level, addr);
  func_Diag_Test_SA(level);
}

/// <tstf_Security_Unlock>
/* To UnlockSA Level1 by physical addressing*/
testfunction tstf_Diag_UnlockSA_L1()
{
  func_Diag_Request_Seed(0x1,0);            
  func_Diag_AwaitSeed_CalcKey(0x1);
  func_Diag_SendKey(0x1,0);
  func_Diag_Test_SA(0x1);
}

/// <tstf_Security_Unlock>
/* To UnlockSA Level3 by physical addressing*/
testfunction tstf_Diag_UnlockSA_L3()
{
  func_Diag_Request_Seed(0x5,0);            
  func_Diag_AwaitSeed_CalcKey(0x5);
  func_Diag_SendKey(0x5,0);
  func_Diag_Test_SA(0x5);
}

/* To UnlockSA Level11 by physical addressing*/
testfunction tstf_Diag_UnlockSA_L19()
{
  func_Diag_Request_Seed(0x19,0);            
  func_Diag_AwaitSeed_CalcKey(0x19);
  func_Diag_SendKey(0x19,0);
  func_Diag_Test_SA(0x19);
}

/// <tstf_Security_Unlock>
/* To UnlockSA Level49 by physical addressing*/
testfunction tstf_Diag_UnlockSA_L49()
{
  func_Diag_Request_Seed(0x61,0);            
  func_Diag_AwaitSeed_CalcKey(0x61);
  func_Diag_SendKey(0x61,0);
  func_Diag_Test_SA(0x61);
}

/// <tstf_Security_Unlock>
/*      To request Seed       */
/* Para1: addr{0-phy, 1-func} */
testfunction tstf_Diag_Request_Seed(byte level, byte addr)
{
  func_Diag_Request_Seed(level, addr);
}

int func_Diag_Request_Seed(byte level, byte addr)
{
  int ret;
  
  if(level == 0x1){
    ret = func_Diag_Send("27 01", addr);                          // send seed rquest
  }
  else if(level == 0x5){
    ret = func_Diag_Send("27 05", addr);                          // send seed rquest
  }
  else if(level == 0x19){
    ret = func_Diag_Send("27 19", addr);                          // send seed rquest
  }
  else if(level == 0x61){
    ret = func_Diag_Send("27 61", addr);                          // send seed rquest
  }
  else{return -1;}
  
  return ret;
}

/// <tstf_Security_Unlock>
/*To Await seed and calculate key */
testfunction tstf_Diag_AwaitSeed_CalcKey(byte level)
{
  func_Diag_AwaitSeed_CalcKey(level);
}

int func_Diag_AwaitSeed_CalcKey(byte level)
{
  
  word lenObtain;
  byte respObtain[32];
  byte fI1,fI2,fI3,fI4,fI5;
  
//   @sysvar::SA::FIN6 = 0xFF ;
//   @sysvar::SA::FIN7 = 0xFF ;
//   @sysvar::SA::FIN8 = 0xFF ;
//   @sysvar::SA::FIN9 = 0xFF ;
//   @sysvar::SA::FIN10 = 0xFF ;
  
  /*Step1, Wait and Get Seed */
  setTestId(1);
  if (1 != testWaitForDiagResponse(pasDiagReqst, cApplicationTimeoutMs) ) {          
    snprintf(gResultString, elcount(gResultString), "Valid response missing or received too late!");
    testStepFail(gTestIdStr, gResultString);
    return -1;
  }
  else{
    testStepPass(gTestIdStr, "Response received successfully.");
    TestReportWriteDiagResponse(pasDiagReqst);                     // write response to report
  
    diagGetLastResponse(pasDiagRespd);
    
    lenObtain = diagGetPrimitiveData(pasDiagRespd,respObtain,elCount(respObtain));
    
    incTestStepId();
    if(respObtain[0]!=0x67)               // to replace lenObtain != 6
    {
      testStepFail(gTestIdStr,"Negative response Received. Response Length is [ %d ].",lenObtain);
      return -1;
    }
    else
    {
      if(level == 0x1){
        fI1 = sysGetVariableInt(sysvar::SA::FIN11);
        fI2 = sysGetVariableInt(sysvar::SA::FIN12);
        fI3 = sysGetVariableInt(sysvar::SA::FIN13);
        fI4 = sysGetVariableInt(sysvar::SA::FIN14);
        fI5 = sysGetVariableInt(sysvar::SA::FIN15);
        G2_SA_Level1_Calculate(respObtain[2],respObtain[3],respObtain[4],fI1, fI2, fI3, fI4, fI5);        // get seed and cal key
      }
      else if(level == 0x5){
        fI1 = sysGetVariableInt(sysvar::SA::FIN1);
        fI2 = sysGetVariableInt(sysvar::SA::FIN2);
        fI3 = sysGetVariableInt(sysvar::SA::FIN3);
        fI4 = sysGetVariableInt(sysvar::SA::FIN4);
        fI5 = sysGetVariableInt(sysvar::SA::FIN5);
        G2_SA_Level5_Calculate(respObtain[2],respObtain[3],respObtain[4],fI1, fI2, fI3, fI4, fI5);
      }
      else if(level == 0x19){
        fI1 = sysGetVariableInt(sysvar::SA::FIN6);
        fI2 = sysGetVariableInt(sysvar::SA::FIN7);
        fI3 = sysGetVariableInt(sysvar::SA::FIN8);
        fI4 = sysGetVariableInt(sysvar::SA::FIN9);
        fI5 = sysGetVariableInt(sysvar::SA::FIN10);
        G2_SA_Level5_Calculate(respObtain[2],respObtain[3],respObtain[4],fI1, fI2, fI3, fI4, fI5);
      }
      else if(level == 0x61){
        fI1 = sysGetVariableInt(sysvar::SA::FIN16);
        fI2 = sysGetVariableInt(sysvar::SA::FIN17);
        fI3 = sysGetVariableInt(sysvar::SA::FIN18);
        fI4 = sysGetVariableInt(sysvar::SA::FIN19);
        G2_SA_Level61_Calculate(respObtain[2],respObtain[3],respObtain[4],respObtain[5]);
      }
      else{}
      teststeppass(gTestIdStr, "Seed is Received.\nCalculated Key: [%02X %02X %02X]", return_key[0],return_key[1],return_key[2]);
    }
  }
  
  return 1;
} 
testfunction tstf_Diag_set_Key()
{
  func_Diag_set_Key();
}

int func_Diag_set_Key()
{
  @sysvar::SA::FIN6 = 0x55 ;
  @sysvar::SA::FIN7 = 0x55 ;
  @sysvar::SA::FIN8 = 0x55 ;
  @sysvar::SA::FIN9 = 0x55 ;
  @sysvar::SA::FIN10 = 0x55 ;
  
  return 1;
}

int func_Diag_set_DefaultKey()
{
  @sysvar::SA::FIN6 = 0xFF ;
  @sysvar::SA::FIN7 = 0xFF ;
  @sysvar::SA::FIN8 = 0xFF ;
  @sysvar::SA::FIN9 = 0xFF ;
  @sysvar::SA::FIN10 = 0xFF ;
  
  return 1;
}

/// <tstf_Security_Unlock>
/* Seed has been awaited, this function */
/* is to get the seed and calculate key */
/* Para1: level{ 0x1, 0x3, 0x11, 0x49 } */
testfunction tstf_Diag_StoreSeed_CalcKey(byte level)
{
  func_Diag_StoreSeed_CalcKey(level);
}

int func_Diag_StoreSeed_CalcKey(byte level)
{
  word lenObtain;
  byte respObtain[32];
  byte fI1,fI2,fI3,fI4,fI5;
  
  setTestId(1);
  
  diagGetLastResponse(pasDiagRespd);
  
  lenObtain = diagGetPrimitiveData(pasDiagRespd,respObtain,elCount(respObtain));
  
  if(respObtain[0]!=0x67)               // to replace lenObtain != 6
  {
    testStepFail(gTestIdStr,"Negative response detected. Response Length is [ %d ].",lenObtain);
    return -1;
  }
  else
  {
    if(level == 0x1){
        fI1 = sysGetVariableInt(sysvar::SA::FIN11);
        fI2 = sysGetVariableInt(sysvar::SA::FIN12);
        fI3 = sysGetVariableInt(sysvar::SA::FIN13);
        fI4 = sysGetVariableInt(sysvar::SA::FIN14);
        fI5 = sysGetVariableInt(sysvar::SA::FIN15);
        G2_SA_Level1_Calculate(respObtain[2],respObtain[3],respObtain[4],fI1, fI2, fI3, fI4, fI5);        // get seed and cal key
        teststeppass(gTestIdStr, "Seed is Received.\nCalculated Key: [%02X %02X %02X]", return_key[0],return_key[1],return_key[2]);
      }
      else if(level == 0x5){
        fI1 = sysGetVariableInt(sysvar::SA::FIN1);
        fI2 = sysGetVariableInt(sysvar::SA::FIN2);
        fI3 = sysGetVariableInt(sysvar::SA::FIN3);
        fI4 = sysGetVariableInt(sysvar::SA::FIN4);
        fI5 = sysGetVariableInt(sysvar::SA::FIN5);
        G2_SA_Level5_Calculate(respObtain[2],respObtain[3],respObtain[4],fI1, fI2, fI3, fI4, fI5);
        teststeppass(gTestIdStr, "Seed is Received.\nCalculated Key: [%02X %02X %02X]", return_key[0],return_key[1],return_key[2]);
      }
      else if(level == 0x19){
        fI1 = sysGetVariableInt(sysvar::SA::FIN6);
        fI2 = sysGetVariableInt(sysvar::SA::FIN7);
        fI3 = sysGetVariableInt(sysvar::SA::FIN8);
        fI4 = sysGetVariableInt(sysvar::SA::FIN9);
        fI5 = sysGetVariableInt(sysvar::SA::FIN10);
        G2_SA_Level5_Calculate(respObtain[2],respObtain[3],respObtain[4],fI1, fI2, fI3, fI4, fI5);
        teststeppass(gTestIdStr, "Seed is Received.\nCalculated Key: [%02X %02X %02X]", return_key[0],return_key[1],return_key[2]);
      }
      else if(level == 0x61){
        fI1 = sysGetVariableInt(sysvar::SA::FIN16);
        fI2 = sysGetVariableInt(sysvar::SA::FIN17);
        fI3 = sysGetVariableInt(sysvar::SA::FIN18);
        fI4 = sysGetVariableInt(sysvar::SA::FIN19);
        G2_SA_Level61_Calculate(respObtain[2],respObtain[3],respObtain[4],respObtain[5]);
        teststeppass(gTestIdStr, "Seed is Received.\nCalculated Key: [%02X %02X %02X %02X]", return_keySA61[0],return_keySA61[1],return_keySA61[2],return_keySA61[3]);
      }
    
  }

  return 1;
}  

/// <tstf_Security_Unlock>
/* To Send Calculated SA Key        */
/* Para1:level{0x1, 0x3, 0x11, 0x49}*/
/* Para2: addr{0-phy, 1-func}       */
testfunction tstf_Diag_SendKey(byte level, byte addr)
{
  func_Diag_SendKey(level, addr);
}

  testfunction StopCANoeSimulationAPP()
{
  @IL::Klemme30 = 0;
  @sysvar::NM_Time::MsgSendEnable = 0;
//  testWaitForTimeout(5000);
}

testfunction StartCANoeSimulationAPP()
{
  @IL::Klemme30 = 1;
  @sysvar::NM_Time::MsgSendEnable = 1;
//  testWaitForTimeout(2000);
}

int func_Diag_SendKey(byte level, byte addr)
{
  int ret;
  char temp[8];
  
  sendkey[0] = '\0';
  
  if(level == 0x1){
    strncpy(sendkey, "27 02 ",elCount(sendkey));              // send key
  }
  else if(level == 0x5){
    strncpy(sendkey, "27 06 ",elCount(sendkey));              // send key
  }
  else if(level == 0x19){
    strncpy(sendkey, "27 1A ",elCount(sendkey));              // send key
  }
  else if(level == 0x61){
    strncpy(sendkey, "27 62 ",elCount(sendkey));              // send key
  }
  else{}
  
  if(level == 0x61){
    snprintf(key1, elcount(key1) ,"%02X", return_keySA61[0]);
    snprintf(key2, elcount(key2) ,"%02X", return_keySA61[1]);
    snprintf(key3, elcount(key3) ,"%02X", return_keySA61[2]);
    snprintf(key4, elcount(key3) ,"%02X", return_keySA61[3]);

    strncat(sendkey, key1, elCount(sendkey));   
    strncat(sendkey, " ", elCount(sendkey));
    strncat(sendkey, key2, elCount(sendkey));
    strncat(sendkey, " ", elCount(sendkey));
    strncat(sendkey, key3, elCount(sendkey));
    strncat(sendkey, " ", elCount(sendkey));
    strncat(sendkey, key4, elCount(sendkey));
    
  }
  else{
    snprintf(key1, elcount(key1) ,"%02X", return_key[0]);
    snprintf(key2, elcount(key2) ,"%02X", return_key[1]);
    snprintf(key3, elcount(key3) ,"%02X", return_key[2]);

    strncat(sendkey, key1, elCount(sendkey));   
    strncat(sendkey, " ", elCount(sendkey));
    strncat(sendkey, key2, elCount(sendkey));
    strncat(sendkey, " ", elCount(sendkey));
    strncat(sendkey, key3, elCount(sendkey));
  }
  
      
  ret = func_Diag_Send(sendkey,addr);
  
  return ret;
}


/*To prepare sending key, and store in sendkey*/
/* Para1:level{0x1, 0x3, 0x11, 0x49}          */
/* Para2: addr{0-phy, 1-func}                 */
testfunction tstf_Diag_Preapare_Key(byte level, byte addr)
{
  func_Diag_Preapare_Key(level, addr);
}

int func_Diag_Preapare_Key(byte level, byte addr)
{
  //int ret;
  char temp[8];
  
  sendkey[0] = '\0';
  
  if(level == 0x1){
    strncpy(sendkey, "27 02 ",elCount(sendkey));              // send key
  }
  else if(level == 0x5){
    strncpy(sendkey, "27 06 ",elCount(sendkey));              // send key
  }
  else if(level == 0x19){
    strncpy(sendkey, "27 1A ",elCount(sendkey));              // send key
  }
  else if(level == 0x61){
    strncpy(sendkey, "27 62 ",elCount(sendkey));              // send key
  }
  else{}
  
  if(level == 0x61){
    snprintf(key1, elcount(key1) ,"%02X", return_keySA61[0]);
    snprintf(key2, elcount(key2) ,"%02X", return_keySA61[1]);
    snprintf(key3, elcount(key3) ,"%02X", return_keySA61[2]);
    snprintf(key4, elcount(key3) ,"%02X", return_keySA61[3]);

    strncat(sendkey, key1, elCount(sendkey));   
    strncat(sendkey, " ", elCount(sendkey));
    strncat(sendkey, key2, elCount(sendkey));
    strncat(sendkey, " ", elCount(sendkey));
    strncat(sendkey, key3, elCount(sendkey));
    strncat(sendkey, " ", elCount(sendkey));
    strncat(sendkey, key4, elCount(sendkey));
    
  }
  else{
    snprintf(key1, elcount(key1) ,"%02X", return_key[0]);
    snprintf(key2, elcount(key2) ,"%02X", return_key[1]);
    snprintf(key3, elcount(key3) ,"%02X", return_key[2]);

    strncat(sendkey, key1, elCount(sendkey));   
    strncat(sendkey, " ", elCount(sendkey));
    strncat(sendkey, key2, elCount(sendkey));
    strncat(sendkey, " ", elCount(sendkey));
    strncat(sendkey, key3, elCount(sendkey));
  }
    
  setTestId(1);
  testStepPass(gTestIdStr, "Key sending is prepared (not send out) and stored in sendkey = [%s]", sendkey);
  //ret = func_Diag_Send(sendkey,addr);
  
  return 1;
}

/// <tstf_Security_Unlock>
/* Check SA Response  */
testfunction tstf_Diag_Test_SA(byte level)
{
  func_Diag_Test_SA(level);
}
  
int func_Diag_Test_SA(byte level)
{
  int ret;
  char failTiming[128];
  
  if(level == 0x1){
    ret = func_Diag_Test("67 02");                          // check SA response
//    if(ret=-1)
//    {
//      snprintf(failTiming, elCount(failTiming), "timing: %4f", getLocalTime());
//      func_txtRpt_Append_CaseResult("67 02 unlock fail",failTiming,"","","");
//    }
  }
  else if(level == 0x5){
    ret = func_Diag_Test("67 06");                          // check SA response
  }
  else if(level == 0x19){
    ret = func_Diag_Test("67 1A");                          // check SA response
  }
  else if(level == 0x61){
    ret = func_Diag_Test("67 62");                          // check SA response
  }
  else{return -1;}
  
  return ret;
}

void G2_SA_Level1_Calculate(byte ps1, byte ps2, byte ps3, byte pInput1, byte pInput2, byte pInput3, byte pInput4, byte pInput5)
{
    byte ChallengeBit[8];
    byte Fixed_Value[5]         = {0xFF,0xFF,0xFF,0xFF,0xFF};
    dword uLoad_Initial_Value   = 0x00C541A9;                 /* Load Initial Value array */
    dword uPositionA            = 0x00000000;
    dword uPositionB			      = 0x00000000;
    dword uPositionC            = 0x00000000;
    int ChallengeData           = 0x00;
    int uB24                    = 0x00;
    int u8Index                 = 0x00;
    
    /* Step#1:Load Challenge Bits */
    ChallengeBit[0] = ps1;
    ChallengeBit[1] = ps2;
    ChallengeBit[2] = ps3;
    
    ChallengeBit[3] = pInput1;
    ChallengeBit[4] = pInput2;
    ChallengeBit[5] = pInput3;
    ChallengeBit[6] = pInput4;
    ChallengeBit[7] = pInput5;
   
    /*
    ChallengeBit[3] = 0x0F;
    ChallengeBit[4] = 0x7F;
    ChallengeBit[5] = 0x13;
    ChallengeBit[6] = 0xBE;
    ChallengeBit[7] = 0x3D;
    */
    
    /* Step#2:Load Initial Value */
    uPositionA = uLoad_Initial_Value;

	  /* Step#3:Xor calculate */
	  for(u8Index = 0x00; u8Index < 64; u8Index++)
	  {
       ChallengeData = ChallengeBit[u8Index/0x08];

		   uB24 = ((ChallengeData >> (u8Index%0x08)) & 0x01) ^ (uPositionA & 0x00000001);
		 
     	 uPositionB = (uPositionA >> 0x01) & 0x00FFFFFF ;     
		 
		   uPositionC = (uPositionB & 0x006F6FD7)                            /* clear C24 C21 C16 C13 C6 C5 bits to 0 */
		 	             | (uB24 << 23)                                        /* or C24 bit */
		 	             | ((uB24 ^ ((uPositionB >> 20) & 0x00000001)) << 20)  /* or C21 bit */
		 	             | ((uB24 ^ ((uPositionB >> 15) & 0x00000001)) << 15)  /* or C16 bit */
		 	             | ((uB24 ^ ((uPositionB >> 12) & 0x00000001)) << 12)  /* or C13 bit */
		 	             | ((uB24 ^ ((uPositionB >> 5) & 0x00000001))  << 5)   /* or C6 bit */
		 	             | ((uB24 ^ ((uPositionB >> 3) & 0x00000001))  << 3);  /* or C4 bit */
    
		   uPositionA = uPositionC;
//       write("[u8Index %d]uPositionC is 0x%2x",u8Index, uPositionC);
	  }
	
    /* Get Key value */
	  return_key[0] = (int)(uPositionC >> 4);                                                 /* C12 C11 C10 C9 C8 C7 C6 C5 */
	  return_key[1] = (((int)(uPositionC >> 8)) & 0xF0) | (((int)(uPositionC >> 20)) & 0x0F); /* C16 C15 C14 C13 C24 C23 C22 C21 */
	  return_key[2] = (((int)(uPositionC << 4)) & 0xF0) | (((int)(uPositionC >> 16)) & 0x0F); /* C4 C3 C2 C1 C20 C19 C18 C17 */
    
//    write("returnKey[0] is 0x%2x, returnKey[1] is 0x%2x, returnKey[2] is 0x%2x",SA01_Key[0], SA01_Key[1], SA01_Key[2]);
}

void G2_SA_Level5_Calculate(byte s1, byte s2, byte s3, byte Input1, byte Input2, byte Input3, byte Input4, byte Input5)
{
 int i = 0;
 int j = 0;
 int index = 0;
// byte F_byte[8]={0x00,0x00,0x00,0x41, 0xaa, 0x42, 0xbb, 0x43};//E2U
// byte F_byte[8]={0x00,0x00,0x00,0x1a, 0x2b, 0x3c, 0x4d, 0x5e};
// byte F_byte[8]={0x00,0x00,0x00,0xFF, 0xFF, 0xFF, 0xFF, 0xFF};//E3
 byte F_byte[8]={0x00,0x00,0x00,0x00, 0x00, 0x00, 0x00, 0x00};//E3U
 int CB_bit[64];
 byte A_byte[3]={0xc5, 0x41, 0xa9};
 int A_bit[24];
 int B_bit[24];
 int C_bit[24]; 
 int ret;
 
 F_byte[0]=s1;
 F_byte[1]=s2;
 F_byte[2]=s3;
 F_byte[3]=Input1;
 F_byte[4]=Input2;
 F_byte[5]=Input3;
 F_byte[6]=Input4;
 F_byte[7]=Input5;
 
 for(i=0;i<8;i++)
 {
   for(j=0;j<8;j++)
   {
     index = i*8+j;
     ret = ((F_byte[i]>>j)&0x01);
     CB_bit[index]=ret;
   }
 }
 
 for(i=2;i>=0;i--)
 {
   for(j=0;j<8;j++)
   {
     switch(i)
     {
       case 0:
         index = 2*8+j;
         break;
       case 2:
         index = j;
         break;
       default:
         index = i*8+j;
         break;
     }
     ret = (A_byte[i]>>j)&0x01; 
     A_bit[index]=ret;
   }
 }
 
 for(j=0;j<64;j++)
 {
   for(i=0;i<24;i++)
   {
     if(i==23)
     {
       B_bit[23]=A_bit[0]^CB_bit[j];
     }
     else
     {
       B_bit[i]= A_bit[i+1];        
     }
   }
  
   for(i=0;i<24;i++)
   {
     switch(i)
     {
       case 3:
       case 5:
       case 12:
       case 15:
       case 20:
         C_bit[i]=B_bit[23]^B_bit[i];
         break; 
       default:
         C_bit[i]=B_bit[i]; 
         break;
     }
     A_bit[i]=C_bit[i];
   }
 }  
 
 return_key[0] = C_bit[11]*128+C_bit[10]*64+C_bit[9]*32+C_bit[8]*16+C_bit[7]*8+C_bit[6]*4+C_bit[5]*2+C_bit[4];
 return_key[1] = C_bit[15]*128+C_bit[14]*64+C_bit[13]*32+C_bit[12]*16+C_bit[23]*8+C_bit[22]*4+C_bit[21]*2+C_bit[20];
 return_key[2] = C_bit[3]*128+C_bit[2]*64+C_bit[1]*32+C_bit[0]*16+C_bit[19]*8+C_bit[18]*4+C_bit[17]*2+C_bit[16];
 write("security_key is 0x%x 0x%x 0x%x", return_key[0], return_key[1], return_key[2]); 
}

void G2_SA_Level61_Calculate(byte s1, byte s2, byte s3, byte s4)
{
  byte seedArray[4] = {0x00,0x00,0x00,0x00};/* seed array ,ECU send to tester value*/
  byte cal_Data[4]   = {0x00,0x00,0x00,0x00};/*temp array is used to calculation key*/
  byte xorArray[4]  = {0xf1,0xa5,0x63,0x2b}; /*Xor array is used to calculation key */
  int i = 0;
  
  seedArray[0]=s1;
  seedArray[1]=s2;
  seedArray[2]=s3;
  seedArray[3]=s4;
  
  for(i=0;i<4;i++)
  {
    cal_Data[i] = seedArray[i] ^ xorArray[i];
    teststeppass("Data61", "Seed is %x, calculated data is %x", seedArray[i], cal_Data[i]);
  }
  /*calculate second step */
	return_keySA61[0] = seedArray[3] ^ cal_Data[0];
	return_keySA61[1] = seedArray[2] ^ cal_Data[1];
	return_keySA61[2] = seedArray[1] ^ cal_Data[2];
	return_keySA61[3] = seedArray[0] ^ cal_Data[3];
  teststeppass("Key61", "return key is %x %x %x %x", return_keySA61[0],return_keySA61[1],return_keySA61[2],return_keySA61[3]);
}

/// <tstf_Diag_Basic>
/* Send Diag Request with functional Addressing */
/* Maximum bytes is 5000                        */
/* Para1: diagString {such as "10 03"}          */
testfunction tstf_Diag_Send_Functional(char diagString[])
{
  func_Diag_Send(diagString,1);
}

/// <tstf_Diag_Basic>
/* Send Diag Request with Physical Addressing   */ 
/* Maximum bytes is 5000                        */
/* Para1: diagString {such as "10 03"}          */
/* Para2: addressing {0-physical, 1-functional} */
testfunction tstf_Diag_Send(char diagString[])
{
  func_Diag_Send(diagString,0);
}

int func_Diag_Send(char diagString[], byte addressing)
{
  dword diagLen;
  byte rawDiagReqst[5000];                                                //Maximum bytes is 5000
  word i;
  long ret;
  
  /*Step0, to check input validity */
  setTestId(0);
  diagLen = stringToBytes(diagString, rawDiagReqst);
  if(diagLen <= 0){
    testStepFail(gTestIdStr, "Invalid Input: diagLen [ %d ]! Test Step Quit!", diagLen);
    return -1;
  }
  
  /*Step1, to Compose and Send Diag Requset */
  setTestId(1);
  strncpy(gResultString, diagString, elCount(gResultString));             // copy diag commands
  Teststep(gTestIdStr, "To Send Diag Commands: [ %s ].", diagString);
  
  diagResize(pasDiagReqst, diagLen);
  for(i=0;i<diagLen;i++){
    diagSetPrimitiveByte(pasDiagReqst,i,rawDiagReqst[i]);
  }
  
  if(0==addressing){                           //Physical Addressing
    diagSendRequest(pasDiagReqst);
  }
  else{                                        // Functional Addressing
    diagSendFunctional(pasDiagReqst);
  }
  
  if(printDiag != 0) testReportWriteDiagObject(pasDiagReqst);
  
  
  incTestStepId();
  if (1!=(ret=testWaitForDiagRequestSent(pasDiagReqst, cApplicationTimeoutMs))){                        // Wait until the complete request has been sent
//    testStepFail(gTestIdStr, "Failed to finish sending the request.(Return code = %d )!", ret);            // e.g. in case of long requests which spread over several messages (segmented message)
    testStepWarning(gTestIdStr, "Failed to finish sending the request.(Return code = %d )!", ret);
    return -1;
  }
  
  testStepPass(gTestIdStr, "Diag Commands: [ %s ] is Send on the Bus.", gResultString);
  
  return diagLen;
}

/// <tstf_Diag_Basic>
/* Send Diag Request with Physical Addressing  */ 
/* Maximum bytes is 5000                       */
/* Para1: diagData in bytes Arry               */
/* Para2: diaLen, data bytes to send           */
testfunction tstf_Diag_Send(byte diagData[], dword diagLen)
{
  func_Diag_Send(diagData, diagLen, 0);
}

/// <tstf_Diag_Basic>
/* Send Diag Request with Functional Addressing */ 
/* Maximum bytes is 5000                        */
/* Para1: diagData in bytes Arry                */
/* Para2: diaLen, data bytes to send            */
testfunction tstf_Diag_Send_Functional(byte diagData[], dword diagLen)
{
  func_Diag_Send(diagData, diagLen, 1);
}

int func_Diag_Send(byte diagData[], word diagLen, byte addressing)
{
  byte rawDiagReqst[5000];                                           //Maximum bytes is 5000
  word i;
  long ret;
  
  /*Step0, to check input Validity */
  if(elCount(diagData)<diagLen){
    testStepWarning("0","Invalid input: Arry size of diagData [ %d ] is less than diagLen [ %d ]. So [ %d ] is used.", elCount(diagData),diagLen,elCount(diagData));
    diagLen = elCount(diagData);
  }
  
  if(diagLen <= 0){
    testStepFail(gTestIdStr, "Invalid Input: diagLen [ %d ]! Test Step Quit!", diagLen);
    return -1;
  }
 
  /*Step1, to print out and send Diag Data */
  setTestId(1);
  func_Print_Send_Data(diagLen, diagData);
  
  diagResize(pasDiagReqst, diagLen);

  for(i=0;i<diagLen;i++)
  {
    diagSetPrimitiveByte(pasDiagReqst, i, diagData[i]);
  }
  
  if(0==addressing){                 //Physical Addressing
    diagSendRequest(pasDiagReqst);
  }
  else{                             // Functional Addressing
    diagSendFunctional(pasDiagReqst);
  }
  
  if(printDiag != 0) testReportWriteDiagObject(pasDiagReqst);
  
  /*Step2, Wait for Diag is send on the bus */
  incTestStepId();
  if (1!=(ret=testWaitForDiagRequestSent(pasDiagReqst, cApplicationTimeoutMs))){                        // Wait until the complete request has been sent
    testStepFail(gTestIdStr, "Failed to finish sending the request.(Return code = %d)!", ret);          // e.g. in case of long requests which spread over several messages (segmented message)
    return -1;
  }
  
  testStepPass(gTestIdStr, "Diag Commands is Send on the Bus.");
  
  return diagLen;
  
}

/// <tstf_PowerCtrl>
/*Set TOE Power supply Output On */
/*Para1: on_off {0-off, 1-on }   */
testfunction tstf_TOE_Output_On()
{
  func_TOE_Output_OnOff(1);
}

/// <tstf_PowerCtrl>
testfunction tstf_TOE_Output_Off()
{
  func_TOE_Output_OnOff(0);
}

int func_TOE_Output_OnOff(byte on_off)
{
  setTestId(1);
  
  if(0 == on_off){
    testStepPass(gTestIdStr,"To Set TOE Power Ouput Off.");
  }
  else if(1 == on_off){
    testStepPass(gTestIdStr,"To Set TOE Power Ouput On.");
  }
  else{
    testStepFail(gTestIdStr,"Invalid Input on_off = [%d].",on_off);
    return -1;
  }
  
//  @sysvar::extDevice::TOE_Output_OnOff = on_off; 
  putValue(Env_PSU_Output, on_off);
  putValue(Env_PSU_Write_Values, 1);
  settimer(clearBut, 20);
  return 1;
}

on timer clearBut
{
  putValue(Env_PSU_Write_Values, 0);
}

/// <tstf_PowerCtrl>
/*Set TOE Power supply CH1 Voltage */
/*Para1: volSet { V }              */
testfunction tstf_TOE_CH1_vSet(float volSet)
{
  func_TOE_vSet(volSet,1);
}

/// <tstf_PowerCtrl>
testfunction tstf_TOE_CH2_vSet(float volSet)
{
  func_TOE_vSet(volSet,2);
}

int func_TOE_vSet(float volSet, byte Chn)
{
  setTestId(1);
  testStepPass(gTestIdStr,"To Set TOE Power supply CH%d to: [%.2f] V.", Chn,volSet);
  
  if(Chn == 1){
//    sysSetVariableFloat(sysvar::extDevice::TOE_CH1_vSet, volSet);
    putValue(Env_PSU_Channel, 1);
    putValue(Env_PSU_Voltage, volSet);
  }
  
  if(Chn == 2){
//    sysSetVariableFloat(sysvar::extDevice::TOE_CH2_vSet, volSet);
    putValue(Env_PSU_Channel, 2);
    putValue(Env_PSU_Voltage, volSet);
  }
  
  putValue(Env_PSU_Write_Values, 1);
  settimer(clearBut, 20);
  
  //testStepPass(gTestIdStr,"Set TOE Power supply CH%d to: [%.2f] V.", Chn,volSet);
  return 1;
}

/// <tstf_PowerCtrl>
/*Init Power Supply*/
testfunction tstf_TOE_Init(word port, float volSet)
{
  func_TOE_Init(port, volSet);
}

int func_TOE_Init(word port, float volSet)
{
  putValue(Env_PSU_ComPort, port);
  putValue(Env_PSU_Port_OpenClose, 1);
  testWaitForTimeout(500);
  
  putValue( Env_PSU_Name, 0x6);                 // TOE8952
  putValue( Env_PSU_Channel, 0x1);              // Channel 1
  putValue( Env_PSU_Output, 1);
  putValue( Env_PSU_Voltage, volSet);
  
  testWaitForTimeout(50);
  putValue(Env_PSU_Write_Values, 1);
  settimer(clearBut, 50);
  
  return 1;
}


/// <tstf_PowerCtrl>
/*To read out the voltage on the channel              */
/* that you have set voltage or Current previously    */
testfunction tstf_TOE_vRead()
{
  func_TOE_vRead();
}

float func_TOE_vRead()
{
  long ret;
  
  incTestStepId();
  
  putValue(Env_PSU_Read_V, 1);
  
  ret =  testWaitForSignalInRange(Env_PSU_Read_V, 0, 0, 1200);                      // Env_PSU_Read_V will be set to 0 when response is get
  
  if(ret != 1){
    testStepPass("1","Failed to Read Voltage on Channel [ %d ] is [ %.2f V ].", @Env_PSU_Channel, @Env_PSU_COM_Volts);
    return -1;
  }
  else{
    testStepPass("1","Voltage Read on Channel [ %d ] is [ %.2f V ].", @Env_PSU_Channel, @Env_PSU_COM_Volts);
  }
  
  return @Env_PSU_COM_Volts;
  
}

/// <tstf_PowerCtrl>
/*To read out the Current on the channel              */
/* that you have set voltage or Current previously    */
testfunction tstf_TOE_cRead()
{
  func_TOE_cRead();
}

float func_TOE_cRead()
{
  long ret;
  incTestStepId();
  
  putValue(Env_PSU_Read_I, 1);
  
  ret =  testWaitForSignalInRange(Env_PSU_Read_I, 0, 0, 1200);                      // Env_PSU_Read_I will be set to 0 when response is get
  
  if(ret != 1){
    testStepPass("1","Failed to Read Current on Channel [ %d ] is [ %.5f A ].", @Env_PSU_Channel, @Env_PSU_COM_Amps);
    return -1;
  }
  else{
    testStepPass("1","Current Read on Channel [ %d ] is [ %.5f V ].", @Env_PSU_Channel, @Env_PSU_COM_Amps);
  }
  
  return @Env_PSU_COM_Amps;
  
}

/// <tstf_Diag_Basic>
/*check no response from ECU within a time*/
/*Para1: inTime { ms }                    */
testfunction tstf_Diag_NoResponse(dword inTime)
{
  func_Diag_NoResponse(inTime);
}

int func_Diag_NoResponse(dword inTime)
{
  setTestId(1);
  if(inTime > 0)
  {
    teststep(gTestIdStr,"Check No Response Received.");
  }
  else
  {
    testStepFail(gTestIdStr,"Parameter inTime [%d] is not Valid.", inTime);
    return -1;
  }
  
  incTestStepId();
  if (1 != testWaitForDiagResponse(pasDiagReqst, inTime) ) {          
    testStepPass(gTestIdStr, "No response is Detected within [ %d ] ms!", inTime);
    return 1;
  }
  else
  {
    testStepFail(gTestIdStr, "Below Diag Response is Received!");
    if(printDiag != 0) TestReportWriteDiagResponse(pasDiagReqst);
    return -1;
  }
  
}

/// <tstf_Diag_Services>
/*check response all raw date bytes */
testfunction tstf_Diag_Test_0x78(char tstString[])
{
  // diagGetResponseCode();   // pos -1; negative NRC = 0x78
}

/// <tstf_Diag_Basic>
/* Check Full response - all raw date bytes  */
/* Para1 tstString, such as "62 F1 86 03"    */
/* Note, the response length is also checked */
testfunction tstf_Diag_Test(char tstString[])
{
  func_Diag_Test(tstString);
}

int func_Diag_Test(char tstString[])
{
  dword lenObtain;
  dword lenExpect;
  byte respObtain[512];
  byte respExpect[512];
  word i,j, fail;
  
  fail = 0;
  /* Step 0 check Input Validity */
  setTestId(0);
  lenExpect = stringToBytes(tstString, respExpect);
  if(lenExpect > 0 ){                             
    testStepPass(gTestIdStr,"Check Diag Response: Bytes[%d] Data[%s]",lenExpect, tstString);          // expect input is valid
  }
  else{
    testStepFail(gTestIdStr,"Expected Diag Response is inValid: Bytes[%d] Data[%s]", lenExpect, tstString);
    return -1;
  }
  
  /*Step 1, Wait for and Get Diag Response */
  incTestStepId();
  if (1 != testWaitForDiagResponse(pasDiagReqst, cApplicationTimeoutMs) ) {          
    testStepFail(gTestIdStr, "Valid response missing or received too late!");
    return -1;
  }
  
  testStepPass(gTestIdStr, "Response received successfully.");
  if(printDiag != 0) TestReportWriteDiagResponse(pasDiagReqst);                     // write response to report
  
  diagGetLastResponse(pasDiagRespd);
  lenObtain = diagGetPrimitiveData(pasDiagRespd,respObtain,elCount(respObtain));
  /*Step 1.1, Print Diag Response */
  func_Print_Received_Data(lenObtain, respObtain);
  
  
  /*Optianl Step - Optional, To Filter out a DTC if needed*/
  if( maskDTC_flag == 1)  {                                          // to mask DTC           59 02 09 0x9D, 0x0C, 0x54 09 , C1 00 87 09,
    func_FilterOut_DTC(respObtain, lenObtain);
  }
  
  /*Step2, Check received Data Length*/
  incTestStepId();
  if(lenObtain != lenExpect)
  {
    testStepFail(gTestIdStr, "Received bytes [ %d ] not equal to expected [ %d ].",lenObtain, lenExpect);
    return -1;
  }

  /*Step2, Check received Data bytes by bytes */
  for(i=0;i<lenExpect;i++){
    incSubStepId();
    if(respExpect[i] != respObtain[i])
    {
      testStepFail(gTestIdStr,"Received byte[ %d ] 0x%02X not equal to expected 0x%02X.",i, respObtain[i],respExpect[i]);
      fail++;
    }
    else{
      testStepPass(gTestIdStr,"Received byte[ %d ] 0x%02X equals to expected 0x%02X.",i, respObtain[i],respExpect[i]);
    }
  }
  
  if(fail != 0) return -1;
  testStepPass(gTestIdStr, "Received bytes equals to expected bytes.");
 
  return lenObtain;
}


/// <tstf_Diag_Basic>
/* Check Full response - all raw date bytes            */
/* Para1 respExpect, response data in byte arry        */
/* Para2 lenExpect, response data length, it determins */
/* How many bytes in Para1 arry will be compared with  */
testfunction tstf_Diag_Test(byte respExpect[], dword lenExpect)
{
  func_Diag_Test(respExpect, lenExpect);
}

int func_Diag_Test(byte respExpect[], dword lenExpect)
{
  dword lenObtain;
  byte respObtain[512];
 
  word i, j, fail;
  
  fail = 0;
  /* Step 0 check Input Validity */
  setTestId(0);
  if(elCount(respExpect)< lenExpect){
    testStepWarning(gTestIdStr, "Invalid input: respExpect arry Size [ %d ] is less than lenExpect [% d ]. [ %d ] is used.", elCount(respExpect),lenExpect,elCount(respExpect));
    lenExpect = elCount(respExpect);
  }
  
  if(lenExpect <= 0){
    testStepFail(gTestIdStr, "Invalid Input: lenExpect [ %d ]! Test Step Quit!", lenExpect);
    return -1;
  }
  
  /*Step 1, Wait for and Get Diag Response */
  incTestStepId();
  if (1 != testWaitForDiagResponse(pasDiagReqst, cApplicationTimeoutMs) ) {          
    testStepFail(gTestIdStr, "Valid response missing or received too late!");
    return -1;
  }
  
  testStepPass(gTestIdStr, "Response received successfully.");
  if(printDiag != 0) TestReportWriteDiagResponse(pasDiagReqst);                     // write response to report
  
  diagGetLastResponse(pasDiagRespd);
  lenObtain = diagGetPrimitiveData(pasDiagRespd,respObtain,elCount(respObtain));
  /*Step 1.1, Print Diag Response */
  func_Print_Received_Data(lenObtain, respObtain);
  
  
  /*Optinal Step - Optional, To Filter out a DTC if needed*/
  if( maskDTC_flag == 1)  {                                                        // to mask DTC           59 02 09 0x9D, 0x0C, 0x54 09 , C1 00 87 09,
    func_FilterOut_DTC(respObtain, lenObtain);
  }
  
  /*Step 2, to check Response Data Length */
  incTestStepId();
  if(lenObtain != lenExpect)
  {
    testStepFail(gTestIdStr,"Received bytes [ %d ] not equal to expected [ %d ].",lenObtain, lenExpect);
    return -1;
  }

  /*Step 2, to check Response Data */
  for(i=0;i<lenExpect;i++)
  {
    incSubStepId();
    if(respExpect[i] != respObtain[i])
    {
      testStepFail(gTestIdStr,"Received byte[ %d ] 0x%02X not equal to expected 0x%02X.",i, respObtain[i],respExpect[i]);
      fail++;
    }
    else{
      testStepPass(gTestIdStr,"Received byte[ %d ] 0x%02X equals to expected 0x%02X.",i, respObtain[i],respExpect[i]);
    }
  }
  
  if(fail != 0) return -1;
  testStepPass(gTestIdStr, "Received bytes equals to expected bytes.");
 
  return lenObtain;
}

/*To Print out all received response data*/
int func_Print_Received_Data(dword lenObtain, byte respObtain[])
{
  dword i;
  
  incSubStepId();
  gResultString[0] = '\0';                             // clear buffer
  snprintf(gResultString, elcount(gResultString), "Received Data is : ");
  for(i=0; i<lenObtain; i++){
    snprintf(gResultString, elcount(gResultString), "%s %02X", gResultString, respObtain[i]);
  }
  testStep(gTestIdStr, "%s", gResultString);
  
  return 1;
}

/*To Print out all Send Request data*/
int func_Print_Send_Data(dword lenSend, byte dataSend[])
{
  dword i;
  
  incSubStepId();
  gResultString[0] = '\0';                             // clear buffer
  snprintf(gResultString, elcount(gResultString), "Diag Request to Send is : ");
  for(i=0; i<lenSend; i++){
    snprintf(gResultString, elcount(gResultString), "%s %02X", gResultString, dataSend[i]);
  }
  testStep(gTestIdStr, "%s", gResultString);
  
  return 1;
}

/*To remove masked DTC form Diag response and rewrite response length*/
//func_FilterOut_DTC(byte respObtain[], dword &lenObtain)
//{
//  dword i, j;
//   
//  if(respObtain[0] == 0x59 && respObtain[1] ==0x02 && lenObtain>=7)
//  {
//    for(i=3;i<lenObtain;i+=4)
//    {
//      /* Filter out 1st DTC*/
//      if(maskDTC[0]==respObtain[i] && maskDTC[1]==respObtain[i+1] && maskDTC[2]==respObtain[i+2])
//      {
//        for(j=i; j<lenObtain-4;j++)                  //    i =3, len == 11, 
//        {
//          respObtain[j] = respObtain[j+4];
//        }
//        lenObtain -= 4;
//        teststep(gTestIdStr,"DTC [%02X%02X%02X] is ignored!",maskDTC[0],maskDTC[1],maskDTC[2]);
////            break;
//      }
//      
//      /* Filter out 2nd DTC*/
//      if(maskDTC[3]==respObtain[i] && maskDTC[4]==respObtain[i+1] && maskDTC[5]==respObtain[i+2])
//      {
//        for(j=i; j<lenObtain-4;j++)                  //    i =3, len == 11, 
//        {
//          respObtain[j] = respObtain[j+4];
//        }
//        lenObtain -= 4;
//        teststep(gTestIdStr,"DTC [%02X%02X%02X] is ignored!",maskDTC[3],maskDTC[4],maskDTC[5]);
//        break;
//      }
//      if(maskDTC[6]==respObtain[i] && maskDTC[7]==respObtain[i+1] && maskDTC[8]==respObtain[i+2])
//      {
//        for(j=i; j<lenObtain-4;j++)                  //    i =3, len == 11, 
//        {
//          respObtain[j] = respObtain[j+4];
//        }
//        lenObtain -= 4;
//        teststep(gTestIdStr,"DTC [%02X%02X%02X] is ignored!",maskDTC[6],maskDTC[7],maskDTC[8]);
//        break;
//      }
//      if(maskDTC[9]==respObtain[i] && maskDTC[10]==respObtain[i+1] && maskDTC[11]==respObtain[i+2])
//      {
//        for(j=i; j<lenObtain-4;j++)                  //    i =3, len == 11, 
//        {
//          respObtain[j] = respObtain[j+4];
//        }
//        lenObtain -= 4;
//        teststep(gTestIdStr,"DTC [%02X%02X%02X] is ignored!",maskDTC[9],maskDTC[10],maskDTC[11]);
//        break;
//      }
//      if(maskDTC[12]==respObtain[i] && maskDTC[13]==respObtain[i+1] && maskDTC[14]==respObtain[i+2])
//      {
//        for(j=i; j<lenObtain-4;j++)                  //    i =3, len == 11, 
//        {
//          respObtain[j] = respObtain[j+4];
//        }
//        lenObtain -= 4;
//        teststep(gTestIdStr,"DTC [%02X%02X%02X] is ignored!",maskDTC[12],maskDTC[13],maskDTC[14]);
//        break;
//      }
//      if(maskDTC[15]==respObtain[i] && maskDTC[16]==respObtain[i+1] && maskDTC[17]==respObtain[i+2])
//      {
//        for(j=i; j<lenObtain-4;j++)                  //    i =3, len == 11, 
//        {
//          respObtain[j] = respObtain[j+4];
//        }
//        lenObtain -= 4;
//        teststep(gTestIdStr,"DTC [%02X%02X%02X] is ignored!",maskDTC[15],maskDTC[16],maskDTC[17]);
//        break;
//      }
//      if(maskDTC[18]==respObtain[i] && maskDTC[19]==respObtain[i+1] && maskDTC[20]==respObtain[i+2])
//      {
//        for(j=i; j<lenObtain-4;j++)                  //    i =3, len == 11, 
//        {
//          respObtain[j] = respObtain[j+4];
//        }
//        lenObtain -= 4;
//        teststep(gTestIdStr,"DTC [%02X%02X%02X] is ignored!",maskDTC[18],maskDTC[19],maskDTC[20]);
//        break;
//      }
//    }
//    
//  }
//    
//}

func_FilterOut_DTC(byte respObtain[], dword &lenObtain)
{
  dword i, j;
   
  if(respObtain[0] == 0x59 && respObtain[1] ==0x02 && lenObtain>=7)
  {
    for(i=3;i<lenObtain;i+=4)
    {
      /* Filter out 1st DTC*/
      if(maskDTC[0]==respObtain[i] && maskDTC[1]==respObtain[i+1] && maskDTC[2]==respObtain[i+2])
      {
        for(j=i; j<lenObtain-4;j++)                  //    i =3, len == 11, 
        {
          respObtain[j] = respObtain[j+4];
        }
        lenObtain -= 4;
        teststep(gTestIdStr,"DTC [%02X%02X%02X] is ignored!",maskDTC[0],maskDTC[1],maskDTC[2]);
            break;
      }
    }
    for(i=3;i<lenObtain;i+=4)
    {
      /* Filter out 2st DTC*/
      if(maskDTC[3]==respObtain[i] && maskDTC[4]==respObtain[i+1] && maskDTC[5]==respObtain[i+2])
      {
        for(j=i; j<lenObtain-4;j++)                  //    i =3, len == 11, 
        {
          respObtain[j] = respObtain[j+4];
        }
        lenObtain -= 4;
        teststep(gTestIdStr,"DTC [%02X%02X%02X] is ignored!",maskDTC[3],maskDTC[4],maskDTC[5]);
            break;
      }
    }  
    for(i=3;i<lenObtain;i+=4)
    {
      /* Filter out 3st DTC*/
      if(maskDTC[6]==respObtain[i] && maskDTC[7]==respObtain[i+1] && maskDTC[8]==respObtain[i+2])
      {
        for(j=i; j<lenObtain-4;j++)                  //    i =3, len == 11, 
        {
          respObtain[j] = respObtain[j+4];
        }
        lenObtain -= 4;
        teststep(gTestIdStr,"DTC [%02X%02X%02X] is ignored!",maskDTC[6],maskDTC[7],maskDTC[8]);
            break;
      }
    }
    for(i=3;i<lenObtain;i+=4)
    {
      /* Filter out 2st DTC*/
      if(maskDTC[9]==respObtain[i] && maskDTC[10]==respObtain[i+1] && maskDTC[11]==respObtain[i+2])
      {
        for(j=i; j<lenObtain-4;j++)                  //    i =3, len == 11, 
        {
          respObtain[j] = respObtain[j+4];
        }
        lenObtain -= 4;
        teststep(gTestIdStr,"DTC [%02X%02X%02X] is ignored!",maskDTC[9],maskDTC[10],maskDTC[11]);
            break;
      }
    }
//    for(i=3;i<lenObtain;i+=4)
//    {
//      /* Filter out 0xD0,0x36,0x00 DTC*/
//      if(maskDTC[12]==respObtain[i] && maskDTC[13]==respObtain[i+1] && maskDTC[14]==respObtain[i+2])
//      {
//        for(j=i; j<lenObtain-4;j++)                  //    i =3, len == 11, 
//        {
//          respObtain[j] = respObtain[j+4];
//        }
//        lenObtain -= 4;
//        teststep(gTestIdStr,"DTC [%02X%02X%02X] is ignored!",maskDTC[12],maskDTC[13],maskDTC[14]);
//            break;
//      }
//    }
//    for(i=3;i<lenObtain;i+=4)
//    {
//      /* Filter out 0xEF,0x02,0x00 DTC*/
//      if(maskDTC[15]==respObtain[i] && maskDTC[16]==respObtain[i+1] && maskDTC[17]==respObtain[i+2])
//      {
//        for(j=i; j<lenObtain-4;j++)                  //    i =3, len == 11, 
//        {
//          respObtain[j] = respObtain[j+4];
//        }
//        lenObtain -= 4;
//        teststep(gTestIdStr,"DTC [%02X%02X%02X] is ignored!",maskDTC[15],maskDTC[16],maskDTC[17]);
//            break;
//      }
//    }
//    for(i=3;i<lenObtain;i+=4)
//    {
//      /* Filter out 0xEF,0x08,0x00 DTC*/
//      if(maskDTC[18]==respObtain[i] && maskDTC[19]==respObtain[i+1] && maskDTC[20]==respObtain[i+2])
//      {
//        for(j=i; j<lenObtain-4;j++)                  //    i =3, len == 11, 
//        {
//          respObtain[j] = respObtain[j+4];
//        }
//        lenObtain -= 4;
//        teststep(gTestIdStr,"DTC [%02X%02X%02X] is ignored!",maskDTC[18],maskDTC[19],maskDTC[20]);
//            break;
//      }
//    }
//    for(i=3;i<lenObtain;i+=4)
//    {
//      /* Filter out 0xEF,0x54,0x00 DTC*/
//      if(maskDTC[21]==respObtain[i] && maskDTC[22]==respObtain[i+1] && maskDTC[23]==respObtain[i+2])
//      {
//        for(j=i; j<lenObtain-4;j++)                  //    i =3, len == 11, 
//        {
//          respObtain[j] = respObtain[j+4];
//        }
//        lenObtain -= 4;
//        teststep(gTestIdStr,"DTC [%02X%02X%02X] is ignored!",maskDTC[21],maskDTC[22],maskDTC[23]);
//            break;
//      }
//    }
//    for(i=3;i<lenObtain;i+=4)
//    {
//      /* Filter out 0xEF,0x55,0x00 DTC*/
//      if(maskDTC[24]==respObtain[i] && maskDTC[25]==respObtain[i+1] && maskDTC[26]==respObtain[i+2])
//      {
//        for(j=i; j<lenObtain-4;j++)                  //    i =3, len == 11, 
//        {
//          respObtain[j] = respObtain[j+4];
//        }
//        lenObtain -= 4;
//        teststep(gTestIdStr,"DTC [%02X%02X%02X] is ignored!",maskDTC[24],maskDTC[25],maskDTC[26]);
//            break;
//      }
//    }
//    for(i=3;i<lenObtain;i+=4)
//    {
//      /* Filter out 0xEF,0x59,0x00 DTC*/
//      if(maskDTC[27]==respObtain[i] && maskDTC[28]==respObtain[i+1] && maskDTC[29]==respObtain[i+2])
//      {
//        for(j=i; j<lenObtain-4;j++)                  //    i =3, len == 11, 
//        {
//          respObtain[j] = respObtain[j+4];
//        }
//        lenObtain -= 4;
//        teststep(gTestIdStr,"DTC [%02X%02X%02X] is ignored!",maskDTC[27],maskDTC[28],maskDTC[29]);
//            break;
//      }
//    } 
//    for(i=3;i<lenObtain;i+=4)
//    {
//      /* Filter out 0xEF,0x59,0x00 DTC*/
//      if(maskDTC[30]==respObtain[i] && maskDTC[31]==respObtain[i+1] && maskDTC[32]==respObtain[i+2])
//      {
//        for(j=i; j<lenObtain-4;j++)                  //    i =3, len == 11, 
//        {
//          respObtain[j] = respObtain[j+4];
//        }
//        lenObtain -= 4;
//        teststep(gTestIdStr,"DTC [%02X%02X%02X] is ignored!",maskDTC[30],maskDTC[31],maskDTC[32]);
//            break;
//      }
//    }
    }
    
}



/// <tstf_CanTp>
/*To wait and check the raw data of a single Msg Frame         */
/*Para1 msgID, such as 0x7A5                                   */
/*Para2 tstString, expected data. Note: for a diag response,   */
/*the Valid length is needed, such as '02 50 03', as           */
/*this API checks the raw data of a single frame               */
/*Para3,offset, from which byte to test, starting from 0       */
/*The checked bytes length is decided by para2 tstString       */
testfunction tstf_RawFrame_Test_Offset(dword msgID, char tstString[], word offset)
{
  func_SetBusContext(ecuBusChn);
  func_RawFrame_Test_Offset(msgID, tstString, offset);
}

int func_RawFrame_Test_Offset(dword msgID, char tstString[], word offset)
{
  long lenObtain;
  long lenExpect;
  long lenOffset;
  byte respObtain[64];
  byte respExpect[64];
  word i, j, fail;
  long idx;
  message *getMsg;                      // to get received Msg Data
  
  fail = 0;
  /* Step 1 check Input Validity */
  setTestId(1);
  lenExpect = stringToBytes(tstString, respExpect);
  if(lenExpect >= 0 ){                             
    testStepPass(gTestIdStr,"Check Msg [ 0x%X ] Raw Data: [ %s ] from byte [ %d ]",msgID, tstString, offset);          // expect input is valid
  }
  else{
    testStepFail(gTestIdStr,"Expected Msg [ 0x%X ] Raw Data is inValid: Data[ %s ] from byte [ %d ].", msgID, tstString, offset);
    return -1;
  }
  
  /*Step 2, wait for expected Msg */
  incTestStepId();
  testJoinMessageEvent(msgID);
  idx = testWaitForAnyJoinedEvent(msgTimeoutMs);
  if(1 != idx) {
    testStepFail(gTestIdStr, "Valid Msg missing or received too late!");
    return -1;
  }
  else{ 
    testStepPass(gTestIdStr, "Msg [0x%X] received successfully.", msgID);
  }
  
  /*Step2.1 Print Recieved Msg Data */
  incSubStepId();
  testGetWaitEventMsgData(idx, getMsg);
  lenObtain = getMsg.DataLength;
  
  gResultString[0] = '\0';                                 // clear buffer
  snprintf(gResultString, elcount(gResultString), "Received Msg Data is : ");
  for(i=0; i<lenObtain; i++){
      respObtain[i] = getMsg.byte(i);
      snprintf(gResultString, elcount(gResultString), "%s %02X", gResultString, respObtain[i]);
    }
  testStep(gTestIdStr, "%s", gResultString);
  
  
  /*Step 3, check the obtained Msg length */  
  if(lenExpect == 0 )           // expected and obtained Msg data is empty, pass
  {
    incTestStepId();
    
    if(lenObtain == 0){
      testStepPass(gTestIdStr, "Received Msg [0x%X] has no Payload, as expected.", msgID);
      return 1;
    }
    else{
      testStepFail(gTestIdStr, "Received Msg [0x%X] has [%d] bytes, but expected is [%d] bytes.", msgID, lenObtain, lenExpect);
      return -1;
    }
  }
  
  /*Step4, Compare obtained data with expectation */
  incTestStepId();
  lenOffset = lenExpect + offset;
  if(lenObtain >= lenOffset ){
    
    
    for(j=0;j<lenExpect;j++)
    {
      incSubStepId();
      if(respExpect[j] != respObtain[j+offset])
      {
        testStepFail(gTestIdStr,"Received byte[ %d ] [ 0x%02X ] not equal to expected [ 0x%02X ].", j+offset, respObtain[j+offset], respExpect[j]);
        fail++;
      }
      else{
        testStepPass(gTestIdStr,"Received byte[ %d ] [ 0x%02X ] equals to expected [ 0x%02X ].", j+offset, respObtain[j+offset], respExpect[j]);
      }
    }
    
    if(fail != 0) return -1;
    incSubStepId();
    testStepPass(gTestIdStr, "Received bytes equals to expected bytes.");
   
  }
  else{
    testStepFail(gTestIdStr, "Expected data bytes [ %d ] is more than Response [ %d ].",lenOffset, lenObtain);
  }
    
  return lenObtain;
}

/// <tstf_CanTp>
/* Send one Raw Frame on CAN1 by default        */ 
/* Maximum bytes is 64                          */
/* Para1: msgID { such as 0x123 }               */
/* Para2: dataStr { Data,such as "1122334455" } */
/* Para3: length is the msg bytes { 6,8,12,etc }*/
testfunction tstf_RawFrame_Send(dword msgID,  char dataStr[], word msgLen)
{
  func_SetBusContext(ecuBusChn);
  func_RawFrame_Send( msgID, dataStr, msgLen);
}

int func_RawFrame_Send(dword msgID, char dataStr[], word length)
{
  long dataLen;                               // Data length
  byte rawDataBuf[64];                        // Maximum bytes is 64
  word i;
  long ret;
  message *msgBuf;
  
  /* Step1: To check if Parameter input is correct */
  setTestId(1);
  gResultString[0] = '\0';
  snprintf(gResultString, elcount(gResultString), "msgID: [ 0x%02X ], length: [ %d ], data: [ %s ]", msgID, length, dataStr);
  Teststep(gTestIdStr, "To Send One Raw Frame: %s.", gResultString);
  
  for(i=0; i<elcount(rawDataBuf); i++){                   // clear buffer
    rawDataBuf[i] = 0x0;
  }
  
  dataLen = stringToBytes(dataStr, rawDataBuf);
  if(dataLen <= 0)
  {
    testStepFail(gTestIdStr, "Input Error: dataStr [ %s ]! Test Step Quit!", dataStr);
    return -1;
  }
  
  if(dataLen > length){                // bytes is more than msg length
    testStepWarning(gTestIdStr, "Input Warning: dataStr bytes [ %d ] is not equal to length [ %d ]. Will take [ %d ] as Msg Length.", dataLen, length, length);
  }
  
  /* Step2 To compose the raw frame */
  msgBuf.id          = msgID;
  msgBuf.DataLength  = length;
  msgBuf.BRS         = 1;                     // if CAN FD frame, BRS is 1 by default. no affact CAN frame.
  msgBuf.FDF = 1;
  for(i=0; i<length; i++){
    msgBuf.byte(i) = rawDataBuf[i];
  }
  output(msgBuf);
    
    
  /* Step3: Wait for msgBuf is on the bus */
  incTestStepId();
  if( 1 != testWaitForMessage(msgBuf.id, msgTimeoutMs)){                                          // Wait until the msg is on the bus, max wait 500ms                      
    testStepFail(gTestIdStr, "Failed to finish sending the Frame (Return code=%d)!", ret);        // e.g. in case of long requests which spread over several messages (segmented message)
    return -1;
  }
  
  testStepPass(gTestIdStr, "One Frame: %s \n is Send on the Bus.", gResultString);
  return dataLen;
  
}


/// <tstf_CanTp>
/* Send one Raw Frame on CAN1 by default            */ 
/* Maximum bytes is 64                              */
/* Para1: msgID { such as 0x123 }                   */
/* Para2: msgLen { Msg length, 6, 8, 12, etc }      */
/* Para3: data { Data Arry, not String }            */
/* Para4: bytsToCopy, bytes to copy from Para3 data */
testfunction tstf_RawFrame_Send(dword msgID, word msgLen, byte data[], word bytsToCopy)
{
  func_SetBusContext(ecuBusChn);
  func_RawFrame_Send( msgID, msgLen, data, bytsToCopy);
}

/* API to send raw Msg */
int func_RawFrame_Send(dword msgID, word msgLen, byte data[], word bytsToCopy)
{
  long dataLen;                               // Data length
  byte rawDataBuf[64];                        // Maximum bytes is 64
  word i;
  long ret;
  message *msgBuf;
  
  /* Step1: To check if Parameter input is correct */
  setTestId(1);
  dataLen = elCount(data);
  if(bytsToCopy <= dataLen && bytsToCopy <= msgLen && bytsToCopy <= 64){}
  else{
    testStepFail(gTestIdStr, "Input Error! Rule: bytsToCopy <= dataLen && bytsToCopy <= msgLen.\n bytsToCopy[%d], dataLen[%d], msgLen[%d]",bytsToCopy,dataLen,msgLen);
    return -1;
  }
  
  for(i=0; i<elcount(rawDataBuf); i++){                   // clear buffer
    rawDataBuf[i] = 0x0;
  }
  
  for(i=0; i<bytsToCopy; i++){                           // copy to buffer
    rawDataBuf[i] = data[i];
  }
  /*Step1.1, to print out Data*/
  gResultString[0] = '\0';                             // clear buffer
  snprintf(gResultString, elcount(gResultString), "Msg to Send: msgID [ 0x%X ], msgLen [ %d ], data: ", msgID, msgLen);
  for(i=0; i<bytsToCopy; i++){
    snprintf(gResultString, elcount(gResultString), "%s %02X", gResultString, data[i]);
  }
  testStepPass(gTestIdStr, "%s", gResultString);
  
  /* Step2 To compose the raw frame */
  msgBuf.id          = msgID;
  msgBuf.DataLength  = msgLen;
  msgBuf.BRS         = 1;                     // if CAN FD frame, BRS is 1 by default. no affact CAN frame.
  for(i=0; i<msgLen; i++){
    msgBuf.byte(i) = rawDataBuf[i];
  }
  output(msgBuf);
    
  /* Step3: Wait for msgBuf is on the bus */
  incTestStepId();
  if( 1 != testWaitForMessage(msgBuf.id, msgTimeoutMs)){                                          // Wait until the msg is on the bus, max wait 500ms                      
    testStepFail(gTestIdStr, "Failed to finish sending the Frame (Return code=%d)!", ret);        // e.g. in case of long requests which spread over several messages (segmented message)
    return -1;
  }
  
  testStepPass(gTestIdStr, "%s \n is Send on the Bus.", gResultString);
  return bytsToCopy;
  
}





/*Function to copy Diag Response as string */
int func_Diag_Resp_Copy(char respVessel[])
{
  word lenObtain;
  byte respObtain[512];
  word i;
  char temp[8];
  char retString[1024];
  
  respVessel[0] = '\0';
  retString[0]  = '\0';
  
  setTestId(1);
  teststep(gTestIdStr,"To Copy Diag Response.");
  
  if (1 != testWaitForDiagResponse(pasDiagReqst, cApplicationTimeoutMs) ) {          
    snprintf(gResultString, elcount(gResultString), "Valid response missing or received too late!");
    testStepFail(gTestIdStr, gResultString);
    strncpy(respVessel, "", elCount(respVessel));
    return -1;
  }
  
  else{
    incTestStepId();
    testStepPass(gTestIdStr, "Response received successfully.");
    if(printDiag != 0) TestReportWriteDiagResponse(pasDiagReqst);                     // write response to report
    
    //lenExpect = stringToBytes(tstString, respExpect);
    diagGetLastResponse(pasDiagRespd);
    
    lenObtain = diagGetPrimitiveData(pasDiagRespd,respObtain,elCount(respObtain));
    
    incTestStepId();
    for(i=0; i<lenObtain; i++)
    {      
      snprintf(temp, elcount(temp), "%02X", respObtain[i]);
        
      strncat(retString, temp, elCount(retString));
      
      if(i != lenObtain-1){                          //copy last byte
        strncat(retString, " ", elCount(retString));
      }
    }
    strncpy(respVessel, retString, elCount(respVessel));
    testStepPass(gTestIdStr, "Received Diag Response is [%s] - [%s].", retString, respVessel);
    //write("retString= %s\nrespVessel = %s",retString, respVessel );
  }
  
  return lenObtain;
  
}

/// <tstf_Diag_Basic>
/* Expect Negative response and check NRC */
testfunction tstf_Diag_Chk_NRC(byte expNRC)
{
  func_Diag_Chk_NRC(expNRC);
}

int func_Diag_Chk_NRC(byte expNRC)
{
  long Ret;
  
  /*Step1, Wait and Get Diag Response */
  setTestId(1);
  teststep(gTestIdStr,"Check Negative Response NRC: [%x]", expNRC);
  
  if (1 != testWaitForDiagResponse(pasDiagReqst, cApplicationTimeoutMs) ) {    
    snprintf(gResultString, elcount(gResultString), "Valid response missing or received too late!");
    testStepFail(gTestIdStr, gResultString);
    return -1;
  }
  
  testStepPass(gTestIdStr, "Response received successfully.");
  if(printDiag != 0) TestReportWriteDiagResponse(pasDiagReqst);                     // write response to report
  
  /*Step2, Get and Check NRC */
  incTestStepId();
  Ret = diagGetLastResponseCode (pasDiagReqst);
  
  if(Ret == -1)     // positive response
  {
    testStepFail(gTestIdStr,"Positive Response is Received. [%d]", Ret);
    return -1;
  }
  else if(Ret > 0)
  {
    if(Ret == expNRC){
      testStepPass(gTestIdStr,"Negative response is received and NRC is expected [0x%x]", Ret);
    }
    else{
      testStepFail(gTestIdStr,"Negative response is received but NRC is [0x%x] != expected [0x%x]", Ret, expNRC);
      return -1;
    }  
  }
  else if(Ret == 0){
    testStepFail(gTestIdStr,"Response is not received! [%d]", Ret);
    return -1;
  }
  else
  {
    testStepFail(gTestIdStr,"Other Failures [%d]", Ret);
    return -1;
  }
    
  return Ret;
}

/// <tstf_Diag_Basic>
/*check some raw date bytes of response                              */
/*Para1, offset, from which byte to check, start form 0              */
/*Para2, len, checked bytes                                          */
/*Para3, tstString, expected bytes in string, the bytes number must  */
/*Be equal to Para2 len                                              */
testfunction tstf_Diag_Test_Offset(dword offset, dword len, char tstString[])
{
  func_Diag_Test_Offset(offset, len, tstString);
}

int func_Diag_Test_Offset(dword offset, dword len, char tstString[])
{
  dword lenObtain;
  dword lenExpect;
  byte respObtain[512];
  byte respExpect[512];
  word i, fail;
  
  fail=0;
  /*Step 0, check input validity */
  setTestId(0);
  lenExpect = stringToBytes(tstString, respExpect);
  if(lenExpect <= 0 ){                             
    testStepFail(gTestIdStr, "Expected Diag Response is inValid: Data[ %s ] of byte [ %d ].", tstString, offset);
    return -1;
  }
  
  testStepPass(gTestIdStr, "Check Diag Response: Data[ %s ] of byte [ %d ].", tstString, offset);          // expect input is valid
  
  if(lenExpect != len)
  {
    testStepWarning(gTestIdStr, "Parameter input of len [%d] doesn't match tstString length [%d].\n Length [%d] is used.",len,lenExpect,lenExpect);
  }
  
  /*Step 1, Wait for and Get daig response */
  incTestStepId();
  if (1 != testWaitForDiagResponse(pasDiagReqst, cApplicationTimeoutMs) ) {          
    testStepFail(gTestIdStr, "Valid response missing or received too late!");
    return -1;
  }
  
  testStepPass(gTestIdStr, "Response received successfully.");
  if(printDiag != 0) TestReportWriteDiagResponse(pasDiagReqst);                                     // write response to report
  
  diagGetLastResponse(pasDiagRespd);
  lenObtain = diagGetPrimitiveData(pasDiagRespd,respObtain,elCount(respObtain));
  /*Step 1.1, Print Diag Response */
  func_Print_Received_Data(lenObtain, respObtain);
  
  /*Step2, Check response length with expectation */
  if(lenExpect+offset > lenObtain ){
    incTestStepId();
    testStepFail(gTestIdStr,"Negative Response or Invalid Input: offset [ %d ] + lenExpect [ %d ] is more than Response length [ %d ].", offset,lenExpect,lenObtain);
    lenExpect = lenObtain - offset;
  }
  
  /*Step2, Compare response with expectation*/
  incTestStepId();
  for(i=0;i<lenExpect;i++)
  {
    incSubStepId();
    if(respExpect[i] != respObtain[i+offset])
    {
      testStepFail(gTestIdStr,"Received byte[%d] 0x%02X not equal to expected 0x%02X.",i+offset, respObtain[i+offset],respExpect[i]);
      fail++;
    }
    else{
      testStepPass(gTestIdStr,"Received byte[%d] 0x%02X equals to expected 0x%02X.",i+offset, respObtain[i+offset],respExpect[i]);
    }
  }
  
  if(fail!=0 || i==0) return -1;
  incSubStepId();
  testStepPass(gTestIdStr, "Received bytes equals to expected bytes.");
    
  return lenObtain;
}

/// <tstf_Diag_Basic>
/*check some raw date bytes of response                         */
/*Para1, offset, from which byte to check, start form 0         */
/*Para2, lenExpect, checked bytes number                        */
/*Para3, respExpect, expected data                              */
/*Note: Para2 deternind how many bytes in Para3 will be Checked */
testfunction tstf_Diag_Test_Offset(dword offset, dword lenExpect, byte respExpect[])
{
  func_Diag_Test_Offset(offset, lenExpect, respExpect);
}

int func_Diag_Test_Offset(dword offset, dword lenExpect, byte respExpect[])
{
  dword lenObtain;
  byte respObtain[512];
  word i, fail;
  
  fail=0;
  /*Step 0, check input validity */
  setTestId(0);
  if(elCount(respExpect)<lenExpect){
    testStepWarning(gTestIdStr, "Parameter input of lenExpect [%d] doesn't match respExpect length [%d].\n Length [%d] is used.",lenExpect,elCount(respExpect),elCount(respExpect));
    lenExpect = elCount(respExpect);
  }
  
  if(lenExpect <= 0 ){ 
    testStepFail(gTestIdStr, "Invalid input: lenExpect [ %d ]. Test Step Quit!", lenExpect);
    return -1;
  }
  

  /*Step 1, Wait for and Get daig response */
  incTestStepId();
  if (1 != testWaitForDiagResponse(pasDiagReqst, cApplicationTimeoutMs) ) {          
    testStepFail(gTestIdStr, "Valid response missing or received too late!");
    return -1;
  }
  
  testStepPass(gTestIdStr, "Response received successfully.");
  if(printDiag != 0) TestReportWriteDiagResponse(pasDiagReqst);                                     // write response to report
  
  diagGetLastResponse(pasDiagRespd);
  lenObtain = diagGetPrimitiveData(pasDiagRespd,respObtain,elCount(respObtain));
  /*Step 1.1, Print Diag Response */
  func_Print_Received_Data(lenObtain, respObtain);
  
  /*Step2, Check response length with expectation */
  if(lenExpect+offset > lenObtain ){
    incTestStepId();
    testStepFail(gTestIdStr,"Negative Response or Invalid Input: offset [ %d ] + lenExpect [ %d ] is more than Response length [ %d ].", offset,lenExpect,lenObtain);
    lenExpect = lenObtain - offset;
  }
  
  /*Step2 or 3, Check response Data with expectation */
  incTestStepId();
  for(i=0;i<lenExpect;i++){
    incSubStepId();
    if(respExpect[i] != respObtain[i+offset]){
      testStepFail(gTestIdStr,"Received byte[%d] 0x%02X not equal to expected 0x%02X.",i+offset, respObtain[i+offset],respExpect[i]);
      fail++;
    }
    else{
      testStepPass(gTestIdStr,"Received byte[%d] 0x%02X equals to expected 0x%02X.",i+offset, respObtain[i+offset],respExpect[i]);
    }
  }
  
  if(fail!=0 || i==0) return -1;
  incSubStepId();
  testStepPass(gTestIdStr, "Received bytes equals to expected bytes.");
    
  return lenObtain;
}

/// <tstf_Diag_Basic>
/*check some raw date bytes of response                         */
/*Para1, offset, from which byte to check, start form 0         */
/*Para2, len, checked bytes number                              */
/*Para3, tstString, expected data in string, its bytes must     */
/*Be eqaul to Para2 len                                         */
/*Note: This API Continue to check last diag response. Does not */
/*Wait for a new response.                                      */
testfunction tstf_Diag_ContinueTest_Offset(dword offset, dword len, char tstString[])
{
  func_Diag_ContinueTest_Offset(offset, len, tstString);
}

int func_Diag_ContinueTest_Offset(dword offset, dword len, char tstString[])
{
  dword lenObtain;
  dword lenExpect;
  byte respObtain[512];
  byte respExpect[512];
  word i, fail;
  
  fail = 0;
  /*Step 0, check input validity */
  setTestId(0);
  lenExpect = stringToBytes(tstString, respExpect);
  if(lenExpect > 0 ){                             
    testStepPass(gTestIdStr,"Continue to Check Diag Response: Data[%s] from byte [%d].", tstString, offset);          // expect input is valid
  }
  else{
    testStepFail(gTestIdStr,"Expected Diag Response is inValid: Data[%s] from byte [%d].", tstString, offset);
    return -1;
  }
  
  if(lenExpect != len)
  {
    testStepFail(gTestIdStr, "Parameter input of len [%d] doesn't match tstString length [%d].",len,lenExpect);
    return -1;
  }
  
  /*Step 1, get last diag response */
  incTestStepId();
  if( diagGetLastResponse(pasDiagRespd) < 0 )
  {
    testStepFail(gTestIdStr, "Last Response is not found.");
    return -1;
  }
   
  testStepPass(gTestIdStr, "Last Response is found successfully.");
  if(printDiag != 0) TestReportWriteDiagResponse(pasDiagReqst);                     // write response to report
  lenObtain = diagGetPrimitiveData(pasDiagRespd,respObtain,elCount(respObtain));
  /*Step 1.1, Print Diag Response */
  func_Print_Received_Data(lenObtain, respObtain);
    
  /*Step 2, check Diag response data */
  incTestStepId();
  for(i=0;i<lenExpect;i++)
  {
    incSubStepId();
    if(respExpect[i] != respObtain[i+offset]){
      testStepFail(gTestIdStr,"Received byte[ %d ] 0x%02X not equal to expected 0x%02X.",i+offset, respObtain[i+offset],respExpect[i]);
      fail++;
    }
    else{
      testStepPass(gTestIdStr,"Received byte[ %d ] 0x%02X equals to expected 0x%02X.",i+offset, respObtain[i+offset],respExpect[i]);
    }
  }
  
  if(fail != 0) return -1;
  incSubStepId();
  testStepPass(gTestIdStr, "Received bytes equals to expected bytes.");
  return lenObtain;
     
}

testfunction tstf_Diag_TurnSystemOn()
{
  resetCanEx(1); 
  func_Diag_TurnSystemOn();
  func_Diag_SetTarget();
  func_TestPresent_Ctrl(0);
  tstf_Comm_SetSignal(isVehModMngtGlbSafe1UsgModSts,1);
  tstf_Comm_SetSignal_Chk(isVehSpdLgtA,0);
  testWaitForTimeout(1500);
  setSignal(isGearLvrIndcn,0);
}


int func_Diag_TurnSystemOn()
{
  testWaitForTimeout(200);
  sysSetVariableInt(sysvar::IL::Klemme15, 1);
  testWaitForTimeout(200);
  
//  setSignal(PEPS_PowerModeValidity,2);
//  setSignal(PEPS_PowerMode,2);
//  setSignal(EMS_EngStatus,0);
  
  return 1; 
}

/* Test case condition initialize */
testfunction tstf_Diag_TurnSystemOff()
{
  func_Diag_TurnSystemOff();
}

func_Diag_TurnSystemOff()
{
  func_Diag_Send("10 01",0);
  tstf_Comm_SetSignal(isVehModMngtGlbSafe1UsgModSts,1);
  tstf_Comm_SetSignal_Chk(isVehSpdLgtA,0);
  testWaitForTimeout(2000);
}

/* Test case condition initialize for aging DTC test */
/*Para1, slpTime, optional, ms                       */
testfunction tstf_ECU_SleepAndWakeup()
{
  func_ECU_SleepAndWakeup(8000);
}

testfunction tstf_ECU_SleepAndWakeup(dword slpTime)
{
  func_ECU_SleepAndWakeup(slpTime);
}

func_ECU_SleepAndWakeup(dword slpTime)
{
  @IL::Klemme30 = 0;
  testWaitForTimeout(slpTime);
  @IL::Klemme30 = 1;
  testWaitForTimeout(2000);
}


/// <tstf_PowerCtrl>
/* TOE output OnOff */
testfunction tstf_TOE_Output_OnOff()
{
  func_TOE_Output_OnOff(0);
  testWaitForTimeout(2000);
  func_TOE_Output_OnOff(1);
  testWaitForTimeout(5000);
}

/* ECU reset by service 0x11 */
testfunction tstf_Diag_ECUReset()
{
  func_Diag_ECUReset();
}

func_Diag_ECUReset()
{
  func_Diag_Send("11 01",0);
  func_Diag_Test("51 01");
  testWaitForTimeout(3000);
}

/* Set car configuretion parameter */
testfunction tstf_CarConfigSet(int CCP,byte para)
{
  func_CarConfigSet(CCP,para);
}

func_CarConfigSet(int CCP,byte para)
{
  teststep("","Set CarConfig CCP #%d is %02X",CCP,para);
  switch (CCP){
    case(1):
      @Func::CfgPara1 = para;break;
    case(142):
      @Func::CfgPara142 = para;break;
    case(495):
      @Func::CfgPara495 = para;break;
    case(100):
      @Func::CfgPara100 = para;break;
    case(401):
      @Func::CfgPara401 = para;break;
    case(211):
      @Func::CfgPara211 = para;break;
    case(13):
      @Func::CfgPara13 = para;break;
    case(152):
      @Func::CfgPara152 = para;break;
    case(153):
      @Func::CfgPara153 = para;break;
    case(494):
      @Func::CfgPara494 = para;break;
    case(23):
      @Func::CfgPara23 = para;break;
    case(479):
      @Func::CfgPara479 = para;break;
    case(146):
      @Func::CfgPara146 = para;break;
    case(316):
      @Func::CfgPara316 = para;break;
    case(53):
      @Func::CfgPara53 = para;break;
    case(18):
      @Func::CfgPara18 = para;break;
    case(177):
      @Func::CfgPara177 = para;break;  
    case(154):
      @Func::CfgPara154 = para;break;
    case(58):
      @Func::CfgPara58 = para;break;
    case(220):
      @Func::CfgPara220 = para;break;
    case(8):
      @Func::CfgPara8 = para;break;
    case(10):
      @Func::CfgPara10 = para;break;
    case(17):
      @Func::CfgPara17 = para;break;
    case(139):
      @Func::CfgPara139 = para;break;
    case(140):
      @Func::CfgPara140 = para;break;
    case(156):
      @Func::CfgPara156 = para;break;
    case(197):
      @Func::CfgPara197 = para;break;
    case(409):
      @Func::CfgPara409 = para;break;
    case(483):
      @Func::CfgPara483 = para;break;  
    case(540):
      @Func::CfgPara540 = para;break;
    case(565):
      @Func::CfgPara565 = para;break;
    case(77):
      @Func::CfgPara77 = para;break;
    case(157):
      @Func::CfgPara157 = para;break;
    case(484):
      @Func::CfgPara484 = para;break;
    case(500):
      @Func::CfgPara500 = para;break;  
    case(614):
      @Func::CfgPara614 = para;break;
    case(628):
      @Func::CfgPara628 = para;break;
    default:break;    
}
tstf_Comm_SetSignal(isVehModMngtGlbSafe1UsgModSts,1);
  testWaitForTimeout(1000);
  tstf_Comm_SetSignal(isVehModMngtGlbSafe1UsgModSts,13);
//  @Func::CC_Enable_1 = 1;
    testWaitForTimeout(30000);
}

/* Read session by service 0x22 */
testfunction tstf_Diag_ConfirmSession(byte session)
{
  func_Diag_ConfirmSession(session);
}

func_Diag_ConfirmSession(byte session)
{
  char testData[16];
  char Para[8];
  
  func_Diag_Send("22 F1 86",0);
  Para[0]='\0';
  snprintf(Para,elcount(Para),"%X",session);
  strncpy(testData,"62 F1 86 0",elCount(testData));
  strncat(testData,Para,elCount(testData));
  func_Diag_Test(testData);
}


/* Clear DTC for all group via functional addressing */
testfunction tstf_Diag_ClearDTC_Functional()
{
  func_Diag_ClearDTC_Functional();
}

func_Diag_ClearDTC_Functional()
{
  func_Diag_Send("14 FF FF FF",1);
  func_Diag_Test("54");
//  testWaitForTimeout(500);
}

/// <tstf_DTC>
/* Clear DTC info 14 FF FF FF */
testfunction tstf_Diag_ClearDTC()
{
  func_Diag_ClearDTC();
}

func_Diag_ClearDTC()
{
  func_Diag_Send("14 FF FF FF",0);
  func_Diag_Test("54");
}

/// <tstf_DTC>
/* Read DTC info 19 02 09 */
testfunction tstf_Diag_ReadNoDTC()
{
  func_Diag_ReadNoDTC();
}

func_Diag_ReadNoDTC()
{
  func_Diag_Send("19 02 27",0);
  func_Diag_Test("59 02 ff");
}


/// <tstf_DTC>
/* Read DTC info 19 02 09        */
/* Just read out all DTC, no check*/
testfunction tstf_Diag_ReadOutDTC()
{
  func_Diag_ReadOutDTC();
}

func_Diag_ReadOutDTC()
{
  func_Diag_Send("19 02 09",0);
  func_Diag_Chk_PosResp(0x19);
}

/// <tstf_DTC>
/* API to check DTC status, diag resp of "19 02"    */
/*Para1: dtcStr, is string, but must contain 4bytes */
/* 3 bytes for DTC number, 1 byte for DTC status    */
/* Such as "1B0C5409"                               */
testfunction tstf_Check_DTCStatus(char dtcStr[])
{
  func_Diag_Send("190209", 0);
  func_Check_DTCStatus(dtcStr);
}

/// <tstf_DTC>
testfunction tstf_Check_DTCStatus(char dtcStr1[], char dtcStr2[])
{
  func_Diag_Send("190209", 0);
  func_Check_DTCStatus(dtcStr1);
  func_ContinueCheck_DTCStatus(dtcStr2);
}

/// <tstf_DTC>
testfunction tstf_Check_DTCStatus(char dtcStr1[], char dtcStr2[], char dtcStr3[])
{
  func_Diag_Send("190209", 0);
  func_Check_DTCStatus(dtcStr1);
  func_ContinueCheck_DTCStatus(dtcStr2);
  func_ContinueCheck_DTCStatus(dtcStr3);
}

/// <tstf_DTC>
testfunction tstf_ContinueCheck_DTCStatus(char dtcStr[])
{
  func_ContinueCheck_DTCStatus(dtcStr);
}

int func_Check_DTCStatus(char dtcStr[])
{
  dword lenObtain;
  dword lenExpect;
  byte respObtain[512];
  byte respExpect[512];
  word i;
  
  /* Step 1 check Input Validity */
  setTestId(1);
  lenExpect = stringToBytes(dtcStr, respExpect);
  if(lenExpect == 4 ){                             
    testStepPass(gTestIdStr,"Check DTC Status: [ %s ] [ %d ] Bytes.", dtcStr, lenExpect);          // expect input is valid
  }
  else{
    testStepFail(gTestIdStr,"Expected DTC is inValid: [ %s ] [ %d ] Bytes.", dtcStr, lenExpect);
    return -1;
  }
  
  /*Step 2, Wait and Get Diag Response */
  incTestStepId();
  if (1 != testWaitForDiagResponse(pasDiagReqst, cApplicationTimeoutMs) ) {          
    testStepFail(gTestIdStr, "Valid response missing or received too late!");
    return -1;
  }

  testStepPass(gTestIdStr, "Response received successfully.");
  
  if(printDiag != 0) TestReportWriteDiagResponse(pasDiagReqst);                     // write response to report
  diagGetLastResponse(pasDiagRespd);
  lenObtain = diagGetPrimitiveData(pasDiagRespd, respObtain, elCount(respObtain));
  /*Step 2.1, Print Diag Response */
  func_Print_Received_Data(lenObtain, respObtain);
  
  
  /*If Setps, Check Diag response is related to DTC reading*/
  if( respObtain[0] != 0x59 || respObtain[1] != 0x02){                  // not DTC reading service "59 02"
    incTestStepId();
    testStepFail(gTestIdStr, "Diag Response is not 0x59. Received Bytes[ %d ].", lenObtain);
    return -1;
  }
  
  if( lenObtain < 7 ){                       // no DTC reported 
    incTestStepId();
    testStepFail(gTestIdStr, "No DTC reported. Received Bytes[ %d ].", lenObtain);
    return -1;
  }
      
  /*Step3: Search expected DTC is in Diag Response*/
  incTestStepId();
  for(i=3;i<lenObtain;i+=4)
  {
    if(respExpect[0]==respObtain[i] && respExpect[1]==respObtain[i+1] && respExpect[2]==respObtain[i+2])      // DTC is found
    {
      if(respExpect[3]==respObtain[i+3]){
        testStepPass(gTestIdStr,"DTC [ %s ] is Found and DTC Status is Matched !", dtcStr);
        return lenObtain;
      }
      else{
        testStepFail(gTestIdStr,"DTC [ %s ] is Found but DTC Status is not Matched ! Obtained [ %d ].", dtcStr, respObtain[i+3]);
        return -1;
      }
    }
  }
    
  testStepFail(gTestIdStr,"DTC [ %s ] is not Found !", dtcStr);           //DTC is not found
  return -1;
}

int func_ContinueCheck_DTCStatus(char dtcStr[])
{
  dword lenObtain;
  dword lenExpect;
  byte respObtain[512];
  byte respExpect[512];
  word i;
  
  /* Step 1 check Input Validity */
  setTestId(1);
  lenExpect = stringToBytes(dtcStr, respExpect);
  if(lenExpect == 4 ){                             
    testStepPass(gTestIdStr,"Check DTC Status: [ %s ] [ %d ] Bytes.", dtcStr, lenExpect);          // expect input is valid
  }
  else{
    testStepFail(gTestIdStr,"Expected DTC is inValid: [ %s ] [ %d ] Bytes.", dtcStr, lenExpect);
    return -1;
  }
  
  /*Step 2, Directly get Diag Response */
  if( diagGetLastResponse(pasDiagRespd) < 0 )
  {
    testStepFail(gTestIdStr, "Last Response is not found.");
    return -1;
  }
  testStepPass(gTestIdStr, "Last Response is found successfully.");
  if(printDiag != 0) TestReportWriteDiagResponse(pasDiagReqst);                                         // write response to report
  lenObtain = diagGetPrimitiveData(pasDiagRespd, respObtain, elCount(respObtain));
  /*Step 2.1, Print Diag Response */
  func_Print_Received_Data(lenObtain, respObtain);
  
  /*Check Diag response is related to DTC reading*/
  if( respObtain[0] != 0x59 || respObtain[1] != 0x02){                  // not DTC reading service "59 02"
    testStepFail(gTestIdStr, "Diag Response is not 0x59. Received Bytes[ %d ].", lenObtain);
    return -1;
  }
  
  if( lenObtain < 7 ){                       // no DTC reported 
    testStepFail(gTestIdStr, "No DTC reported. Received Bytes[ %d ].", lenObtain);
    return -1;
  }
      
  /*Step3: Search expected DTC is in Diag Response*/
  incTestStepId();
  for(i=3;i<lenObtain;i+=4)
  {
    if(respExpect[0]==respObtain[i] && respExpect[1]==respObtain[i+1] && respExpect[2]==respObtain[i+2])      // DTC is found
    {
      if(respExpect[3]==respObtain[i+3]){
        testStepPass(gTestIdStr,"DTC [ %s ] is Found and DTC Status is Matched !", dtcStr);
        return lenObtain;
      }
      else{
        testStepFail(gTestIdStr,"DTC [ %s ] is Found but DTC Status is not Matched ! Obtained [ %d ].", dtcStr, respObtain[i+3]);
        return -1;
      }
    }
  }
    
  testStepFail(gTestIdStr,"DTC [ %s ] is not Found !", dtcStr);           //DTC is not found
  return -1;
}

/* API to check DTC Snapshot ID*/
int func_Check_DTCSnapID(char dtcStr[])
{
  dword lenObtain;
  dword lenExpect;
  byte respObtain[512];
  byte respExpect[512];
  word i;
  
  /* Step 1 check Input Validity */
  setTestId(1);
  lenExpect = stringToBytes(dtcStr, respExpect);
  if(lenExpect == 4 ){                             
    testStepPass(gTestIdStr,"Check DTC Status: [ %s ] [ %d ] Bytes.", dtcStr, lenExpect);          // expect input is valid
  }
  else{
    testStepFail(gTestIdStr,"Expected DTC is inValid: [ %s ] [ %d ] Bytes.", dtcStr, lenExpect);
    return -1;
  }
  
  /*Step 2, wait for Diag Response */
  incTestStepId();
  if (1 != testWaitForDiagResponse(pasDiagReqst, cApplicationTimeoutMs) ) {          
    testStepFail(gTestIdStr, "Valid response missing or received too late!");
    return -1;
  }

  testStepPass(gTestIdStr, "Response received successfully.");
  if(printDiag != 0) TestReportWriteDiagResponse(pasDiagReqst);                     // write response to report
  
  diagGetLastResponse(pasDiagRespd);
  lenObtain = diagGetPrimitiveData(pasDiagRespd, respObtain, elCount(respObtain));
  /*Step 2.1, Print Diag Response */
  func_Print_Received_Data(lenObtain, respObtain);
  
  /*If Steps, Check Diag response is related to DTC reading*/
  if( respObtain[0] != 0x59 || respObtain[1] != 0x03){                  // not DTC reading service "59 02"
    incTestStepId();
    testStepFail(gTestIdStr, "Diag Response is not 0x59. Received Bytes[ %d ].", lenObtain);
    return -1;
  }
  
  if( lenObtain < 6 ){                       // no DTC reported 
    incTestStepId();
    testStepFail(gTestIdStr, "No DTC reported. Received Bytes[ %d ].", lenObtain);
    return -1;
  }
      
  /*Step3: Search expected DTC is in Diag Response*/
  incTestStepId();
  for(i=2;i<lenObtain;i+=4)
  {
    if(respExpect[0]==respObtain[i] && respExpect[1]==respObtain[i+1] && respExpect[2]==respObtain[i+2])      // DTC is found
    {
      if(respExpect[3]==respObtain[i+3]){
        testStepPass(gTestIdStr,"DTC [ %s ] is Found and DTC Status is Matched !", dtcStr);
        return lenObtain;
      }
      else{
        testStepFail(gTestIdStr,"DTC [ %s ] is Found but DTC Status is not Matched ! Obtained [ %d ].", dtcStr, respObtain[i+3]);
        return -1;
      }
    }
  }
    
  testStepFail(gTestIdStr,"DTC [ %s ] is not Found !", dtcStr);           //DTC is not found
  return -1;
}

/// <tstf_CanCom>
/* To set a signal to a specific value */
testfunction tstf_Comm_SetSignal(signal * targetSig, float valueSet)
{
  func_Comm_SetSignal(targetSig, valueSet);
}

int func_Comm_SetSignal(signal * targetSig, float valueSet)
{
  char buffer[64];
  buffer[0] = '\0';
  
  getSignalDescriptionForValue(targetSig, valueSet, buffer, elCount(buffer));
  
  setTestId(1);
  teststep(gTestIdStr,"Set signal %s to: [%f][%s]", targetSig.name, valueSet, buffer);    //
  
  setsignal(targetSig, valueSet);                    // set physical value
  
  return 1;
}


/// <tstf_CanCom>
/* To set a signal to a specific value via sig name */
/*Para1, targetSig, sinal name, string              */
testfunction tstf_Comm_SetSignal(char targetSig[], float valueSet)
{
  func_Comm_SetSignal(targetSig, valueSet);
}

int func_Comm_SetSignal(char targetSig[], float valueSet)
{
  setTestId(1);    
  setsignal(targetSig, valueSet);                    // set physical value
  
  testStepPass(gTestIdStr,"Set signal %s to: [%f]", targetSig, valueSet);
  
  return 1;
}


/// <tstf_Diag_Basic>
/*check response length of all raw bytes       */
/*Para1, lenExpect, expected bytes of Response */
testfunction tstf_Diag_Test_Response_Length(dword lenExpect)
{
  func_Diag_Test_Response_Length(lenExpect);
}

int func_Diag_Test_Response_Length(dword lenExpect)
{
  dword lenObtain;
  byte respObtain[2056];
  byte respExpect[2056];
  
  setTestId(1);
  teststep(gTestIdStr,"Check Diag Response Length: [%d]", lenExpect);
  
  if (1 != testWaitForDiagResponse(pasDiagReqst, cApplicationTimeoutMs) ) {          
    testStepFail(gTestIdStr, "Valid response missing or received too late!");
    return -1;
  }
  
  testStepPass(gTestIdStr, "Response received successfully.");
  if(printDiag != 0) TestReportWriteDiagResponse(pasDiagReqst);                     // write response to report
  
  diagGetLastResponse(pasDiagRespd);
  lenObtain = diagGetPrimitiveData(pasDiagRespd,respObtain,elCount(respObtain));
  /*Step 1.1, Print Diag Response */
  func_Print_Received_Data(lenObtain, respObtain);
  
  /*Step2, Check Diag Response Length*/
  incTestStepId();
  if(lenObtain != lenExpect)
  {
    testStepFail(gTestIdStr,"Received bytes [ %d ] not equal to expected [ %d ].",lenObtain, lenExpect);
    return -1;
  }
  else
  {
    testStepPass(gTestIdStr, "Received bytes [ %d ] equals to expected bytes [ %d ].",lenObtain,lenExpect);
  }
    
  return lenObtain;
}




word SendRequestAndWaitForResponse(diagRequest *req, enum bool posResponseExpected)
{
  long ret;
  // print diag data to report.
  testReportWriteDiagObject(req);
  
  // Sending the request
  if (0 > (ret=req.SendRequest())) { 
    snprintf(gResultString, elcount(gResultString), "Sending the request failed (Return code=%d)!", ret);
    testStepFail(gTestIdStr, gResultString);
    return 0;
  }
  //testStepPass(gTestIdStr, "Sending the request succeded.");

  //incTestStepId();
  // Wait until the complete request has been sent, e.g. in case of long requests which spread over several messages (segmented message)
  if (1!=(ret=testWaitForDiagRequestSent(req, cApplicationTimeoutMs))){ 
    snprintf(gResultString, elcount(gResultString), "Failed to finish sending the request (Return code=%d)!", ret);
    testStepFail(gTestIdStr, gResultString);
    return 0;
  }
  testStepPass(gTestIdStr, "Request was sent successfully.");

  incTestStepId();
  // Wait until the complete response has been received, e.g. segmented messages might take some time for transmission
  if (1!=(ret=testWaitForDiagResponse(req, cApplicationTimeoutMs))) {          
    snprintf(gResultString, elcount(gResultString), "Valid response missing or received too late (Return code=%d)!", ret);
    testStepFail(gTestIdStr, gResultString);
    return 0;
  }
  testStepPass(gTestIdStr, "Response received successfully.");
  TestReportWriteDiagResponse(req); // write response to report

  incTestStepId();
  // Check whether the response was a positive response
  if (-1==(ret=diagGetLastResponseCode(req))) {
    if (!posResponseExpected) {
      snprintf(gResultString, elcount(gResultString), "Positive response received although negative response was expected!");
      testStepFail(gTestIdStr, gResultString);
      return 0;
    }
    testStepPass(gTestIdStr, "Positive Response received as expected.");
  }
  else if (ret>0) {
    if (posResponseExpected) {
      snprintf(gResultString, elcount(gResultString), "Negative response received (NRC=0x%02x) although positive response was expected!", ret);
      testStepFail(gTestIdStr, gResultString);
      return 0;
    }
    testStepPass(gTestIdStr, "Negative Response received as expected (NRC=%d).", ret);
  }
  return 1;
}

/// <tstf_Diag_Basic>
testfunction tstf_Diag_SetTarget()
{
  func_Diag_SetTarget();           // Geely_FS11_PAS3
}

int func_Diag_SetTarget()
{
  diagSetTarget(EcuTargert);  // Geely_FS11_PAS3
  return 1;
}

/// <tstf_Diag_Basic>
testfunction tstf_Diag_CloseChn()
{
  long i;
  char ecuQual[100];
  i = diagGetTargetCount();
  while( i-- > 0)
  {
     diagGetTargetQualifier( i, ecuQual, elcount(ecuQual));
     write( "Target %d: %s", i, ecuQual);
  }

  
  diagCloseChannel(EcuTargert);
}

/// <tstf_Diag_Basic>
/* Tester Present on/off      */
/* para1: on_off{0-off, 1-on} */
testfunction tstf_Diag_tstPresent(byte on_off)
{
  func_TestPresent_Ctrl(on_off);
}

int func_TestPresent_Ctrl(byte on_off)
{
  setTestId(1);
  
  if(0 != on_off){
    diagStartTesterPresent(EcuTargert);
    testWaitForTimeout(50);
    testStepPass(gTestIdStr, "Set Tester Present On [%d].",diagGetTesterPresentState(EcuTargert));
    
  }
  else{
    diagStopTesterPresent(EcuTargert);
    testWaitForTimeout(50);
    testStepPass(gTestIdStr, "Set Tester Present Off [%d].",diagGetTesterPresentState(EcuTargert));
    
  }
  
  return 1;
}



/* Below function is for CAN trace logging */
testfunction tstf_TestLog_Start(char logName[])
{
  func_TestLogControl(logName, "", 1);
}

testfunction tstf_TestLog_Stop()
{
  func_TestLogControl("", "", 0);
}
testfunction tstf_TestLog_Start_Path(char logName[], char logPathNew[])
{
  func_TestLogControl(logName, logPathNew, 1);
}


int func_TestLogControl(char logName[], char logPathNew[], byte on_off)
{
  char logPath[256];
  char logTime[16];

  func_get_TimeString(logTime);
  
  if(on_off == 0x01)
  {    
    if(strncmp(logPathNew,"",elCount(logPathNew)) == 0)
    {
      strncpy(logPath, "\\Test\\TraceLog\\", elCount(logPath));
    }else{
      strncpy(logPath, logPathNew, elCount(logPath));
    }
    strncat(logPath, logName, elCount(logPath)); 
//    strncat(logPath, "_M{MeasurementIndex}_", elCount(logPath));
    strncat(logPath, logTime, elCount(logPath));
    strncat(logPath, ".blf", elCount(logPath));
    
    setLogFileName("Logging", logPath);
    sysSetVariableInt(sysvar::Func::LogEnable, 0x01);
    testStepPass("1", "Start CAN Trace Log: %s", logPath);
  }
  else if(on_off == 0x00)
  {
    sysSetVariableInt(sysvar::Func::LogEnable, 0x00);
    testWaitForTimeout(5);
    stopLogging ();
    
    logPath[0] = '\0';                                     // clear logPath
    testStepPass("2", "Stop CAN Trace Logging.");
  }
  
  return 1;
 }

/*Function to get time String HH-MM-SS*/
void func_get_TimeString(char logTime[])
{
  long timeBuff[9];
  
  getLocalTime(timeBuff);
  snprintf(logTime, elCount(logTime), "_%02d_%02d_%02d_%02d", timeBuff[3],timeBuff[2],timeBuff[1],timeBuff[0]);
}

/// <tstf_CanCom>
/* To Print Sig Value in the report      */
testfunction tstf_Comm_ReportSignal(signal *sig)
{
  func_Comm_ReportSignal(sig);
}
/// <tstf_CanCom>
/* To Print Sig Value in the report      */
testfunction tstf_Comm_ReportSignal_Double(signal *sig1, signal *sig2)
{
  func_Comm_ReportSignal(sig1);
  func_Comm_ReportSignal(sig2);
}
/// <tstf_CanCom>
testfunction tstf_Comm_ReportSignal_Triple(signal *sig1, signal *sig2, signal *sig3) 
{
  func_Comm_ReportSignal(sig1);
  func_Comm_ReportSignal(sig2);
  func_Comm_ReportSignal(sig3);
}

/// <tstf_CanCom>
testfunction tstf_Comm_ReportSignal_Sextuple(signal *sig1, signal *sig2, signal *sig3,signal *sig4, signal *sig5, signal *sig6)
{
  func_Comm_ReportSignal(sig1);
  func_Comm_ReportSignal(sig2);
  func_Comm_ReportSignal(sig3);
  func_Comm_ReportSignal(sig4);
  func_Comm_ReportSignal(sig5);
  func_Comm_ReportSignal(sig6);
}

int func_Comm_ReportSignal(signal *sig)
{
  float sigValue;
  char buffer[64];
  
  buffer[0] = '\0';
  //strncpy(buffer, "", elCount(buffer));      // clear buffer
  
  sigValue = getsignal(sig);
  getSignalDescriptionForValue(sig, sigValue, buffer, elCount(buffer));
  
  TestCaseReportMeasuredValue(sig.name, sigValue, buffer);
  return 1;
}

/// <tstf_CanCom>
/*To Report measured value of a Sys Variable Int */
testfunction tstf_Comm_ReportSysVarInt(sysvarInt *aSysVar)
{
  func_Comm_ReportSysVarInt(aSysVar);
}

/// <tstf_CanCom>
testfunction tstf_Comm_ReportSysVarInt_Triple(sysvarInt *aSysVar1, sysvarInt *aSysVar2, sysvarInt *aSysVar3)
{
  func_Comm_ReportSysVarInt(aSysVar1);
  func_Comm_ReportSysVarInt(aSysVar2);
  func_Comm_ReportSysVarInt(aSysVar3);
}

int func_Comm_ReportSysVarInt(sysvarInt *aSysVar)
{
  char buffer[64];
  
  buffer[0] = '\0';
  
  sysGetVariableDescriptionForValue(aSysVar, @aSysVar, buffer, elCount(buffer));
  
  TestCaseReportMeasuredValue(aSysVar.name, @aSysVar, buffer);
  return 1;
}

/// <tstf_CanCom>
/*To Report measured value of a Sys Variable float */
testfunction tstf_Comm_ReportSysVarFloat(sysvarFloat *aSysVar)
{
  func_Comm_ReportSysVarFloat(aSysVar);
}

/// <tstf_CanCom>
testfunction tstf_Comm_ReportSysVarFloat_Triple(sysvarFloat *aSysVar1, sysvarFloat *aSysVar2, sysvarFloat *aSysVar3)
{
  func_Comm_ReportSysVarFloat(aSysVar1);
  func_Comm_ReportSysVarFloat(aSysVar2);
  func_Comm_ReportSysVarFloat(aSysVar3);
}

int func_Comm_ReportSysVarFloat(sysvarFloat *aSysVar)
{
  char buffer[64];
  
  buffer[0] = '\0';
  
  sysGetVariableDescriptionForValue(aSysVar, @aSysVar, buffer, elCount(buffer));
  
  TestCaseReportMeasuredValue(aSysVar.name, @aSysVar, buffer);
  return 1;
}

/// <tstf_CanCom>
/* Set Signal to a value and check it within 300ms      */
testfunction tstf_Comm_SetSignal_Chk(signal *aSig, float val)
{
  func_Comm_SetSig_Chk(aSig, val);
}

int func_Comm_SetSig_Chk(signal *aSig, float val)
{
  long result;
  char buffer[64];
  
  buffer[0] = '\0';
  getSignalDescriptionForValue(aSig, val, buffer, elCount(buffer));
  
  
  /*Step 1 to set a signal value*/
  $aSig.phys = val;
  
  result = testWaitForSignalMatch(aSig, val, 3000);
  
  if(1 != result){
    testStepFail("1","%s \n value is [ %.3f ], failed to set as expected [ %.3f ] [ %s ].",aSig.name, $aSig.phys,  val, buffer);
    return -1;
  }
  else{
    testStepPass("1","%s \n value is [ %.3f ], set as expected [ %.3f ] [ %s ].",aSig.name, $aSig.phys, val, buffer);
  }
  
  return 1;
}

/// <tstf_CanCom>
/* Set Sysvar Int to a value  */
testfunction tstf_Comm_SetSysInt(sysvarInt *aSig, dword val)
{
  func_Comm_SetSysInt(aSig, val);
}

int func_Comm_SetSysInt(sysvarInt *aSig, dword val)
{
  long result;
  char buffer[64];
  
  buffer[0] = '\0';
  
  if(0x0 != sysGetVariableDescriptionForValue(aSig, val, buffer, elCount(buffer)) ){   //0, no error, function successful
    buffer[0] = '\0';
  }
    
  
  /*Step 1 to set a signal value*/
  @aSig = val;
  
  testStepPass("1","%s \n value is [ %d ], set as expected [ %d ] [ %s ].",aSig.name, @aSig, val, buffer);

  
  return 1;
}


/// <tstf_CanCom>
/* Awiat signal match within a time and      */
/* re-check it after 1000ms                  */
testfunction tstf_Comm_CheckSignal(signal *sig, long val)
{
  func_Comm_Sig_DoubleCheck(sig, val, 1000, 1000);
}

/// <tstf_CanCom>
/* Awiat signal match and check only once     */
testfunction tstf_Comm_AwaitSignalMatch(signal *sig, float val, dword timeout)
{
  func_Comm_Sig_DoubleCheck(sig, val, timeout, 0);
}

/// <tstf_CanCom>
testfunction tstf_Comm_AwaitSignalMatch(signal *sig, float val, dword timeout, dword timeout_2nd)
{
  func_Comm_Sig_DoubleCheck(sig, val, timeout, timeout_2nd);
}

int func_Comm_Sig_DoubleCheck(signal *sig, float val, dword timeout, dword timeout_2nd)
{
  long result;
  char buffer[64];
  
  buffer[0] = '\0';
  
  getSignalDescriptionForValue(sig, val, buffer, elCount(buffer));
  
  /*Step 1 To await signal value match within a time*/
  //testStep("0","To await sig [%s] value matches [ %.3f ][ %s ] within [ %d ] ms",sig.name, val, buffer, timeout);
  
  /*Step 2 Start to await and check resut */
  result = TestWaitForSignalMatch(sig, val, timeout);
  if(1 != result){
    testStepFail("1","%s \n obtained value is [ %.3f ], not as expected [ %.3f ] [ %s ].",sig.name, $sig.phys,  val, buffer);
    return -1;
  }
  else{
    testStepPass("1","%s \n obtained value is [ %.3f ], same as expected [ %.3f ] [ %s ].",sig.name, $sig.phys, val, buffer);
  }

  /*Step 3 wait for 1000ms  and re-check the signal value*/
  if(timeout_2nd <= 0)
    return 1;
  
  testWaitForTimeout(timeout_2nd);
  result = testValidateSignalMatch("2nd_check_signal", sig, val);                  // 0-means this API executes OK, not mean signal check OK.
  if (result != 0)
  {
    //testStepFail("2","Sig 2nd check: %s \n obtained value is [ %.3f ], not as expected [ %.3f ] [ %s ].",sig.name, $sig.phys, val, buffer);
    return -1;
  }
  else{
     //testStepPass("2","Sig 2nd check: %s \n obtained value is [ %.3f ], same as expected [ %.3f ] [ %s ].",sig.name, $sig.phys, val, buffer);
  }
  
  return 1;
}
/// <tstf_CanCom>
testfunction tstf_Comm_AwaitSignalNotMatch(signal *sig, float val, dword timeout)
{
  func_Comm_Sig_CheckNotMatch(sig, val, timeout);
}

int func_Comm_Sig_CheckNotMatch(signal *sig, float val, dword timeout)
{
  long result;
  char buffer[64];
  
  buffer[0] = '\0';
  
  getSignalDescriptionForValue(sig, val, buffer, elCount(buffer));
  
  /*Step 1 To await signal value match within a time*/
  //testStep("0","To await sig [%s] value matches [ %.3f ][ %s ] within [ %d ] ms",sig.name, val, buffer, timeout);
  
  /*Step 2 Start to await and check resut */
  result = TestWaitForSignalMatch(sig, val, timeout);
  if(1 == result){
    testStepFail("1","%s \n obtained value is [ %.3f ], matched as expected [ %.3f ] [ %s ].",sig.name, $sig.phys,  val, buffer);
    return -1;
  }
  else{
    testStepPass("1","%s \n obtained value is [ %.3f ], not matched as expected [ %.3f ] [ %s ].",sig.name, $sig.phys, val, buffer);
  }
  
  return 1;
}
/// <tstf_CanCom>
testfunction tstf_Comm_AwaitSignal_Among_Values(signal *sig, float val1, float val2, float val3, dword timeout)
{
  func_Comm_AwaitSig_Among_Values(sig, val1, val2, val3, timeout);
}

int func_Comm_AwaitSig_Among_Values(signal *sig, float val1, float val2, float val3, dword timeout)
{
  long result1, result2, result3, verdict;
  float val;
  char buffer1[64], buffer2[64], buffer3[64], buffer9[64], buffer0[64];
  dword qT_ms = 50;           //50ms
  dword elapseTime;           // ms, elapsed time, or accumulated
  byte index;
  
  buffer0[0] = buffer1[0] = buffer2[0] =buffer3[0] =buffer9[0] = '\0';
  elapseTime = 0;                                            // query timer init
  verdict  = 0x0;                                            // 0 mean sig val not happened, 1 means happended
  index    = 0x0;
  
  getSignalDescriptionForValue(sig, val1, buffer1, elCount(buffer1));
  getSignalDescriptionForValue(sig, val2, buffer2, elCount(buffer2));
  getSignalDescriptionForValue(sig, val3, buffer3, elCount(buffer3));
  
  /*Step 1 To await signal value match within a time*/
  testStepPass("0","Within [ %d ] ms, to await sig [%s] value is among \n [ %.3f ][ %s ]\n [ %.3f ][ %s ]\n [ %.3f ][ %s ]\n ",timeout,sig.name, val1,buffer1, val2,buffer2, val3,buffer3);
  
  /*Step 2 Start to await and check resut */
  do{
      if( 0x1== CheckSignalMatch(sig, val1) ){
        verdict = 0x1; val = val1;
      }
      else if(0x1== CheckSignalMatch(sig, val2)){
        verdict = 0x1; val = val2;
      }
      else if(0x1== CheckSignalMatch(sig, val3)){
        verdict = 0x1; val = val3;
      }
      else{}
      
      elapseTime += qT_ms;
      testWaitForTimeout(qT_ms);
      //write("Tcounter=%d, timeout=%d, verdict=%d", elapseTime, timeout, verdict);
  }
  while(elapseTime < timeout && verdict != 0x1 );
  
  if(1 != verdict){
    getSignalDescriptionForValue(sig, $sig, buffer9, elCount(buffer9));
    testStepFail("1","%s \n obtained value is [ %.3f ] [ %s ], not as expected [ %.3f ] [ %.3f ] [ %.3f ] .",sig.name, $sig.phys, buffer9, val1,val2,val3);
    return -1;
  }
  else{
    getSignalDescriptionForValue(sig, val, buffer0, elCount(buffer0));
    testStepPass("1","%s \n obtained value is [ %.3f ], same as expected [ %.3f ] [ %s ].",sig.name, $sig.phys, val, buffer0);
  }
  
  return 1;
}


/// <tstf_CanCom>
/* Awiat signal is in a range     */
testfunction tstf_Comm_AwaitSignalInRange(signal *aSig, float aLowLimit, float aHighLimit, dword aTimeout)
{
  func_Comm_AwaitSignalInRange(aSig, aLowLimit , aHighLimit, aTimeout);
}

int func_Comm_AwaitSignalInRange(signal *aSig, float aLowLimit, float aHighLimit, dword aTimeout)
{
  long result;

  /*Step 1 Start to await and check resut */
  result = testWaitForSignalInRange(aSig, aLowLimit, aHighLimit, aTimeout);
  if(1 != result){
    testStepFail("1","%s \n obtained value is [ %.3f ], not as expected [ %.3f, %.3f ].", aSig.name, $aSig.phys, aLowLimit, aHighLimit);
    return -1;
  }
  else{
    testStepPass("1","%s \n obtained value is [ %.3f ], same as expected [ %.3f, %.3f ].", aSig.name, $aSig.phys, aLowLimit, aHighLimit);
  }
  
  return 1;
}


/// <tstf_CanCom>
/* Awiat sys variable, ect is in a range     */
testfunction tstf_Comm_AwaitSignalInRange(sysvarFloat *aSysvar, float aLowLimit, float aHighLimit, dword aTimeout)
{
  func_Comm_AwaitSignalInRange(aSysvar, aLowLimit , aHighLimit, aTimeout);
}

int func_Comm_AwaitSignalInRange(sysvarFloat *aSysvar, float aLowLimit, float aHighLimit, dword aTimeout)
{
  long result;
 
  /*Step 1 Start to await and check resut */
  result = testWaitForSignalInRange(aSysvar, aLowLimit, aHighLimit, aTimeout);
  if(1 != result){
    testStepFail("1","%s \n obtained value is [ %.3f ], not as expected [ %.3f, %.3f ].", aSysvar.name, @aSysvar, aLowLimit, aHighLimit);
    return -1;
  }
  else{
    testStepPass("1","%s \n obtained value is [ %.3f ], same as expected [ %.3f, %.3f ].", aSysvar.name, @aSysvar, aLowLimit, aHighLimit);
  }
  
  return 1;
}

/// <tstf_CanCom>
/* Awiat sys variable, ect is in a range     */
testfunction tstf_Comm_AwaitSignalInRange(sysvarInt *aSysvar, long aLowLimit, long aHighLimit, dword aTimeout)
{
  func_Comm_AwaitSignalInRange(aSysvar, aLowLimit , aHighLimit, aTimeout);
}

int func_Comm_AwaitSignalInRange(sysvarInt *aSysvar, long aLowLimit, long aHighLimit, dword aTimeout)
{
  long result;
 
  /*Step 1 Start to await and check resut */
  result = testWaitForSignalInRange(aSysvar, aLowLimit, aHighLimit, aTimeout);
  if(1 != result){
    testStepFail("1","%s \n obtained value is [ %d ], not as expected [ %d, %d ].", aSysvar.name, @aSysvar, aLowLimit, aHighLimit);
    return -1;
  }
  else{
    testStepPass("1","%s \n obtained value is [ %d ], same as expected [ %d, %d ].", aSysvar.name, @aSysvar, aLowLimit, aHighLimit);
  }
  
  return 1;
}

/// <tstf_Diag_Services>
/*To enter session:   */
/*0x1 default session,0x2 programming session,0x3 extend session*/
testfunction tstf_Diag_EnterSession(byte session)
{
  func_Diag_EnterSession(session);
}

func_Diag_EnterSession( byte session)
{
  char sendData[10];
  char Para[3];
  
  sendData[0]='\0';
  strncpy(sendData, "10 0",elCount(sendData));
  ltoa(session,Para,16);
  strncat(sendData,Para,elCount(sendData));
  
  func_Diag_Send(sendData,0);
  func_Diag_Chk_PosResp(0x10);
   
}


/// <tstf_Diag_Basic>
/*To check positive response received */
/* Para1, Service, such 0x22, 0x19    */
testfunction tstf_Diag_Chk_PosResp(dword Service)
{
  func_Diag_Chk_PosResp(Service);
}

int func_Diag_Chk_PosResp(dword Service)
{
  long Ret;
 
  /*Step1, Wait Diag Response */
 setTestId(1);
 teststep(gTestIdStr,"Check Positive Response of Service: [ 0x%X ]", Service);
 
 if (1 != testWaitForDiagResponse(pasDiagReqst, cApplicationTimeoutMs) ) {          
   snprintf(gResultString, elcount(gResultString), "Valid response missing or received too late!");
   testStepFail(gTestIdStr, gResultString);
   return -1;
 }
 
 testStepPass(gTestIdStr, "Response received successfully.");
 if(printDiag != 0) TestReportWriteDiagResponse(pasDiagReqst);                     // write response to report
 
 /*Step2, Get and check Response Code */
 incTestStepId();
 Ret = diagGetLastResponseCode (pasDiagReqst);
 
 if(Ret == -1)     // positive response
 {
   testStepPass(gTestIdStr,"Positive Response is Received.");
   return 1;
 }
 else if(Ret > 0)
 {
   testStepFail(gTestIdStr,"Negative response is received and NRC is [ 0x%X ]", Ret);
    return -1;
 }
 else if(Ret == 0){
   testStepFail(gTestIdStr,"Response is not received! [ %d ]", Ret);
   return -1;
 }
 else
 {
   testStepFail(gTestIdStr,"Other Failures [%d]", Ret);
   return -1;
 }
   

 return 1;
}

/// <tstf_Diag_Services>
/* To test all unsupported services by physical addressing*/
testfunction tstf_Diag_Send_Unsupported_Services()
{
  func_Diag_Send_Unsupported_Services(0);  
}

/// <tstf_Diag_Services>
/* To test all unsupported services by functional addressing*/
testfunction tstf_Diag_Send_Unsupported_Services_Functional()
{
  func_Diag_Send_Unsupported_Services(1);  
}

int func_Diag_Send_Unsupported_Services(byte addressing)
{
  byte Services[15] = {0x10,0x11,0x14,0x19,0x22,0x27,0x28,0x2E,0x2F,0x31,0x34,0x36,0x37,0x3E,0x85};
  long loop;
  long index;
  char sendData[10];
  char str[5];
  int Ser;  
  
  for(loop=0;loop<=0xff;loop++)
  {
    Ser = 0;
    for(index=0;index<15;index++)
    {
      if(loop == Services[index])
      {
        Ser++;
      }
    }   
      if(Ser == 0)
      {
        sendData[0]='\0';
        
        if(loop<16)
        {
          strncpy(sendData,"0",elCount(sendData));
          ltoa(loop,str,16);
          strncat(sendData,str,elCount(sendData));
        }
        else
        {          
          ltoa(loop,sendData,16);
        }         
        
        if(addressing == 0)
          {
            func_Diag_Send(sendData,0); // physical
//            func_Diag_Chk_NRC(0x11); 
            testWaitForTimeout(50);
          }
       else
          {           
            func_Diag_Send(sendData,1); // functional
            func_Diag_NoResponse(50);
             testWaitForTimeout(50);
          }
      }     
  } 
  return 1;
}


/// <tstf_Diag_Services>
/* To test all unsupported services by functional addressing*/
testfunction tstf_Diag_Unsupported_Subfunction_0x27()
{
  func_Diag_Unsupported_Subfunction_0x27(1);  
}

int func_Diag_Unsupported_Subfunction_0x27(byte addressing)
{
  long loop;
  long index;
  char sendData[16];
  char str[8];
  int Ser;  
  
  for(loop=0;loop<=0xff;loop++)
  {
    sendData[0]='\0';
    snprintf(str,elCount(str),"%02X",loop);
    strncpy(sendData,"27 ",elCount(sendData));
    strncat(sendData,str,elCount(sendData));
    
    func_Diag_Send(sendData,0);
    testWaitForTimeout(500);
  } 
  return 1;
}

/// <tstf_Security_Unlock>
/*Continue to check seed value always have a non zero value and not all 0xFF values */
/*Parameter len is the length of security seed*/
/*for example:if seed is 4 bytes,the len is 4*/
testfunction tstf_Diag_SeedValueTest(word len)
{
  func_Diag_SeedValueTest(len);
}

int func_Diag_SeedValueTest(word len)
{
  word lenObtain;
//  word lenExpect;
  byte respObtain[512];
//  byte respExpect[512];
  word i;
  word v1;
  word v2;
  
  v1 = 0;
  v2 = 0;
  setTestId(1);
  
  if( 0<= diagGetLastResponse(pasDiagRespd) )
  {
    testStepPass(gTestIdStr, "Last Response is found successfully.");
    if(printDiag != 0) TestReportWriteDiagResponse(pasDiagReqst);                     // write response to report
  }
  else
  {
    testStepFail(gTestIdStr, "Last Response is not found.");
    return -1;
  }
  
  incTestStepId();  
  lenObtain = diagGetPrimitiveData(pasDiagRespd,respObtain,elCount(respObtain));
  if((lenObtain-2) != len)
  {
    testStepFail(gTestIdStr, "Parameter input of len [%d] doesn't match tstString length [%d].",lenObtain,len);
    return -1;
  }
//  len = lenObtain - 2;
  
  incTestStepId();
  for(i=2;i<len;i++)
  {
    if(respObtain[i] == 0x00)
    {
      v1++;
    }
    if(respObtain[i] == 0xff)
    {
      v2++;
    }
  }
  
  if(v1 == len || v2 == len)
  {
    testStepFail(gTestIdStr, "Seed value is always zero value or all 0xFF values");
    return -1;
  }
  else
  {
    testStepPass(gTestIdStr, "Seed value is not always zero value or all 0xFF values.");
  }
  
  return 1;
     
}

/// <tstf_CanCom>
/*This API must be called in each test case before you call */
/* tstf_ChkStart_Signal or tstf_ChkStart_SysVar             */
testfunction tstf_ChkInit()
{
  func_ChkInit();
}

int func_ChkInit()
{
  ChkCnt_Sig = 0;
  ChkCnt_Sys = 0;
  
  return 1;
}

/// <tstf_CanCom>
/* Checks the value of a signal, an environment variable or a system variable. */
/* The value should be inside the given value range (inclusive limits).        */
/*For example, to check no emergencey brake while Vehicle is in searching slots*/
testfunction tstf_ChkStart_Signal(signal *aSig, double aMin, double aMax)
{
  func_ChkStart_Signal(aSig, aMin, aMax);
}

int func_ChkStart_Signal(signal *aSig, double aMin, double aMax)
{
  dword idx = 0;
  idx = ChkCnt_Sig;
  
  ChkId_Sig[idx]  = 0;
  
  testStepPass("ChkStart"," Check index is [ %d ]. \n Check signal %s \n is within Range [%f, %f].", idx+1, aSig.name, aMin, aMax);
  
  /* checks the value of the signal (should be inside the given range) */
  ChkId_Sig[idx] = ChkStart_MsgSignalValueRangeViolation (aSig, aMin, aMax);
  
  TestAddCondition(ChkId_Sig[idx]);
  
  ChkCnt_Sig++;
  return 1;
}


/// <tstf_CanCom>
/* Stop BackGroup Check, idx start from 1, max idx is 16     */
/* 1 corespond to the first tstf_ChkStart_Signal you called  */
/* 2 corespond to the second tstf_ChkStart_Signal you called */
testfunction tstf_ChkStop_Signal(dword idx)
{
  func_ChkStop_Signal(idx);
}

int func_ChkStop_Signal(dword idx)
{
  if(idx > 16 || idx < 1){
    testStepFail("1","Check ID [ %d ] is out of range [ 1, 16 ].", idx);
    return 0;
  }
  
  if(idx > ChkCnt_Sig){
    testStepFail("1","Check ID [ %d ] exceeds active Check Number [ %d ].", idx, ChkCnt_Sig);
    return 0;
  }
  //remove check condition
  TestRemoveCondition(ChkId_Sig[idx-1]);
  // stop the check
  if(0x0 != ChkControl_Stop(ChkId_Sig[idx-1])) {
    testStepFail("ChkStop","Fail to Stop Check [ %d ].", idx);
    return 0;
  }
  
  testStepPass("ChkStop","Stop Check [ %d ].", idx);
  
  return 1;
}

/*Back ground Check a signal continously, for a time */
/*Para1, aSig, siangl to be checked                  */
/*Para2, aMin, signal value min value                */
/*Para3, aMax, signal value max value                */
/*Para4, chkTime, to check how long - ms             */
testfunction tstf_ContinousChk_Signal(signal *aSig, float aMin, float aMax, dword chkTime)
{
  func_ChkInit();
  func_ChkStart_Signal(aSig, aMin, aMax);          // Back Ground Check
  testWaitForTimeout(chkTime);
  func_ChkStop_Signal(1);
}

/// <tstf_CanCom>
/* Checks the value of a signal, an environment variable or a system variable. */
/* The value should be inside the given value range (inclusive limits).        */
testfunction tstf_ChkStart_SysVar(sysvarInt *aSysVar, double aMin, double aMax)
{
  func_ChkStart_SysVar(aSysVar, aMin, aMax);
}

int func_ChkStart_SysVar(sysvarInt *aSysVar, double aMin, double aMax)
{
  dword idx = 0;
  idx = ChkCnt_Sys;
  
  ChkId_Sys[idx]  = 0;
  
  // checks the value of the signal (should be inside the given range)
  ChkId_Sys[idx] = ChkStart_MsgSignalValueRangeViolation (aSysVar, aMin, aMax);
  
  TestAddCondition(ChkId_Sys[idx]);
  
  testStepPass("ChkStart"," Check index is [ %d ]. \n Check Sysvar %s is within Range [%.3f, %.3f].", idx+1, aSysVar.name, aMin, aMax);
  
  ChkCnt_Sys++;
  return 1;
}


/* To switch between different Bus */
dword func_SetBusContext(byte index)
{
  dword bus;
  
  if(index == ecuBusChn){
    bus = getBusNameContext(ecuBus);
  }
  else if(index == castleBusChn){
    bus = getBusNameContext(castleBus);
  }
  else if(index == vs6BusChn){
    bus = getBusNameContext(vs6Bus);
  }
  else if(index == ecuBusChn2){
    bus = getBusNameContext(ecuBus2);
  }
  else{
    bus = getBusNameContext(ecuBus);
  }
  
  teststep("Bus","Bus Context is set to [ %d ].",index);
  setBusContext(bus);
  return 1;
}

/// <tstf_CanCom>
/* Stop BackGroup Check, idx start from 1, max idx is 16     */
/* 1 corespond to the first tstf_ChkStart_Signal you called  */
/* 2 corespond to the second tstf_ChkStart_Signal you called */
testfunction tstf_ChkStop_SysVar(dword idx)
{
  func_ChkStop_SysVar(idx);
}

int func_ChkStop_SysVar(dword idx)
{
  if(idx > 16 || idx < 1){
    testStepFail("1","Check ID [ %d ] is out of range [ 1, 16 ].", idx);
    return 0;
  }
  
  if(idx > ChkCnt_Sys){
    testStepFail("1","Check ID [ %d ] exceeds active Check Number [ %d ].", idx, ChkCnt_Sys);
    return 0;
  }
  //remove check condition
  TestRemoveCondition(ChkId_Sys[idx-1]);
  // stop the check
  ChkControl_Stop(ChkId_Sys[idx-1]);
  
  testStepPass("ChkStop","Stop Check [ %d ].", idx);
  
  return 1;
}


/// <tstf_CanTp>
/*Send User defined Msg1 on the ECU bus via CAN  */
/*Para1, msgID, message ID                   */
/*Para2, msgLen, message payload length      */
/*Para3, dataStr, payload data in String     */
/*Para4, cycle, message cycle in ms          */
/*Para5, frames, frames to send in total     */
/*If para5 frames =1, para4 cycle is not     */
/* used, it could be any value               */
/*Para chn, optional, set as 2 if CAN2 is desired       */
/*Important: Msg transmit will stop when Test node stop */
testfunction tstf_Send_UserMsg1(dword msgID, byte msgLen, char dataStr[], dword cycle, dword frames)
{
  func_SetBusContext(ecuBusChn);
  func_Send_UserMsg1(msgID, msgLen, dataStr, cycle, frames, 0, 0, ecuBusChn);
}
/// <tstf_CanTp>
testfunction tstf_Send_UserMsg1(dword msgID, byte msgLen, char dataStr[], dword cycle, dword frames, byte chn)
{
  func_SetBusContext(chn);
  func_Send_UserMsg1(msgID, msgLen, dataStr, cycle, frames, 0, 0,  chn);
}

/// <tstf_CanTp>
/*Send User defined Msg1 on the ECU bus via CANFD  */
testfunction tstf_Send_UserMsg1_CanFD(dword msgID, byte msgLen, char dataStr[], dword cycle, dword frames)
{
  func_SetBusContext(ecuBusChn);
  func_Send_UserMsg1(msgID, msgLen, dataStr, cycle, frames, 0, 1, ecuBusChn);
}
/// <tstf_CanTp>
testfunction tstf_Send_UserMsg1_CanFD(dword msgID, byte msgLen, char dataStr[], dword cycle, dword frames, byte chn)
{
  func_SetBusContext(chn);
  func_Send_UserMsg1(msgID, msgLen, dataStr, cycle, frames, 0, 1,  chn);
}

/// <tstf_CanTp>
/*Send Remote Transmission Request via normal CAN*/
testfunction tstf_Send_UserMsg1_RTR(dword msgID, dword cycle, dword frames)
{
  func_SetBusContext(ecuBusChn);
  func_Send_UserMsg1(msgID, 0, "", cycle, frames, 1, 0, ecuBusChn);
}
/// <tstf_CanTp>
testfunction tstf_Send_UserMsg1_RTR(dword msgID, dword cycle, dword frames, byte chn)
{
  func_SetBusContext(chn);
  func_Send_UserMsg1(msgID, 0, "", cycle, frames, 1, 0, chn);
}

/// <tstf_CanTp>
/*Send Remote Transmission Request via CAN FD*/
testfunction tstf_Send_UserMsg1_RTR_CanFD(dword msgID, dword cycle, dword frames)
{
  func_SetBusContext(ecuBusChn);
  func_Send_UserMsg1(msgID, 0, "", cycle, frames, 1, 1, ecuBusChn);
}
/// <tstf_CanTp>
testfunction tstf_Send_UserMsg1_RTR_CanFD(dword msgID, dword cycle, dword frames, byte chn)
{
  func_SetBusContext(chn);
  func_Send_UserMsg1(msgID, 0, "", cycle, frames, 1, 1, chn);
}


/*Basic function to send raw frames         */
/*Para rtr, 0x1 - remote frame send request */
int func_Send_UserMsg1(dword msgID, byte msgLen, char dataStr[], dword cycle, dword frames, byte rtr, byte FDF, byte chn)
{
  word dataLen;
  byte payload[64];
  word i;
  
  /*Step1, check input validity and compose Msg */
  setTestId(1);
  dataLen = stringToBytes(dataStr, payload);
  if(dataLen <= msgLen <= 64){}
  else{
    testStepFail(gTestIdStr, "Input invalid, Test step Skip! Remember: dataLen %d <=  %d msgLen <= 64", dataLen, msgLen);
  }
  
  userMsg1.msgChannel = chn;
  userMsg1.id         = msgID;
  userMsg1.DataLength = msgLen;
  userMsg1.rtr        = rtr;           // remote transimission request
  userMsg1.FDF        = FDF;           // for CAN FD protocal msg
  if(FDF==0x1) userMsg1.BRS = 0x1;     // applicable for CANFD
  
  for(i=0; i<dataLen; i++){
    userMsg1.byte(i) = payload[i];
  }
  
  /*Step2, Send out 1st frame*/
  msgCnt1 = 1; maxCnt1 = frames;
  output(userMsg1);
  
  if(frames > 1){
   setTimerCyclic(msgTimer1, cycle);
  }
  
  testStepPass(gTestIdStr, "User Msg1 Transmit is Triggered:\n Chn [ %d ], ID [ 0x%X ], DataLength [ %d ], Payload [ %s ], Cycle [ %d ms], Frames [ %d ].", chn,msgID,msgLen,dataStr,cycle,frames);
  
  return 1;
}

on timer msgTimer1
{
  if(msgCnt1 < maxCnt1){
    output(userMsg1);
    msgCnt1++;
  }
  else{
    cancelTimer(msgTimer1);
    testStepPass("userMsg1", "[ %d ] frames of Msg [ 0x%X ] has been send on the bus.", msgCnt1, userMsg1.id);
  }
}

/// <tstf_CanTp>
/*Send User defined Msg2 on the ECU bus via CAN */
/*Para1, msgID, message ID                   */
/*Para2, msgLen, message payload length      */
/*Para3, dataStr, payload data in String     */
/*Para4, cycle, message cycle in ms          */
/*Para5, frames, frames to send in total     */
/*If para5 frames =1, para4 cycle is not     */
/* used, it could be any value               */
/*Important: Msg transmit will stop when Test node stop */
testfunction tstf_Send_UserMsg2(dword msgID, byte msgLen, char dataStr[], dword cycle, dword frames)
{
  func_SetBusContext(ecuBusChn);
  func_Send_UserMsg2(msgID, msgLen, dataStr, cycle, frames, 0, 0, ecuBusChn);
}
/// <tstf_CanTp>
testfunction tstf_Send_UserMsg2(dword msgID, byte msgLen, char dataStr[], dword cycle, dword frames, byte chn)
{
  func_SetBusContext(chn);
  func_Send_UserMsg2(msgID, msgLen, dataStr, cycle, frames, 0, 0, chn);
}
/// <tstf_CanTp>
/*Send User defined Msg2 on the ECU bus via CANFD */
testfunction tstf_Send_UserMsg2_CanFD(dword msgID, byte msgLen, char dataStr[], dword cycle, dword frames)
{
  func_SetBusContext(ecuBusChn);
  func_Send_UserMsg2(msgID, msgLen, dataStr, cycle, frames, 0, 1, ecuBusChn);
}
/// <tstf_CanTp>
testfunction tstf_Send_UserMsg2_CanFD(dword msgID, byte msgLen, char dataStr[], dword cycle, dword frames, byte chn)
{
  func_SetBusContext(chn);
  func_Send_UserMsg2(msgID, msgLen, dataStr, cycle, frames, 0, 1, chn);
}

/// <tstf_CanTp>
/*Send Remote Transmission Request via CAN*/
testfunction tstf_Send_UserMsg2_RTR(dword msgID, dword cycle, dword frames)
{
  func_SetBusContext(ecuBusChn);
  func_Send_UserMsg2(msgID, 0, "", cycle, frames, 1, 0, ecuBusChn);
}
/// <tstf_CanTp>
testfunction tstf_Send_UserMsg2_RTR(dword msgID, dword cycle, dword frames, byte chn)
{
  func_SetBusContext(chn);
  func_Send_UserMsg2(msgID, 0, "", cycle, frames, 1, 0, chn);
}

/// <tstf_CanTp>
/*Send Remote Transmission Request via CANFD*/
testfunction tstf_Send_UserMsg2_RTR_CanFD(dword msgID, dword cycle, dword frames)
{
  func_SetBusContext(ecuBusChn);
  func_Send_UserMsg2(msgID, 0, "", cycle, frames, 1, 1, ecuBusChn);
}
/// <tstf_CanTp>
testfunction tstf_Send_UserMsg2_RTR_CanFD(dword msgID, dword cycle, dword frames, byte chn)
{
  func_SetBusContext(chn);
  func_Send_UserMsg2(msgID, 0, "", cycle, frames, 1, 1, chn);
}

int func_Send_UserMsg2(dword msgID, byte msgLen, char dataStr[], dword cycle, dword frames, byte rtr, byte FDF, byte chn)
{
  word dataLen;
  byte payload[64];
  word i;
  
  /*Step1, check input validity and compose Msg */
  setTestId(1);
  dataLen = stringToBytes(dataStr, payload);
  if(dataLen <= msgLen <= 64){}
  else{
    testStepFail(gTestIdStr, "Input invalid, Test step Skip! Remember: dataLen %d <=  %d msgLen <= 64", dataLen, msgLen);
  }
  
  userMsg2.msgChannel = chn;
  userMsg2.id         = msgID;
  userMsg2.DataLength = msgLen;
  userMsg2.rtr        = rtr;           // remote transimission request
  userMsg2.FDF        = FDF;           // for CAN FD protocal msg
  if(FDF==0x1) userMsg2.BRS = 0x1;     // applicable for CANFD
  
  for(i=0; i<dataLen; i++){
    userMsg2.byte(i) = payload[i];
  }
  
  /*Step2, Send out 1st frame*/
  msgCnt2 = 1; maxCnt2 = frames;
  output(userMsg2);
  
  if(frames > 1){
   setTimerCyclic(msgTimer2, cycle);
  }
  
  testStepPass(gTestIdStr, "User Msg2 Transmit is Triggered:\n Chn [ %d ], ID [ 0x%X ], DataLength [ %d ], Payload [ %s ], Cycle [ %d ms], Frames [ %d ].", chn,msgID,msgLen,dataStr,cycle,frames);
  
  return 1;
}

on timer msgTimer2
{
  if(msgCnt2 < maxCnt2){
    output(userMsg2);
    msgCnt2++;
  }
  else{
    cancelTimer(msgTimer2);
    testStepPass("userMsg2", "[ %d ] frames of Msg [ 0x%X ] has been send on the bus.", msgCnt2, userMsg2.id);
  }
}

/// <tstf_CanTp>
/*Send User defined Msg3 on the ECU bus via CAN */
/*Para1, msgID, message ID                   */
/*Para2, msgLen, message payload length      */
/*Para3, dataStr, payload data in String     */
/*Para4, cycle, message cycle in ms          */
/*Para5, frames, frames to send in total     */
/*If para5 frames =1, para4 cycle is not     */
/* used, it could be any value               */
/*Important: Msg transmit will stop when Test node stop */
testfunction tstf_Send_UserMsg3(dword msgID, byte msgLen, char dataStr[], dword cycle, dword frames)
{
  func_SetBusContext(ecuBusChn);
  func_Send_UserMsg3(msgID, msgLen, dataStr, cycle, frames, 0, 0, ecuBusChn);
}
/// <tstf_CanTp>
testfunction tstf_Send_UserMsg3(dword msgID, byte msgLen, char dataStr[], dword cycle, dword frames, byte chn)
{
  func_SetBusContext(chn);
  func_Send_UserMsg3(msgID, msgLen, dataStr, cycle, frames, 0, 0, chn);
}
/// <tstf_CanTp>
/*Send User defined Msg3 on the ECU bus via CANFD */
testfunction tstf_Send_UserMsg3_CanFD(dword msgID, byte msgLen, char dataStr[], dword cycle, dword frames)
{
  func_SetBusContext(ecuBusChn);
  func_Send_UserMsg3(msgID, msgLen, dataStr, cycle, frames, 0, 1, ecuBusChn);
}
/// <tstf_CanTp>
testfunction tstf_Send_UserMsg3_CanFD(dword msgID, byte msgLen, char dataStr[], dword cycle, dword frames, byte chn)
{
  func_SetBusContext(chn);
  func_Send_UserMsg3(msgID, msgLen, dataStr, cycle, frames, 0, 1, chn);
}

/// <tstf_CanTp>
/*Send Remote Transmission Request via CAN*/
testfunction tstf_Send_UserMsg3_RTR(dword msgID, dword cycle, dword frames)
{
  func_SetBusContext(ecuBusChn);
  func_Send_UserMsg3(msgID, 0, "", cycle, frames, 1, 0, ecuBusChn);
}
/// <tstf_CanTp>
testfunction tstf_Send_UserMsg3_RTR(dword msgID, dword cycle, dword frames, byte chn)
{
  func_SetBusContext(chn);
  func_Send_UserMsg3(msgID, 0, "", cycle, frames, 1, 0, chn);
}

/// <tstf_CanTp>
/*Send Remote Transmission Request via CANFD*/
testfunction tstf_Send_UserMsg3_RTR_CanFD(dword msgID, dword cycle, dword frames)
{
  func_SetBusContext(ecuBusChn);
  func_Send_UserMsg3(msgID, 0, "", cycle, frames, 1, 1, ecuBusChn);
}
/// <tstf_CanTp>
testfunction tstf_Send_UserMsg3_RTR_CanFD(dword msgID, dword cycle, dword frames, byte chn)
{
  func_SetBusContext(chn);
  func_Send_UserMsg3(msgID, 0, "", cycle, frames, 1, 1, chn);
}


/*Basic function to send raw frames         */
/*Para rtr, 0x1 - remote frame send request */
int func_Send_UserMsg3(dword msgID, byte msgLen, char dataStr[], dword cycle, dword frames, byte rtr, byte FDF, byte chn)
{
  word dataLen;
  byte payload[64];
  word i;
  
  /*Step1, check input validity and compose Msg */
  setTestId(1);
  dataLen = stringToBytes(dataStr, payload);
  if(dataLen <= msgLen <= 64){}
  else{
    testStepFail(gTestIdStr, "Input invalid, Test step Skip! Remember: dataLen %d <=  %d msgLen <= 64", dataLen, msgLen);
  }
  
  userMsg3.msgChannel = chn;
  userMsg3.id         = msgID;
  userMsg3.DataLength = msgLen;
  userMsg3.rtr        = rtr;     // remote transimission request
  userMsg3.FDF        = FDF;     // for CAN FD protocal msg
  if(FDF==0x1) userMsg3.BRS = 0x1;     // applicable for CANFD
  
  for(i=0; i<dataLen; i++){
    userMsg3.byte(i) = payload[i];
  }
  
  /*Step2, Send out 1st frame*/
  msgCnt3 = 1; maxCnt3 = frames;
  output(userMsg3);
  
  if(frames > 1){
   setTimerCyclic(msgTimer3, cycle);
  }
  
  testStepPass(gTestIdStr, "User Msg3 Transmit is Triggered:\n Chn [ %d ], ID [ 0x%X ], DataLength [ %d ], Payload [ %s ], Cycle [ %d ms], Frames [ %d ].", chn,msgID,msgLen,dataStr,cycle,frames);
  
  return 1;
}

on timer msgTimer3
{
  if(msgCnt3 < maxCnt3){
    output(userMsg3);
    msgCnt3++;
  }
  else{
    cancelTimer(msgTimer3);
    testStepPass("userMsg3", "[ %d ] frames of Msg [ 0x%X ] has been send on the bus.", msgCnt3, userMsg3.id);
  }
}
on timer VH6501_Stop1
{
  @sysvar::VH6501::StopDisturbace = 1;
  write("22222222");
  setTimer(VH6501_Stop1,5);
}
on timer VH6501_Stop2
{
  @sysvar::VH6501::StopDisturbace = 0;
  write("333333333333333333333333333333");
  cancelTimer(VH6501_Stop1);
}

on timer VH6501_Start1
{
  @sysvar::VH6501::StartTrigger = 1;
  write("000000000");
  setTimer(VH6501_Start1,5);
}
on timer VH6501_Start2
{
  @sysvar::VH6501::StartTrigger = 0;
  write("111111111111111111111111111111111");
  cancelTimer(VH6501_Start1);
}

void func_VH6501_Start()
{
  setTimer(VH6501_Start1,5);
  setTimer(VH6501_Start2,50);
}
void func_VH6501_Stop()
{
  setTimer(VH6501_Stop1,5);
  setTimer(VH6501_Stop2,50);
}
testfunction tstf_VH6501_Stop()
{
  func_VH6501_Stop();
}

testfunction tstf_VH6501_Start()
{
  func_VH6501_Start();
}
/*To Cancel User Msg1 Send */
testfunction tstf_CancelSend_UserMsg1()
{
  func_CancelSend_UserMsg1();
}
int func_CancelSend_UserMsg1()
{
  cancelTimer(msgTimer1);
  testStepPass("userMsg1", "Msg Send [ 0x%X ]  has been Cancelled, [ %d ] frames has been sent on the bus.", userMsg1.id, msgCnt1);
  
  return 1;
}

/*To Cancel User Msg2 Send */
testfunction tstf_CancelSend_UserMsg2()
{
  func_CancelSend_UserMsg2();
}
int func_CancelSend_UserMsg2()
{
  cancelTimer(msgTimer2);
  testStepPass("userMsg2", "Msg Send [ 0x%X ]  has been Cancelled, [ %d ] frames has been sent on the bus.", userMsg2.id, msgCnt2);
  
  return 1;
}

/*To Cancel User Msg3 Send */
testfunction tstf_CancelSend_UserMsg3()
{
  func_CancelSend_UserMsg3();
}
int func_CancelSend_UserMsg3()
{
  cancelTimer(msgTimer3);
  testStepPass("userMsg3", "Msg Send [ 0x%X ]  has been Cancelled, [ %d ] frames has been sent on the bus.", userMsg3.id, msgCnt3);
  
  return 1;
}

/*To check if Msg is on the default bus - Channel 1              */
/*Para1, msgID                                                   */
/*Para2, simulated, msg direction, 0-RX, 1-TX                    */
/*CANoe orient, For instance, msg send by CANoe, then it is TX-1 */
/*Msg send by DUT, then it is RX-0                               */
/*Para3, chkTime, max wait time - ms                             */
/*Para4, chn, optional, msg is on which bus, 1,2,3 ?             */
testfunction tstf_Chk_MsgOnBus(dword msgID, byte simulated, dword chkTime)
{
  func_Chk_MsgOnBus(msgID, simulated, chkTime, -1, 1);                      // check on default channel 1
}

testfunction tstf_Chk_MsgOnBus(dword msgID, byte simulated, dword chkTime, byte chn)
{
  func_Chk_MsgOnBus(msgID, simulated, chkTime, -1, chn);                    // -1 means no lenght/dlc check
}


/*To check  Msg length                                           */
/*Para1, msgID                                                   */
/*Para2, simulated, msg direction, 0-RX, 1-TX                    */
/*CANoe orient, For instance, msg send by CANoe, then it is TX-1 */
/*Msg send by DUT, then it is RX-0                               */
/*Para3, chkTime, max wait time - ms                             */
/*Para4, msgLen, msg length, not DLC                             */
/*Para5, chn, optional, msg is on which bus, 1,2,3 ?             */
testfunction tstf_Chk_MsgLength(dword msgID, byte simulated, dword chkTime, int msgLen)
{
  func_Chk_MsgOnBus(msgID, simulated, chkTime, msgLen, 1);                      // check on default channel 1
}

testfunction tstf_Chk_MsgLength(dword msgID, byte simulated, dword chkTime, int msgLen, byte chn)
{
  func_Chk_MsgOnBus(msgID, simulated, chkTime, msgLen, chn);                      // check on default channel 1
}

int func_Chk_MsgOnBus(dword msgID, byte simulated, dword chkTime, int msgLen, byte chn)
{
  long idx;
  message *getMsg;
  byte fail;
  
  fail=0;
  /*Step Set bus context to Chn*/
  func_SetBusContext(chn);
  
  /*Step 1, wait for expected Msg */
  setTestId(1);
  testJoinMessageEvent(msgID);
  idx = testWaitForAnyJoinedEvent(chkTime);
  if(1 != idx) {
    testStepFail(gTestIdStr, "Msg [0x%X] is not detected on bus [ %d ].", msgID, chn);
    return -1;
  }
  else{ 
    testStepPass(gTestIdStr, "Msg [0x%X] is detected on bus [ %d ].", msgID, chn);
  }
  
  /*Step2.1 Print Recieved Msg Data */
  incSubStepId();
  testGetWaitEventMsgData(idx, getMsg);
  
  if( getMsg.simulated == simulated &&  simulated == 1){   // 1 simulated TX;
    testStepPass(gTestIdStr,"And Msg [ 0x%X ] is TX Msg.", getMsg.id);
  }
  else if( getMsg.simulated == simulated &&  simulated == 0){   // 0-not simulated RX
    testStepPass(gTestIdStr,"And Msg [ 0x%X ] is RX Msg.", getMsg.id);
  }
  else{
    testStepFail(gTestIdStr,"But Msg [ 0x%X ] is TX-RX type [ %d ] is not as expected [ %d ]. (0-RX, 1-TX)", getMsg.id, getMsg.simulated, simulated);
    fail = 1;
  }
  
  /*Step2.2, Check Msg Data Lenght or DLC */
  if(msgLen<0){                                   // negative means no DLC check
    return (fail!=0?-1:1);
  }
  
  incSubStepId();
  if(getMsg.DataLength != msgLen){
    testStepFail(gTestIdStr,"But Msg length [ %d ] is not as expected [ %d ].", getMsg.DataLength, msgLen);
    return -1;
  }
  
  testStepPass(gTestIdStr,"And Msg length [ %d ] is as expected [ %d ].", getMsg.DataLength, msgLen);
  return 1;
}

/*Await Msg on the bus */
int func_Await_MsgOnBus(dword msgID, dword chkTime, byte chn)
{
  long idx;
  message *getMsg;
  
  /*Step Set bus context to Chn*/
  func_SetBusContext(chn);
  
  /*Step 1, wait for expected Msg */
  setTestId(1);
  testJoinMessageEvent(msgID);
  idx = testWaitForAnyJoinedEvent(chkTime);
  if(1 != idx) {
    testStep(gTestIdStr, "Msg [0x%X] is not detected on bus [ %d ].", msgID, chn);
    return -1;
  }
  else{ 
    testStep(gTestIdStr, "Msg [0x%X] is detected on bus [ %d ].", msgID, chn);
  }
  
  return 1;
  
}


/*Check Msg is not on the bus for a time      */
/*Para1, msgID[], array, contains some Msg ID */
/*Para2, msgNum, how many msgs to check       */
/*Para3, chkTime,how long to observe in ms    */
/*Para4, chn, optional, check on which channel*/
testfunction tstf_Chk_MsgOffBus(dword msgID[], word msgNum, dword chkTime)
{
  func_Chk_MsgOffBus(msgID, msgNum, chkTime, ecuBusChn);
}
testfunction tstf_Chk_MsgOffBus(dword msgID[], word msgNum, dword chkTime, byte chn)
{
  func_Chk_MsgOffBus(msgID, msgNum, chkTime, chn);
}

int func_Chk_MsgOffBus(dword msgID[], word msgNum, dword chkTime, byte chn)
{
  word i;
  long idx;
  message *getMsg;
  
  /*Step1, check input validity */
  setTestId(1);
  if( msgNum>0 && msgNum <= elcount(msgID) ){}
  else{
    testStepFail(gTestIdStr, "API input [ %d ] is not valid!", msgNum);
    return -1;
  }
  
  /*Step2, Set bus context to Chn*/
  func_SetBusContext(chn);
  
  /*Step 1, add Msg to check */
  for(i=0;i<msgNum;i++){
    testJoinMessageEvent(msgID[i]);
    incSubStepId();
    testStep(gTestIdStr,"Msg [ 0x%X ] is added for check!", msgID[i]);
  }
  
  /*Step2, Observe added Msgs*/
  incTestStepId();
  idx = testWaitForAnyJoinedEvent(chkTime);
  if(idx > 0) {                                     // Msg occurred
    testStepFail(gTestIdStr, "Msg [0x%X] is detected on bus [ %d ].", msgID[idx-1], chn);
    return -1;
  }
  else if(idx == 0){                                // timeout
    testStepPass(gTestIdStr, "No added Msg is detected on bus [ %d ].", chn);
  }
  else{                                             // other failures
    testStepFail(gTestIdStr, "Other failures [ %d ].", idx);
    return -1;  
  }
  
  return 1;
}

/*Error Frame Handle*/
testfunction  tstf_Clear_ErrorFrames(byte chn)
{
  func_Clear_ErrorFrames(chn);
}

int func_Clear_ErrorFrames(byte chn)
{
  if(chn==1){
    teststep("1"," [ %d ] Error Frames on CAN1 is Cleared.", errorMsgCnt1);
    errorMsgCnt1=0;
    return 1;
  }
  else if(chn==2){
    teststep("1"," [ %d ] Error Frames on CAN2 is Cleared.", errorMsgCnt2);
    errorMsgCnt2=0;
    return 1;
  }
  else{
    testStepFail("1", "Bus Channel [ %d ] is not supported.(1 or 2 supported only)", chn);
    return 0;
  }
}

/*Check no error frames happend on the bus chn*/
testfunction tstf_Chk_No_ErrorFrame(byte chn)
{
  dword number;
  
  number = func_Get_ErrorFrames(chn);
  
  if(number == 0){
    testStepPass("2", "No Error frames on bus [ %d ].", chn);
  }
  else{
    testStepFail("2", "There are [ %d ] Error frames on bus [ %d ].", number, chn);
  }
  
}

testfunction tstf_Chk_ErrorFrame(byte chn)
{
  dword number;
  
  number = func_Get_ErrorFrames(chn);
  
  if(number == 0){
    testStepFail("2", "No Error frames on channel [ %d ].", chn);
    testStep("3","number is %d",number);
  }
  else{
    testStepPass("2", "There are [ %d ] Error frames on channel [ %d ].", number, chn);
    testStep("3","number is %d",number);
  }
  
}

dword func_Get_ErrorFrames(byte chn)
{
  if(chn==1){
    teststep("1","Error Frames on CAN1 is [ %d ].", errorMsgCnt1);
    return errorMsgCnt1;
  }
  else if(chn==2){
    teststep("1","Error Frames on CAN2 is [ %d ].", errorMsgCnt2);
    return errorMsgCnt2;
  }
  else{
    testStepFail("1", "Bus Channel [ %d ] is not supported.(1 or 2 supported only)", chn);
    return 0;
  }
  
}

on CAN1.ErrorFrame
{
  errorMsgCnt1++;
}

on CAN2.ErrorFrame
{
  errorMsgCnt2++;
}

/*To report current busload */
testfunction tstf_Report_Busload(byte chn)
{
  func_Report_Busload(chn);
}

long func_Report_Busload(byte chn)
{
  long busload;
  
  if(chn==1){
    busload = CAN1.BusLoad;
    TestCaseReportMeasuredValue("CAN1 Busload", busload, "%");
  }
  else if(chn==2){
    busload = CAN2.BusLoad;
    TestCaseReportMeasuredValue("CAN2 Busload", busload, "%");
  }
  else if(chn==3){
    busload = CAN3.BusLoad;
    TestCaseReportMeasuredValue("CAN3 Busload", busload, "%");
  }
  else if(chn==4){
    busload = CAN4.BusLoad;
    TestCaseReportMeasuredValue("CAN4 Busload", busload, "%");
  }
  else{
    testStepFail("","Bus Channel [%d] is invalid.<=4", chn);
  }
  
  return busload;
}


/*Check all DUT Tx msg cycle are within Range   */
/*Para1, chkTime, check time, ms                */
/*Para2, input, user defined struct msgComp     */
/*Para3, chn, optional, default is 1 means CAN1 */
testfunction tstf_Chk_DutMsgCycleVilation(dword chkTime, struct msgComp input[])
{
  func_Chk_DutMsgCycleVilation(chkTime, input, ecuBusChn);
}
testfunction tstf_Chk_DutMsgCycleVilation(dword chkTime, struct msgComp input[], byte chn)
{
  func_Chk_DutMsgCycleVilation(chkTime, input, chn);
}

int func_Chk_DutMsgCycleVilation(dword chkTime, struct msgComp input[], byte chn)
{
  dword checkID[64];
  word i;
  
  func_SetBusContext(chn);
  /*Step1, add test condition*/
  setTestId(1);
  for(i=2; i<elcount(input); i++){
    checkID[i] = ChkStart_MsgAbsCycleTimeViolation (input[i].msgID, input[i].minCyle, input[i].maxCyle);
    
    incSubStepId();
    if(checkID[i] > 0){
      testAddCondition(checkID[i]);
      testStepPass(gTestIdStr, "Cycle Chk for Msg [ 0x%X ] is created! Expected Range[ %d, %d] ms.",input[i].msgID, input[i].minCyle, input[i].maxCyle);
    }
    else{
      testStepFail(gTestIdStr, "Cycle Chk for Msg [ 0x%X ] is not created! Expected Range[ %d, %d] ms.",input[i].msgID, input[i].minCyle, input[i].maxCyle);
    }
  }
  
  /*Step2*/
  testWaitForTimeout(chkTime);
  
  for(i=2; i<elcount(input); i++){
    testRemoveCondition(checkID[i]);
  }
  
  return 1;
}

/*API to generate txt Report, default path is Test/Tracelog_02/   */
/*Para1 rptName, report name in string                            */
/*Note: Call tstf_txtRpt_Close to close current txt report before */
/*You create a new txt report file, very important                */
testfunction tstf_txtRpt_Create(char rptName[])
{
  func_txtRpt_Create(rptName);
}

int func_txtRpt_Create(char rptName[])
{
  char txtRptName[64];
  char timeStr[16];
  
  txtIdx        = 0;                                                      // Init case index
  txtRptName[0] = '\0';                                                   // clear txt report name
  func_get_TimeString(timeStr);                                           // get time string, HH-MM-SS
  snprintf(txtRptName, elcount(txtRptName), "%s%s", rptName, timeStr);    // compose txt report name with time
  
  setWritePath(txtRptPath);
  txtRptHandle = openFileWrite(txtRptName, 0);
  
  if(txtRptHandle == 0){
    testStepFail("0", "Txt Report is failed to Create. %s - %s", txtRptPath, txtRptName);
    return 0;
  }
  
  testStepPass("0", "Txt Report [ %d ] is Created. %s - %s", txtRptHandle, txtRptPath, txtRptName);
  return txtRptHandle;
}

/*API to write test case reuslt in to txt Report file                       */
/*Para1 title, test case title in string                                    */
/*Para2 verdict, test resut, pass or fail                                   */
/*Para3, info1, more of test resut info, such as slot detected or not       */
/*Para4, info2, more of test resut info, such as fail reason                */
/*Para5, info3, more of test resut info, such as trace log, comment, etc    */
/*Note: symbol ',' can't be pass to any of these parameters, very important */
testfunction tstf_txtRpt_Append_CaseResult(char title[], char verdict[])
{
  func_txtRpt_Append_CaseResult(title, verdict, "", "", "");
}

testfunction tstf_txtRpt_Append_CaseResult(char title[], char verdict[], char info1[])
{
  func_txtRpt_Append_CaseResult(title, verdict, info1, "", "");
}

testfunction tstf_txtRpt_Append_CaseResult(char title[], char verdict[], char info1[], char info2[])
{
  func_txtRpt_Append_CaseResult(title, verdict, info1, info2, "");
}

testfunction tstf_txtRpt_Append_CaseResult(char title[], char verdict[], char info1[], char info2[], char info3[])
{
  func_txtRpt_Append_CaseResult(title, verdict, info1, info2, info3);
}

int func_txtRpt_Append_CaseResult(char title[], char verdict[], char info1[], char info2[], char info3[])
{
  char buffer[512];
  long retVal;
  
  txtIdx++;
  
  buffer[0] = '\0';
  snprintf(buffer, elCount(buffer), "%d %s %s", txtIdx,title,verdict);
  
  if(strncmp(info1, "", elCount(info1)) !=0  ){            //info1 is not blank
    snprintf(buffer, elCount(buffer), "%s %s", buffer,info1);
  }
  
  if(strncmp(info2, "", elCount(info2)) !=0  ){            //info2 is not blank
    snprintf(buffer, elCount(buffer), "%s %s", buffer,info2);
  }
  
  if(strncmp(info3, "", elCount(info3)) !=0  ){            //info3 is not blank
    snprintf(buffer, elCount(buffer), "%s %s", buffer,info3);
  }
  
  snprintf(buffer, elCount(buffer), "%s\n", buffer);
  retVal = filePutString(buffer, elCount(buffer), txtRptHandle);
  
  if(retVal == 0){
    testStepWarning("0","Failed to write Case Result in to txt Report [ %d ].\n %s", txtRptHandle, buffer);
    return 0;
  }
  
  testStepPass("0","Success to write Case Result in to txt Report [ %d ].\n %s", txtRptHandle, buffer);
  
  return 1;
}


/*Test API to close txt Report */
testfunction tstf_txtRpt_Close()
{
  func_txtRpt_Close();
}

int func_txtRpt_Close()
{
  long retVal;
  
  retVal = fileClose(txtRptHandle);              // to close txt report file.
  
  if(retVal == 0){
    testStepFail("0","Txt Report [ %d ] is failed to close.", txtRptHandle);
    return 0;
  }
  
  testStepPass("0","Txt Report [ %d ] is closed.", txtRptHandle);
  return 1;
}

testfunction tstf_Verify_Session(word Req_msgID, byte session)
{
  int result_flag = 0;
  char buffer[50];
  
  byte req_data[3] = {0x22,0xF1,0x86};
  byte exp_data[4] = {0x62,0xF1,0x86,0x02};
  testStep("Verify_Session","Verify_Session");
  exp_data[3] = session;
  tstf_func_output_CANFD_msg(Req_msgID,3,req_data);
  tstf_func_Check_Response(4,exp_data);
}

int tstf_func_Check_Response(int lenExpect, byte respExpect[])
{
  word lenObtain;
  byte respObtain[512];
  char ResultString[512];
  char pString[512];
  char tString[512];
  word i,j;
  long ret3;
  int rtn;
  float t1,t2;
  
  setTestId(1);
  snprintf(pString, elcount(pString), "to Check Diag Response: ");
  for(i= 0;i<lenExpect;i++)
  {
    snprintf(tString, elcount(tString), "%02x ",respExpect[i]);
    strncat(pString,tString,elCount(pString));
  }
  teststep(gTestIdStr,"[%s]",pString);
  rtn = testWaitForDiagResponse(pasDiagReqst, 500);
  if (rtn != 1) //the erase is take too much time ,so set 1 min as a red line
  {          
    snprintf(ResultString, elcount(ResultString), "Valid response missing or received too late!");
    write("Valid response missing or received too late");
    testStepFail(gTestIdStr, ResultString);
    teststep(gTestIdStr,"[%d]",rtn);
    return -1;
  }
  
  else
  {
    incTestStepId();
    testStepPass(gTestIdStr, "Response received successfully.");
    //TestReportWriteDiagResponse(pasDiagReqst);                     // write response to report
    
   // lenExpect = stringToBytes(tstString, respExpect);
    diagGetLastResponse(pasDiagRespd);
    t2 = timeNow();
   // ret3=DiagGetLastResponseCode(pasDiagReqst);
   // if(ret3==0x78)
    lenObtain = diagGetPrimitiveData(pasDiagRespd,respObtain,elCount(respObtain));
   
    incTestStepId();
    if(lenObtain != lenExpect)
    {
      testStepFail(gTestIdStr,"Received bytes [ %d ] not equal to expected [ %d ].",lenObtain, lenExpect);
      return -1;
    }
    else
    {
      for(i=0;i<lenExpect;i++)
      {
        if(respExpect[i] != respObtain[i])
        {
          testStepFail(gTestIdStr,"Received byte[%d] 0x%x not equal to expected 0x%x.",i, respObtain[i],respExpect[i]);
          return -1;
        }
      }
      
      testStepPass(gTestIdStr, "Received bytes equals to expected bytes.");
    }
    
  }
  
  return lenObtain;
  
}

void tstf_func_output_CANFD_msg(word Req_msgID,int Array_len, byte Req_Data[])   // "10 03"
{
  word i;
  char pString[128];
  char tString[128];
  long rtn;
  int max;
  word Req_msgID_Phy = 0x760;
  
  setTestId(1);
 
  if(Array_len>15)
  { max = 15; }
  else 
  {
     max = Array_len;
  }
  snprintf(pString,elCount(pString),"To Send Diag Commands:");
  for(i= 0;i<max;i++)
  {
    snprintf(tString, elcount(tString), "%02x ",Req_Data[i]);
    strncat(pString,tString,elCount(pString));
  }
  
  Teststep(gTestIdStr, "%s", pString);
  diagResize(pasDiagReqst, Array_len);

  for(i=0;i<Array_len;i++)
  {
    diagSetPrimitiveByte(pasDiagReqst,i,Req_Data[i]);
  }
  
  if(Req_msgID_Phy == Req_msgID)  //Physical Addressing
  {
    //t1 = timeNow();
   rtn = diagSendRequest(pasDiagReqst);
    
  }
  else              // Functional Addressing
  {
   rtn =  diagSendFunctional(pasDiagReqst);
  }
  rtn = testWaitForDiagRequestSent(pasDiagReqst, 2000);
  if(Req_Data[0] != 0x36)
  {
    testReportWriteDiagObject(pasDiagReqst);
  }
  if(rtn >= 0)
  {
    incTestStepId();
    Teststep(gTestIdStr, " [ %s ] is Send on the Bus.\n", pString);
    
  }
  else
  {
    
    Teststep(gTestIdStr, " [ %s ] is Send on the Bus fail,error code is %d.\n", pString,rtn);
  }
 
  
}

/*Error Frame Monitor*/
//on errorFrame
//{
//  Res = TestSupplyTextEvent("ErrorFrame occurred!");
//}
//
//testcase _WaitForAnyEvent()
//{
//  TestCaseTitle("Demo","WaitForAnyJoinedEvents");
//
//  EventHandle1 = testJoinSysVarEvent(sysvar::Sysvar1);
//  EventHandle2 = TestJoinTextEvent("ErrorFrame occurred!");
//
//  lReturn = TestWaitForAnyJoinedEvent (5000);
//
//
//  if (lReturn > 0)
//  {
//    if (lReturn == EventHandle1)
//    {
//      Res = testGetJoinedEventOccured(lReturn, mEventTime);
//      write("mEventTime = %I64d ; Res = %ld ; lReturn = %ld",mEventTime,Res,lReturn);
//    }
//    if (lReturn == EventHandle2)
//    {
//      Res = testGetJoinedEventOccured(lReturn, mEventTime);
//      write("mEventTime = %I64d ; Res = %ld ; lReturn = %ld",mEventTime,Res,lReturn);
//    }
//  }
//}
