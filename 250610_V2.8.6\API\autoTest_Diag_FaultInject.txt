/*@!Encoding:1252*/

/* Configurable for differenct Projects.*/
variables
{
  const word  dutApp_MsgNum     = 7;
  const word  dutEvent_MsgNum   = 1;         // not include NM and Diag frames
  const word  dutNm_MsgNum      = 1;
  
  dword dutApp_MsgID[7]      = {0x2AB,0x191,0x390,0x190,0x135,0x136,0x294};
  dword dutEvent_MsgID[1]    = {0x6EC};
  dword dutNm_MsgID[1]       = {0x438};
  
  
  
  struct MsgInfo {
    dword msgID[100];
    byte  msgLen[100];
    dword msgCyc[100];
    dword msgCnt;
  };
  
  struct MsgInfo msgInfo_Can1 = {
    {0x65,0x69,0x80,0x185,0x90,0x180,0x30D,0x59,0x45,0x501,0x7FF,0x760},
    {64,  64,  64,  64,   64,  64,   64,   64,  64,  8,    8,    64},
    {10,  20,  30,  60,   100, 200,  0,    800, 0,   0,    0,    0},
    12
  };
  
    struct MsgInfo msgInfo_Can2 = {
    {0x26,0x4E,0x1BC,0x533},
    {8,  8,  8,  8},
    {0,  10, 25, 0},
    4
  };
  
  
  //message * dutApp_Msg;
  
  
  
  
  dword checkId[128];
  
  char rptString[200];                   //string to print in report
  char rptInfo[16];
  enum UB_error_type {ub_error0 = 0, no_ub_error = 1};
  enum EnableState {Enable = 0, Disable = 1};
}

/* Set CRC signal error type.                                  */
/* errorType {0-zero, 1-freeze, 2-Plus1, 3-Minus1, 4-Random}.  */
testfunction tstf_Diag_CrcSig_errorType(word errorType)
{
  func_Diag_CrcSig_errorType(errorType);
  
}

int func_Diag_CrcSig_errorType(word errorType)
{
  char sysDescribe[32];                        // to store sysvar description for a value
  
  setTestId(1);
  if(errorType > 4)
  {
    testStepFail(gTestIdStr, "Error Type input [%d] is Wrong, not defined within [0-4].",errorType);
    return -1;
  }
  
  sysGetVariableDescriptionForValue(sysvar::Diag::crc_errorType,errorType,sysDescribe,elCount(sysDescribe));
  testStepPass(gTestIdStr, "CRC Error Type is set to [%d - %s].  ", errorType, sysDescribe);
  
  @sysvar::Diag::crc_errorType = errorType;
  return 1;
}

/* Set Alive counter error type.                 */
/* Para1: errorType {0-zero, 1-freeze, 2-Random}. */
testfunction tstf_Diag_AlvSig_errorType(word errorType)
{
  func_Diag_AlvSig_errorType(errorType);
}

int func_Diag_AlvSig_errorType(word errorType)
{
  char sysDescribe[32];                        // to store sysvar description for a value
  
  setTestId(1);
  if(errorType > 2)
  {
    testStepFail(gTestIdStr, "Error Type input [%d] is Wrong, not defined within [0-2].",errorType);
    return -1;
  }
  
  sysGetVariableDescriptionForValue(sysvar::Diag::alv_errorType,errorType,sysDescribe,elCount(sysDescribe));
  testStepPass(gTestIdStr, "Alive Counter Error Type is set to [%d - %s].  ", errorType, sysDescribe);
  
  @sysvar::Diag::alv_errorType = errorType;
  return 1;
}

/* Manipulate CRC Signal.            */
/* Para1: msgID {msg ID }            */
/* Para2: error {0-no error, 1-error} */
testfunction tstf_Diag_CrcSig_FaultInject(sysvarInt *crcErrorID)
{
  func_Diag_CrcSig_Ctrl(crcErrorID, 1);
}

testfunction tstf_Diag_CrcSig_FaultRemove(sysvarInt *crcErrorID)
{
  func_Diag_CrcSig_Ctrl(crcErrorID, 0);
}

int func_Diag_CrcSig_Ctrl(sysvarInt *crcErrorID, word error)
{
  char sigGroup[128];
  
  strncpy(sigGroup, crcErrorID.name, elcount(sigGroup));
  
  setTestId(1);
  if(error != 0)
  {
    testStep(gTestIdStr, "To Inject CRC Failure on sigGroup [%s].", sigGroup);
  }
  else
  {
    testStep(gTestIdStr, "To Remove CRC Failure on sigGroup [%s].", sigGroup);
  }
  
  incTestStepId();

  
  if(error != 0)            // failure
  {
    @crcErrorID = 0x1;
    testStepPass(gTestIdStr, "CRC Failure on sigGroup [%s] is Injected!", sigGroup);
  }
  else                     // no failure
  {
    @crcErrorID = 0x0;
    testStepPass(gTestIdStr, "CRC Failure on sigGroup [%s] is Removed!", sigGroup);
  }
  return 1;
}

/* Manipulate Alive Counter Signal.  */
/* Para1: msgID {msg ID }             */
/* Para2: error {0-no error, 1-error} */
testfunction tstf_Diag_AlvSig_FaultInject(sysvarInt *AlvErrorID)
{
  func_Diag_AlvSig_Ctrl(AlvErrorID, 1);
}

testfunction tstf_Diag_AlvSig_FaultRemove(sysvarInt *AlvErrorID)
{
  func_Diag_AlvSig_Ctrl(AlvErrorID, 0);
}

int func_Diag_AlvSig_Ctrl(sysvarInt *AlvErrorID, word error)
{
  char sigGroup[128];
  
  strncpy(sigGroup, AlvErrorID.name, elcount(sigGroup));
  
  setTestId(1);
  if(error != 0)
  {
    testStep(gTestIdStr, "To Inject Alv Failure on sigGroup [%s].", sigGroup);
  }
  else
  {
    testStep(gTestIdStr, "To Remove Alv Failure on sigGroup [%s].", sigGroup);
  }
  
  incTestStepId();

  
  if(error != 0)            // failure
  {
    @AlvErrorID = 0x1;
    testStepPass(gTestIdStr, "Alv Failure on sigGroup [%s] is Injected!", sigGroup);
  }
  else                     // no failure
  {
    @AlvErrorID = 0x0;
    testStepPass(gTestIdStr, "Alv Failure on sigGroup [%s] is Removed!", sigGroup);
  }
  return 1;
}

//neo
//ub
testfunction tstf_Diag_UB_FaultCtrl(sysvarInt *ubErrorCtrl, enum UB_error_type ub_type)
{
  func_Diag_UB_FaultCtrl(ubErrorCtrl, ub_type);
}

int func_Diag_UB_FaultCtrl(sysvarInt *ubErrorCtrl, enum UB_error_type ub_type)
{
  char UbErrorCtrlName[128];
  
  strncpy(UbErrorCtrlName, ubErrorCtrl.name, elcount(UbErrorCtrlName));
  
  //enum UB_error_type {ub_error0 = 0, no_ub_error = 1};
  
  setTestId(1);
  if(ub_type != no_ub_error)    // Inject UB error
  {
    testStep(gTestIdStr, "To Inject UB error on sigGroup [%s].", UbErrorCtrlName);
  }
  else               // Remove UB error
  {
    testStep(gTestIdStr, "To Remove UB error on sigGroup [%s].", UbErrorCtrlName);
  }
  
  incTestStepId();
  
  if(ub_type != no_ub_error)
  {
    if(strncmp(UbErrorCtrlName, "AsyALgtCtrlMod_ub_error", elcount(UbErrorCtrlName)) == 0){
      @sysvar::Diag::AsyALgtCtrlMod_ub_error = 0x0; 
    }
    else if(strncmp(UbErrorCtrlName, "AsyLatCtrlModReqGroup_ub_error", elcount(UbErrorCtrlName)) == 0){
      @sysvar::Diag::AsyLatCtrlModReqGroup_ub_error = 0x0;
    }
    else{
      @ubErrorCtrl = 0x0;
    }
    testStepPass(gTestIdStr, "UB errore on sigGroup [%s] is Injected!", UbErrorCtrlName);
  }
  else
  {
    if(strncmp(UbErrorCtrlName, "AsyALgtCtrlMod_ub_error", elcount(UbErrorCtrlName)) == 0){
      @sysvar::Diag::AsyALgtCtrlMod_ub_error = 0x1;
    }
    else if(strncmp(UbErrorCtrlName, "AsyLatCtrlModReqGroup_ub_error", elcount(UbErrorCtrlName)) == 0){
      @sysvar::Diag::AsyLatCtrlModReqGroup_ub_error = 0x1;
    }
    else{
      @ubErrorCtrl = 0x2;
    }
    testStepPass(gTestIdStr, "UB errore on sigGroup [%s] is RemoveD!", UbErrorCtrlName);
  }

  
//  if(enable != 0)   // ensable
//  {
//    testStepPass(gTestIdStr, "Msg [%s] is Restored successfully!", MsgCtrlName);
//  }
//  else             // disable
//  {
//    testStepPass(gTestIdStr, "Msg [%s] is Stopped!", MsgCtrlName);
//  }
  
  return 1;
}


//////////////



/* Stop Msg Sending on the Bus         */
/* Para1: msgID {msg ID }              */
/* Para2: enable {0-disable, 1-enable} */
//testfunction tstf_Diag_EnableMsg(sysvarInt *msgCtrl)
//{
//  func_Diag_MsgCtrl(msgCtrl, 1);
//}
//
//testfunction tstf_Diag_DisableMsg(sysvarInt *msgCtrl)
//{
//  func_Diag_MsgCtrl(msgCtrl, 0);
//}

testfunction tstf_Diag_MsgCtrl(sysvarInt *msgCtrl, word enable)
{
  func_Diag_MsgCtrl(msgCtrl, enable);
}

int func_Diag_MsgCtrl(sysvarInt *msgCtrl, word enable)
{
  char MsgCtrlName[128];
  
  strncpy(MsgCtrlName, msgCtrl.name, elcount(MsgCtrlName));
  
  setTestId(1);
  if(enable != 0)    // ensable=1
  {
    testStep(gTestIdStr, "To Restore Msg [%s].", MsgCtrlName);
  }
  else               // disable=0
  {
    testStep(gTestIdStr, "To Stop Msg Msg [%s].", MsgCtrlName);
  }
  
  incTestStepId();
  
  if(enable != 1)
  {
    @msgCtrl = 0x1;  //According to panel
  }
  else
  {
    @msgCtrl = 0x0;
  }

  
  if(enable != 0)   // ensable
  {
    testStepPass(gTestIdStr, "Msg [%s] is Restored successfully!", MsgCtrlName);
  }
  else             // disable
  {
    testStepPass(gTestIdStr, "Msg [%s] is Stopped!", MsgCtrlName);
  }
  
  return 1;
}

//neo
testfunction tstf_Diag_MsgCycle_Ctrl(sysvarInt *msgCtrl, dword cycle)
{
  func_Diag_MsgCycle_Ctrl(msgCtrl, cycle);
}
int func_Diag_MsgCycle_Ctrl(sysvarInt *msgCtrl, dword cycle)
{
  
  long cycleRes;
  char MsgCtrlName[128];
  
  strncpy(MsgCtrlName, msgCtrl.name, elcount(MsgCtrlName));
    
  setTestId(1);
  
  if(cycle <= 0)
  {
    testStepFail(gTestIdStr,"Msg [%s] Cycle [%d] set is not Valid!",MsgCtrlName,cycle);
    return -1;
  }
  else
  {
    testStep(gTestIdStr, "To Set Msg [%s] Cycle to [%d].", MsgCtrlName, cycle);
  }
 
  
  incTestStepId();
  
  @msgCtrl = cycle;

  if (@msgCtrl != cycle)
  {
    testStepFail(gTestIdStr, "Msg [%s] is failed on Cycle Inject! Please check your msgID.", MsgCtrlName);
    return -1;
  }
  testStepPass(gTestIdStr, "Msg [%s] Cycle is set to [%d ms].", MsgCtrlName, cycle);
  return 1;
}



/* Fault Inject on MSg Cycle  */
/* Para1: msgID {msg ID }     */
/* Para2: cycle { ms    }     */
//testfunction tstf_Diag_FaultInject_MsgCycle(sysvarInt *msgID, dword cycle)
//{
//  func_Diag_FaultInject_MsgCycle(msgID, cycle);
//}
//
//int func_Diag_FaultInject_MsgCycle(sysvarInt *msgCtrl, dword cycle)
//{
//  
//  long cycleRes;
//  char MsgCtrlName[128];
//  
//  strncpy(MsgCtrlName, msgCtrl.name, elcount(MsgCtrlName));
//    
//  setTestId(1);
//  
//  if(cycle <= 0)
//  {
//    testStepFail(gTestIdStr,"Msg [%s] Cycle [%d] set is not Valid!",MsgCtrlName,cycle);
//    return -1;
//  }
//  else
//  {
//    testStep(gTestIdStr, "To Set Msg [%s] Cycle to [%d].", MsgCtrlName, cycle);
//  }
// 
//  
//  incTestStepId();
//  
//  //cycleRes =  testSetMsgCycleTime(msgID, cycle);
//  @msgCtrl = cycle;
// 
//  
//  if (cycleRes != 0)
//  {
//    testStepFail(gTestIdStr, "Msg [%s] is failed on Cycle Inject! Please check your msgID.", MsgCtrlName);
//    return -1;
//  }
//  testStepPass(gTestIdStr, "Msg [%s] Cycle is set to [%d ms].", MsgCtrlName, cycle);
//  return 1;
//}

/* Remove Fault Inject on MSg Cycle  */
/* Para1: msgID {msg ID }            */
//testfunction tstf_Diag_FaultRemove_MsgCycle(sysvarInt *msgID)
//{
//  func_Diag_FaultRemove_MsgCycle(msgID);
//}
//
//int func_Diag_FaultRemove_MsgCycle(sysvarInt *msgCtrl)
//{
//  long cycleRes;
//  char MsgCtrlName[128];
//  
//  strncpy(MsgCtrlName, msgCtrl.name, elcount(MsgCtrlName));
//  
//  setTestId(1);
//  
//  testStep(gTestIdStr, "To Reset Msg [%s] Cycle to Default.", MsgCtrlName);
//  
//  incTestStepId();
//  
// // cycleRes = TestResetMsgCycleTime(msgCtrl);
//  //@msgCtrl  NEO
//  
//  
//
//  if (cycleRes != 0)
//  {
//    testStepFail(gTestIdStr, "Msg [%s] is failed on Cycle Inject! Please check your msgID.", MsgCtrlName);
//    return -1;
//  }
//  testStepPass(gTestIdStr, "Msg [%s] Cycle is Reset to Default.", MsgCtrlName);
//  return 1;
//}

/* Alive counter disturb for several times.  */
/* Para1: msgID {msg ID }             */
/* Para2: error {0-no error, 1-error} */
/* Para3: alvcnt disturb counter {alvMax} */
//testfunction tstf_Diag_DisturbAlvCnt(dword msgID, int alvMax)
//{
//  //func_Diag_DisturbAlvCnt(msgID, 1, alvMax);
//  @Valeo::HKMC::CRC_TIMING_CRPT::Corrupt_Timing_n_times = alvMax;
//  testStep("0", "To Inject Alive Counter Failure on Msg 0x%X for %d times ", msgID, alvMax);
//  
//  func_Diag_AlvSig_Ctrl(msgID, 1);
//}


/* Crc disturb for several times.  */
/* Para1: msgID {msg ID }             */
/* Para2: error {0-no error, 1-error} */
/* Para3: crc disturb counter {crcMax} */
//testfunction tstf_Diag_DisturbCrc(dword msgID, int crcMax)
//{
////  @Valeo::HKMC::CRC_TIMING_CRPT::Corrupt_Timing_n_times = crcMax;
////  testStep("0", "To Inject CRC Failure on Msg 0x%X for %d times ", msgID, crcMax);
////  func_Diag_CrcSig_Ctrl(msgID, 1);
//}

//neo
testfunction tstf_Diag_MsgDLC_Ctrl(sysvarInt *msgCtrl, word msgDLC)
{
  func_Diag_MsgDLC_Ctrl(msgCtrl, msgDLC);
}
int func_Diag_MsgDLC_Ctrl(sysvarInt *msgCtrl, word msgDLC)
{
  long lenRes;
  char MsgCtrlName[128];
  
  strncpy(MsgCtrlName, msgCtrl.name, elcount(MsgCtrlName));
  
  setTestId(1);
  
  if(msgDLC > 64)                // dlc 0-15
  {
    testStepFail(gTestIdStr,"Msg [%s} DLC Length [%d] is out of Range [0 - 64]!",MsgCtrlName,msgDLC);
    return -1;
  }
  else
  {
    testStep(gTestIdStr, "To Set Msg [%s] DLC Length to [%d].", MsgCtrlName, msgDLC);
  }
  
  
  
  incTestStepId();
  @msgCtrl = msgDLC;
  
  
  if (@msgCtrl != msgDLC)
  {
    testStepFail(gTestIdStr, "Msg [%s] is failed on DLC Length Inject! Please check your msgID.", MsgCtrlName);
    return -1;
  }
  
  testStepPass(gTestIdStr, "Msg [%s] DLC Length is set to [%d]!", MsgCtrlName, msgDLC);
  return 1;
  
}



/* Inject Fault on Msg length      */
/* Para1: msgID {msg ID }          */
/* Para2: msgDLC {msg length, 0-15} */
//testfunction tstf_Diag_MsgDLC_FaultInject(sysvarInt *msgID, word msgDLC)
//{
//  func_Diag_MsgDLC_FaultInject(msgID, msgDLC);
//}

//int func_Diag_MsgDLC_FaultInject(sysvarInt *msgCtrl, word msgDLC)
//{
//  long lenRes;
//  char MsgCtrlName[128];
//  
//  strncpy(MsgCtrlName, msgCtrl.name, elcount(MsgCtrlName));
//  
//  setTestId(1);
//  
//  if(msgDLC > 64)                // dlc 0-15
//  {
//    testStepFail(gTestIdStr,"Msg [%s} DLC Length [%d] is out of Range [0 - 64]!",MsgCtrlName,msgDLC);
//    return -1;
//  }
//  else
//  {
//    testStep(gTestIdStr, "To Set Msg [%s] DLC Length to [%d].", MsgCtrlName, msgDLC);
//  }
//  
//  
//  
//  incTestStepId();
//  @msgCtrl = msgDLC;
//  
//  
//  if (lenRes != 0)
//  {
//    testStepFail(gTestIdStr, "Msg [%s] is failed on DLC Length Inject! Please check your msgID.", MsgCtrlName);
//    return -1;
//  }
//  
//  testStepPass(gTestIdStr, "Msg [%s] DLC Length is set to [%d]!", MsgCtrlName, msgDLC);
//  return 1;
//  
//}
//
///* Remove Fault on Msg Length      */
///* Msg Length is set to default    */
///* Para1: msgID {msg ID }          */
//testfunction tstf_Diag_MsgDLC_FaultRemove(sysvarInt *msgID)
//{
//  func_Diag_MsgDLC_FaultRemove(msgID);
//}
//
//int func_Diag_MsgDLC_FaultRemove(sysvarInt *msgID)
//{
//  long lenRes;
//  char msgCtrl[128];
//  
//  strncpy(msgCtrl, msgID.name, elcount(msgCtrl));
//  
//  setTestId(1);
//  testStep(gTestIdStr, "To Reset Msg [%s] DLC.", msgCtrl);
//  incTestStepId();
//  
////  lenRes = TestResetMsgDLC(msgID);
//  //@msgCtrl  NEO
//
//  if (lenRes != 0)
//  {
//    testStepFail(gTestIdStr, "Msg [%s} is failed on DLC Length Remove! Please check your msgID.", msgCtrl);
//    return -1;
//  }
//  testStepPass(gTestIdStr, "Msg [%s] Length is Reset!", msgCtrl);
//  
//  
//  return 1;
//  
//}

/* Check all DUT App Msg Enabled            */
/* Para1: chkTime {ms-how long to observe}  */
/* This API Only checkd Periodic Msg        */
/* For Event Msg check, please use          */
testfunction tstf_Chk_DutAppMsg_CycleTime(dword chkTime, double min, double max)
{
  func_Chk_DutAppMsg_CycleTime(chkTime, min, max);         // min = 0, means low limit not check.
}

int func_Chk_DutAppMsg_CycleTime(dword chkTime, double min, double max)
{
  word i;
   
  strncpy(rptString,"\0",elCount(rptString));    // clear rptString
  
  setTestId(1);
  testStep(gTestIdStr, "To Check all DUT App Msg are Enabled and Msg Cycles are within [%f - %f].", min, max);
  
  for(i=0; i<dutApp_MsgNum; i++)
  {
    //checkId[i] = ChkStart_MsgRelOccurrenceViolation(dutApp_MsgID[i], min, max);
    snprintf(rptInfo, elcount(rptInfo), "[0x%X] ", dutApp_MsgID[i]);
    strncat(rptString,rptInfo,elCount(rptString));
  }
  
//  checkId[0] = ChkCreate_MsgRelCycleTimeViolation (AVM_TxAppMsg_2AB, min, max);
//  checkId[1] = ChkCreate_MsgRelCycleTimeViolation (AVM_TxAppMsg_135, min, max);
//  checkId[2] = ChkCreate_MsgRelCycleTimeViolation (AVM_TxAppMsg_136, min, max);
//  checkId[3] = ChkCreate_MsgRelCycleTimeViolation (AVM_TxAppMsg_190, min, max);
//  checkId[4] = ChkCreate_MsgRelCycleTimeViolation (AVM_TxAppMsg_191, min, max);
//  checkId[5] = ChkCreate_MsgRelCycleTimeViolation (AVM_TxAppMsg_294, min, max);
//  checkId[6] = ChkCreate_MsgRelCycleTimeViolation (AVM_TxAppMsg_390, min, max);
  //checkId[7] = ChkStart_MsgRelOccurrenceViolation(AVM_TxTestMsg_6EC, min, max); //Event Msg
  
  for(i=0; i<dutApp_MsgNum; i++)
  {
    TestAddCondition(checkId[i]);
  }
  
  if(chkTime > 0)
  {
    TestWaitForTimeout(chkTime);
  }
  
  for(i=0; i<dutApp_MsgNum; i++)
  {
    TestRemoveCondition(checkId[i]);
  }
  
  incTestStepId();
  testStepPass(gTestIdStr,"Below Msgs Cycles are checked during %d ms :\n %s",chkTime,rptString);
  return 1;
}

/**/ //this api is use for cycle_Msg?
//testfunction tstf_Chk_Msg_CycleTime(word msgID, dword chkTime, double min, double max)
//{
//  func_Chk_Msg_CycleTime(msgID, chkTime, min, max);
//}
//
//int func_Chk_Msg_CycleTime(dword msgID, dword chkTime, double min, double max)
//{
//  dword checkId;
//  
//  
//  strncpy(rptString,"\0",elCount(rptString));    // clear rptString
//  
//  setTestId(1);
//  testStep(gTestIdStr, "To Check Msg [0x%X] Cycle is within [%f - %f]*[ DB defined Cycle]ms.", msgID, min, max);
//  
//  incTestStepId();
//  
//  TestAddCondition(checkId);
//  
//  if(chkTime > 0)
//  {
//    TestWaitForTimeout(chkTime);
//  }
// 
//  TestRemoveCondition(checkId);
//  
//  testStepPass(gTestIdStr,"Msg [0x%X] Cycle is checked during %d ms.\n",msgID,chkTime);
//  
//  
//  
//  return 1;
//}

/* Check all DUT App Msg Disabled           */
/* Para1: chkTime {ms-how long to observe}  */
/* Note: Only periodic Msg is checked       */
/* Event Msg Check please use:              */
testfunction tstf_Chk_DutAppMsg_Disabled(dword chkTime)
{
  func_Chk_DutAppMsg_Disabled(chkTime);
}

int func_Chk_DutAppMsg_Disabled(dword chkTime)
{
  word i;
  
  strncpy(rptString,"\0",elCount(rptString));    // clear rptString
  
  setTestId(1);
  testStep(gTestIdStr, "To Check DUT App Msg are Disabled. Msg Num = [%d].",dutApp_MsgNum);
  
  for(i=0; i<dutApp_MsgNum; i++)
  {
    checkId[i] = ChkStart_MsgOccurrenceCount (dutApp_MsgID[i], 0);
    
    snprintf(rptInfo, elcount(rptInfo), "[0x%X] ", dutApp_MsgID[i]);
    strncat(rptString,rptInfo,elCount(rptString));
  }
  
  for(i=0; i<dutApp_MsgNum; i++)
  {
    TestAddCondition(checkId[i]);
  }
  
  if(chkTime > 0)
  {
    TestWaitForTimeout(chkTime);
  }
  
  for(i=0; i<dutApp_MsgNum; i++)
  {
    TestRemoveCondition(checkId[i]);
  }
  
  incTestStepId();
  testStepPass(gTestIdStr,"Below Msgs are checked during %d ms :\n %s",chkTime,rptString);
  return 1;
}

/* Check all DUT NM Msg Disabled           */
/* Para1: chkTime {ms-how long to observe} */
testfunction tstf_Chk_DutNmMsg_Disabled(dword chkTime)
{
  func_Chk_DutNmMsg_Disabled(chkTime);
}

int func_Chk_DutNmMsg_Disabled(dword chkTime)
{
  word i;
  
  strncpy(rptString,"\0",elCount(rptString));    // clear rptString
  
  setTestId(1);
  testStep(gTestIdStr, "To Check DUT NM Msg is Disabled. Msg Num = [%d].",dutNm_MsgNum);
  
  for(i=0; i<dutNm_MsgNum; i++)
  {
    checkId[i] = ChkStart_MsgOccurrenceCount (dutNm_MsgID[i], 0);
    
    snprintf(rptInfo, elcount(rptInfo), "[0x%X] ", dutNm_MsgID[i]);
    strncat(rptString,rptInfo,elCount(rptString));
  }
  
  for(i=0; i<dutNm_MsgNum; i++)
  {
    TestAddCondition(checkId[i]);
  }
  
  if(chkTime > 0)
  {
    TestWaitForTimeout(chkTime);
  }
  
  for(i=0; i<dutNm_MsgNum; i++)
  {
    TestRemoveCondition(checkId[i]);
  }
  
  incTestStepId();
  testStepPass(gTestIdStr,"Below Msgs are checked during %d ms :\n %s",chkTime,rptString);
  return 1;
}

