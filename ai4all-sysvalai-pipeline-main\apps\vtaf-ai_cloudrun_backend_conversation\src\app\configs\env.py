from pydantic_settings import BaseSettings

class AppSettings(BaseSettings):
    SERVICES_PROJECT_ID: str
    SERVICES_DATASET_ID: str
    FIRESTORE_DATABASE_ID: str
    FIRESTORE_SESSION_COLLECTION: str
    SERVICES_ACCOUNT_EMAIL: str
    IAP_CLIENT_ID: str
    VTAF_API_URL: str
    COLLECTION_NAME: str
    class Config:
        env_file = "app\configs\.env"
        case_sensitive = True

settings = AppSettings()


