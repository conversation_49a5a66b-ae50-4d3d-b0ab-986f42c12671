#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地调试测试脚本 - 不需要外部认证
"""

import sys
import os
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_config_loading():
    """测试配置加载"""
    print("=== 测试配置加载 ===")
    
    try:
        from configs.env import settings
        print(f"✅ 配置加载成功")
        print(f"  - PROJECT_ID: {settings.PROJECT_ID}")
        print(f"  - REGION: {settings.REGION}")
        print(f"  - DEFAULT_MODEL: {settings.DEFAULT_MODEL}")
        return True
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def test_data_processing():
    """测试数据处理功能"""
    print("\n=== 测试数据处理功能 ===")
    
    try:
        from utils.data_processing import parse_test_cases, parse_test_script
        
        # 测试用例解析
        test_response = """
Confidence Score: 90%

**Test Case ID**: TC_DEBUG_001
**Covered Requirement ID**: REQ_DEBUG_001
**Test Objective**: Test local debugging functionality
**Test Condition**: Local environment is set up correctly
**Test Action**: Run debug test
**Test Expectation**: All components work correctly
"""
        
        result = parse_test_cases(test_response)
        confidence = result.get('Confidence Score', '')
        test_cases = result.get('Test Cases', [])
        
        print(f"✅ 测试用例解析成功")
        print(f"  - 置信度分数: {confidence}%")
        print(f"  - 测试用例数量: {len(test_cases)}")
        
        if test_cases:
            first_case = test_cases[0]
            print(f"  - 第一个测试用例ID: {first_case[0] if len(first_case) > 0 else 'N/A'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据处理测试失败: {e}")
        return False

def test_config_service():
    """测试配置服务"""
    print("\n=== 测试配置服务 ===")
    
    try:
        from services.config_service import ConfigurationService
        
        config_service = ConfigurationService()
        
        # 测试配置文件存在性
        config_exists = config_service.config_exists()
        print(f"✅ 配置文件检查: {'存在' if config_exists else '不存在'}")
        
        # 测试配置读取
        if config_exists:
            config = config_service.get_configuration()
            print(f"✅ 配置读取成功")
            print(f"  - prompt_sheet: {config.get('prompt_sheet', 'N/A')}")
            print(f"  - test_spec_sheet: {config.get('test_spec_sheet', 'N/A')}")
        
        # 测试模型选项
        model_options = config_service.get_model_options()
        gemini_models = model_options.get('gemini_models', [])
        print(f"✅ 模型选项获取成功")
        print(f"  - Gemini模型数量: {len(gemini_models)}")
        print(f"  - 第一个Gemini模型: {gemini_models[0] if gemini_models else 'N/A'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置服务测试失败: {e}")
        return False

def test_logger():
    """测试日志功能"""
    print("\n=== 测试日志功能 ===")
    
    try:
        from utils.logger import get_logger
        
        logger = get_logger("test_debug")
        
        # 测试不同级别的日志
        logger.info("这是一条信息日志")
        logger.warning("这是一条警告日志")
        logger.debug("这是一条调试日志")
        
        print("✅ 日志功能测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 日志功能测试失败: {e}")
        return False

def test_ai_service_init():
    """测试AI服务初始化（不实际调用API）"""
    print("\n=== 测试AI服务初始化 ===")
    
    try:
        # 只测试导入和基本初始化，不调用实际API
        from services.ai_service import AIService, GEMINI_MODELS
        
        print(f"✅ AI服务模块导入成功")
        print(f"  - 支持的Gemini模型数量: {len(GEMINI_MODELS)}")
        print(f"  - 第一个模型: {GEMINI_MODELS[0] if GEMINI_MODELS else 'N/A'}")
        
        # 注意：这里不实际初始化AIService，因为需要认证
        print("⚠️  跳过实际AI服务初始化（需要认证文件）")
        
        return True
        
    except Exception as e:
        print(f"❌ AI服务初始化测试失败: {e}")
        return False

def test_batch_processor_init():
    """测试批处理器初始化"""
    print("\n=== 测试批处理器初始化 ===")
    
    try:
        from services.batch_processor import BatchProcessor, CASE_HEADER_INDEX
        
        print(f"✅ 批处理器模块导入成功")
        print(f"  - 测试用例头部字段数量: {len(CASE_HEADER_INDEX)}")
        print(f"  - 第一个字段: {CASE_HEADER_INDEX[0] if CASE_HEADER_INDEX else 'N/A'}")
        
        # 注意：这里不实际初始化BatchProcessor，因为需要AI服务
        print("⚠️  跳过实际批处理器初始化（需要AI服务）")
        
        return True
        
    except Exception as e:
        print(f"❌ 批处理器初始化测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 VTAF AI Multiprompt 本地调试测试")
    print("=" * 60)
    
    tests = [
        ("配置加载", test_config_loading),
        ("数据处理功能", test_data_processing),
        ("配置服务", test_config_service),
        ("日志功能", test_logger),
        ("AI服务初始化", test_ai_service_init),
        ("批处理器初始化", test_batch_processor_init)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'=' * 60}")
    print("🎯 测试总结")
    print(f"{'=' * 60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"总体结果: {passed}/{total} 测试通过")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed == total:
        print(f"\n🎉 所有本地调试测试通过！")
        print("💡 下一步：添加 Google Cloud 认证文件以测试完整功能")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查相关配置")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
