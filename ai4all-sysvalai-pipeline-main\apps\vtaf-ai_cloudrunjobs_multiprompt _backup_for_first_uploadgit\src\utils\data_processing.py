import re
from typing import Dict, List, Any
from utils.logger import get_logger

logger = get_logger(__name__)

# 测试用例字段列表
fields = [
    "Test Case ID", "Covered Requirement ID", "Test Objective", "Test Condition", "Test Action",
    "Test Expectation"
]

def parse_test_cases(text: str) -> Dict[str, Any]:
    """
    解析AI响应中的测试用例
    Args:
        text: AI响应文本
    Returns:
        包含测试用例和置信度分数的字典
    """
    try:
        results = {}  # 使用字典存储结果

        # 1. 首先提取 Confidence Score
        confidence_matches = re.findall(r"Confidence Score:\s*(?:\*+)?\s*(\d+)%", text)  # 添加(?:\*+)?
        results["Confidence Score"] = confidence_matches[0] if confidence_matches else ""

        # 2. 使用 Test Case ID 分割文本 - 更灵活的匹配模式
        case_texts = re.findall(
            r"(?i)(?:\*+|#+|:+|：+)*\s*Test Case ID\s*(?:\*+|#+|:+|：+)*\s*:.*?(?=(?:\*+|#+|:+|：+)*\s*Test Case ID\s*(?:\*+|#+|:+|：+)*\s*:|\Z)",
            text, re.DOTALL)

        # 3. 解析每个测试用例
        cases = []
        for case_text in case_texts:
            case_data = parse_single_case(case_text)  # 解析其他字段
            case_data_list = [case_data.get(field, '') for field in fields[:]]
            cases.append(case_data_list)
        results["Test Cases"] = cases  # 将测试用例列表存储到 results 字典中

        return results # 返回字典

    except Exception as e:
        logger.error(f"Failed to parse test cases: {e}")
        return {"Test Cases": [], "Confidence Score": ""}

def parse_test_script(text: str) -> Dict[str, Any]:
    """
    解析测试脚本，提取Confidence Score以及CAPL脚本
    Args:
        text: 包含测试脚本的文本
    Returns:
        包含置信度分数和CAPL脚本的字典
    """
    results = {}  # 初始化一个空字典来存储结果

    try:
        # 1. 提取置信度分数
        # 使用正则表达式查找 "Confidence Score:" 后面的数字百分比
        # (?:\*+)? 用于匹配可选的星号
        confidence_matches1 = re.search(r"confidence level is\s*\**(\d+)%", text)  # 改进正则表达式
        confidence_matches2 = re.findall(r"Confidence Score:\s*(?:\*+)?\s*(\d+)%", text)  # 添加(?:\*+)?
        if confidence_matches1:
            results["Confidence Score"] = confidence_matches1.group(1) if confidence_matches1 else ""
        else:
            # 如果找到匹配项，则将第一个匹配项转换为整数并存储在字典中;否则，存储 None
            results["Confidence Score"] = int(confidence_matches2[0]) if confidence_matches2 else ""

        # 2. 提取 CAPL 脚本
        # 使用正则表达式查找以 "```capl" 开头和 "```" 结尾的 CAPL 脚本
        # re.DOTALL 允许 "." 匹配换行符
        capl_script_match = re.search(r"```capl\n(.*?)```", text, re.DOTALL)

        # 如果找到匹配项，则提取脚本内容并存储在字典中;否则，存储 None
        results["Capl"] = capl_script_match.group(1) if capl_script_match else None

    except (IndexError, AttributeError) as e:  # 处理潜在的错误，例如找不到匹配项
        logger.error(f"解析测试脚本时出错: {e}")  # 打印错误消息
        return {}  # 出错时返回空字典

    return results  # 返回包含结果的字典

def parse_single_case(text: str) -> Dict[str, str]:
    """
    解析单条测试用例文本，将其转换为一个字典。

    Args:
        text (str): 单条测试用例文本。

    Returns:
        dict: 解析后的测试用例数据，以字段名为键。
    """
    case_data = {}
    current_field = None

    for line in text.splitlines():
        line = line.strip()
        if not line:
            if current_field is not None:
                case_data[current_field] += "\n"
            continue

        match = None
        for field in fields:
            # 使用正则表达式进行模糊匹配，忽略大小写，并可选匹配冒号
            match = re.match(rf"(?i)(?:\*+|#+|:+|：+)*\s*{field}s?\s*(?:\*+|#+|:+|：+)*\s*:?\s*(.*)", line)
            if match:
                # 如果遇到新的字段，并且当前字段不是 Test Expectation，则保存当前字段内容
                if current_field is not None and current_field != "Test Expectation":
                    case_data[current_field] = clean_field_value(case_data[current_field])

                current_field = field
                case_data[current_field] = match.group(1).strip().replace('*', '')
                break
        else:
            if current_field is not None:
                case_data[current_field] += " " + line.replace('*', '')

    # 处理最后一个字段
    if current_field is not None:
        case_data[current_field] = clean_field_value(case_data[current_field])

    # 逐个处理 Test Condition/Test Action/Test Expectation，在序号前加换行
    for field in ("Test Condition", "Test Action", "Test Expectation"):
        if field in case_data:
            case_data[field] = re.sub(r'(?<!\n)(\d+\.)', r'\n\1', case_data[field])
        # 处理 Covered Requirement ID，将逗号替换为换行符
    if "Covered Requirement ID" in case_data:
        case_data["Covered Requirement ID"] = case_data["Covered Requirement ID"].replace(",", "\n").replace("，", "\n")

    # 处理 Test Expectation 字段的无效内容
    if "Test Expectation" in case_data:
        test_expectation = case_data["Test Expectation"]
        test_expectation = re.sub(r"\n\s*(?!(\d+\.|$))[^$]*", "", test_expectation, flags=re.MULTILINE)
        case_data["Test Expectation"] = test_expectation  # 更新字典

    return case_data

def clean_field_value(value: str) -> str:
    """
    清理字段值，包括：
    - 移除开头和结尾的空白字符
    - 将连续的空白字符替换为单个空格
    - 将逗号和顿号替换为换行符
    """
    value = value.strip()
    # value = re.sub(r'\s+', ' ', value)
    # value = value.replace(",", "\n").replace("，", "\n")

    # if any(field in value for field in ("Test Condition", "Test Action", "Test Expectation")):
    #     value = re.sub(r'(?<!\n)(\d+\.)', r'\n\1', value)
    #
    # if "Test Expectation" in value:
    #     value = re.sub(r"\n\s*(?!\d+\.)(.*)", "", value)

    return value

def print_test_cases(results: Dict[str, Any]):
    """打印测试用例（用于调试）"""
    print(f"Confidence Score: {results.get('Confidence Score', '')}")  # 打印Confidence Score
    print("-" * 20)
    for case_data in results.get("Test Cases", []):
        for i, field_value in enumerate(case_data):
            if i < len(fields):
                print(f"{fields[i]}: {field_value}")
        print()

def count_tokens(text: str) -> int:
    """估算token数量"""
    # 简单的token计算，实际应该使用模型的tokenizer
    return len(text.split())

def validate_response(response: str) -> bool:
    """验证响应格式"""
    if not response or response.startswith("Error:"):
        return False
    return True

def extract_confidence_score(text: str) -> str:
    """提取置信度分数"""
    confidence_matches = re.findall(r"Confidence Score:\s*(?:\*+)?\s*(\d+)%", text)
    return confidence_matches[0] if confidence_matches else ""

def format_test_case_for_sheet(case_data: List[str]) -> List[str]:
    """格式化测试用例数据以便写入表格"""
    # 确保有6个字段
    while len(case_data) < 6:
        case_data.append("")
    return case_data[:6]  # 只取前6个字段