"""
qdrant.py

Defines API endpoints for interacting with the Qdrant vector database.
Includes functionality for health checks, adding documents, and performing vector-based searches.

The endpoints rely on the QdrantProcessor for interfacing with the database and
require the collection name to be provided in each request.
"""

import logging
import fastapi

from fastapi import APIRouter, HTTPException
from fastapi.responses import ORJSONResponse
from qdrant_client.models import Filter

from app.routers.v1.schemas.qdrant_requests import AddDocumentRequest, VectorSearchRequest, CountDocumentsRequest
from app.dependencies.qdrant_auth import get_qdrant_processor

logger = logging.getLogger("vtaf")
qdrant_router = APIRouter(prefix="/vectordb")


@qdrant_router.get(
    "/health",
    response_class=ORJSONResponse,
    description="Health check endpoint for the Knowledgebase service",
    summary="Health Check API",
    status_code=fastapi.status.HTTP_200_OK,
)
async def health_check() -> dict:
    """
    Health check endpoint for the Qdrant vector store service.
    Confirms that the service is reachable and functioning.
    """
    logger.info("Qdrant Health check request received")
    return {
        "name": "Qdrant Service",
        "version": "v1",
        "status": "ok",
    }


@qdrant_router.post(
    "/documents/add",
    response_class=ORJSONResponse,
    description="Add a document to the Qdrant vector database",
    summary="Add Document API",
    status_code=fastapi.status.HTTP_200_OK,
)
async def add_document(request: AddDocumentRequest):
    """
    Adds a document to the specified Qdrant collection.

    Args:
        request (DocumentAddRequest): Contains the collection name, content, and metadata.

    Returns:
        JSON response from the vector store upon successful insertion.

    Raises:
        HTTPException: If an error occurs while adding the document.
    """
    try:
        processor = await get_qdrant_processor(request.collection_name)
        results = await processor.add_document(
            content=request.content,
            metadata=request.metadata,
        )
        return results
    except Exception as e:
        logger.error(f"Error adding document: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@qdrant_router.post(
    "/documents/search",
    response_class=ORJSONResponse,
    description="Search for documents in the Qdrant vector database",
    summary="Search Documents API",
    status_code=fastapi.status.HTTP_200_OK,
)
async def search_documents(request: VectorSearchRequest):
    """
    Performs a semantic search within a Qdrant collection using vector similarity.

    Args:
        request (SearchRequest): Includes the collection name, query string,
                                 score threshold, result limit, and optional filters.

    Returns:
        dict: A dictionary containing the matched documents.

    Raises:
        HTTPException: If an error occurs during the search.
    """
    try:
        processor = await get_qdrant_processor(request.collection_name)
        qdrant_filter = Filter(**request.filter) if request.filter else None

        results = await processor.search(
            query=request.query,
            limit=request.limit,
            score_threshold=request.score_threshold,
            filter=qdrant_filter,
        )
        return results
    except Exception as e:
        logger.error(f"Error searching documents: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@qdrant_router.post(
    "/documents/count",
    response_class=ORJSONResponse,
    description="Counts documents in the Qdrant vector database",
    summary="Count Documents API",
    status_code=fastapi.status.HTTP_200_OK,
)   
async def count_documents(request: CountDocumentsRequest):
    """
    Counts documents in a Qdrant collection, optionally applying a filter.

    Args:
        request (CountDocumentsRequest): Includes the collection name and optional filters.

    Returns:
        dict: A dictionary containing the count of documents.

    Raises:
        HTTPException: If an error occurs during the count operation.
    """
    try:
        processor = await get_qdrant_processor(request.collection_name)
        qdrant_filter = Filter(**request.filter) if request.filter else None

        results = processor.count_documents(filter=qdrant_filter)
        return results
    except Exception as e:
        logger.error(f"Error counting documents: {e}")
        raise HTTPException(status_code=500, detail=str(e))
        
