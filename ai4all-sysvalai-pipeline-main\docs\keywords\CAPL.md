# Group {CAPL}
## Add_CAPL

- [ ] TestStand    
- [x] CAPL    
- [x] Python     


**Path:**


**Descriptions:** *This Keyword is to add a snippet in a CAPL Code*


***Command Set:***

	CodeSnippet
		-CodeSnippet=Snippet


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Snippet| String| NA| TestWaitForTimeout(10);|  Snippet to add in CAPL Code| Required

## Set_CAPL

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Set_CAPL.seq*


**Descriptions:** *This keword is developed for CAPL related function*


***Command Set:***

	TestTrigger
		-TestTrigger=Module;Test


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Module| String| NA| |  Holds the par value for CAPL Module
| Test| String| NA| | Holds the par value for CAPL Test

## Set_DTC

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** **


**Descriptions:** *[CAPL] : this is an CAPL keyword which is used to set the DTC. *


***Command Set:***

	FaultMessageMechanism
		-FaultMessageMechanism=Status


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Status| Ring| AllowRTval=False,Active,InActive| | set active or deactive

## Set_Vosstrex

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Set_Vosstrex.seq*


**Descriptions:** *This Keyword is developed to control Vosstrex Panel*


***Command Set:***

	Start
		-Start=
	Stop
		-Stop=
	Panel
		-Panel=Control;Value
	Launch
		-Launch=


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Control| String| NA| Xyz|  Item to Conttol in Vosstrex
| Value| String| NA| | Data to pass to Vosstrex Panel. 
E.g 2;5 etc

