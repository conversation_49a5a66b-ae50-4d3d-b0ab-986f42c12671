/*@!Encoding:1252*/
/*autoTest_UPA_API.cin                                                    */
/*Standard Test Lib for UPA Control, Obstacle seeting and Fault Injection */
/*Author: <PERSON><PERSON><PERSON>, 2020/3/15 ,  Version 2.0, Released                  */

variables
{
  /*Need Config for each project */
  byte   comChannel      = 0x3;                      // 0x2 - CAN2, 0x3 - CAN3
  byte   pimSlot         = 0x1;                      // PIM slot, default is 0x1
  dword  aWaitOnBus      = 500;                      // Time to wait for msg on the bus -ms
  dword  aWaitC5Ack      = 500;                      // Time to wait for Castle ACK msg - 0x400 -ms
  byte   switchPressType = 1;                        // 0 - Normal, 1- SC_TO_GND, 2-SC_TO_BAT
  byte   maxSlot         = 8;                        // Max PIM Slots on a Castle5 Chassis
  
  const dword minWaitTime = 5;                       //minimum wait time 5ms
  const word  Echo2nd_min = 20;                      // second echo min 2cm
  const word  Echo2nd_max = 100;                     // second echo min 10cm
  /*Config End*/
  

  /* Msg Defination for Castle Communication */
  message * castleRequest;                
  message * castleResponse;   
  
  message SensorDistanceUpdated msg_C5Ack;
  message SystemClock           msg_C5Clock;
  message SensorDistanceFIL msg_SetFILDistance;
  message SensorDistanceFIR msg_SetFIRDistance;
  message SensorDistanceFOL msg_SetFOLDistance;
  message SensorDistanceFOR msg_SetFORDistance;
  message SensorDistanceFSL msg_SetFSLDistance;
  message SensorDistanceFSR msg_SetFSRDistance;
  message SensorDistanceRIL msg_SetRILDistance;
  message SensorDistanceRIR msg_SetRIRDistance;
  message SensorDistanceROL msg_SetROLDistance;
  message SensorDistanceROR msg_SetRORDistance;
  message SensorDistanceRSL msg_SetRSLDistance;
  message SensorDistanceRSR msg_SetRSRDistance;
  
  message SensorDistanceUpdated getMsg_0x400;
  message *getMsg;
  /* End of Msg Defination*/
  
  /*enum of Senor ID*/
  enum enumSensorNameALL { SENSOR_RSL=1, SENSOR_ROL=2, SENSOR_RIL=3, SENSOR_RIR=4, SENSOR_ROR=5, SENSOR_RSR=6,
                           SENSOR_FSL=9, SENSOR_FOL=10, SENSOR_FIL=11, SENSOR_FIR=12, SENSOR_FOR=13, SENSOR_FSR=14 };  
  
  char strSensor[16][24] = {"notDef","SENSOR_RSL","SENSOR_ROL","SENSOR_RIL","SENSOR_RIR","SENSOR_ROR","SENSOR_RSR",
                            "notDef","notDef","SENSOR_FSL","SENSOR_FOL","SENSOR_FIL","SENSOR_FIR","SENSOR_FOR","SENSOR_FSR","notDef"};
  
  enum SensorPWR_ID {sensorPwr_Front = 0, sensorPwr_Rear = 1};
  enum SensorPWR_Error {pwr_normal=0, pwr_SC_TO_GND=1, pwr_SC_TO_VCC=2, pwr_SC_TO_UB12V=3};
  
  char strSensorPWR[3][24] = {"sensorPwr_Front", "sensorPwr_Rear","notDef"};
  char strPWRError[6][24]  = {"pwr_normal", "pwr_SC_TO_GND","pwr_SC_TO_VCC","pwr_SC_TO_UB12V","notDef","notDef"};
  /*End*/
  
  
  /*enum of Senor Failure Type*/
  enum enumSensorFailType {
    NORMAL,           //00
    SC_TO_GND,        //01 Short circuit to GND
    SC_TO_VCC,        //02 Short circuit to plus
    OPEN,             //03 Sensor no response
    OPEN_CIRCUIT,     //04 Open circuit
    ATTENUATION_TIME, //05 Short Attenuationtime
    RECIEIVE_PATH,    //06 Open Receivepath
    VERIFY,           //07 Verification
    ICE_MUD,          //08 ICE/MUD
    HEAVY_NOISE       //09
    };
  
   char strError[10][24] = {"NORMAL","SC_TO_GND","SC_TO_VCC","OPEN","OPEN_CIRCUIT",
                          "ATTENUATION_TIME","RECIEIVE_PATH","VERIFY","ICE_MUD","HEAVY_NOISE"};
  /*End*/
}

on preStart
{
  func_UPA_InitPim();
  func_UPA_InitBus();
}

testfunction tstf_UPA_Init()
{
  func_SetBusContext(castleBusChn);                                 //Castle 5 Bus
  
  func_Get_PIM_Slot_ID();
  func_UPA_InitPim();
  func_UPA_InitBus();
  func_UPA_InitFailure();
  func_UPA_InitSensor_NACK(0);
  
  func_SetBusContext(ecuBusChn);
  
}

dword func_UPA_InitPim()
{
  pimSlot = @sysvar::Castle5_Control::pim_slot;
  @sysvar::Castle5_Control::enable_2ndEcho = 1;
  
  return 1;
}

dword func_UPA_InitBus()
{
  /*To set Msg ID and Channels */
  castleRequest.id  = 0x100 + @sysvar::Castle5_Control::pim_slot;            // PIM slot is 1, so Diag ID is 0x100+ slot ID
  castleResponse.id = 0x200 + @sysvar::Castle5_Control::pim_slot;            // PIM slot is 1, so Diag ID is 0x200+ slot ID
  
  castleRequest.msgChannel      = comChannel;
  castleResponse.msgChannel     = comChannel;
  
  msg_C5Ack.msgChannel          = comChannel;
  msg_C5Clock.msgChannel        = comChannel;
  getMsg_0x400.msgChannel       = comChannel;
  msg_SetFILDistance.msgChannel = comChannel;
  msg_SetFIRDistance.msgChannel = comChannel;
  msg_SetFOLDistance.msgChannel = comChannel;
  msg_SetFORDistance.msgChannel = comChannel;
  msg_SetFSLDistance.msgChannel = comChannel;
  msg_SetFSRDistance.msgChannel = comChannel;
  
  msg_SetRILDistance.msgChannel = comChannel;
  msg_SetRIRDistance.msgChannel = comChannel;
  msg_SetROLDistance.msgChannel = comChannel;
  msg_SetRORDistance.msgChannel = comChannel;
  msg_SetRSLDistance.msgChannel = comChannel;
  msg_SetRSRDistance.msgChannel = comChannel;
  /* End */
  
  return 1;
}

/* To Get PIM slot ID automatically           */
/* Author Xiaokui YI, Date 2020/07/26         */
testfunction tstf_Get_PIM_Slot_ID()
{
  func_SetBusContext(castleBusChn); 
  func_Get_PIM_Slot_ID();
  func_SetBusContext(ecuBusChn); 
  
}

byte func_Get_PIM_Slot_ID()
{
  byte expByte;
  byte i, getSlot;               // to get PIM Slot
  dword ret1, ret2;
  ret1 = 0;
  ret2 = 0;
 
  castleRequest.msgChannel      = comChannel;
  castleResponse.msgChannel     = comChannel;
  
  /*Step 1, to compose the diag request */
  castleRequest.dlc = 0x8;
  castleRequest.byte(0) = 0x2;
  castleRequest.byte(2) = 0x10;                     // Klemme15 - Lid: 0x10
  castleRequest.byte(1) = 0x19;                     // Output "On" - Sid: 0x19
  
  testStepPass("1","To Set PIM KL15 On.");
    
  /* Step 2, to send a request with ID:getSlot, to check if PIM response */
  for(getSlot=1; getSlot<maxSlot; getSlot++){
    
    castleRequest.id  = 0x100 + getSlot;          // PIM slot is 1, so Diag ID is 0x100+ slot ID
    castleResponse.id = 0x200 + getSlot;          // PIM slot is 1, so Diag ID is 0x200+ slot ID
    
    ret1 = func_sendMsg_onBus(castleRequest);
    if(ret1 != 1){
      testStepFail("2","PIM Control Request hex[0x%X : %02X %02X %02X %02X] is not Sent to the Bus!", castleRequest.id, castleRequest.byte(1),castleRequest.byte(2),castleRequest.byte(3),castleRequest.byte(4));
      return 0;
    }
    
    /*Step 3, to await and check PIM response */
    ret2 = testWaitForMessage(castleResponse.id, aWaitOnBus);
    if(ret2 != 1){
      testStepWarning("3","PIM Response [0x%X] is not detected on the bus within %d ms.", castleResponse.id, aWaitOnBus);
    }
    else{
      break;
    }
    
  }
  
  if(getSlot >= maxSlot){
    testStepFail("4", "No PIM Response, PIM Slot ID is not obtained, default as 0x1. [ %d ]", getSlot);
    @sysvar::Castle5_Control::pim_slot = 1;                    
    return 0;
  }
  
  testStepPass("5", "PIM Slot ID is detected [ %d ], PIM ID is automatically Set.", getSlot);
  @sysvar::Castle5_Control::pim_slot = getSlot;
 
  return getSlot;
}


/*Press UPA button for a time */
testfunction tstf_PIM_PressRelease_UPA_Button(dword press_ms)
{
  func_SetBusContext(castleBusChn); 
  func_PIM_SwitchControl(0x2, 0x1);               // UPA, SC_TO_GND
  testWaitForTimeout(press_ms+1);
  func_PIM_SwitchControl(0x2, 0x0);               // UPA, SC_TO_GND
  func_SetBusContext(ecuBusChn); 
}

/*Press P4U button for a time */
testfunction tstf_PIM_PressRelease_P4U_Button(dword press_ms)
{
  func_SetBusContext(castleBusChn); 
  func_PIM_SwitchControl(0x3, 0x1);               // P4U, SC_TO_GND
  testWaitForTimeout(press_ms+1);
  func_PIM_SwitchControl(0x3, 0x0);               // P4U, SC_TO_GND
  func_SetBusContext(ecuBusChn); 
}

/* To shortcut UPA or P4U button to GND or BAT or Normal                */
/* parameter 1: switchID, UPA - 0x2, P4U - 0x3, etc                     */
/* parameter 2: Presstype, NORMAL - 0, SC_TO_GND - 1, SC_TO_VOLTAGE - 2 */
/* Author Xiaokui YI, Date 2020/06/06                                   */
dword func_PIM_SwitchControl(byte switchID, dword Presstype)
{
  byte expByte;
  dword ret1, ret2;
  ret1 = 0;
  ret2 = 0;
  
  castleRequest.id  = 0x100 + @sysvar::Castle5_Control::pim_slot;          // PIM slot is 1, so Diag ID is 0x100+ slot ID
  castleResponse.id = 0x200 + @sysvar::Castle5_Control::pim_slot;          // PIM slot is 1, so Diag ID is 0x200+ slot ID
  castleRequest.msgChannel      = comChannel;
  castleResponse.msgChannel     = comChannel;
  
  castleRequest.dlc = 0x8;
  castleRequest.byte(0) = 0x3;
  castleRequest.byte(2) = 0x3D;              // Switch - Lid: 0x3D
  castleRequest.byte(3) = Presstype;         // NORMAL - 0, SC_TO_GND - 1, SC_TO_VOLTAGE - 2
  
  if(switchID == 0x2 && Presstype != 0){
    castleRequest.byte(1) = 0x2;             // UPA Switch
    testStepPass("1","To Press UPA Button.");
  }
  if(switchID == 0x3 && Presstype != 0){
    castleRequest.byte(1) = 0x3;             // P4U Switch
    testStepPass("1","To Press P4U Button.");
  }
  if(switchID == 0x2 && Presstype == 0){
    castleRequest.byte(1) = 0x2;             // UPA Switch
    testStepPass("1","To Release UPA Button.");
  }
  if(switchID == 0x3 && Presstype == 0){
    castleRequest.byte(1) = 0x3;             // P4U Switch
    testStepPass("1","To Release P4U Button.");
  }
  
  
  
  ret1 = func_sendMsg_onBus(castleRequest);
  if(ret1 != 1){
    testStepFail("2","Press Request hex[0x%X : %02X %02X %02X %02X] is not Sent to the Bus!", castleRequest.id, castleRequest.byte(1),castleRequest.byte(2),castleRequest.byte(3),castleRequest.byte(4));
    return 0;
  }
  else{
    testStepPass("2","Press Request hex[0x%X : %02X %02X %02X %02X] is Sent to the Bus!", castleRequest.id, castleRequest.byte(1),castleRequest.byte(2),castleRequest.byte(3),castleRequest.byte(4));
  }
  
  ret2 = testWaitForMessage(castleResponse.id, aWaitOnBus);
  if(ret2 != 1){
    testStepFail("3","Castle Response [0x%X] is not detected on the bus within %d ms.", castleResponse.id, aWaitOnBus);
    return 0;
  }
  
  testGetWaitEventMsgData(getMsg);
  expByte = 0x40 + castleRequest.byte(1);
  if(getMsg.byte(1) != expByte){
    testStepFail("3", "Castle Response [0x%X] is received, but data hex[%02X %02X %02X %02X] is not as expected [%02X].", castleResponse.id,  getMsg.byte(0), getMsg.byte(1), getMsg.byte(2), getMsg.byte(3), expByte);
    return 0;
  }
  else{
    testStepPass("3", "Button is Pressed. Castle Response [0x%X] is received: hex[%02X %02X %02X %02X].", castleResponse.id, getMsg.byte(0), getMsg.byte(1),getMsg.byte(2),getMsg.byte(3));
  }
  
  return expByte;
}

/*To Reset ECU power, turn power off and on           */
/*Para 1: time_ms, the time between off and on        */
/*To use this API, the ECU power must be connected to */
/*PIM, instead of external Power supply.              */
testfunction tstf_PIM_EcuPower_Reset(dword time_ms)
{
  func_SetBusContext(castleBusChn); 
  func_PIM_KL15_Control(0);
  func_PIM_KL30_Control(0);
  testWaitForTimeout(time_ms+1);
  func_PIM_KL15_Control(1);
  func_PIM_KL30_Control(1);
  func_SetBusContext(ecuBusChn); 
}

/* To set PIM KL15 On or Off           */
/* parameter 1: onOff, 0-Off, 1-On     */
/* Author Xiaokui YI, Date 2020/07/26  */
dword func_PIM_KL15_Control(byte onOff)
{
  byte expByte;
  dword ret1, ret2;
  ret1 = 0;
  ret2 = 0;
  
  castleRequest.id  = 0x100 + @sysvar::Castle5_Control::pim_slot;          // PIM slot is 1, so Diag ID is 0x100+ slot ID
  castleResponse.id = 0x200 + @sysvar::Castle5_Control::pim_slot;          // PIM slot is 1, so Diag ID is 0x200+ slot ID
  castleRequest.msgChannel      = comChannel;
  castleResponse.msgChannel     = comChannel;
  
  /*Step 1, to compose the diag request */
  castleRequest.dlc = 0x8;
  castleRequest.byte(0) = 0x2;
  castleRequest.byte(2) = 0x10;                     // Klemme15 - Lid: 0x10
  
  if(onOff==1){
    castleRequest.byte(1) = 0x19;                   // Output "On" - Sid: 0x19
    testStepPass("1","To Set PIM KL15 On.");
  }          
  else if(onOff==0){
    castleRequest.byte(1) = 0x18;                   //Output "Off" - Sid: 0x18
    testStepPass("1","To Set PIM KL15 Off.");
  }          
  else{}

  /*Step 2, to send contorl request on the bus*/
  ret1 = func_sendMsg_onBus(castleRequest);
  if(ret1 != 1){
    testStepFail("2","PIM KL15 Control Request hex[0x%X : %02X %02X %02X %02X] is not Sent to the Bus!", castleRequest.id, castleRequest.byte(1),castleRequest.byte(2),castleRequest.byte(3),castleRequest.byte(4));
    return 0;
  }
  else{
    testStepPass("2","PIM KL15 Control Request hex[0x%X : %02X %02X %02X %02X] is Sent to the Bus!", castleRequest.id, castleRequest.byte(1),castleRequest.byte(2),castleRequest.byte(3),castleRequest.byte(4));
  }
  
  /*Step 3, to await and check PIM response */
  ret2 = testWaitForMessage(castleResponse.id, aWaitOnBus);
  if(ret2 != 1){
    testStepFail("3","Castle Response [0x%X] is not detected on the bus within %d ms.", castleResponse.id, aWaitOnBus);
    return 0;
  }
  
  testGetWaitEventMsgData(getMsg);
  expByte = 0x40 + castleRequest.byte(1);
  if(getMsg.byte(1) != expByte){
    testStepFail("3", "Castle Response [0x%X] is received, but data hex[%02X %02X %02X %02X] is not as expected [%02X].", castleResponse.id,  getMsg.byte(0), getMsg.byte(1), getMsg.byte(2), getMsg.byte(3), expByte);
    return 0;
  }
  else{
    testStepPass("3", "PIM KL15 is set. Castle Response [0x%X] is received: hex[%02X %02X %02X %02X].", castleResponse.id, getMsg.byte(0), getMsg.byte(1),getMsg.byte(2),getMsg.byte(3));
  }
  
  return expByte;
}

/* To set PIM KL30 On or Off           */
/* parameter 1: onOff, 0-Off, 1-On     */
/* Author Xiaokui YI, Date 2020/07/26  */
dword func_PIM_KL30_Control(byte onOff)
{
  byte expByte;
  dword ret1, ret2;
  ret1 = 0;
  ret2 = 0;
  
  castleRequest.id  = 0x100 + @sysvar::Castle5_Control::pim_slot;          // PIM slot is 1, so Diag ID is 0x100+ slot ID
  castleResponse.id = 0x200 + @sysvar::Castle5_Control::pim_slot;          // PIM slot is 1, so Diag ID is 0x200+ slot ID
  castleRequest.msgChannel      = comChannel;
  castleResponse.msgChannel     = comChannel;
  
  /*Step 1, to compose the diag request */
  castleRequest.dlc = 0x8;
  castleRequest.byte(0) = 0x2;
  castleRequest.byte(2) = 0x11;                     // Klemme30 - Lid: 0x10
  
  if(onOff==1){
    castleRequest.byte(1) = 0x19;                   // Output "On" - Sid: 0x19
    testStepPass("1","To Set PIM KL30 On.");
  }          
  else if(onOff==0){
    castleRequest.byte(1) = 0x18;                   //Output "Off" - Sid: 0x18
    testStepPass("1","To Set PIM KL30 Off.");
  }          
  else{}

  /*Step 2, to send contorl request on the bus*/
  ret1 = func_sendMsg_onBus(castleRequest);
  if(ret1 != 1){
    testStepFail("2","PIM KL30 Control Request hex[0x%X : %02X %02X %02X %02X] is not Sent to the Bus!", castleRequest.id, castleRequest.byte(1),castleRequest.byte(2),castleRequest.byte(3),castleRequest.byte(4));
    return 0;
  }
  else{
    testStepPass("2","PIM KL30 Control Request hex[0x%X : %02X %02X %02X %02X] is Sent to the Bus!", castleRequest.id, castleRequest.byte(1),castleRequest.byte(2),castleRequest.byte(3),castleRequest.byte(4));
  }
  
  /*Step 3, to await and check PIM response */
  ret2 = testWaitForMessage(castleResponse.id, aWaitOnBus);
  if(ret2 != 1){
    testStepFail("3","Castle Response [0x%X] is not detected on the bus within %d ms.", castleResponse.id, aWaitOnBus);
    return 0;
  }
  
  testGetWaitEventMsgData(getMsg);
  expByte = 0x40 + castleRequest.byte(1);
  if(getMsg.byte(1) != expByte){
    testStepFail("3", "Castle Response [0x%X] is received, but data hex[%02X %02X %02X %02X] is not as expected [%02X].", castleResponse.id,  getMsg.byte(0), getMsg.byte(1), getMsg.byte(2), getMsg.byte(3), expByte);
    return 0;
  }
  else{
    testStepPass("3", "PIM KL30 is set. Castle Response [0x%X] is received: hex[%02X %02X %02X %02X].", castleResponse.id, getMsg.byte(0), getMsg.byte(1),getMsg.byte(2),getMsg.byte(3));
  }
  
  return expByte;
}

/*Set obstacle to Senosr with Distance cm */
/*Author LingLi He       2020/06/01       */
/*Reviewed by Xiaokui Yi 2020/06/04       */
testfunction tstf_UPA_SetObstacleTo_Sensor_mm(enum enumSensorNameALL Sensorname, dword dist_mm)
{
  func_SetBusContext(castleBusChn);                                 //Castle 5 Bus
  func_UPA_SetObstacleTo_Sensor_mm(Sensorname, dist_mm);
  func_SetBusContext(ecuBusChn);                                    //Default Bus
  testWaitForTimeout(2000);
}

testfunction tstf_UPA_SetObstacleTo_Sensor_cm(enum enumSensorNameALL Sensorname, dword dist_cm)
{
  dword dist_mm;
  dist_mm = dist_cm * 10;
  
  func_SetBusContext(castleBusChn);                                 //Castle 5 Bus
  func_UPA_SetObstacleTo_Sensor_mm(Sensorname, dist_mm);
  func_SetBusContext(ecuBusChn);                                    //Default Bus
}

testfunction tstf_UPA_SetObstacleTo_allFrontSensor_mm(dword dist_mm)
{
  func_SetBusContext(castleBusChn);                                  //Castle 5 Bus
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_FOL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_FIL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_FOR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_FIR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_FSL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_FSR, dist_mm);
  func_SetBusContext(ecuBusChn);                                 //Default Bus
}

testfunction tstf_UPA_SetObstacleTo_allFrontSensor_cm(dword dist_cm)
{
  dword dist_mm;
  dist_mm = dist_cm * 10;
  
  func_SetBusContext(castleBusChn);                                 //Castle 5 Bus
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_FOL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_FIL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_FOR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_FIR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_FSL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_FSR, dist_mm);
  func_SetBusContext(ecuBusChn);                                 //Default Bus
}

testfunction tstf_UPA_SetObstacleTo_allRearSensor_mm(dword dist_mm)
{
  func_SetBusContext(castleBusChn);                                 //Castle 5 Bus
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_ROL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_RIL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_ROR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_RIR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_RSL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_RSR, dist_mm);
  func_SetBusContext(ecuBusChn);                                 //Default Bus
}

testfunction tstf_UPA_SetObstacleTo_allRearSensor_cm(dword dist_cm)
{
  dword dist_mm;
  dist_mm = dist_cm * 10;
  
  func_SetBusContext(castleBusChn);                                 //Castle 5 Bus
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_ROL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_RIL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_ROR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_RIR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_RSL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_RSR, dist_mm);
  func_SetBusContext(ecuBusChn);                                 //Default Bus
}

testfunction tstf_UPA_SetObstacleTo_allSensor_mm(dword dist_mm)
{
  func_SetBusContext(castleBusChn);                                 //Castle 5 Bus
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_FOL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_FIL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_FOR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_FIR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_FSL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_FSR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_ROL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_RIL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_ROR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_RIR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_RSL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm(SENSOR_RSR, dist_mm);
  func_SetBusContext(ecuBusChn);                                 //Default Bus
}

testfunction tstf_UPA_SetObstacleTo_allSensor_mm_NACK(dword dist_mm)
{
  func_SetBusContext(castleBusChn);                                 //Castle 5 Bus
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_FOL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_FIL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_FOR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_FIR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_FSL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_FSR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_ROL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_RIL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_ROR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_RIR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_RSL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_RSR, dist_mm);
  func_SetBusContext(ecuBusChn);                                 //Default Bus
}

void func_UPA_InitSensor_NACK(dword dist_mm)
{
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_FOL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_FIL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_FOR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_FIR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_FSL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_FSR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_ROL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_RIL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_ROR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_RIR, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_RSL, dist_mm);
  testWaitForTimeout(minWaitTime);
  func_UPA_SetObstacleTo_Sensor_mm_NACK(SENSOR_RSR, dist_mm);
  testWaitForTimeout(minWaitTime);
}

void func_UPA_InitFailure()
{
  func_UPA_InjectFailureTo_Sensor(SENSOR_RIL, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_ROL, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_RIR, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_ROR, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_RSL, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_RSR, NORMAL);
  testWaitForTimeout(minWaitTime);
  
  func_UPA_InjectFailureTo_Sensor(SENSOR_FIL, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_FOL, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_FIR, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_FOR, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_FSL, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_FSR, NORMAL);
  testWaitForTimeout(minWaitTime);
  
  func_UPA_InjectFailureTo_SensorPWR(sensorPwr_Front, pwr_normal);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_SensorPWR(sensorPwr_Rear,  pwr_normal);
  testWaitForTimeout(minWaitTime);
  
}

dword func_UPA_SetObstacleTo_Sensor_mm(enum enumSensorNameALL sensorName,dword dist_mm)
{
  long ret1,ret2;
  dword msgID;
  dword ubSensor;
  
  ret1     = 0;
  ret2     = 0;
  ubSensor = 0x0;
  msgID = 0xFF;

  switch (sensorName)
  {
    case SENSOR_FIL:
      testStepPass("1","To Set sensor FIL to %d mm.",dist_mm);
      msg_SetFILDistance.FIL_first_direct_Echo = dist_mm;
      msg_SetFILDistance.FIL_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1  = func_sendMsg_onBus(msg_SetFILDistance);                                   // send and await Request on the bus
      msgID = msg_SetFILDistance.id;
      break;
      
    case SENSOR_FIR:
      testStepPass("1","To Set sensor FIR to %d mm.",dist_mm);
      msg_SetFIRDistance.FIR_first_direct_Echo = dist_mm;
      msg_SetFIRDistance.FIR_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);
      ret1  = func_sendMsg_onBus(msg_SetFIRDistance);
      msgID = msg_SetFIRDistance.id;
      break;
      
    case SENSOR_FOL:
      testStepPass("1","To Set sensor FOL to %d mm.",dist_mm);
      msg_SetFOLDistance.FOL_first_direct_Echo = dist_mm;
      msg_SetFOLDistance.FOL_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1  = func_sendMsg_onBus(msg_SetFOLDistance);
      msgID = msg_SetFOLDistance.id;
      break;
      
    case SENSOR_FOR:
      testStepPass("1","To Set sensor FOR to %d mm.",dist_mm);
      msg_SetFORDistance.FOR_first_direct_Echo = dist_mm;
      msg_SetFORDistance.FOR_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1  = func_sendMsg_onBus(msg_SetFORDistance);
      msgID = msg_SetFORDistance.id;
      break;
      
    case SENSOR_FSL:
      testStepPass("1","To Set sensor FSL to %d mm.",dist_mm);
      msg_SetFSLDistance.FSL_first_direct_Echo = dist_mm;
      msg_SetFSLDistance.FSL_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1  = func_sendMsg_onBus(msg_SetFSLDistance);
      msgID = msg_SetFSLDistance.id;
      break;
      
    case SENSOR_FSR:
      testStepPass("1","To Set sensor FSR to %d mm.",dist_mm);
      msg_SetFSRDistance.FSR_first_direct_Echo = dist_mm;
      msg_SetFSRDistance.FSR_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1  = func_sendMsg_onBus(msg_SetFSRDistance);
      msgID = msg_SetFSRDistance.id;
     break;
      
    case SENSOR_RIL:
      testStepPass("1","To Set sensor RIL to %d mm.",dist_mm);
      msg_SetRILDistance.RIL_first_direct_Echo = dist_mm;
      msg_SetRILDistance.RIL_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1  = func_sendMsg_onBus(msg_SetRILDistance);
      msgID = msg_SetRILDistance.id;
      break;
      
    case SENSOR_RIR:
      testStepPass("1","To Set sensor RIR to %d mm.",dist_mm);
      msg_SetRIRDistance.RIR_first_direct_Echo = dist_mm;
      msg_SetRIRDistance.RIR_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1  = func_sendMsg_onBus(msg_SetRIRDistance);
      msgID = msg_SetRIRDistance.id;
      break;
      
    case SENSOR_ROL:
      testStepPass("1","To Set sensor ROL to %d mm.",dist_mm);
      msg_SetROLDistance.ROL_first_direct_Echo = dist_mm;
      msg_SetROLDistance.ROL_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1  = func_sendMsg_onBus(msg_SetROLDistance);
      msgID = msg_SetROLDistance.id;
      break;
      
    case SENSOR_ROR:
      testStepPass("1","To Set sensor ROR to %d mm.",dist_mm);
      msg_SetRORDistance.ROR_first_direct_Echo = dist_mm;
      msg_SetRORDistance.ROR_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1  = func_sendMsg_onBus(msg_SetRORDistance);
      msgID = msg_SetRORDistance.id;
      break;
      
    case SENSOR_RSL:
      testStepPass("1","To Set sensor RSL to %d mm.",dist_mm);
      msg_SetRSLDistance.RSL_first_direct_Echo = dist_mm;
      msg_SetRSLDistance.RSL_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1  = func_sendMsg_onBus(msg_SetRSLDistance);
      msgID = msg_SetRSLDistance.id;
      break;
      
    case SENSOR_RSR:
      testStepPass("1","To Set sensor RSR to %d mm.",dist_mm);
      msg_SetRSRDistance.RSR_first_direct_Echo = dist_mm;
      msg_SetRSRDistance.RSR_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1  = func_sendMsg_onBus(msg_SetRSRDistance);
      msgID = msg_SetRSRDistance.id;
      break;
      
    default:
      testStepFail("1","Senor ID [%d] is not supported!",sensorName);
      return 0;
      break; 
  }  

  if (ret1 !=1){
    testStepFail("2","Sensor Dist Set Request [0x%2X] is not sent on the Bus!", msgID);
    return 0;
  }
  else{
    testStepPass("2","Sensor Dist Set Request [0x%2X] is sent on the Bus!", msgID);
  }

  
  ret2 = testWaitForMessage(msg_C5Ack.id, aWaitC5Ack);                  // await for C5 ACK on the bus
  if(ret2 != 1){
    testStepFail("3","Castle ACK msg [0x%2X] is not sent on the Bus!", msg_C5Ack.id);
    return 0;
  }
 
  testGetWaitEventMsgData(getMsg_0x400);                                // C5 ACK is received
  ubSensor = getMsg_0x400.Sensor;                                       // get updated sensor ID
  if( ubSensor != sensorName){
    testStepFail("3","Castle ACK msg [0x%2X] is received, but updated sensor [0x%02X] is not expected [0x%02X].", msg_C5Ack.id, ubSensor, sensorName);
    return 0;
  }
  else{
    testStepPass("3","Castle ACK msg [0x%2X] is received, and updated sensor [0x%02X] is expected [0x%02X].", msg_C5Ack.id, ubSensor, sensorName);
  }
  
  return 1;
}


dword func_UPA_SetObstacleTo_Sensor_mm_NACK(enum enumSensorNameALL sensorName,dword dist_mm)
{
  long ret1,ret2;
  dword msgID;
  dword ubSensor;
  
  ret1     = 0;
  ret2     = 0;
  msgID    = 0xFF;
  ubSensor = 0x0;

  switch (sensorName)
  {
    case SENSOR_FIL:
      msgID = 0x508;
      teststep("1","To Set sensor FIL to %d mm.",dist_mm);
      msg_SetFILDistance.FIL_first_direct_Echo = dist_mm;
      msg_SetFILDistance.FIL_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1 = func_sendMsg_onBus(msg_SetFILDistance);                      // send and await Request on the bus
      break;
      
    case SENSOR_FIR:
      msgID = 0x509;
      teststep("1","To Set sensor FIR to %d mm.",dist_mm);
      msg_SetFIRDistance.FIR_first_direct_Echo = dist_mm;
      msg_SetFIRDistance.FIR_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1 = func_sendMsg_onBus(msg_SetFIRDistance);
      break;
      
    case SENSOR_FOL:
      msgID = 0x507;
      teststep("1","To Set sensor FOL to %d mm.",dist_mm);
      msg_SetFOLDistance.FOL_first_direct_Echo = dist_mm;
      msg_SetFOLDistance.FOL_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1 = func_sendMsg_onBus(msg_SetFOLDistance);
      break;
      
    case SENSOR_FOR:
      msgID = 0x50A;
      teststep("1","To Set sensor FOR to %d mm.",dist_mm);
      msg_SetFORDistance.FOR_first_direct_Echo = dist_mm;
      msg_SetFORDistance.FOR_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1 = func_sendMsg_onBus(msg_SetFORDistance);
      break;
      
    case SENSOR_FSL:
      msgID = 0x506;
      teststep("1","To Set sensor FSL to %d mm.",dist_mm);
      msg_SetFSLDistance.FSL_first_direct_Echo = dist_mm;
      msg_SetFSLDistance.FSL_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1 = func_sendMsg_onBus(msg_SetFSLDistance);
      break;
      
    case SENSOR_FSR:
      msgID = 0x50B;
      teststep("1","To Set sensor FSR to %d mm.",dist_mm);
      msg_SetFSRDistance.FSR_first_direct_Echo = dist_mm;
      msg_SetFSRDistance.FSR_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1 = func_sendMsg_onBus(msg_SetFSRDistance);
     break;
      
    case SENSOR_RIL:
      msgID = 0x502;
      teststep("1","To Set sensor RIL to %d mm.",dist_mm);
      msg_SetRILDistance.RIL_first_direct_Echo = dist_mm;
      msg_SetRILDistance.RIL_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1 = func_sendMsg_onBus(msg_SetRILDistance);
      break;
      
    case SENSOR_RIR:
      msgID = 0x503;
      teststep("1","To Set sensor RIR to %d mm.",dist_mm);
      msg_SetRIRDistance.RIR_first_direct_Echo = dist_mm;
      msg_SetRIRDistance.RIR_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1 = func_sendMsg_onBus(msg_SetRIRDistance);
      break;
      
    case SENSOR_ROL:
      msgID = 0x501;
      teststep("1","To Set sensor ROL to %d mm.",dist_mm);
      msg_SetROLDistance.ROL_first_direct_Echo = dist_mm;
      msg_SetROLDistance.ROL_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1 = func_sendMsg_onBus(msg_SetROLDistance);
      break;
      
    case SENSOR_ROR:
      msgID = 0x504;
      teststep("1","To Set sensor ROR to %d mm.",dist_mm);
      msg_SetRORDistance.ROR_first_direct_Echo = dist_mm;
      msg_SetRORDistance.ROR_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1 = func_sendMsg_onBus(msg_SetRORDistance);
      break;
      
    case SENSOR_RSL:
      msgID = 0x500;
      teststep("1","To Set sensor RSL to %d mm.",dist_mm);
      msg_SetRSLDistance.RSL_first_direct_Echo = dist_mm;
      msg_SetRSLDistance.RSL_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1 = func_sendMsg_onBus(msg_SetRSLDistance);
      break;
      
    case SENSOR_RSR:
      msgID = 0x505;
      teststep("1","To Set sensor RSR to %d mm.",dist_mm);
      msg_SetRSRDistance.RSR_first_direct_Echo = dist_mm;
      msg_SetRSRDistance.RSR_second_direct_Echo = func_genRandom_2ndEcho(dist_mm);     // set 2nd echo
      ret1 = func_sendMsg_onBus(msg_SetRSRDistance);
      break;
      
    default:
      testStepFail("1","Senor ID [%d] is not supported!",sensorName);
      return 0;
      break; 
  }  

  if (ret1 !=1){
    testStepFail("2","Sensor Dist Set Request [0x%2X] is not sent on the Bus!", msgID);
    return 0;
  }
  else{
    testStepPass("2","Sensor Dist Set Request [0x%2X] is sent on the Bus!", msgID);
  }
  
  return 1;
}


/* To Inject or Remove Failure on UPA Sensor */
/* Para1: sensor_id {sensor_FIL=11, sensor_FIR =12, sensor_FOL =10, sensor_FOR=13, sensor_FSL=9,           */
/* sensor_FSR=14, sensor_RIL=3,  sensor_RIR=4,   sensor_ROL=2,   sensor_ROR=5,  sensor_RSL=1, sensor_RSR=6}*/
/* Para2: Normal=0, SC_To_GND=1, SC_To_VCC=2, Open=3,Open_Circuit=4,                                       */
/* Attenuation_Time=5,Receive_Path=6,Verify=7,ICE=8,Heavy_Noise=9                                          */
/*Author LingLi He       2020/06/01                                                                        */
/*Reviewed by Xiaokui Yi 2020/06/04                                                                        */
testfunction tstf_UPA_InjectFailureTo_Sensor(enum enumSensorNameALL Sensorname, enum enumSensorFailType failType)
{
  func_SetBusContext(castleBusChn);                                 //Castle 5 Bus
  func_UPA_InjectFailureTo_Sensor(Sensorname, failType);
  func_SetBusContext(ecuBusChn);                                 //Default Bus
}

testfunction tstf_UPA_InjectFailureTo_allFrontSensor(enum enumSensorFailType failType)
{
  func_SetBusContext(castleBusChn);                                 //Castle 5 Bus
  func_UPA_InjectFailureTo_Sensor(SENSOR_FIL, failType);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_FOL, failType);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_FIR, failType);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_FOR, failType);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_FSR, failType);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_FSL, failType);
  func_SetBusContext(ecuBusChn);                                 //Default Bus
}

testfunction tstf_UPA_InjectFailureTo_allRearSensor(enum enumSensorFailType failType)
{
  func_SetBusContext(castleBusChn);                                 //Castle 5 Bus
  func_UPA_InjectFailureTo_Sensor(SENSOR_RIL, failType);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_ROL, failType);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_RIR, failType);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_ROR, failType);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_RSR, failType);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_RSL, failType);
  func_SetBusContext(ecuBusChn);                                 //Default Bus
}


testfunction tstf_UPA_RemoveFailureOn_Sensor(enum enumSensorNameALL Sensorname)
{
  func_SetBusContext(castleBusChn);                              //Castle 5 Bus
  func_UPA_InjectFailureTo_Sensor(Sensorname, NORMAL);
  func_SetBusContext(ecuBusChn);                                 //Default Bus
}

testfunction tstf_UPA_RemoveFailureOn_allFrontSensor()
{
  func_SetBusContext(castleBusChn);                                 //Castle 5 Bus
  func_UPA_InjectFailureTo_Sensor(SENSOR_FIL, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_FOL, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_FIR, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_FOR, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_FSL, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_FSR, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_SetBusContext(ecuBusChn);                                 //Default Bus
}

testfunction tstf_UPA_RemoveFailureOn_allRearSensor()
{
  func_SetBusContext(castleBusChn);                                 //Castle 5 Bus
  func_UPA_InjectFailureTo_Sensor(SENSOR_RIL, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_ROL, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_RIR, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_ROR, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_RSL, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_UPA_InjectFailureTo_Sensor(SENSOR_RSR, NORMAL);
  testWaitForTimeout(minWaitTime);
  func_SetBusContext(ecuBusChn);                                 //Default Bus
}

dword func_UPA_InjectFailureTo_Sensor(enum enumSensorNameALL Sensorname, enum enumSensorFailType failType)
{
  long ret1, ret2;
  byte expByte;
  char printError[36];
  char printSensor[36];
  
  castleRequest.id  = 0x100 + @sysvar::Castle5_Control::pim_slot;            // PIM slot is 1, so Diag ID is 0x100+ slot ID
  castleResponse.id = 0x200 + @sysvar::Castle5_Control::pim_slot;            // PIM slot is 1, so Diag ID is 0x200+ slot ID
  
  castleRequest.msgChannel      = comChannel;
  castleResponse.msgChannel     = comChannel;
  
  ret1 = 0; ret2 = 0; expByte = 0;
  strncpy(printSensor, strSensor[Sensorname], elCount(printSensor));              // copy Sensor name description
  strncpy(printError,  strError[failType], elCount(printError));                  // copy error type description
  
  switch (Sensorname)
  {
    case SENSOR_FIL:  
    case SENSOR_FIR: 
    case SENSOR_FOL: 
    case SENSOR_FOR:
    case SENSOR_FSL:
    case SENSOR_FSR:
    case SENSOR_RIL:
    case SENSOR_RIR:
    case SENSOR_ROL:
    case SENSOR_ROR:
    case SENSOR_RSL:
    case SENSOR_RSR:
      castleRequest.dlc     = 0x8;
      castleRequest.byte(0) = 0x4;
      castleRequest.byte(1) = 0x1D;
      castleRequest.byte(2) = 0x3D;
      castleRequest.byte(3) = Sensorname;
      castleRequest.byte(4) = failType;
      testStepPass("1","To Inject Fault [%s] on Senosr [%s].",printError, printSensor);
    break;
      
    default:
      testStepFail("1","Senor ID [%d] is not supported!", Sensorname);
      return 0;
    break; 
  }  
  
  ret1 = func_sendMsg_onBus(castleRequest);
  if(ret1 != 1){
    testStepFail("2","Fault Injection Request hex[0x%X : %02X %02X %02X %02X] is not Sent to the Bus!", castleRequest.id, castleRequest.byte(1),castleRequest.byte(2),castleRequest.byte(3),castleRequest.byte(4));
    return 0;
  }
  else{
    testStepPass("2","Fault Injection Request hex[0x%X : %02X %02X %02X %02X] is Sent to the Bus!", castleRequest.id, castleRequest.byte(1),castleRequest.byte(2),castleRequest.byte(3),castleRequest.byte(4));
  }
  
  ret2 = testWaitForMessage(castleResponse.id, aWaitOnBus);
  if(ret2 != 1){
    testStepFail("3","Castle Response [0x%X] is not detected on the bus within %d ms.", castleResponse.id, aWaitOnBus);
    return 0;
  }
  
  testGetWaitEventMsgData(getMsg);
  expByte = 0x40 + castleRequest.byte(1);
  if(getMsg.byte(1) != expByte){
    testStepFail("3", "Castle Response [0x%X] is received, but data hex[%02X %02X %02X %02X] is not as expected [%02X].", castleResponse.id,  getMsg.byte(0), getMsg.byte(1), getMsg.byte(2), getMsg.byte(3), expByte);
    return 0;
  }
  else{
    testStepPass("3", "Sensor Failure is Injected Successfully. Castle Response [0x%X] is received: hex[%02X %02X %02X %02X].", castleResponse.id, getMsg.byte(0), getMsg.byte(1),getMsg.byte(2),getMsg.byte(3));
  }
  
  return 1;
}

/* To Inject or Remove Failure on UPA Sensor PWR                                         */
/* Para1: sensorPwr_id {sensorPwr_Front = 0, enum sensorPwr_Rear = 1}                    */
/* Para2: pwr_error { pwr_normal=0, pwr_SC_TO_GND=1, pwr_SC_TO_VCC=2, pwr_SC_TO_UB12V=3} */
/* Author Xiaokui Yi, Date: 2020/06/04                                                   */
testfunction tstf_UPA_InjectFailureTo_FrontPWR(enum SensorPWR_Error ErrorType)
{
  func_SetBusContext(castleBusChn); 
  func_UPA_InjectFailureTo_SensorPWR(sensorPwr_Front, ErrorType);
  func_SetBusContext(ecuBusChn); 
}

testfunction tstf_UPA_InjectFailureTo_RearPWR(enum SensorPWR_Error ErrorType)
{
  func_SetBusContext(castleBusChn); 
  func_UPA_InjectFailureTo_SensorPWR(sensorPwr_Rear, ErrorType);
  func_SetBusContext(ecuBusChn); 
}

testfunction tstf_UPA_RemoveFailureOn_FrontPWR()
{
  func_SetBusContext(castleBusChn); 
  func_UPA_InjectFailureTo_SensorPWR(sensorPwr_Front, pwr_normal);
  func_SetBusContext(ecuBusChn); 
}

testfunction tstf_UPA_RemoveFailureOn_RearPWR()
{
  func_SetBusContext(castleBusChn); 
  func_UPA_InjectFailureTo_SensorPWR(sensorPwr_Rear, pwr_normal);
  func_SetBusContext(ecuBusChn); 
}



dword func_UPA_InjectFailureTo_SensorPWR(enum SensorPWR_ID SensorPWR, enum SensorPWR_Error ErrorType)
{
  long ret1, ret2;
  byte expByte;
  char printError[36];
  char printSensorPWR[36];
  
  castleRequest.id  = 0x100 + @sysvar::Castle5_Control::pim_slot;             // PIM slot is 1, so Diag ID is 0x100+ slot ID
  castleResponse.id = 0x200 + @sysvar::Castle5_Control::pim_slot;             // PIM slot is 1, so Diag ID is 0x200+ slot ID
  
  castleRequest.msgChannel      = comChannel;
  castleResponse.msgChannel     = comChannel;
  
  ret1 = 0; ret2 = 0; expByte = 0;
  strncpy(printSensorPWR, strSensorPWR[SensorPWR], elCount(printSensorPWR));              // copy Sensor name description
  strncpy(printError,     strPWRError[ErrorType], elCount(printError));                  // copy error type description
  
  castleRequest.dlc     = 0x8;
  castleRequest.byte(0) = 0x3;
  castleRequest.byte(2) = 0x3D;
  castleRequest.byte(3) = ErrorType;
  switch (SensorPWR)
  {
    case sensorPwr_Front:
      castleRequest.byte(1) = 0x13;
      testStepPass("1","To Inject Fault [%s] on Front Senosr PWR [%s].",printError, printSensorPWR);
    break;
      
    case sensorPwr_Rear: 
      castleRequest.byte(1) = 0x14;
      testStepPass("1","To Inject Fault [%s] on Rear Senosr PWR [%s].",printError, printSensorPWR);
    break;
      
    default:
      testStepFail("1","Senor PWR [%s] is not supported!", printSensorPWR);
      return 0;
    break; 
  }  
  
  ret1 = func_sendMsg_onBus(castleRequest);
  if(ret1 != 1){
    testStepFail("2","Fault Injection Request hex[0x%X : %02X %02X %02X] is not Sent to the Bus!", castleRequest.id, castleRequest.byte(1),castleRequest.byte(2),castleRequest.byte(3));
    return 0;
  }
  else{
    testStepPass("2","Fault Injection Request hex[0x%X : %02X %02X %02X] is Sent to the Bus!", castleRequest.id, castleRequest.byte(1),castleRequest.byte(2),castleRequest.byte(3));
  }
  
  ret2 = testWaitForMessage(castleResponse.id, aWaitOnBus);
  if(ret2 != 1){
    testStepFail("3","Castle Response [0x%X] is not detected on the bus within %d ms.", castleResponse.id, aWaitOnBus);
    return 0;
  }
  
  testGetWaitEventMsgData(getMsg);
  expByte = 0x40 + castleRequest.byte(1);
  if(getMsg.byte(1) != expByte){
    testStepFail("3", "Castle Response [0x%X] is received, but data hex[%02X %02X %02X %02X] is not as expected [%02X].", castleResponse.id,  getMsg.byte(0), getMsg.byte(1), getMsg.byte(2), getMsg.byte(3), expByte);
    return 0;
  }
  else{
    testStepPass("3", "Sensor PWR Failure is Injected Successfully. Castle Response [0x%X] is received: hex[%02X %02X %02X %02X].", castleResponse.id,  getMsg.byte(0), getMsg.byte(1),getMsg.byte(2),getMsg.byte(3));
  }
  
  return 1;
}

dword func_sendMsg_onBus(message *objMsg)
{
  dword ret;
  ret  = 0;
  
  output(objMsg);
  ret = testWaitForMessage(objMsg.id, aWaitOnBus);
  
  return ret;
}

//dword func_SetBusContext(byte index)
//{
//  dword bus;
//  
//  if(index == ecuBusChn){
//    bus = getBusNameContext(ecuBus);
//  }
//  else if(index == castleBusChn){
//    bus = getBusNameContext(castleBus);
//  }
//  else if(index == vs6BusChn){
//    bus = getBusNameContext(vs6Bus);
//  }
//  else if(index == ecuBusChn2){
//    bus = getBusNameContext(ecuBus2);
//  }
//  else{
//    bus = getBusNameContext(ecuBus);
//  }
//  
//  setBusContext(bus);
//  //teststep("Bus","Bus Context is set to [%d].",bus);
//  return 1;
//}

dword func_genRandom_2ndEcho(dword echo_mm)
{
  dword genEcho;
  
  if(@sysvar::Castle5_Control::enable_2ndEcho == 0x0)
    return 0;
  
  if(echo_mm != 0){
    genEcho = echo_mm + random(Echo2nd_max-Echo2nd_min) + Echo2nd_min;
  }
  else{
    genEcho = 0;
  }
  
  return genEcho;
}