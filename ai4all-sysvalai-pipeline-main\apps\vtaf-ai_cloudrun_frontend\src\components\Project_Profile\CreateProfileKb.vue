<template>
  <q-stepper
    v-model="step"
    animated
    flat
    class="full-width"
    done-color="blue-10"
    active-color="light-blue-5"
    inactive-color="red-7"
  >
    <!-- Step 1: Select BG (Still uses Cards) -->
    <q-step :name="1" :title="step1Title" icon="mdi-domain" :done="step > 1">
      <div class="row q-mb-md">
        <q-card
          v-for="item in profile_orgInputs"
          :key="item.bg"
          class="col-4 cursor-pointer bg-card"
          :class="getCardHoverClass(item.bg)"
          @click="selectBG(item)"
          bordered
          hoverable
        >
          <q-img :src="getImageUrl(item.bg)" :alt="item.bg" height="200px" />
          <q-card-section
            class="text-center text-h5 q-pa-md"
            :class="getTextColorClass(item.bg)"
          >
            {{ item.bg }}
          </q-card-section>
        </q-card>
      </div>
      <q-btn
        label="Next"
        color="primary"
        class="q-mt-md"
        @click="step++"
        :disable="!profile_bg"
      />
    </q-step>

    <!-- Step 2: Select PG (Use Buttons) -->
    <q-step :name="2" :title="step2Title" icon="diversity_3" :done="step > 2">
      <div class="column items-center q-gutter-sm q-mb-md">
        <q-btn
          v-for="pg in distinctPGs"
          :key="pg"
          @click="selectPG(pg)"
          color="blue-10"
          class="pg-btn"
          outline
        >
          {{ pg }}
        </q-btn>
      </div>
      <div class="q-mt-md row justify-between">
        <q-btn label="Previous" color="secondary" @click="step--" />
        <q-btn
          label="Next"
          color="primary"
          @click="step++"
          :disable="!profile_pg"
        />
      </div>
    </q-step>

    <!-- Step 3: Select PL (Use Buttons) -->
    <q-step :name="3" :title="step3Title" icon="category" :done="step > 3">
      <div class="column items-center q-gutter-sm q-mb-md">
        <q-btn
          v-for="item in filteredPLs"
          :key="item.pl"
          @click="selectPL(item)"
          color="blue-10"
          class="pl-btn"
          outline
        >
          {{ item.pl }}
        </q-btn>
      </div>
      <q-btn
        label="Previous"
        color="secondary"
        class="q-mt-md"
        @click="step--"
      />
    </q-step>
    <!-- Step 4: Generating Data (Use Buttons) -->
    <q-step :name="4" :title="fetching_data_status" icon="download">
      <div class="column q-gutter-sm q-mb-md">
        <q-inner-loading :showing="stepper_loading">
          <div class="q-mt-md text-blue-10 text-h6 text-weight-bold">
            Preparing Data
          </div>
          <q-spinner-bars size="75px" color="blue-10" />
        </q-inner-loading>
      </div>
    </q-step>
  </q-stepper>
  <div class="row">
    <div class="col-4 q-mr-lg">
      <div
        v-if="
          profile_functionality_inputs.length != 0 &&
          profile_bg &&
          profile_pg &&
          profile_pl
        "
      >
        <q-card class="q-pa-lg bg-white shadow-4 rounded-borders animated-card">
          <q-card-section>
            <div class="row q-mb-md">
              <q-icon
                name="build_circle"
                size="28px"
                color="blue-10"
                class="q-mr-sm functionality-icon"
              />
              <div class="text-h5 text-blue-10">Functionality</div>
            </div>

            <q-select
              filled
              dense
              use-input
              input-debounce="0"
              v-model="profile_selectedFunctionality"
              :options="filteredFunctions"
              @filter="filterFn"
              label="Choose an option"
              emit-value
              map-options
              class="glowing-select"
              style="max-width: 300px; width: 100%"
            />
          </q-card-section>
        </q-card>
      </div>
    </div>
    <div class="col-4 q-mr-lg">
      <div v-if="profile_selectedFunctionalityUuid">
        <q-card class="q-pa-lg bg-white shadow-4 rounded-borders animated-card">
          <q-card-section>
            <div class="row q-mb-md">
              <q-icon
                name="assignment"
                size="28px"
                color="blue-10"
                class="q-mr-sm functionality-icon"
              />
              <div class="text-h5 text-blue-10">Projects</div>
            </div>

            <q-select
              filled
              dense
              use-input
              input-debounce="0"
              v-model="profile_selected_project"
              :options="filteredProjects"
              @filter="filterFn"
              label="Choose an option"
              emit-value
              map-options
              class="glowing-select"
              style="max-width: 300px; width: 100%"
              @update:model-value="getVariant()"
            />
          </q-card-section>
        </q-card>
      </div>
    </div>
    <div class="col-3">
      <div v-if="profile_selectedFunctionalityUuid">
        <q-card class="q-pa-lg bg-white shadow-4 rounded-borders animated-card">
          <q-card-section>
            <div class="row q-mb-md">
              <q-icon
                name="account_tree"
                size="28px"
                color="blue-10"
                class="q-mr-sm functionality-icon"
              />
              <div class="text-h5 text-blue-10">Variants</div>
            </div>

            <q-select
              filled
              dense
              use-input
              input-debounce="0"
              v-model="profile_selected_project_variant"
              :options="filteredVariants"
              @filter="filterFn"
              label="Choose an option"
              emit-value
              map-options
              class="glowing-select"
              :loading="create_profile_project_variant_loading"
              style="max-width: 300px; width: 100%"
            />
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>

  <!-- Algorithm Selection -->
  <div
    v-if="
      profile_selectedFunctionality &&
      Object.keys(profile_algorithm_dropdown).length
    "
    class="q-mt-md"
  >
    <q-card class="q-pa-md">
      <q-card-section class="text-h6">Algorithm Selection</q-card-section>
      <q-list bordered separator>
        <q-expansion-item
          v-for="(items, algorithmName) in profile_algorithm_dropdown"
          :key="algorithmName"
          expand-separator
          :label="algorithmName"
          header-class="text-black"
        >
          <q-card-section>
            <div v-for="item in items" :key="item">
              <q-checkbox
                :model-value="
                  profile_selectedAlgorithms[algorithmName]?.includes(item) ||
                  false
                "
                :label="item"
                @update:model-value="
                  (val) => updateAlgorithmSelection(val, algorithmName, item)
                "
              />
            </div>
          </q-card-section>
        </q-expansion-item>
      </q-list>
    </q-card>
  </div>
  <!-- Sensor Selection -->
  <div
    v-if="
      profile_selectedFunctionality &&
      Object.keys(profile_sensor_dropdown).length
    "
    class="q-mt-md"
  >
    <q-card class="q-pa-md">
      <q-card-section class="text-h6">Sensors</q-card-section>
      <q-list bordered separator>
        <q-expansion-item
          v-for="(items, categoryName) in profile_sensor_dropdown"
          :key="categoryName"
          expand-separator
          :label="categoryName"
          header-class="text-black"
          expand-icon-class="text-black"
        >
          <q-card-section>
            <div v-for="item in items" :key="item">
              <q-checkbox
                :model-value="
                  profile_selectedSensors[categoryName]?.includes(item) || false
                "
                :label="item"
                @update:model-value="
                  (val) => updateSensorList(val, categoryName, item)
                "
              />
            </div>
          </q-card-section>
        </q-expansion-item>
      </q-list>
    </q-card>
  </div>
  <q-btn
    v-if="
      profile_selectedFunctionality &&
      profile_selectedFunctionalityUuid
    "
    class="q-mt-md"
    label="ADD PROJECT"
    color="blue-10"
    icon-right="next_plan"
    @click="add_to_bigquery()"
    :disable="!isFormValid"
  ></q-btn>
  <q-inner-loading :showing="initial_loading">
    <div class="q-mt-md text-blue-10 text-h6 text-weight-bold">
      Getting Organization Details...
    </div>
    <q-spinner-bars size="75px" color="blue-10" />
  </q-inner-loading>
  <q-inner-loading :showing="email_check_loading">
    <div class="q-mt-md text-blue-10 text-h6 text-weight-bold">
      Checking Permission...
    </div>
    <q-spinner-bars size="75px" color="blue-10" />
  </q-inner-loading>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { storeToRefs } from "pinia";
import { useProfileManagementStore } from "src/stores/ai_testcase/profile-management-store";
import { useRouter } from "vue-router";

const router = useRouter();

const project_profile_testcase_config = useProfileManagementStore();
const {
  pl_champion,
  profile_orgInputs,
  profile_bg,
  profile_pg,
  profile_pl,
  profile_selected_org_uuid,
  profile_functionality_inputs,
  profile_functions_dropdown,
  profile_selectedFunctionality,
  profile_selectedFunctionalityUuid,
  profile_algorithms_and_sensor_inputs,
  profile_algorithm_dropdown,
  profile_sensor_dropdown,
  profile_selectedAlgorithms,
  profile_selectedSensors,
  profile_projects_inputs,
  profile_projects_dropdown,
  profile_selected_project,
  profile_selected_project_uuid,
  profile_project_variants_inputs,
  profile_project_variants_dropdown,
  profile_selected_project_variant,
  profile_selected_project_variant_uuid,
  fetching_data_status,
  stepper_loading,
  initial_loading,
  create_profile_project_variant_loading,
  email_check_loading,
} = storeToRefs(project_profile_testcase_config);
const {
  get_user_email,
  get_org_inputs,
  get_functionality_input,
  get_algorithms_and_sensor_input,
  create_project_profile,
  get_profile_projects,
  get_profile_project_variant,
} = project_profile_testcase_config;

const pl_pg_for_bg = ref("");
const step = ref(1);

const isFormValid = computed(() => {
  return (
    profile_bg.value &&
    profile_pg.value &&
    profile_pl.value &&
    profile_selectedFunctionality.value &&
    profile_selected_project.value &&
    profile_selected_project_uuid.value &&
    profile_selected_project_variant.value &&
    profile_selected_project_variant_uuid.value
  );
});

// Step 1: Select BG
function selectBG(item) {
  profile_bg.value = item.bg;
  pl_pg_for_bg.value = item;
  step.value = 2;
}

const step1Title = computed(() => {
  return step.value > 1 && profile_bg.value
    ? profile_bg.value
    : "Business Group";
});

const step2Title = computed(() => {
  return step.value > 2 && profile_pg.value
    ? profile_pg.value
    : "Product Group";
});

const step3Title = computed(() => {
  return step.value > 3 && profile_pl.value ? profile_pl.value : "Product Line";
});

function getImageUrl(name) {
  return `src/assets/home/<USER>
}

// Step 2: Get distinct PGs from selectedBG
const distinctPGs = computed(() => {
  const pgSet = new Set();
  pl_pg_for_bg.value?.details?.forEach((d) => pgSet.add(d.pg));
  return Array.from(pgSet);
});

function selectPG(pg) {
  profile_pg.value = pg;
  step.value = 3;
}

// Step 3: Filter PLs based on selected PG
const filteredPLs = computed(() => {
  return (
    pl_pg_for_bg.value?.details?.filter((d) => d.pg === profile_pg.value) || []
  );
});

async function selectPL(item) {
  step.value = 4;
  console.log(item);
  profile_pl.value = item.pl;
  profile_selected_org_uuid.value = item.org_uuid;

  // let status = await getChampion();
  // if (status) {
  //   console.log("Not an valid user");
  // } else {
  //   await get_algorithms_and_sensor_input();
  //   await get_functionality_input();
  // }

  await get_algorithms_and_sensor_input();
  await get_functionality_input();
}

function getTextColorClass(bg) {
  const colorMap = {
    brain: "text-teal text-bold",
    light: "text-amber-8 text-bold",
    power: "text-blue-9 text-bold",
  };
  return colorMap[bg.toLowerCase()] || "text-black";
}

function getCardHoverClass(bg) {
  const colorMap = {
    brain: "hover-brain",
    light: "hover-light",
    power: "hover-power",
  };
  return colorMap[bg.toLowerCase()] || "";
}

// const getChampion = async () => {
//   if (!valid_user_for_create_knowledge_profile.value) {
//     get_champion();
//     return true;
//   } else {
//     console.log("You are valid user");
//     return false;
//   }
// };

onMounted(async () => {
  await get_user_email();
  profile_bg.value = "";
  profile_pg.value = "";
  profile_pl.value = "";
  profile_functionality_inputs.value = [];
  profile_functions_dropdown.value = [];
  profile_selectedFunctionality.value = "";
  profile_selectedFunctionalityUuid.value = "";
  profile_algorithms_and_sensor_inputs.value = [];
  profile_algorithm_dropdown.value = [];
  profile_sensor_dropdown.value = [];
  profile_selectedAlgorithms.value = {};
  profile_selectedSensors.value = {};
  profile_selected_org_uuid.value = "";
  profile_selected_project.value = "";
  pl_champion.value = [];
  // selectedFunctionality.value = "";
  // selectedFunctionalityUuid.value = "";
  // functionality_inputs.value = [];
  // algorithm_dropdown.value = null;
  // sensor_dropdown.value = null;
  // selectedOrgUUID.value = "";
  // selectedAlgorithms.value = {};
  // selectedSensors.value = {};
  get_org_inputs();
  get_profile_projects();
});

const getVariant = () => {
  const match = profile_projects_inputs.value.find(
    (item) => item.project === profile_selected_project.value
  );

  if (match) {
    profile_selected_project_uuid.value = match.project_uuid;
  } else {
    // If no match found
    profile_selected_project_uuid.value = null;
  }

  get_profile_project_variant();
};

// Function to update algorithm selection and store it in dictionary format
const updateAlgorithmSelection = (isChecked, category, item) => {
  if (isChecked) {
    if (!profile_selectedAlgorithms.value[category]) {
      profile_selectedAlgorithms.value[category] = [];
    }
    if (!profile_selectedAlgorithms.value[category].includes(item)) {
      profile_selectedAlgorithms.value[category].push(item);
    }
  } else {
    profile_selectedAlgorithms.value[category] =
      profile_selectedAlgorithms.value[category].filter((i) => i !== item);
    if (profile_selectedAlgorithms.value[category].length === 0) {
      delete profile_selectedAlgorithms.value[category]; // Remove category if empty
    }
  }
};

const updateSensorList = (isChecked, categoryName, item) => {
  if (isChecked) {
    if (!profile_selectedSensors.value[categoryName]) {
      profile_selectedSensors.value[categoryName] = [];
    }
    if (!profile_selectedSensors.value[categoryName].includes(item)) {
      profile_selectedSensors.value[categoryName].push(item);
    }
  } else {
    profile_selectedSensors.value[categoryName] = profile_selectedSensors.value[
      categoryName
    ].filter((i) => i !== item);
    if (profile_selectedSensors.value[categoryName].length === 0) {
      delete profile_selectedSensors.value[categoryName]; // Remove category if empty
    }
  }
};

watch(profile_selectedFunctionality, (newVal) => {
  if (!newVal) {
    profile_selectedFunctionalityUuid.value = null;
    return;
  }

  for (const group of profile_functionality_inputs.value) {
    const match = group.details.find((item) => item.value === newVal);
    if (match) {
      profile_selectedFunctionalityUuid.value = match.uuid;
      return;
    }
  }

  // If no match found
  profile_selectedFunctionalityUuid.value = null;
});

watch(profile_selected_project_variant, (newVal) => {
  if (!newVal) {
    profile_selected_project_variant_uuid.value = null;
    return;
  }

  const match = profile_project_variants_inputs.value.find(
    (item) => item.variant === newVal
  );

  if (match) {
    profile_selected_project_variant_uuid.value = match.variant_uuid;
    return;
  }

  // If no match found
  profile_selected_project_variant_uuid.value = null;
});

// Filter query for real-time filtering
const filterQuery = ref("");

const filteredFunctions = computed(() => {
  return profile_functions_dropdown.value.filter((table) =>
    table.toLowerCase().includes(filterQuery.value.toLowerCase())
  );
});

const filteredProjects = computed(() => {
  return profile_projects_dropdown.value.filter((table) =>
    table.toLowerCase().includes(filterQuery.value.toLowerCase())
  );
});

const filteredVariants = computed(() => {
  return profile_project_variants_dropdown.value.filter((table) =>
    table.toLowerCase().includes(filterQuery.value.toLowerCase())
  );
});

const filterFn = (val, update) => {
  filterQuery.value = val;
  update();
};

const add_to_bigquery = async () => {
  await create_project_profile();

  router.push("/ai_testcase");
};

const go_to_ai_testcase = () => {
  router.push("/ai_testcase");
};
</script>

<style scoped>
.q-card.bg-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  min-height: 100px;
  padding: 12px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  animation: fadeInUp 0.5s ease both;
}

/* Button enhancements */
.q-btn {
  transition: all 0.3s ease;
  border-radius: 10px;
  font-weight: 600;
}

.q-btn:hover {
  transform: scale(1.03);
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1);
}

.q-btn:active {
  transform: scale(0.97);
}

/* Stepper animation */
.q-stepper {
  animation: fadeIn 0.5s ease-in;
}
.pg-btn {
  width: 50%;
}

.pl-btn {
  width: 50%;
}
.hover-brain:hover {
  background: linear-gradient(145deg, #e0f7f4, #b2dfdb); /* Teal-ish */
}

.hover-light:hover {
  background: linear-gradient(145deg, #fff8e1, #ffe082); /* Yellow-ish */
}

.hover-power:hover {
  background: linear-gradient(145deg, #e3f2fd, #90caf9); /* Blue-ish */
}
</style>
