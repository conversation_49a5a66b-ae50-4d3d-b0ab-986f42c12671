# Suppress warnings
import json
from typing import Literal, <PERSON>, Tuple, Any

import pandas as pd
import yaml
from crewai import Agent, Crew, Task, TaskOutput
from crewai import Process
from crewai.tools import tool
from pydantic import BaseModel, Field

# Load agent configurations from YAML
with open("app/config/test_script/agents.yaml", "r") as f:
    agents = yaml.safe_load(f)

# Load task configurations from YAML
with open("app/config/test_script/tasks.yaml", "r") as f:
    tasks = yaml.safe_load(f)


class AutomationSteps(BaseModel):
    Precondition: str = Field(..., description="Numbered steps starting with PC_ and separated by newlines")
    TestProcedure: str = Field(..., description="Numbered steps starting with TP_ and separated by newlines")
    Reset: str = Field(..., description="Numbered steps starting with RST_ and separated by newlines")


class KeywordsDetail(BaseModel):
    VTAFKeywordSyntax: str = Field(..., description="VTAF-AI Automation Keywords")
    Parameters: str = Field(..., description="Keywords Parameters details")
    Description: str = Field(..., description="Keywords Descriptions")


class KeywordsDetails(BaseModel):
    """Represents a collection of test cases."""

    VTAFKeywords: List[KeywordsDetail] = Field(..., description="List of VTAF-AI  Automation Keywords")


class AutoTestcaseModel(BaseModel):
    class TestcaseInner(BaseModel):
        sAutomationSteps: AutomationSteps
        sAutomationExpectation: str = Field(..., description="Numbered steps starting with eTP_, separated by newlines")
        TestType: Literal["Automatic", "SemiAutomatic", "Manual"]
        Notes: str

    testcase: TestcaseInner

def validate_json_output(result: TaskOutput) -> Tuple[bool, Any]:
    """Validate and parse JSON output."""
    try:
        # Try to parse as JSON
        data = json.loads(result)
        return (True, data)
    except json.JSONDecodeError as e:
        return (False, "Invalid JSON format")


@tool
def load_keywords_from_excel_tool() -> str:
    '''
    Reads and formats keyword definitions from an Excel file
    :return:
    '''
    df = pd.read_excel("VTAF_TestcaseGeneration_Template.xlsx")
    formatted_keywords = []
    for _, row in df.iterrows():
        syntax = row["Syntax"]
        params = row["Parameters"]
        desc = row["Description"]
        formatted_keywords.append(f"{syntax} - {params}\nDescription: {desc}")

    return "\n\n".join(formatted_keywords)  # Return as a long string, usable by LLM


def create_test_script_crew(request_email: str, llm):
    automation_test_engineer_agent = Agent(
        config=agents["Automation_Test_Engineer"],
        llm=llm)

    read_keywords_task = Task(
        name="Read Keywords Task",
        config=tasks["read_keyword_definitions"],
        agent=automation_test_engineer_agent,
        tools=[load_keywords_from_excel_tool],
        output_json=KeywordsDetails,

    )

    generate_precondition_steps = Task(
        name="Generate Precondition Steps Task",
        config=tasks["generate_precondition_steps"],
        agent=automation_test_engineer_agent,
        context=[
            read_keywords_task,
        ]
    )

    generate_test_procedure_steps = Task(
        name="Generate Test Procedure Steps Task",
        config=tasks["generate_test_procedure_steps"],
        agent=automation_test_engineer_agent,
        context=[
            read_keywords_task,
        ]
    )

    generate_reset_steps = Task(
        name="Generate Reset Steps Task",
        config=tasks["generate_reset_steps"],
        agent=automation_test_engineer_agent,
        context=[
            read_keywords_task,
        ]
    )

    generate_test_expectations = Task(
        name="Generate Test Expectations Task",
        config=tasks["generate_test_expectations"],
        agent=automation_test_engineer_agent,
        context=[
            read_keywords_task,
        ]
    )

    generate_test_notes = Task(
        name="Generate Test Notes Task",
        config=tasks["generate_test_notes"],
        agent=automation_test_engineer_agent,
        context=[
            generate_precondition_steps,
            generate_test_procedure_steps,
            generate_reset_steps,
            generate_test_expectations,
        ]
    )

    compile_final_testcase_json = Task(
        name="Compile Final Testcase Task",
        config=tasks["compile_final_testcase_json"],
        agent=automation_test_engineer_agent,
        # You can reference outputs of previous tasks here if chaining
        context=[
            generate_precondition_steps,
            generate_test_procedure_steps,
            generate_reset_steps,
            generate_test_expectations,
            generate_test_notes
        ],
        output_json=AutoTestcaseModel,
        markdown=False,
        guardrail=validate_json_output,
        max_retries=3  # Limit retry attempts
    )

    crew = Crew(
        agents=[automation_test_engineer_agent],
        tasks=[read_keywords_task, generate_precondition_steps, generate_test_procedure_steps, generate_reset_steps,
               generate_test_expectations,
               generate_test_notes, compile_final_testcase_json],
        process=Process.sequential
    )
    crew.name = request_email or "<EMAIL>"

    return crew
