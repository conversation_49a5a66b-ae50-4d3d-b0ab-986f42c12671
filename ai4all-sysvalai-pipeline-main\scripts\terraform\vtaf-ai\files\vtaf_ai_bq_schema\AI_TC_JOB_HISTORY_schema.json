[{"mode": "NULLABLE", "name": "user_email", "type": "STRING", "description": "user_email"}, {"mode": "NULLABLE", "name": "timestamp", "type": "TIMESTAMP", "description": "timestamp"}, {"mode": "NULLABLE", "name": "job_start_time", "type": "TIMESTAMP", "description": "job_start_time"}, {"mode": "NULLABLE", "name": "job_end_time", "type": "TIMESTAMP", "description": "job_end_time"}, {"mode": "NULLABLE", "name": "job_duration", "type": "STRING", "description": "job_duration"}, {"mode": "NULLABLE", "name": "bg", "type": "STRING", "description": "bg"}, {"mode": "NULLABLE", "name": "pg", "type": "STRING", "description": "pg"}, {"mode": "NULLABLE", "name": "pl", "type": "STRING", "description": "pl"}, {"mode": "NULLABLE", "name": "status", "type": "STRING", "description": "status"}, {"mode": "NULLABLE", "name": "execution_status", "type": "STRING", "description": "execution_status"}, {"mode": "NULLABLE", "name": "user_upload", "type": "STRING", "description": "user_upload"}, {"mode": "NULLABLE", "name": "user_input", "type": "STRING", "description": "user_input"}, {"mode": "NULLABLE", "name": "output_json", "type": "STRING", "description": "output_json"}, {"mode": "NULLABLE", "name": "output_csv", "type": "STRING", "description": "output_csv"}, {"mode": "NULLABLE", "name": "total_token", "type": "INT64", "description": "total_token"}, {"mode": "NULLABLE", "name": "prompt_token", "type": "INT64", "description": "prompt_token"}, {"mode": "NULLABLE", "name": "cached_prompt_token", "type": "INT64", "description": "cached_prompt_token"}, {"mode": "NULLABLE", "name": "completion_token", "type": "INT64", "description": "completion_token"}, {"mode": "NULLABLE", "name": "successfull_agent_request", "type": "INT64", "description": "successfull_agent_request"}, {"mode": "NULLABLE", "name": "total_req", "type": "INT64", "description": "total_req"}, {"mode": "NULLABLE", "name": "completed_req", "type": "INT64", "description": "completed_req"}, {"mode": "NULLABLE", "name": "execution_id", "type": "STRING", "description": "execution_id"}, {"mode": "NULLABLE", "name": "llm_model", "type": "STRING", "description": "llm_model"}, {"mode": "NULLABLE", "name": "llm_location", "type": "STRING", "description": "llm_location"}, {"mode": "NULLABLE", "name": "project", "type": "STRING", "description": "project"}, {"mode": "NULLABLE", "name": "project_variant", "type": "STRING", "description": "project_variant"}, {"mode": "NULLABLE", "name": "euf_feature", "type": "STRING", "description": "euf_feature"}, {"mode": "NULLABLE", "name": "is_project", "type": "BOOL", "description": "is_project"}, {"mode": "NULLABLE", "name": "num_of_tc", "type": "INT64", "description": "num_of_tc"}, {"mode": "NULLABLE", "name": "agent_yaml", "type": "STRING", "description": "agent_yaml"}, {"mode": "NULLABLE", "name": "task_yaml", "type": "STRING", "description": "agent_yaml"}, {"mode": "NULLABLE", "name": "is_yaml_edited", "type": "BOOLEAN", "description": "is_yaml_edited"}, {"mode": "NULLABLE", "name": "trace_url", "type": "STRING", "description": "trace_url"}, {"mode": "NULLABLE", "name": "trace_id", "type": "STRING", "description": "trace_id"}]