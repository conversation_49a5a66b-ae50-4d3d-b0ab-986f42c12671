<template>
  <div class="q-mt-md q-ml-md q-mr-md">
    <div class="row items-center q-gutter-sm">
      <div style="min-width: 60px;">Model</div>
      <q-select
        v-model="selectedModel"
        :options="modelOptions"
        outlined
        dense
        emit-value
        map-options
        style="flex: 1;"
        bg-color="white"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const selectedModel = ref('gemini-2.5-pro-preview-05-06');
const modelOptions = [
  'gemini-1.5-pro',
  'gemini-1.5-flash',
  'gemini-2.5-pro-preview-05-06',
  'gemini-1.0-pro',
  // Add more models as needed
];
</script>
