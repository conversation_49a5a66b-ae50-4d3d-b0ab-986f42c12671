#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script Single Service - 单个测试脚本生成服务
对应原始ai_generate.py中的do_tc_single_task_send()方法
处理单个测试脚本生成任务
"""

import os
from typing import Dict, Any, Optional
from utils.logger import get_logger
from services.testscript_batch_service import TestScriptBatchService

logger = get_logger(__name__)

class TestScriptSingleService(TestScriptBatchService):
    """单个测试脚本生成服务 - 继承自批量服务，但只处理单个任务"""
    
    def __init__(self):
        super().__init__()
        
    def execute(self) -> int:
        """执行单个测试脚本生成任务"""
        try:
            logger.info("🚀 Starting test script single job")
            
            # 获取配置
            config = self._get_config()
            self._validate_config(config)
            
            # 设置为单个任务模式
            config['task_type'] = 'single'
            config['single_sheet_name'] = os.getenv('SINGLE_SHEET_NAME', 'Prompt_Single')
            
            # 初始化头文件数据
            head_files = self._load_head_files(config)
            head_file_prompt = config.get('head_file_prompt', '')
            
            # 连接到表格
            if not self.sheet_manager.connect_sheet(config['prompt_sheet']):
                raise Exception("Failed to connect to prompt sheet")
            
            # 处理单个测试脚本生成
            self._process_single_script(config, head_files, head_file_prompt)
            
            logger.info(f"✅ Test script single completed. Cases: {self.case_count}, Errors: {self.error_count}")
            return 0
            
        except Exception as e:
            logger.exception(f"Test script single job failed: {e}")
            return 1
    
    def _process_single_script(self, config: Dict[str, Any], head_files: Dict[str, str], head_file_prompt: str):
        """处理单个测试脚本生成"""
        sheet_name = config.get('single_sheet_name', 'Prompt_Single')
        
        logger.info(f"Processing single script from sheet: {sheet_name}")
        
        # 读取提示词数据
        prompts = self.sheet_manager.read_by_sheet_name(sheet_name)
        
        if not prompts:
            logger.warning(f"No prompts found in sheet: {sheet_name}")
            return
        
        # 只处理第一个未处理的提示词
        for prompt_data in prompts:
            try:
                status = prompt_data.get('Status', '')
                if status in ['generated', 'processing']:
                    continue  # 跳过已处理的
                
                category = prompt_data.get('category', sheet_name)
                prompt_text = prompt_data.get('prompt', '')
                
                if not prompt_text:
                    continue
                
                logger.info(f"Processing single prompt: {category}")
                
                # 处理模块和头文件
                module = self._extract_module_from_category(category)
                self._process_head_file(module, head_files, head_file_prompt)
                
                # 生成测试脚本
                response = self.ai_service.generate_response(prompt_text)
                
                if response.startswith("Error"):
                    self.error_count += 1
                    logger.error(f"Failed to generate script: {response}")
                    # 更新状态为失败
                    self._update_prompt_status(sheet_name, prompt_data, 'failed')
                    return
                
                # 保存结果
                capl_contents = {}
                self._save_script_result(prompt_data, response, capl_contents)
                self.case_count += 1
                
                # 更新状态为完成
                self._update_prompt_status(sheet_name, prompt_data, 'generated')
                
                # 只处理一个提示词就退出
                break
                
            except Exception as e:
                self.error_count += 1
                logger.error(f"Error processing single prompt: {e}")
                self._update_prompt_status(sheet_name, prompt_data, 'failed')
                return
    
    def _update_prompt_status(self, sheet_name: str, prompt_data: Dict[str, Any], status: str):
        """更新提示词状态"""
        try:
            # 假设状态在第6列（F列）
            row_number = prompt_data.get('row_number', 2)  # 默认第2行
            self.sheet_manager.update_cell(sheet_name, row_number, 6, status)
            logger.info(f"Updated prompt status to: {status}")
        except Exception as e:
            logger.warning(f"Failed to update prompt status: {e}")
