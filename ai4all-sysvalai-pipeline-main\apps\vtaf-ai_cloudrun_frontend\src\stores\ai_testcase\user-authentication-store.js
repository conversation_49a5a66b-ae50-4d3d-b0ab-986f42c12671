import { ref } from "vue";
import { defineStore } from "pinia";
import { Notify, Loading } from "quasar";
import { ai_testcaseApi } from "src/boot/axios";

export const useUserAuthenticationStore = defineStore(
  "user_authentication_store",
  () => {
    const main_email = ref("");

    async function get_user_email() {
      try {
        Loading.show();
        const response = await ai_testcaseApi.get(`/user/get_user`);
        const data = response.data.data;
        main_email.value = data;
        first_letter_email.value = main_email.value.charAt(0).toUpperCase();
        console.log(data);
      } catch (err) {
        handleFetchError(err);
      } finally {
        Loading.hide();
      }
    }
    return {
      main_email,
      get_user_email,
    };
  }
);
