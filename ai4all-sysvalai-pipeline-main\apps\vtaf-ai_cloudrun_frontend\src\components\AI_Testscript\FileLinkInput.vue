<template>
  <div class="q-pa-md">
    <q-banner
      v-if="showSuccessBanner"
      class="bg-green-2 text-green-10 q-mb-md"
      rounded
      dense
    >
      Job started successfully! You will get an email once it is completed...
    </q-banner>
    <q-card class="q-pa-md" flat bordered>
      <q-card-section>
        <div class="text-h6">TestScript Generation</div>
      </q-card-section>

      <q-card-section>
        <q-input
          filled
          v-model="spreadsheetLink"
          label="Google Sheets URL"
          placeholder="https://docs.google.com/spreadsheets/d/..."
          :rules="[validateSheetURL]"
          clearable
        />
      </q-card-section>

      <q-card-actions align="right">
        <q-btn
          label="Generate TestScript"
          color="primary"
          :disable="!isValid || !spreadsheetLink"
          @click="send_sheet"
          :loading="upload_file_loading"
        />
      </q-card-actions>
    </q-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRunTsGenerationStore } from 'src/stores/ai_testscript/run-ts-generation-store';
import { storeToRefs } from "pinia";

const ai_testscript_config = useRunTsGenerationStore();
const { user_email, spreadsheetLink, showSuccessBanner, upload_file_loading} = storeToRefs(ai_testscript_config);
const { get_user_email, send_sheet } = ai_testscript_config;

const validateSheetURL = (val) => {
  if (!val) return true  // Don't show any validation if empty
  const regex = /^https:\/\/docs\.google\.com\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/
  return regex.test(val) || 'Enter a valid Google Sheets link'
}

const isValid = computed(() => {
  return validateSheetURL(spreadsheetLink.value) === true
})

</script>
