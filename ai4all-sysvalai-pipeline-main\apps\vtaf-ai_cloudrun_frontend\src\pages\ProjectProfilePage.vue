<!-- <template><router-view></router-view></template> -->
<template>
  <div>
    <q-tabs
      v-model="panel"
      dense
      class="text-black"
      active-color="primary"
      indicator-color="primary"
      align="justify"
      narrow-indicator
      active-bg-color="blue-1"
      style="margin-bottom: 1rem"
    >
      <q-tab name="create_project_profile" label="CREATE PROJECT PROFILE" />
      <q-tab name="create_knowledge_base" label="CREATE KNOWLEDGE BASE" />
    </q-tabs>

    <q-tab-panels
      v-model="panel"
      animated
      class="shadow-2 rounded-borders"
      style="margin: 0 1rem 0 1rem; border-radius: 1rem"
    >
      <q-tab-panel name="create_project_profile">
        <create-profile-kb></create-profile-kb>
      </q-tab-panel>
      <q-tab-panel name="create_knowledge_base">
        <knowledge-base-inputs></knowledge-base-inputs>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup>
import { storeToRefs } from "pinia";
import { onMounted } from "vue";
import { useKnowledgebaseManagementStore } from "src/stores/ai_testcase/knowledgebase-management-store";
import { useRouter } from "vue-router";
// import CreateProfile from "src/components/Project_Profile/CreateProfile.vue";
import KnowledgeBaseInputs from "src/components/Project_Profile/KnowledgeBaseInputs.vue";
import CreateProfileKb from "src/components/Project_Profile/CreateProfileKb.vue";

const router = useRouter();

const ai_testcase_config = useKnowledgebaseManagementStore();
const { panel, user_email } = storeToRefs(ai_testcase_config);
const { get_user_email } = ai_testcase_config;

onMounted(async () => {
  await get_user_email();
});
</script>
