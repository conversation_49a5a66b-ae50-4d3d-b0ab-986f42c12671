# Group {Other}
## Check_Recent_Log

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Check_Recent_Log.seq*


**Descriptions:** *This keyword will open the recent log in the Folder provided and check for particular text in the log.*


***Command Set:***

	Pattern
		-Pattern=Folder;TextMatch


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Folder| String| NA| C:\Temp\logs\TeraTerm\M3N|  Provide path where you would like to check for logs| Required
| TextMatch| String| NA| TextMatch|  Input the Text you were looking for in log| Required

## Clear_Failure

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Clear_Failures.seq*


**Descriptions:** *This Keyword will clear failues in main sequence context*


***Command Set:***

	Seq
		-Seq=


***Input Description***



## Dialogs

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Dialogs.seq*


**Descriptions:** **


***Command Set:***

	Popup
		-Popup=Message;Type


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Message| String| NA| Xyz|  popup message to display
| Type| Ring| AllowRTval=True,Simple,PassFail,Image| Simple|  Type of pop-up

## Flash_ECU

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Flash_ECU.seq*


**Descriptions:** *This keyword will Flash ECU.*


***Command Set:***

	Request
		-Request=Settings
	iCAM3
		-iCAM3=Alias;Type;Mode;NCD_Folder
	Ford
		-Ford=ECU_Type;Vehicle_Type;Flash_Mode
	iCAM3-FA
		-iCAM3-FA=Type
	GM
		-GM=FlashingScriptName


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Settings| String| NA| Xyz| Specify Settings from flashing configuration file| 
| Alias| String| NA| xyz|  Specify the alias from Lookup Table| Required
| Type| Ring| AllowRTval=False,All,blFlash,swDeploy,cdDeploy| All|  Specify the Flashing Type| Required
| Mode| Ring| AllowRTval=False,Offline,Online| Offline|  Specify the Mode| Required
| NCD_Folder| String| NA| None|  Specify the NCD Folder Name| Required
| ECU_Type| Ring| AllowRTval=False,HIGH,LOW| HIGH|  Type of ECU| Required
| Vehicle_Type| String| NA| 123|  Specify Vehcile Type| Required
| Flash_Mode| Ring| AllowRTval=False,EyeQ Flashing,Full Flashing,TreeRunner Flashing,Host Flashing,SoC Flashing| SoC Flashing|  Specify Flash mode| Required
| FlashingScriptName| String| NA| Xyz|  | Required

## Generate_Image

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Generate_Image.seq*


**Descriptions:** **


***Command Set:***

	SuperImposition
		-SuperImposition=Template


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Template| String| NA| max_left|  This Template image will get added to last snapped image.| Required

## Get_Security

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Security_Access.seq*


**Descriptions:** *Sends Seed Key request to ECU*


***Command Set:***

	Access
		-Access=Level


***Input Description***



## HMI

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\HMI.seq*


**Descriptions:** *This keyword will control the HMI icons.*


***Command Set:***

	PressIcon
		-PressIcon=GoldenImage
	SetTouch
		-SetTouch=X_Pos;Y_Pos;Duration;Redo


***Input Description***



## Icam3_Flash

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Icam3_Flash.seq*


**Descriptions:** *This keyword is used to flash the icam3 ECU using the BDC, the flashing file path are obtained from "ICAM3_Flash_PDX_Lookup"*


***Command Set:***

	LookupBased
		-LookupBased=Alias


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Alias| String| NA| I020|  Used to get the row index from the lookuptable| Required

## Search_File

- [ ] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** **


**Descriptions:** **


***Command Set:***

	Content
		-Content=Alias;PageNumber;Content


***Input Description***

| Parameter| Data type| Range| Value| Description| Type
|------------|------------|------------|------------|------------|------------
| Alias| String| NA| Xyz|  Specify the Alias of the File Path.| Required
| PageNumber| String| NA| Xyz|  Specify the Page Number need to search| Required
| Content| String| NA| Xyz|  Specify the Content need to search| Required

## Seed_Key

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Security_Access.seq*


**Descriptions:** *Sends Seed Key request to ECU*


***Command Set:***

	Request
		-Request=


***Input Description***



## Set_Button

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** **


**Descriptions:** *This keyword is used to set the button. *


***Command Set:***

	Hard
		-Hard=Alias;Status


***Input Description***



## Set_Car

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Set_Car.seq*


**Descriptions:** *This keyword contains all the car sepecific fuctions like gear etc.*


***Command Set:***

	Gear
		-Gear=Value


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Alias| String| NA| RSPA| used to specifiy the button Alias. 
Current supported values. 
RSPA \ PDW \ SVM 

| Status| Ring| AllowRTval=False,ON,OFF| ON|  Set the status to ON \ OFF.
| Value| Ring| AllowRTval=False,P,R,N,D| P| Holds the gear value

## Set_FaultBox

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Set_FaultBox.seq*


**Descriptions:** *This Keyword is used to set the fault box conditions*


***Command Set:***

	Generic
		-Generic=Alias;Fault;Command
	Camera
		-Camera=Alias;Fault;Camera
	Reset
		-Reset=Alias


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Alias| Ring| AllowRTval=False,FaultA,FaultB| FaultA|  To specify the COM port alias. 
"FaultA" \ "FaultB"
| Fault| String| NA| NoFault|  To sepcify the required fault conditions. 
"NoFault" \ "Open" \ "ShortToGND" \ "ShortToVBAT"
will also the other specific faults. 
| Camera| Ring| AllowRTval=False,Cam1,Cam2,Cam3,Cam4| Cam1|  Specify the Cam Index
| Command| String| NA| ECU| Specify the Command which states the specific section of fault

## Set_Relay

- [ ] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Set_Relay.seq*


**Descriptions:** *This keyword will control the relay box *


***Command Set:***

	LightBox
		-LightBox=Alias;State
	Direct
		-Direct=State;Channel;Port
	REES52
		-REES52=State;Channel;ID


***Input Description***



## TraceLogging

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\TraceLogging.seq*


**Descriptions:** **


***Command Set:***

	Start
		-Start=NewParameter
	Stop
		-Stop=


***Input Description***



## VDA_Service

- [x] TestStand    
- [ ] CAPL    
- [ ] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\VDA_Service.seq*


**Descriptions:** **


***Command Set:***

	Invoke
		-Invoke=service;topic;params
	CompareText
		-CompareText=ParameterName;ParameterValue;Condition
	CompareNumeric
		-CompareNumeric=ParameterName;ValCondition
	FTTI-Test
		-FTTI-Test=service;topic;params


***Input Description***



## Wait

- [x] TestStand    
- [x] CAPL    
- [x] Python     


**Path:** *C:\SmartTest\proj2153_vrl_smarttest_keyword\Smart_Test_Keywords\source\CurrentCodes\KeyWord_Seq\Wait.seq*


**Descriptions:** *Waits the code for specific duration.*


***Command Set:***

	Sec
		-Sec=Seconds
	Ms
		-Ms=MilliSeconds


***Input Description***

| Parameter| Data type| Range| Value| Description
|------------|------------|------------|------------|------------
| Seconds| Float| GELE (>=<=)0.000000,1000.000000| 0|  Wait Time in Seconds
| MilliSeconds| Int| GELE (>=<=)0,30000| 0|  wait in milliseconds

