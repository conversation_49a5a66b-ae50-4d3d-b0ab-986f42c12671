#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行所有测试用例
"""

import sys
import os
import subprocess
import time
from datetime import datetime

def run_test_file(test_file):
    """运行单个测试文件"""
    print(f"\n{'='*60}")
    print(f"运行测试文件: {test_file}")
    print(f"{'='*60}")
    
    try:
        # 运行测试文件
        result = subprocess.run([
            sys.executable, test_file
        ], capture_output=True, text=True, timeout=60)
        
        # 输出结果
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        success = result.returncode == 0
        print(f"\n测试结果: {'✓ 通过' if success else '✗ 失败'}")
        return success
        
    except subprocess.TimeoutExpired:
        print("✗ 测试超时")
        return False
    except Exception as e:
        print(f"✗ 运行测试时出错: {e}")
        return False

def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    modules_to_test = [
        ("utils.data_processing", "数据处理模块"),
        ("utils.logger", "日志模块"),
        ("services.config_service", "配置服务"),
        ("services.ai_service", "AI服务"),
        ("services.sheet_manager", "表格管理器"),
        ("services.batch_processor", "批处理器"),
        ("configs.env", "环境配置")
    ]
    
    import_results = []
    
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
            print(f"✓ {description} ({module_name}): 导入成功")
            import_results.append(True)
        except Exception as e:
            print(f"✗ {description} ({module_name}): 导入失败 - {e}")
            import_results.append(False)
    
    success_count = sum(import_results)
    total_count = len(import_results)
    print(f"\n导入测试结果: {success_count}/{total_count} 成功")
    
    return success_count == total_count

def test_basic_functionality():
    """测试基本功能"""
    print("\n=== 测试基本功能 ===")
    
    try:
        # 测试数据处理
        from utils.data_processing import parse_test_cases, fields
        
        simple_test = "Test Case ID: TC_001\nTest Objective: Simple test"
        result = parse_test_cases(simple_test)
        data_processing_ok = len(result.get('Test Cases', [])) >= 0
        print(f"✓ 数据处理基本功能: {'正常' if data_processing_ok else '异常'}")
        
        # 测试配置服务
        from services.config_service import ConfigurationService
        
        config_service = ConfigurationService()
        default_config = config_service.get_configuration()
        config_service_ok = isinstance(default_config, dict)
        print(f"✓ 配置服务基本功能: {'正常' if config_service_ok else '异常'}")
        
        # 测试模型选项
        model_options = config_service.get_model_options()
        model_options_ok = 'gemini_models' in model_options
        print(f"✓ 模型选项获取: {'正常' if model_options_ok else '异常'}")
        
        return data_processing_ok and config_service_ok and model_options_ok
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def check_dependencies():
    """检查依赖项"""
    print("=== 检查依赖项 ===")
    
    required_packages = [
        "gspread",
        "google-auth",
        "pydantic",
        "pydantic-settings"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package}: 已安装")
        except ImportError:
            print(f"✗ {package}: 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    print("\n所有依赖项检查通过")
    return True

def main():
    """主函数"""
    print("VTAF AI Multiprompt 重构代码测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # 检查当前目录
    current_dir = os.getcwd()
    expected_files = ['main.py', 'utils', 'services', 'configs']
    
    if not all(os.path.exists(f) for f in expected_files):
        print("错误: 请在项目根目录运行此测试")
        print(f"当前目录: {current_dir}")
        print(f"期望文件/目录: {expected_files}")
        return False
    
    # 运行测试步骤
    test_steps = [
        ("依赖项检查", check_dependencies),
        ("模块导入测试", test_imports),
        ("基本功能测试", test_basic_functionality)
    ]
    
    results = []
    
    for step_name, test_func in test_steps:
        print(f"\n{'-'*40}")
        print(f"执行: {step_name}")
        print(f"{'-'*40}")
        
        try:
            result = test_func()
            results.append((step_name, result))
            print(f"\n{step_name}: {'✓ 通过' if result else '✗ 失败'}")
        except Exception as e:
            results.append((step_name, False))
            print(f"\n{step_name}: ✗ 异常 - {e}")
    
    # 运行具体的测试文件
    test_files = [
        "test_data_processing.py",
        "test_config_service.py"
    ]
    
    for test_file in test_files:
        if os.path.exists(test_file):
            result = run_test_file(test_file)
            results.append((f"测试文件: {test_file}", result))
        else:
            print(f"\n警告: 测试文件 {test_file} 不存在")
            results.append((f"测试文件: {test_file}", False))
    
    # 总结
    print(f"\n{'='*80}")
    print("测试总结")
    print(f"{'='*80}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"总体结果: {passed}/{total} 通过")
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
    
    if passed == total:
        print(f"\n🎉 所有测试通过! 重构代码功能正常。")
        return True
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
