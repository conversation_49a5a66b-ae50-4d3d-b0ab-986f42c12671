steps:
- name: 'gcr.io/cloud-builders/docker'
  args: ['build', '-t', 'europe-west1-docker.pkg.dev/valeo-cp2508-dev/valeo-cp2508-vtaf-dev/llm-chat', '.']
- name: 'gcr.io/cloud-builders/docker'
  args: ['push', 'europe-west1-docker.pkg.dev/valeo-cp2508-dev/valeo-cp2508-vtaf-dev/llm-chat']
- name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
  entrypoint: gcloud
  args: ['run', 'deploy', 'llm-chat', '--image', 'europe-west1-docker.pkg.dev/valeo-cp2508-dev/valeo-cp2508-vtaf-dev/llm-chat', '--region', 'europe-west1', '--platform', 'managed']
options:
  logging: CLOUD_LOGGING_ONLY
