#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的测试运行脚本
用于测试日志管理功能集成
"""

import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

def get_log_directory_info():
    """获取日志目录信息"""
    try:
        from services.log_service import LogService
        log_service = LogService()

        # 获取绝对路径
        log_dir_abs = os.path.abspath(log_service.log_dir)

        return {
            "log_dir": log_service.log_dir,
            "log_dir_abs": log_dir_abs,
            "exists": os.path.exists(log_dir_abs)
        }
    except Exception as e:
        return {
            "log_dir": "logs",
            "log_dir_abs": os.path.abspath("logs"),
            "exists": False,
            "error": str(e)
        }

def test_log_save():
    """测试日志保存功能"""
    print("🧪 测试日志保存功能")
    print("=" * 50)

    try:
        # 显示日志目录信息
        log_info = get_log_directory_info()
        print(f"📁 日志目录: {log_info['log_dir']}")
        print(f"📂 绝对路径: {log_info['log_dir_abs']}")

        # 设置环境变量
        os.environ['JOB_TYPE'] = 'log_save'
        os.environ['RUN_MODE'] = 'production'

        # 设置要保存的日志数据
        import json
        from datetime import datetime

        sample_data = {
            "conversation_id": f"test_conv_{int(datetime.now().timestamp())}",
            "session_start": datetime.now().isoformat(),
            "conversations": [
                {
                    "user": "你好，这是一个测试对话",
                    "assistant": "你好！我是AI助手，很高兴为你服务。这是一个测试回复。",
                    "timestamp": datetime.now().isoformat()
                }
            ],
            "metadata": {
                "test_mode": True,
                "source": "run_test.py"
            }
        }

        # 预测保存的文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        predicted_filename = f"multiprompt_log_{timestamp}.json"
        predicted_filepath = os.path.join(log_info['log_dir_abs'], predicted_filename)

        print(f"📄 预期文件名: {predicted_filename}")
        print(f"📍 预期路径: {predicted_filepath}")

        os.environ['LOG_DATA'] = json.dumps(sample_data, ensure_ascii=False, indent=2)

        # 导入并执行
        from services.job_router import JobRouter
        router = JobRouter()
        result = router.route_job()

        if result == 0:
            # 验证文件是否真的被创建
            if os.path.exists(predicted_filepath):
                file_size = os.path.getsize(predicted_filepath)
                print(f"✅ 日志保存测试成功！")
                print(f"📁 保存位置: {predicted_filepath}")
                print(f"📊 文件大小: {file_size} 字节")
            else:
                # 尝试找到实际保存的文件
                from services.log_service import LogService
                log_service = LogService()
                if os.path.exists(log_service.log_dir):
                    log_files = [f for f in os.listdir(log_service.log_dir) if f.endswith('.json')]
                    if log_files:
                        latest_file = sorted(log_files)[-1]
                        actual_filepath = os.path.join(log_info['log_dir_abs'], latest_file)
                        file_size = os.path.getsize(actual_filepath)
                        print(f"✅ 日志保存测试成功！")
                        print(f"📁 实际保存位置: {actual_filepath}")
                        print(f"📊 文件大小: {file_size} 字节")
                    else:
                        print("✅ 日志保存测试成功！（但无法确定具体路径）")
                else:
                    print("✅ 日志保存测试成功！（但无法确定具体路径）")
        else:
            print("❌ 日志保存测试失败！")

        return result == 0

    except Exception as e:
        print(f"❌ 日志保存测试异常: {e}")
        return False
    finally:
        # 清理环境变量
        for key in ['JOB_TYPE', 'LOG_DATA']:
            if key in os.environ:
                del os.environ[key]

def test_log_list():
    """测试日志列表功能"""
    print("\n🧪 测试日志列表功能")
    print("=" * 50)

    try:
        # 显示日志目录信息
        log_info = get_log_directory_info()
        print(f"📁 日志目录: {log_info['log_dir']}")
        print(f"📂 绝对路径: {log_info['log_dir_abs']}")

        # 先直接检查目录内容
        from services.log_service import LogService
        log_service = LogService()

        if os.path.exists(log_service.log_dir):
            log_files = [f for f in os.listdir(log_service.log_dir) if f.endswith('.json')]
            print(f"📄 找到 {len(log_files)} 个日志文件")

            for i, filename in enumerate(log_files[:3], 1):  # 只显示前3个
                filepath = os.path.join(log_info['log_dir_abs'], filename)
                file_size = os.path.getsize(filepath) if os.path.exists(filepath) else 0
                print(f"  {i}. {filename} ({file_size} 字节)")
                print(f"     路径: {filepath}")
        else:
            print("📭 日志目录不存在")

        # 设置环境变量
        os.environ['JOB_TYPE'] = 'log_list'
        os.environ['RUN_MODE'] = 'production'

        # 导入并执行
        from services.job_router import JobRouter
        router = JobRouter()
        result = router.route_job()

        if result == 0:
            print("✅ 日志列表测试成功！")
        else:
            print("❌ 日志列表测试失败！")

        return result == 0

    except Exception as e:
        print(f"❌ 日志列表测试异常: {e}")
        return False
    finally:
        # 清理环境变量
        if 'JOB_TYPE' in os.environ:
            del os.environ['JOB_TYPE']

def test_log_load():
    """测试日志加载功能"""
    print("\n🧪 测试日志加载功能")
    print("=" * 50)

    try:
        # 显示日志目录信息
        log_info = get_log_directory_info()
        print(f"📁 日志目录: {log_info['log_dir']}")
        print(f"📂 绝对路径: {log_info['log_dir_abs']}")

        # 先检查是否有日志文件可以加载
        from services.log_service import LogService
        log_service = LogService()

        if os.path.exists(log_service.log_dir):
            log_files = [f for f in os.listdir(log_service.log_dir) if f.endswith('.json')]
            log_files.sort(reverse=True)  # 最新的在前

            if log_files:
                latest_file = log_files[0]
                latest_filepath = os.path.join(log_info['log_dir_abs'], latest_file)
                file_size = os.path.getsize(latest_filepath) if os.path.exists(latest_filepath) else 0

                print(f"📄 将加载最新文件: {latest_file}")
                print(f"📍 文件路径: {latest_filepath}")
                print(f"📊 文件大小: {file_size} 字节")
            else:
                print("📭 没有找到日志文件可以加载")
        else:
            print("📁 日志目录不存在")

        # 设置环境变量
        os.environ['JOB_TYPE'] = 'log_load'
        os.environ['RUN_MODE'] = 'production'

        # 导入并执行
        from services.job_router import JobRouter
        router = JobRouter()
        result = router.route_job()

        if result == 0:
            print("✅ 日志加载测试成功！")
        else:
            print("❌ 日志加载测试失败！")

        return result == 0

    except Exception as e:
        print(f"❌ 日志加载测试异常: {e}")
        return False
    finally:
        # 清理环境变量
        if 'JOB_TYPE' in os.environ:
            del os.environ['JOB_TYPE']

def test_main_function():
    """测试通过main函数执行日志功能"""
    print("\n🧪 测试通过main函数执行日志功能")
    print("=" * 50)
    
    try:
        # 设置环境变量
        os.environ['EXECUTE_LOG_FUNCTIONS'] = 'true'
        os.environ['SKIP_CONVERSATION_TEST'] = 'true'
        os.environ['EXECUTE_DEFAULT_BUTTONS'] = 'false'
        os.environ['RUN_MODE'] = 'production'
        
        # 导入并执行
        from main import main
        result = main()
        
        if result == 0:
            print("✅ main函数日志功能测试成功！")
        else:
            print("❌ main函数日志功能测试失败！")
            
        return result == 0
        
    except Exception as e:
        print(f"❌ main函数测试异常: {e}")
        return False
    finally:
        # 清理环境变量
        for key in ['EXECUTE_LOG_FUNCTIONS', 'SKIP_CONVERSATION_TEST', 'EXECUTE_DEFAULT_BUTTONS', 'RUN_MODE']:
            if key in os.environ:
                del os.environ[key]

def main():
    """主测试函数"""
    print("🚀 开始日志管理功能简单测试")
    print("=" * 80)
    
    tests = [
        ("日志保存", test_log_save),
        ("日志列表", test_log_list),
        ("日志加载", test_log_load),
        ("main函数集成", test_main_function),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔧 执行测试: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ 测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
    
    # 统计结果
    print("\n" + "="*80)
    print("📊 测试结果汇总")
    print("="*80)
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
        if success:
            success_count += 1
    
    total_tests = len(results)
    success_rate = (success_count / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"\n总计: {success_count}/{total_tests} 测试通过")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_count == total_tests:
        print("\n🎉 所有测试通过！日志管理功能集成成功！")
    else:
        print(f"\n⚠️ 有 {total_tests - success_count} 个测试失败，请检查配置")

if __name__ == '__main__':
    main()
