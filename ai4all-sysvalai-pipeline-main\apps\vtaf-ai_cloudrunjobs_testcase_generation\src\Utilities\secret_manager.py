import os

from google.api_core.exceptions import NotFound
from google.cloud import secretmanager



class GCPSecretManager:
    def __init__(self):
        self.client = secretmanager.SecretManagerServiceClient()
        self.parent = f"projects/{os.getenv('SERVICES_PROJECT_NUMBER')}"

    def add_secret_version(self, secret_id: str, value: str):
        secret_name = f"{self.parent}/secrets/{secret_id}"
        payload = value.encode("UTF-8")
        try:
            version = self.client.add_secret_version(
                request={
                    "parent": secret_name,
                    "payload": {"data": payload},
                }
            )
            print(f"Added secret version: {version.name}")
        except NotFound:
            print(f"Secret '{secret_id}' not found. You may need to create it first.")
            raise

        except Exception as e:
            print(f"Failed to add secret version: {e}")
            raise

    def read_secret(self, secret_id: str, version: str = "latest"):
        name = f"{self.parent}/secrets/{secret_id}/versions/{version}"
        try:
            response = self.client.access_secret_version(request={"name": name})
            payload = response.payload.data.decode("UTF-8")
            print(f"Secret version '{secret_id}/{version}' found.")
            return payload
        except NotFound:
            print(f"Secret version '{secret_id}/{version}' not found.")
            raise

        except Exception as e:
            print(f"Failed to read secret: {e}")
            raise
