import os
import requests
from openai import OpenAI, AuthenticationError, APIError
from configs.env import settings
from utils.logger import get_logger

logger = get_logger(__name__)

class DeepSeekClient:
    def __init__(self):
        self.client = None
        self.model_list = []
        self.messages = []
        self._initialize_client()
        
    def _initialize_client(self):
        """初始化 DeepSeek 客户端"""
        if not settings.DEEPSEEK_API_KEY:
            logger.warning("DeepSeek API key not configured")
            return
            
        try:
            self.client = OpenAI(
                api_key=settings.DEEPSEEK_API_KEY,
                base_url="http://10.168.44.171:3000/api"
            )
            self._load_models()
        except (AuthenticationError, APIError) as e:
            logger.error(f"Failed to initialize DeepSeek client: {e}")
            
    def _load_models(self):
        """加载可用模型列表"""
        try:
            all_models = self.client.models.list()
            self.model_list = [model.id for model in all_models.data]
            logger.info(f"Loaded {len(self.model_list)} DeepSeek models")
        except Exception as e:
            logger.error(f"Failed to load models: {e}")
            
    def update_system_instruction(self, system_instruction):
        """更新系统指令"""
        self.messages.append({
            "role": "system", 
            "content": system_instruction
        })
        
    def get_response(self, model, prompt):
        """获取响应"""
        if not self.client:
            return "DeepSeek client not initialized"
            
        try:
            self.messages.append({"role": "user", "content": prompt})
            response = self.client.chat.completions.create(
                model=model,
                messages=self.messages,
                stream=False
            )
            
            response_message = response.choices[0].message
            self.messages.append(response_message)
            
            return f"{response_message.content}\n{response.usage.total_tokens}"
        except Exception as e:
            logger.error(f"DeepSeek API error: {e}")
            return f"Error: {str(e)}"
            
    def get_response_with_file(self, model, prompt, file_path):
        """带文件的响应"""
        # 实现文件上传和处理逻辑
        pass