import logging
import os
import time
from typing import List, Dict, Any

import pandas as pd
import yaml
from crewai import Agent, Task, Crew, LLM
from crewai.flow.flow import Flow, listen, start
from pydantic import BaseModel, Field

from configs.env import settings
from services.qdrant_storage import QdrantProcessor

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


# Initialize LLM
llm = LLM(
    model=settings.MODEL,
    temperature=0,
    project=settings.PROJECT_ID,
    vertex_location=settings.EMBEDDING_LOCATION,
    verbose=True,
)

# Load agents and tasks
with open("workflows/crewai_config/agents.yaml", "r") as f:
    agents_cfg = yaml.safe_load(f)

with open("workflows/crewai_config/tasks.yaml", "r") as f:
    tasks_cfg = yaml.safe_load(f)

data_extractor_agent = Agent(config=agents_cfg["Data_Extractor"], verbose=True, llm=llm)
summary_writer_agent = Agent(config=agents_cfg["Summary_Writer"], verbose=True, llm=llm)

extraction_task = Task(config=tasks_cfg["extraction_task"], agent=data_extractor_agent, output_json=None)  # Define model if needed
summary_task = Task(config=tasks_cfg["summary_task"], agent=summary_writer_agent, context=[extraction_task])

crew = Crew(
    agents=[data_extractor_agent, summary_writer_agent],
    tasks=[extraction_task, summary_task],
)


class Requirement(BaseModel):
    req_id: str = Field(..., description="Unique identifier for the requirement")
    requirement_description: str = Field(..., description="Description of the requirement")
    keywords: List[str] = Field(..., description="List of keywords related to the requirement")
    entities: List[str] = Field(..., description="List of entities related to the requirement")
    metadatas: Dict[str, Any] = Field(..., description="Metadata associated with the requirement")
    reference_standard: str = Field(None, description="Reference standard for the requirement")
    additional_info: Dict[str, Any] = Field(..., description="Other information related to the requirement")


class KnowledgeBaseFlow(Flow):
    def __init__(self, source_path: str, file_name: str, qdrant_processor: QdrantProcessor):
        super().__init__()
        self.source_path = source_path
        self.file_name = file_name
        self.qdrant = qdrant_processor
        self.status = []

    @start()
    def fetch_requirements(self) -> List[Dict[str, Any]]:
        file_path = os.path.join(self.source_path, self.file_name)
        logger.info(f"Loading requirements from {file_path}")

        try:
            try:
                df = pd.read_excel(file_path)
                logger.info("Read Excel file successfully.")
            except Exception:
                df = pd.read_csv(file_path)
                logger.info("Read CSV file successfully.")
        except Exception as e:
            logger.error(f"Failed to read requirements file: {e}")
            raise

        return df.to_dict(orient="records")

    @listen(fetch_requirements)
    def extract_knowledge(self, requirements: List[Dict[str, Any]]) -> List[Any]:
        wait_time = 15
        max_wait_time = 120
        max_retries = 3
        results = []
        for idx, req in enumerate(requirements):
            attempt = 0
            while attempt < max_retries:
                try:
                    logger.info(f"Processing requirement {idx + 1}/{len(requirements)} - Attempt {attempt + 1}")
                    results = crew.kickoff(inputs={"requirement": req})

                    self.qdrant.add_documents_in_batches(results.raw)

                    self.status.append("Completed")
                    break
                except Exception as e:
                    logger.warning(f"Attempt {attempt + 1} failed: {e}")
                    attempt += 1
                    time.sleep(wait_time)
                    wait_time = min(wait_time * 2, max_wait_time)
            else:
                err_msg = f"Failed processing requirement after {max_retries} attempts."
                logger.error(err_msg)
                self.status.append(err_msg)

        return results


async def run_flow(qdrant_processor):
    flow = KnowledgeBaseFlow(settings.SOURCE_PATH, settings.FILE_NAME, qdrant_processor)
    flow.plot()

    try:
        results = await flow.kickoff_async()
    except Exception as e:
        logger.error(f"Flow execution failed: {e}")
        return {}, ["Flow execution failed"]

    total_token_usage = {}

    try:
        token_usage = results.token_usage

        total_token_usage["total_tokens"] = int(getattr(token_usage, "total_tokens", 0))
        total_token_usage["prompt_tokens"] = int(getattr(token_usage, "prompt_tokens", 0))
        total_token_usage["cached_prompt_tokens"] = int(getattr(token_usage, "cached_prompt_tokens", 0))
        total_token_usage["completion_tokens"] = int(getattr(token_usage, "completion_tokens", 0))
        total_token_usage["successful_requests"] = int(getattr(token_usage, "successful_requests", 0))

        logger.info(f"Total tokens used: {total_token_usage['total_tokens']}")
        estimated_cost = 0.150 * total_token_usage["total_tokens"] / 1_000_000
        logger.info(f"Estimated cost: ${estimated_cost:.4f}")

    except AttributeError:
        logger.warning("Token usage data is not available.")
        raise
    except Exception as e:
        logger.error(f"Error while processing token usage: {e}")
        raise

    return total_token_usage, flow.status

