import { ref } from "vue";
import { defineStore } from "pinia";
import { Notify, Loading } from "quasar";
import { ai_testcaseApi } from "src/boot/axios";

export const useChatWithKbStore = defineStore("use_chat_with_kb_store", () => {
  const project_dropdown = ref([]);
  const selected_project = ref("");
  const user_prompt = ref("");
  const output_area = ref("");
  const response_loading = ref(false);

  async function get_projects() {
    try {
      response_loading.value=true;
      const response = await ai_testcaseApi.get(`/chat_knowledgebase/projects`);
      if (response.status === 200) {
        // Success
        const data = response.data;
        console.log(data);
        project_dropdown.value = data;
        response_loading.value=false;
      } else {
        handleError(response);
        response_loading.value=false;
      }
    } catch (err) {
      console.log(err);
      response_loading.value=false;
    }
  }

    async function get_response() {
    try {
      response_loading.value=true;
      const formdata = new FormData();
      formdata.append("query", user_prompt.value);
      formdata.append("collection_name", "SysVal_VTAF_AI_DataWarehouse");
      formdata.append("projects_to_search", selected_project.value)

      const response = await ai_testcaseApi.post(`/chat_knowledgebase/search_vector_db`, formdata);
      if (response.status === 200) {
        // Success
        const data = response.data.data;
        console.log(data);
        output_area.value = data;
        response_loading.value=false;
      } else {
        handleError(response);
        response_loading.value=false;
      }
    } catch (err) {
      console.log(err);
      response_loading.value=false;
    }
  }

  return {
    project_dropdown,
    selected_project,
    user_prompt,
    output_area,
    response_loading,
    get_projects,
    get_response
  };
});
