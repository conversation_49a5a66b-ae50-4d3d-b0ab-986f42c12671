#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖检测脚本 - 检测所有外部依赖问题
分析每个服务的导入依赖，识别缺失的模块
"""

import os
import sys
import importlib
from pathlib import Path
from typing import Dict, List, Set, Tuple

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from utils.logger import get_logger

logger = get_logger(__name__)

class DependencyChecker:
    """依赖检测器"""
    
    def __init__(self):
        self.missing_dependencies = {}
        self.available_dependencies = set()
        self.service_dependencies = {}
        
    def check_all_dependencies(self):
        """检查所有依赖"""
        logger.info("🔍 开始检测外部依赖...")
        logger.info("=" * 60)
        
        # 检查服务目录中的所有Python文件
        services_dir = Path("services")
        if not services_dir.exists():
            logger.error("services目录不存在")
            return
        
        python_files = list(services_dir.glob("*.py"))
        logger.info(f"找到 {len(python_files)} 个服务文件")
        
        for py_file in python_files:
            if py_file.name.startswith("__"):
                continue
            self._check_file_dependencies(py_file)
        
        # 检查主要模块
        main_files = ["main.py", "docker_entrypoint.py"]
        for main_file in main_files:
            if Path(main_file).exists():
                self._check_file_dependencies(Path(main_file))
        
        self._generate_report()
    
    def _check_file_dependencies(self, file_path: Path):
        """检查单个文件的依赖"""
        try:
            logger.info(f"\n📁 检查文件: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取import语句
            imports = self._extract_imports(content)
            
            file_missing = []
            file_available = []
            
            for import_stmt in imports:
                module_name = self._get_root_module(import_stmt)
                
                # 跳过标准库和本地模块
                if self._is_standard_library(module_name) or self._is_local_module(module_name):
                    continue
                
                # 检查模块是否可用
                if self._check_module_availability(module_name):
                    file_available.append(import_stmt)
                    self.available_dependencies.add(module_name)
                else:
                    file_missing.append(import_stmt)
                    if module_name not in self.missing_dependencies:
                        self.missing_dependencies[module_name] = []
                    self.missing_dependencies[module_name].append(str(file_path))
            
            self.service_dependencies[str(file_path)] = {
                'missing': file_missing,
                'available': file_available
            }
            
            if file_missing:
                logger.warning(f"  ❌ 缺失依赖: {file_missing}")
            if file_available:
                logger.info(f"  ✅ 可用依赖: {file_available}")
            
        except Exception as e:
            logger.error(f"检查文件 {file_path} 时出错: {e}")
    
    def _extract_imports(self, content: str) -> List[str]:
        """提取import语句"""
        imports = []
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # 跳过注释和空行
            if not line or line.startswith('#'):
                continue
            
            # 处理import语句
            if line.startswith('import '):
                module = line[7:].split()[0].split('.')[0]
                imports.append(module)
            elif line.startswith('from ') and ' import ' in line:
                parts = line.split(' import ')
                if len(parts) >= 2:
                    module = parts[0][5:].split('.')[0]
                    imports.append(module)
        
        return list(set(imports))  # 去重
    
    def _get_root_module(self, import_stmt: str) -> str:
        """获取根模块名"""
        return import_stmt.split('.')[0]
    
    def _is_standard_library(self, module_name: str) -> bool:
        """检查是否为标准库模块"""
        standard_libs = {
            'os', 'sys', 'json', 'time', 'datetime', 'pathlib', 'typing',
            'collections', 'itertools', 'functools', 'operator', 're',
            'math', 'random', 'uuid', 'hashlib', 'base64', 'urllib',
            'http', 'email', 'html', 'xml', 'csv', 'configparser',
            'logging', 'threading', 'multiprocessing', 'subprocess',
            'shutil', 'tempfile', 'glob', 'fnmatch', 'getpass'
        }
        return module_name in standard_libs
    
    def _is_local_module(self, module_name: str) -> bool:
        """检查是否为本地模块"""
        local_modules = {
            'services', 'utils', 'configs', 'tests'
        }
        return module_name in local_modules
    
    def _check_module_availability(self, module_name: str) -> bool:
        """检查模块是否可用"""
        try:
            importlib.import_module(module_name)
            return True
        except ImportError:
            # 特殊处理一些已知的模块
            if module_name == 'openai':
                # 检查是否在deepseek_client中使用但实际可用
                try:
                    import openai
                    return True
                except ImportError:
                    return False
            return False
    
    def _generate_report(self):
        """生成依赖报告"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 依赖检测报告")
        logger.info("=" * 60)
        
        # 缺失的依赖
        if self.missing_dependencies:
            logger.error(f"\n❌ 缺失的依赖 ({len(self.missing_dependencies)} 个):")
            for module, files in self.missing_dependencies.items():
                logger.error(f"  📦 {module}")
                for file in files:
                    logger.error(f"    - 使用于: {file}")
        else:
            logger.info("\n✅ 没有缺失的依赖")
        
        # 可用的依赖
        if self.available_dependencies:
            logger.info(f"\n✅ 可用的依赖 ({len(self.available_dependencies)} 个):")
            for module in sorted(self.available_dependencies):
                logger.info(f"  📦 {module}")
        
        # 生成requirements.txt建议
        self._generate_requirements_suggestions()
    
    def _generate_requirements_suggestions(self):
        """生成requirements.txt建议"""
        logger.info("\n" + "=" * 60)
        logger.info("📝 requirements.txt 建议")
        logger.info("=" * 60)
        
        # 已知的包版本映射
        package_versions = {
            'openai': 'openai>=1.0.0',
            'gspread': 'gspread>=5.0.0',
            'google': 'google-cloud-storage>=2.0.0',
            'vertexai': 'google-cloud-aiplatform>=1.0.0',
            'anthropic': 'anthropic>=0.3.0',
            'requests': 'requests>=2.25.0',
            'pandas': 'pandas>=1.3.0',
            'numpy': 'numpy>=1.21.0',
            'flask': 'Flask>=2.0.0',
            'fastapi': 'fastapi>=0.68.0',
            'uvicorn': 'uvicorn>=0.15.0',
            'pydantic': 'pydantic>=1.8.0',
            'sqlalchemy': 'SQLAlchemy>=1.4.0',
            'redis': 'redis>=4.0.0',
            'celery': 'celery>=5.0.0',
            'pytest': 'pytest>=6.0.0',
            'black': 'black>=21.0.0',
            'flake8': 'flake8>=3.9.0',
            'mypy': 'mypy>=0.910'
        }
        
        if self.missing_dependencies:
            logger.info("缺失的依赖包建议添加到requirements.txt:")
            for module in sorted(self.missing_dependencies.keys()):
                suggestion = package_versions.get(module, f"{module}>=1.0.0")
                logger.info(f"  {suggestion}")
        
        # 生成完整的requirements.txt内容
        self._write_requirements_file(package_versions)
    
    def _write_requirements_file(self, package_versions: Dict[str, str]):
        """写入requirements.txt文件"""
        try:
            requirements_file = Path("requirements_complete.txt")
            
            with open(requirements_file, 'w', encoding='utf-8') as f:
                f.write("# VTAF AI Multiprompt - 完整依赖列表\n")
                f.write("# 生成时间: " + str(Path(__file__).stat().st_mtime) + "\n\n")
                
                f.write("# 核心AI服务依赖\n")
                ai_deps = ['openai', 'vertexai', 'anthropic']
                for dep in ai_deps:
                    if dep in self.missing_dependencies:
                        f.write(f"{package_versions.get(dep, f'{dep}>=1.0.0')}\n")
                
                f.write("\n# Google Cloud 依赖\n")
                gcp_deps = ['google', 'gspread']
                for dep in gcp_deps:
                    if dep in self.missing_dependencies:
                        f.write(f"{package_versions.get(dep, f'{dep}>=1.0.0')}\n")
                
                f.write("\n# Web框架依赖\n")
                web_deps = ['flask', 'fastapi', 'uvicorn']
                for dep in web_deps:
                    if dep in self.missing_dependencies:
                        f.write(f"{package_versions.get(dep, f'{dep}>=1.0.0')}\n")
                
                f.write("\n# 数据处理依赖\n")
                data_deps = ['pandas', 'numpy']
                for dep in data_deps:
                    if dep in self.missing_dependencies:
                        f.write(f"{package_versions.get(dep, f'{dep}>=1.0.0')}\n")
                
                f.write("\n# 其他依赖\n")
                other_deps = [dep for dep in self.missing_dependencies.keys() 
                             if dep not in ai_deps + gcp_deps + web_deps + data_deps]
                for dep in other_deps:
                    f.write(f"{package_versions.get(dep, f'{dep}>=1.0.0')}\n")
            
            logger.info(f"\n📄 完整依赖列表已写入: {requirements_file}")
            
        except Exception as e:
            logger.error(f"写入requirements文件时出错: {e}")

def main():
    """主函数"""
    logger.info("🔍 VTAF AI Multiprompt 依赖检测工具")
    logger.info("=" * 60)
    
    checker = DependencyChecker()
    checker.check_all_dependencies()
    
    logger.info("\n🎉 依赖检测完成!")

if __name__ == '__main__':
    main()
