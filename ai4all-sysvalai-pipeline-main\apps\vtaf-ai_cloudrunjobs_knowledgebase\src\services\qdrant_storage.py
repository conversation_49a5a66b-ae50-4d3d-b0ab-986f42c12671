import os
import time

import google.auth
from dotenv import load_dotenv
from google.auth import impersonated_credentials
from google.auth.transport.requests import Request
from langchain_core.documents import Document as LCDocument
from langchain_google_vertexai import VertexAIEmbeddings
from langchain_qdrant import QdrantVectorStore
from qdrant_client import QdrantClient

from configs.env import settings  # Assuming centralized env config loader
from utils.logger import get_logger

logger = get_logger(__name__)


class QdrantProcessor:
    def __init__(
            self,
            QDRANT_JWT: str,
            project_detail: dict
    ):
        load_dotenv()  # Load .env variables (optional here if already done)

        required_env_vars = [
            "PROJECT_ID", "QDRANT_CLIENT_URL", "QDRANT_CLIENT_PORT", "QDRANT_IAP_CLIENT_ID",
            "EMBEDDING_MODEL_NAME", "EMBEDDING_LOCATION", "COLLECTION_NAME", "SERVICE_ACCOUNT_EMAIL"
        ]
        missing_vars = [var for var in required_env_vars if not os.getenv(var)]
        if missing_vars:
            raise EnvironmentError(f"Missing required env vars: {', '.join(missing_vars)}")
        self.project_detail = project_detail
        self.PROJECT_PROFILE_UUID = settings.PROJECT_PROFILE_UUID
        self.CLOUD_RUN_EXECUTION = settings.CLOUD_RUN_EXECUTION
        self.VTAF_AI_PROJECT_ID = settings.VTAF_AI_PROJECT_ID
        self.INPUT_TYPE = settings.INPUT_TYPE
        self.VTAF_AI_PROJECT_VAR_ID = settings.VTAF_AI_PROJECT_VAR_ID
        self.VTAF_AI_EUF_FEA_UID = settings.VTAF_AI_EUF_FEA_UID
        self.PROJECT_ID = settings.PROJECT_ID
        self.QDRANT_CLIENT_URL = settings.QDRANT_CLIENT_URL
        self.QDRANT_CLIENT_PORT = settings.QDRANT_CLIENT_PORT
        self.QDRANT_IAP_CLIENT_ID = settings.QDRANT_IAP_CLIENT_ID
        self.EMBEDDING_MODEL_NAME = settings.EMBEDDING_MODEL_NAME
        self.EMBEDDING_LOCATION = settings.EMBEDDING_LOCATION
        self.COLLECTION_NAME = settings.COLLECTION_NAME
        self.QDRANT_JWT = QDRANT_JWT
        self.EMBEDDING_PARALLELISM = settings.EMBEDDING_PARALLELISM or 1
        self.SERVICE_ACCOUNT_EMAIL = settings.SERVICE_ACCOUNT_EMAIL
        self.cached_token = None
        self.token_expiration_time = 0

        self.service_account_credentials = self.get_impersonated_credentials(self.SERVICE_ACCOUNT_EMAIL)
        self.client = QdrantClient(
            url=self.QDRANT_CLIENT_URL,
            port=int(self.QDRANT_CLIENT_PORT),
            api_key=self.QDRANT_JWT,
            auth_token_provider=self.get_qdrant_token,
            timeout=30,
        )
        self.embedding = VertexAIEmbeddings(
            model_name=self.EMBEDDING_MODEL_NAME,
            project=self.PROJECT_ID,
            location=self.EMBEDDING_LOCATION,
            request_parallelism=self.EMBEDDING_PARALLELISM,
            credentials=self.service_account_credentials,
        )
        self.vector_store = QdrantVectorStore(
            client=self.client,
            collection_name=self.COLLECTION_NAME,
            embedding=self.embedding
        )


    def get_impersonated_credentials(self, target_principal: str, lifetime: int = 300):
        try:
            source_credentials, _ = google.auth.default(scopes=["https://www.googleapis.com/auth/cloud-platform"])
            source_credentials.refresh(Request())
            target_credentials = impersonated_credentials.Credentials(
                source_credentials=source_credentials,
                target_principal=target_principal,
                target_scopes=["https://www.googleapis.com/auth/cloud-platform"],
                lifetime=lifetime,
            )
            return target_credentials
        except Exception as e:
            logger.error(f"Error getting impersonated credentials: {e}")
            raise

    def get_impersonated_id_token(self, target_credentials, target_audience: str) -> str:
        try:
            credentials = impersonated_credentials.IDTokenCredentials(
                target_credentials=target_credentials,
                target_audience=target_audience,
                include_email=True,
            )
            credentials.refresh(Request())
            return credentials.token
        except Exception as e:
            logger.error(f"Error fetching ID token: {e}")
            raise

    def get_qdrant_token(self) -> str:
        if self.cached_token is None or time.time() >= self.token_expiration_time:
            try:
                token = self.get_impersonated_id_token(
                    self.service_account_credentials,
                    self.QDRANT_IAP_CLIENT_ID,
                )
                self.cached_token = token
                self.token_expiration_time = time.time() + 3600
                logger.info("Qdrant ID Token retrieved and cached.")
            except Exception as e:
                logger.error(f"Error getting Qdrant token: {e}")
                raise
        return self.cached_token

    def add_documents_in_batches(self, data):
        try:
            document = LCDocument(
                page_content=data,
                metadata={
                    "project_uuid": self.VTAF_AI_PROJECT_ID,
                    "project": self.project_detail.get("project"),
                    "variant_uuid": self.VTAF_AI_PROJECT_VAR_ID,
                    "variant": self.project_detail.get("variant"),
                    "euf_feature_uuid": self.VTAF_AI_EUF_FEA_UID,
                    "euf_feature": self.project_detail.get("euf_feature"),
                    "type": self.INPUT_TYPE,
                    "profile_uuid": self.PROJECT_PROFILE_UUID,
                    "job_id": self.CLOUD_RUN_EXECUTION,
                }
            )
            self.vector_store.add_documents([document])
            logger.info("Successfully added documents to Qdrant.")
        except Exception as e:
            logger.error(f"Error adding documents in batches: {e}")
            raise
