Data_Extraction_Task:
  description: >
    The Extraction task involves reviewing raw input sources—such as documents, datasets, or transcripts—and accurately identifying and extracting relevant data points.
    This task requires keen attention to detail and the ability to distinguish between critical and non-essential information.
    The extracted data should be structured clearly to support downstream tasks like summarization, analysis, or reporting.
    You need to extract on {text_input}
  expected_output: >
    The expected output is a clean, well-organized collection of key data points or information segments relevant to the task objective.
    This may include structured fields (e.g., names, dates, figures, requirement IDs), categorized content, or highlighted excerpts depending on the input format.
    The data should be output in a consistent and accessible structure, such as JSON, tables, or tagged markdown, to ensure seamless integration with subsequent processes.

Summary_Writer_Task:
  description: >
    The Summary Task focuses on transforming raw or extracted information into a concise, logically organized summary that communicates the most essential takeaways.
    The goal is not just to shorten content, but to enhance its clarity and impact by identifying the core insights and presenting them in a reader-friendly way.
    
  expected_output: >
    The output should be a well-structured summary that emphasizes key findings, decisions, themes, or action items, depending on the context.
    It should avoid repetition or low-value details, and instead highlight what the audience truly needs to know.
    The summary should be written in clear, accessible language and formatted cleanly—typically in markdown or another lightweight structure suitable for collaboration or review.