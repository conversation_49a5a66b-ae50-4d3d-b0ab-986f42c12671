# Use the official Python image from the Docker Hub
FROM python:3.11.9

# Set the working directory in the container
WORKDIR /app

# Copy the current directory contents into the container at /app
COPY . /app/

# Install the dependencies in requirements.txt
RUN pip install -r requirements.txt

# Expose port 8080 (the default port <PERSON> uses)
# EXPOSE 8080

# Set environment variable for FastAPI to run on the correct host and port
# ENV HOST 0.0.0.0
# ENV PORT 8080

# Run FastAPI using uvicorn (ASGI server)
# CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8080"]
CMD exec uvicorn app.main:app --reload --port 8082 --host 0.0.0.0 --workers 1
