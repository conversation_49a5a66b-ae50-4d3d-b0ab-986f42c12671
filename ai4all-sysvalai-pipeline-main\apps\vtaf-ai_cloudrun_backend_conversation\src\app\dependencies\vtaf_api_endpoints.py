import os
import requests
import google.auth
from google.auth.transport.requests import Request
from google.oauth2 import service_account, id_token
from configs.env import settings

iap_client_id = settings.IAP_CLIENT_ID
vtaf_api_url = settings.VTAF_API_URL

GOOGLE_CLOUD_SCOPES = [
    "https://www.googleapis.com/auth/cloud-platform"
]

async def create_jwt_token():
    # credentials, _ = google.auth.default(scopes=GOOGLE_CLOUD_SCOPES)

    # credentials = google.auth.jwt.Credentials.from_signing_credentials(
    #     credentials,
    #     audience=iap_client_id,
    # )

    # auth_request = google_requests.Request()
    # credentials.refresh(auth_request)
    # access_token = credentials.token
    # return access_token
    target_audience = iap_client_id
    token = id_token.fetch_id_token(Request(), target_audience)
    return token

async def vtaf_api_health_check(access_token):
    # Get the identity token
    headers = {"Authorization": f"Bearer {access_token}"}

    response = requests.get(
        f"{vtaf_api_url}/vtaf-api/v1/vectordb/health",
        headers=headers,
        timeout=60,
    )

    print("vtaf api : ", response.status_code)
    print("vtaf api : ", response.text)
    return response

async def search_vector(payload, access_token):
    # Get the identity token
    headers = {"Authorization": f"Bearer {access_token}"}

    response = requests.post(
        f"{vtaf_api_url}/vtaf-api/v1/vectordb/documents/search",
        headers=headers,
        json=payload,
        timeout=60,
    )
    return response