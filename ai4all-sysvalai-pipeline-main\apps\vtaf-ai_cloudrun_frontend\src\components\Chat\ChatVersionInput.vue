<template>
  <div class="q-pa-md" style="max-width: 300px">
    <q-select
      borderless
      v-model="gemini_version"
      use-input
      :options="options"
      label="Gemini Version"
    />
  </div>
</template>

<script setup>
import { storeToRefs } from "pinia";
import { useChatStore } from "src/stores/chat/chat_store";

const chat_store = useChatStore();
const { gemini_version } = storeToRefs(chat_store);
const options = ["gemini-1.5-flash", "gemini-1.5-pro"];
</script>
