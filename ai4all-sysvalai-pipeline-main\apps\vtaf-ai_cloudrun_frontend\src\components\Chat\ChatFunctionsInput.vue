<template>
  <!-- Toggle Panel Button -->
  <div class="row" style="margin-top: 1rem">
    <div class="col" style="text-align: left">
      <q-btn-toggle
        v-model="chat_type"
        style="margin-bottom: 1rem"
        no-caps
        rounded
        unelevated
        toggle-color="blue-10"
        color="white"
        text-color="blue-10"
        :options="[
          { label: 'Single Chat', value: 'single' },
          { label: 'Multi Chat', value: 'multi' },
        ]"
      />
      <q-btn
        v-if="chat_type === 'multi' && chat_history.length > 0"
        color="blue-10"
        icon="close"
        label="Clear"
        style="margin-bottom: 1rem; margin-left: 3rem"
        @click="clearConversation"
      />
    </div>
    <div class="col" style="text-align: end">
      <q-btn
        flat
        fab
        icon="settings"
        color="blue-10"
        @click="toggleSidePanel"
        align="right"
      />
    </div>
  </div>
  <div class="chat-page">
    <div class="chat-main">
      <div
        v-if="!prompt_response"
        class="banner q-pa-lg row justify-center items-center"
        ref="scrollTarget"
      >
        <!-- Image on the left side with 3D rotation animation -->
        <img
          src="src/assets/home/<USER>"
          alt="Help Image"
          class="banner-image q-mr-md"
          style="height: 50px; width: 50px"
        />

        <!-- If prompt_loading is true, show spinner -->
        <div v-if="prompt_loading" class="q-ml-md">
          <q-spinner-dots color="primary" size="40px" />
        </div>

        <!-- Text that displays letter by letter -->
        <h1 v-else class="banner-text text-h5 text-bold">
          {{ displayedText }}
        </h1>
      </div>
      <div class="wrapped-text">
        <template v-for="(chat, index) in chat_history" :key="index">
          <div class="prompt-container">
            <div v-html="chat.prompt" class="bg-grey-3 user_prompt"></div>
            <q-avatar color="primary" class="user_avatar" text-color="white"
              >U</q-avatar
            >
          </div>
          <q-badge outline color="blue-10" label="VTAF.AI" />
          <div v-html="chat.formattedResponses" style="max-width: 100%" />
        </template>
        <div v-if="prompt_banner_loading" class="q-ml-md">
          <q-spinner-dots color="primary" size="40px" />
        </div>
      </div>
    </div>
    <div class="input_row">
      <q-card style="width: 100%">
        <div v-if="file_name && chat_type === 'single'" class="file-name">
          Selected File: {{ file_name }}
        </div>
        <q-card-section style="display: flex; align-items: center">
          <q-btn
            v-if="chat_type === 'single'"
            flat
            round
            icon="attach_file"
            @click="selectFile"
            color="blue-10"
          />
          <q-input
            v-model="prompt"
            type="textarea"
            placeholder="Enter text or file name"
            filled
            @keydown.enter="handleEnter"
            style="flex: 1; margin: 0 8px; overflow-y: auto; max-height: 200px"
            autogrow
          />
          <q-btn flat round icon="north" color="blue-10" @click="handleClick" />
        </q-card-section>
      </q-card>
    </div>
    <!-- Side Panel -->
    <div class="chat-side-panel" v-if="showSidePanel">
      <q-card flat bordered>
        <q-card-section>
          <div class="panel-header">
            <span>Chat Configurations</span>
            <!-- <q-btn flat round dense icon="close" @click="toggleSidePanel" /> -->
          </div>
        </q-card-section>
        <q-card-section>
          <!-- Dropdown Example -->
          <q-select
            v-model="gemini_version"
            :options="options"
            label="Gemini Model"
            style="margin-bottom: 2rem"
          />
          <div class="region-dropdown">
            <label for="region-input">Selected Region:</label>
            <q-input
              v-model="selectedRegion"
              outlined
              dense
              class="region-input"
              readonly
            />

            <q-btn
              label="Select Region"
              color="primary"
              @click="toggleDropdown"
            />

            <q-menu v-model="dropdownOpen" cover persistent>
              <div class="dropdown-content">
                <q-list bordered class="scrollable-dropdown">
                  <q-expansion-item
                    v-for="(regionGroup, index) in regions"
                    :key="index"
                    :label="regionGroup.label"
                    dense
                  >
                    <q-item
                      v-for="region in regionGroup.regions"
                      :key="region.value"
                      clickable
                      @click="selectRegion(region)"
                    >
                      <q-item-section>{{ region.label }}</q-item-section>
                    </q-item>
                  </q-expansion-item>
                </q-list>
              </div>
            </q-menu>
          </div>

          <div class="slider-wrapper" style="margin-top: 2rem">
            <!-- Temperature Slider -->
            <div class="slider-item" style="margin-bottom: 1rem">
              <label class="slider-label">
                Temperature
                <q-tooltip>
                  Adjust the randomness of output: 0 for deterministic, 2 for
                  creative.
                </q-tooltip>
              </label>
              <q-slider
                v-model="temperature"
                :min="0"
                :max="2"
                step="0.1"
                markers
                dense
                label
              />
              <q-input
                v-model.number="temperature"
                :value="temperature"
                outlined
                dense
                class="slider-input"
                type="number"
                :min="0"
                :max="2"
                step="0.1"
              />
            </div>

            <!-- Max Output Tokens Slider -->
            <div class="slider-item">
              <label class="slider-label">
                Output Token Limit
                <q-tooltip>
                  Limit the number of tokens generated in output (1-8192).
                </q-tooltip>
              </label>
              <q-slider
                v-model="output_tokens"
                :min="1"
                :max="8192"
                step="1"
                markers
                dense
                label
              />
              <q-input
                v-model.number="output_tokens"
                :value="output_tokens"
                outlined
                dense
                class="slider-input"
                type="number"
                :min="1"
                :max="8192"
              />
            </div>
          </div>

          <!-- Add more configuration inputs here -->
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { storeToRefs } from "pinia";
import { useChatStore } from "src/stores/chat/chat_store";
import { ref, watch, onMounted, nextTick } from "vue";
import { QSpinnerDots } from "quasar";
import hljs from "highlight.js";
import "highlight.js/styles/default.css";
import MarkdownIt from "markdown-it";

const md = new MarkdownIt({
  html: true, // Allow HTML inside Markdown
  linkify: true, // Convert links to clickable
  typographer: true, // Smart punctuation
});

const chat_store = useChatStore();
const {
  uploaded_file,
  backup_prompt,
  prompt,
  prompt_loading,
  prompt_banner_loading,
  prompt_response,
  file_name,
  gemini_version,
  temperature,
  output_tokens,
  selectedRegion,
  chat_type,
  chat_hisory_clear,
  regions,
  chat_history,
} = storeToRefs(chat_store);
const { getPromptResponse, getMultiChatResponse } = chat_store;
const dropdownOpen = ref(false);
// Toggles the dropdown
const toggleDropdown = () => {
  dropdownOpen.value = dropdownOpen.value;
};
// Handles region selection
const selectRegion = (region) => {
  selectedRegion.value = region.value;
  dropdownOpen.value = false;
};
// Region data

// Side panel state
const showSidePanel = ref(false);

// Dropdown options
const options = ["gemini-2.0-flash-001", "gemini-2.0-flash-lite-001"];

// Toggle the side panel
const toggleSidePanel = () => {
  showSidePanel.value = !showSidePanel.value;
};

const scrollTarget = ref(null);
const displayedText = ref(""); // Holds the animated text

const fullText = "What can I help with?"; // Complete text to display
const typingSpeed = 100; // Speed of typing effect in milliseconds

// Scroll to banner when the component is mounted
onMounted(() => {
  startTypingAnimation(); // Start typing animation on mount
  highlightCode();
});

// Function to simulate typing effect
const startTypingAnimation = () => {
  let index = 0; // Track the current index of the text

  const typeLetter = () => {
    if (index < fullText.length) {
      displayedText.value += fullText.charAt(index); // Add next letter to displayed text
      index++;
      setTimeout(typeLetter, typingSpeed); // Call the function again after typing speed delay
    }
  };

  typeLetter(); // Start the typing effect
};

// Function to highlight code blocks
const highlightCode = async () => {
  await nextTick(); // Wait for DOM updates
  const codeBlocks = document.querySelectorAll("pre code");
  codeBlocks.forEach((block) => {
    hljs.highlightElement(block);
  });
  // await nextTick();
  // hljs.highlightElement(prompt_response.value);
};

watch(
  prompt_response,
  () => {
    if (prompt_response.value) {
      addChatToHistory(
        backup_prompt.value.replace(/\n/g, "<br>"),
        prompt_response.value
      );
    }
  },
  { immediate: true }
);

function addChatToHistory(prompt, response) {
  const formattedResponses = md.render(response);
  chat_history.value.push({ prompt, formattedResponses });
  highlightCode();
}

function copyToClipboard(content) {
  navigator.clipboard.writeText(content).then(() => {
    console.log("Copied to clipboard:", content);
  });
}

const selectFile = () => {
  const input = document.createElement("input");
  input.type = "file";
  input.onchange = (event) => {
    uploaded_file.value = event.target.files[0];
    if (uploaded_file) {
      file_name.value = uploaded_file.value.name; // Display the file name in the input box
    }
  };
  input.click(); // Programmatically click the input to open the file dialog
};

const handleClick = () => {
  if (chat_type.value == "single") {
    prompt_loading.value = true;
    prompt_response.value = "";
    prompt_banner_loading.value = true;
    backup_prompt.value = prompt.value;
    prompt.value = "";
    chat_history.value = [];
    getPromptResponse();
  } else {
    prompt_loading.value = true;
    prompt_response.value = "";
    prompt_banner_loading.value = true;
    backup_prompt.value = prompt.value;
    prompt.value = "";
    getMultiChatResponse();
  }
};

const handleEnter = (event) => {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    handleClick();
  }
};

// Watch for changes on chat_type
watch(chat_type, (newValue) => {
  // Clear messages whenever chat_type changes
  chat_hisory_clear.value = true;
  chat_history.value = [];
  console.log(`Switched to ${newValue} mode, messages cleared.`);
});
onMounted(() => {
  chat_history.value = [];
  highlightCode();
});

const clearConversation = () => {
  chat_hisory_clear.value = true;
  chat_history.value = [];
};
</script>

<style scoped>
.banner {
  border-radius: 12px;

  display: flex;
  justify-content: center; /* Horizontally centers the content */
  align-items: center; /* Vertically centers the content */
  min-height: 150px; /* Adjust the height if needed */
}

/* Styling for the image */
.banner-image {
  object-fit: cover;
  border-radius: 50%;
  animation: rotateImage 2s ease-in-out forwards; /* Apply 3D rotation animation */
  transform-origin: center;
}
.wrapped-text {
  margin: 0 auto;
  font-size: 1rem;
  max-width: 60%;
  padding-left: 2rem;
  padding-right: 2rem;
  word-wrap: break-word;
  overflow: auto;
  padding-bottom: 150px;
}

.user_prompt {
  max-width: 50%;
  padding: 20px;
  margin-bottom: 2rem;
  border-radius: 1rem;
}
.prompt-container {
  display: flex;
  justify-content: flex-end;
}

.input_row {
  display: flex;
  justify-content: center;
  margin: auto;
  margin-top: 1rem;
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 1000; /* Make sure it's above other elements */
}
.file-name {
  margin-left: 2rem;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-weight: bold;
  color: #4caf50;
}
.chat-page {
  display: flex;
  flex-direction: row;
  height: 100%;
}

.chat-main {
  flex: 1;
  padding: 16px;
}

.chat-side-panel {
  position: fixed;
  right: 0;
  width: 300px;
  height: 100%;
  background: #f9f9f9;
  border-left: 1px solid #ddd;
  display: flex;
  flex-direction: column;
  padding: 16px;
  z-index: 5; /* Behind the open button */
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  margin-bottom: 16px;
}

.toggle-panel-btn {
  position: fixed;
  bottom: 16px;
  right: 16px;
}

.region-dropdown {
  position: relative;
  max-width: 300px;
}

.region-input {
  margin-bottom: 12px;
}

.dropdown-content {
  max-height: 200px;
  overflow-y: auto;
  padding: 8px;
}

.scrollable-dropdown {
  max-height: 300px;
  overflow-y: auto;
}
/*center this class vertically*/
.user_avatar {
  margin-left: 10px;
  margin-top: 1rem;
}
</style>
