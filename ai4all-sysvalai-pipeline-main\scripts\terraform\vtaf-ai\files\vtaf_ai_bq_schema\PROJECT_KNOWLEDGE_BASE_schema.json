[{"mode": "NULLABLE", "name": "bg", "type": "STRING", "description": "bg"}, {"mode": "NULLABLE", "name": "layout_definition", "type": "STRING", "description": "layout_definition"}, {"mode": "NULLABLE", "name": "layout_header", "type": "STRING", "description": "layout_header"}, {"mode": "NULLABLE", "name": "doors_server_name", "type": "STRING", "description": "doors_server_name"}, {"mode": "NULLABLE", "name": "doors_module_path", "type": "STRING", "description": "doors_module_path"}, {"mode": "NULLABLE", "name": "doors_module_name", "type": "STRING", "description": "doors_module_name"}, {"mode": "NULLABLE", "name": "project_variant", "type": "STRING", "description": "project_variant"}, {"mode": "NULLABLE", "name": "doors_module_id", "type": "STRING", "description": "doors_module_id"}, {"mode": "NULLABLE", "name": "project_name", "type": "STRING", "description": "project_name"}, {"mode": "NULLABLE", "name": "project_id", "type": "STRING", "description": "project_id"}]