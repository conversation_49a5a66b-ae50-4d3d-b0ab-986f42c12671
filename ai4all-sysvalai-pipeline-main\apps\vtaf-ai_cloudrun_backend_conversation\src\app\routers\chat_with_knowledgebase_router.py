import traceback
from fastapi import APIRouter, HTTPException, Request, Form, Depends
import os
from configs.env import settings
from utilities import get_firestore_client, add_session_fs, search_vector_db, keep_only_question, get_bigquery_client, get_session_by_email_and_session_id
from dependencies.http_client_session import get_ai4all_client
from dependencies.ai4all_auth import get_id_token
from dependencies.ai4all_endpoints import create_session_ai4all, get_session_details_ai4all, delete_ai4all_session, ai4all_health_check
from dependencies.vtaf_api_endpoints import vtaf_api_health_check, create_jwt_token
import uuid

jwt_access_token = None

router = APIRouter()

firestore_database_id = settings.FIRESTORE_DATABASE_ID
firestore_session_id = settings.FIRESTORE_SESSION_COLLECTION
services_project_id = settings.SERVICES_PROJECT_ID
services_dataset_id = settings.SERVICES_DATASET_ID
qdrant_collection_name = settings.COLLECTION_NAME

@router.get("/jwt_token")
async def create_jwt():
    global jwt_access_token
    jwt_access_token = await create_jwt_token()
    print(jwt_access_token)
    return {
        'data': 'success'
    }
    
@router.get("/projects")
def get_projects():
    bq_client = get_bigquery_client()
    query = f"""
                SELECT variant_uuid, CONCAT(t2.project, '_', t1.variant) AS final_projects FROM `{services_project_id}.{services_dataset_id}.PROJECT_VARIANT_TABLE` as t1
                    INNER JOIN `{services_project_id}.{services_dataset_id}.PROJECT_TABLE` as t2 ON t1.project_uuid = t2.project_uuid
                ORDER BY final_projects 
            """
    # Run query
    query_job = bq_client.query(query)
    results = query_job.result()

    variant_dict = {row.final_projects: row.variant_uuid for row in results}
    return variant_dict
    
@router.post("/get_chats")
def chat_with_knowledgebase_history(email: str = Form(...)):
    try:
        # update_result = end_sessions_by_email(email)
        client = get_firestore_client(firestore_database_id)
        doc_ref = client.collection(firestore_session_id).document(email)
        doc = doc_ref.get()
        if not doc.exists:
            return {"sessions": []}

        data = doc.to_dict()
        sessions = data.get("sessions", [])

        return {"sessions": sessions}
    except Exception as e:
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        raise HTTPException(status_code=500, detail=error_message)
    
@router.post("/create_session")
async def create_session(request:Request, id_token=Depends(get_id_token), ai4allclient=Depends(get_ai4all_client), email: str = Form(...), title: str = Form(...), variant_id: str = Form(...)):
    await vtaf_api_health_check(access_token=jwt_access_token)
    await ai4all_health_check(id_token=id_token, ai4allclient=ai4allclient)
    try:
        title_uuid = str(uuid.uuid4()).split("-")[1]
        title_to_update = title+"_"+title_uuid
        print(variant_id, title_to_update)
        created_session = await create_session_ai4all(id_token=id_token, ai4allclient=ai4allclient)
        created_session_id = created_session['session_id']
        result = await add_session_fs(email=email, session_id=created_session_id, title=title_to_update, variant_id=variant_id, project=title)
        return {"session_id": created_session_id}
    except Exception as e:
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        raise HTTPException(status_code=500, detail=error_message)
    
@router.post("/conversation")
async def conversation(request:Request, id_token=Depends(get_id_token), ai4allclient=Depends(get_ai4all_client),new_chat: bool = Form(...), email:str = Form(...), session_id:str = Form(...), query:str = Form(...), projects_to_search:str = Form(...)):
    global jwt_access_token
    jwt_access_token = await create_jwt_token()
    await vtaf_api_health_check(access_token=jwt_access_token)
    await ai4all_health_check(id_token=id_token, ai4allclient=ai4allclient)
    try:
        conversation_result = await search_vector_db(id_token=id_token, ai4allclient=ai4allclient, session_id=session_id, query=query, collection_name=qdrant_collection_name, projects_to_search=projects_to_search, jwt_access_token=jwt_access_token)
        title_to_update_session = conversation_result['metadata']['conversation_title']
        # title_updated_result = update_session_fs_title(email=email, session_id=session_id, title=title_to_update_session)
        return {"status": "success", 'session_id':session_id, 'title':title_to_update_session, 'ai_response':conversation_result['parts'][0]['data']}
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        # Raise HTTPException with 500 status and detailed error
        return {
            "status": "error",
            "detail": str(e)
        }
    
@router.post("/get_session")
async def get_session(request:Request, id_token=Depends(get_id_token), ai4allclient=Depends(get_ai4all_client),email : str = Form(...), session_id : str = Form(...)):
    try:
        await ai4all_health_check(id_token=id_token, ai4allclient=ai4allclient)
        data = await get_session_details_ai4all(id_token=id_token, ai4allclient=ai4allclient, session_id=session_id)
        firestore_session_details = await get_session_by_email_and_session_id(email=email, session_id=session_id)
        if firestore_session_details:
            variant_id = firestore_session_details['variant_id']
            project = firestore_session_details['project']
        if 'detail' in data:
            print(data['detail'])
            return {'status': 'error', "detail": data['detail']}
        print("--------------------------")
        for item in data['messages']:
            item.pop("metadata", None)
        cleaned_data = keep_only_question(data['messages'])
        print("cleaned: ", cleaned_data)
        return {"status": "success", "data": cleaned_data, "variant_id": variant_id, "project": project}
    except Exception as e:
        # Create a detailed error with traceback
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        print(str(e))
        return {
            "status": "error",
            "detail": str(e)
        }
    
@router.post("/delete_session")
async def delete_session(request:Request, id_token=Depends(get_id_token), ai4allclient=Depends(get_ai4all_client), email : str = Form(...), session_id : str = Form(...)):
    try:
        delete_session_status = await delete_ai4all_session(id_token=id_token, ai4allclient=ai4allclient, session_id=session_id)
        client = get_firestore_client(firestore_database_id)
        doc_ref = client.collection(firestore_session_id).document(email)
        doc = doc_ref.get()

        if not doc.exists:
            return {"error": f"No document found for email '{email}'"}

        data = doc.to_dict()
        sessions = data.get("sessions", [])

        # Filter out the session with the given session_id
        updated_sessions = [s for s in sessions if s.get("session_id") != session_id]

        if len(sessions) == len(updated_sessions):
            return {"error": f"Session '{session_id}' not found for email '{email}'"}

        # Update the document with the new sessions list
        doc_ref.update({
            "sessions": updated_sessions
        })

        return {"message": f"Chat deleted"}

    except Exception as e:
        tb = traceback.format_exc()
        error_message = f"Exception occurred: {str(e)}\nTraceback:\n{tb}"
        raise HTTPException(status_code=500, detail=error_message)
