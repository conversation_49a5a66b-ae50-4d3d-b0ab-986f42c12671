# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'AI_114.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets

UI_Style = '''
/* 全局样式 */
QWidget {
    font-family: "Microsoft YaHei", "Segoe UI", sans-serif; /* 统一字体 */
    font-size: 14px;
    color: #333333; /* 主要文字颜色 */
    background-color: #F5F5F5; /* 浅灰色背景 */
}

/* 按钮样式 */
QPushButton {
    background-color: #FFFFFF; /* 白色背景 */
    border: 1px solid #BDBDBD; /* 浅灰色边框 */
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 14px; /* 减小字体大小 */
    color: #333333;
    outline: none; /* 移除焦点边框 */
}
#pushButton { /* "Submit" 按钮 */
    font-size: 12px; /* 减小字体大小 */
}

#pushButton_3 { /* "Set System Prompt" 按钮 */
    font-size: 13px; /* 减小字体大小 */
}

#pushButton_14 { /* "Send TS\nTraining_Dataset" 按钮 */
    font-size: 12px; /* 减小字体大小 */
}

QPushButton:hover {
    background-color: #EEEEEE; /* 鼠标悬停时的浅灰色背景 */
}

QPushButton:pressed {
    background-color: #E0E0E0; /* 鼠标按下时的更浅灰色背景 */
}

QPushButton:disabled {
    background-color: #F0F0F0;
    color: #9E9E9E;
    border-color: #E0E0E0;
}

/* 文本框样式 */
QLineEdit, QTextEdit, QTextBrowser {
    background-color: #FFFFFF;
    border: 1px solid #BDBDBD;
    border-radius: 4px;
    padding: 6px;
    color: #333333;
}

QLineEdit:read-only {
    background-color: #EEEEEE;
    color: #757575;
}

/* 下拉框样式 */
QComboBox {
    background-color: #FFFFFF;
    border: 1px solid #BDBDBD;
    border-radius: 4px;
    padding: 6px;
    color: #333333;
}

QComboBox::drop-down {
    border: 0px;
}

QComboBox::down-arrow {
    image: url(:/png_ico/down_arrow.png); /*替换为你的下拉箭头图片 */
    width: 12px;
    height: 12px;
    margin-right: 8px;
}

QComboBox QAbstractItemView {
    border: 1px solid #BDBDBD;
    background-color: #FFFFFF;
    outline: none;
}

QComboBox QAbstractItemView::item {
    padding: 6px;
    color: #333333;
}

QComboBox QAbstractItemView::item:selected {
    background-color: #BBDEFB; /* 选中时的浅蓝色背景 */
    color: #333333;
}

/* 标签样式 */
QLabel {
    color: #333333;
    font-weight: normal;
}

/* 分组框样式 */
QGroupBox {
    border: 1px solid #BDBDBD;
    border-radius: 4px;
    margin-top: 8px;
    padding: 0px;
    font-weight: bold;
    color: #333333;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    left: 10px;
    top: 0px;
    padding: 0 3px;
    background-color: #F5F5F5; /* 与全局背景色一致 */
}

/* 文本浏览器样式 */
QTextBrowser {
    background-color: #FFFFFF;
    border: 1px solid #BDBDBD;
    border-radius: 4px;
    padding: 6px;
    color: #333333;
}

/* 其他 */
QScrollBar:vertical {
    border: none;
    background: #FAFAFA;
    width: 10px;
    margin: 0px 0 0px 0;
}

QScrollBar::handle:vertical {
    background: #BDBDBD;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:vertical {
    border: none;
    background: none;
    height: 0px;
    subcontrol-position: bottom;
    subcontrol-origin: margin;
}

QScrollBar::sub-line:vertical {
    border: none;
    background: none;
    height: 0px;
    subcontrol-position: top;
    subcontrol-origin: margin;
}'''


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(1250, 900)
        icon = QtGui.QIcon()
        icon.addPixmap(QtGui.QPixmap(":/png_ico/logo.png"), QtGui.QIcon.Normal, QtGui.QIcon.Off)
        Form.setWindowIcon(icon)
        self.gridLayout_2 = QtWidgets.QGridLayout(Form)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout()
        self.verticalLayout_5.setContentsMargins(0, 0, -1, -1)
        self.verticalLayout_5.setSpacing(0)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.widget_4 = QtWidgets.QWidget(Form)
        self.widget_4.setObjectName("widget_4")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.widget_4)
        self.verticalLayout_3.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.pushButton_4 = QtWidgets.QPushButton(self.widget_4)
        self.pushButton_4.setMaximumSize(QtCore.QSize(16777215, 30))
        self.pushButton_4.setWhatsThis("")
        self.pushButton_4.setObjectName("pushButton_4")
        self.horizontalLayout_2.addWidget(self.pushButton_4)
        self.lineEdit_2 = QtWidgets.QLineEdit(self.widget_4)
        self.lineEdit_2.setMaximumSize(QtCore.QSize(16777215, 30))
        self.lineEdit_2.setReadOnly(True)
        self.lineEdit_2.setObjectName("lineEdit_2")
        self.horizontalLayout_2.addWidget(self.lineEdit_2)
        self.verticalLayout_3.addLayout(self.horizontalLayout_2)
        self.textEdit = QtWidgets.QTextEdit(self.widget_4)
        self.textEdit.setObjectName("textEdit")
        self.verticalLayout_3.addWidget(self.textEdit)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.pushButton_7 = QtWidgets.QPushButton(self.widget_4)
        self.pushButton_7.setMinimumSize(QtCore.QSize(0, 40))
        self.pushButton_7.setObjectName("pushButton_7")
        self.horizontalLayout_3.addWidget(self.pushButton_7)
        self.pushButton_9 = QtWidgets.QPushButton(self.widget_4)
        self.pushButton_9.setMinimumSize(QtCore.QSize(0, 40))
        self.pushButton_9.setObjectName("pushButton_9")
        self.horizontalLayout_3.addWidget(self.pushButton_9)
        self.pushButton_10 = QtWidgets.QPushButton(self.widget_4)
        self.pushButton_10.setMinimumSize(QtCore.QSize(0, 40))
        self.pushButton_10.setObjectName("pushButton_10")
        self.horizontalLayout_3.addWidget(self.pushButton_10)
        self.pushButton_8 = QtWidgets.QPushButton(self.widget_4)
        self.pushButton_8.setMinimumSize(QtCore.QSize(0, 40))
        self.pushButton_8.setObjectName("pushButton_8")
        self.horizontalLayout_3.addWidget(self.pushButton_8)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem)
        self.pushButton = QtWidgets.QPushButton(self.widget_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.MinimumExpanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton.sizePolicy().hasHeightForWidth())
        self.pushButton.setSizePolicy(sizePolicy)
        self.pushButton.setMinimumSize(QtCore.QSize(0, 40))
        self.pushButton.setMaximumSize(QtCore.QSize(100, 16777215))
        self.pushButton.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.pushButton.setStyleSheet("")
        self.pushButton.setObjectName("pushButton")
        self.horizontalLayout_3.addWidget(self.pushButton)
        self.verticalLayout_3.addLayout(self.horizontalLayout_3)
        self.horizontalLayout_4.addWidget(self.widget_4)
        self.widget_6 = QtWidgets.QWidget(Form)
        self.widget_6.setObjectName("widget_6")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.widget_6)
        self.verticalLayout_4.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.widget = QtWidgets.QWidget(self.widget_6)
        self.widget.setMinimumSize(QtCore.QSize(0, 50))
        self.widget.setObjectName("widget")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.widget)
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_10.setSpacing(0)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.label_7 = QtWidgets.QLabel(self.widget)
        self.label_7.setMinimumSize(QtCore.QSize(0, 40))
        self.label_7.setMaximumSize(QtCore.QSize(150, 50))
        self.label_7.setText("")
        self.label_7.setPixmap(QtGui.QPixmap(":/png_ico/logo.png"))
        self.label_7.setScaledContents(True)
        self.label_7.setAlignment(QtCore.Qt.AlignCenter)
        self.label_7.setObjectName("label_7")
        self.horizontalLayout_10.addWidget(self.label_7)
        self.label_8 = QtWidgets.QLabel(self.widget)
        self.label_8.setMinimumSize(QtCore.QSize(0, 40))
        self.label_8.setMaximumSize(QtCore.QSize(150, 50))
        self.label_8.setText("")
        self.label_8.setPixmap(QtGui.QPixmap(":/png_ico/Gemini2.png"))
        self.label_8.setScaledContents(True)
        self.label_8.setAlignment(QtCore.Qt.AlignCenter)
        self.label_8.setObjectName("label_8")
        self.horizontalLayout_10.addWidget(self.label_8)
        self.horizontalLayout_10.setStretch(0, 1)
        self.horizontalLayout_10.setStretch(1, 1)
        self.verticalLayout_4.addWidget(self.widget)
        self.textEdit_2 = QtWidgets.QTextEdit(self.widget_6)
        self.textEdit_2.setObjectName("textEdit_2")
        self.verticalLayout_4.addWidget(self.textEdit_2)
        self.pushButton_3 = QtWidgets.QPushButton(self.widget_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton_3.sizePolicy().hasHeightForWidth())
        self.pushButton_3.setSizePolicy(sizePolicy)
        self.pushButton_3.setMinimumSize(QtCore.QSize(0, 40))
        self.pushButton_3.setMaximumSize(QtCore.QSize(140, 60))
        self.pushButton_3.setObjectName("pushButton_3")
        self.verticalLayout_4.addWidget(self.pushButton_3)
        self.horizontalLayout_4.addWidget(self.widget_6)
        self.horizontalLayout_4.setStretch(0, 6)
        self.horizontalLayout_4.setStretch(1, 3)
        self.verticalLayout_5.addLayout(self.horizontalLayout_4)
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_5.setContentsMargins(0, 0, -1, -1)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.widget_5 = QtWidgets.QWidget(Form)
        self.widget_5.setObjectName("widget_5")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.widget_5)
        self.verticalLayout_2.setContentsMargins(11, 0, -1, -1)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.textBrowser = QtWidgets.QTextBrowser(self.widget_5)
        self.textBrowser.setOpenExternalLinks(True)
        self.textBrowser.setObjectName("textBrowser")
        self.verticalLayout_2.addWidget(self.textBrowser)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.pushButton_14 = QtWidgets.QPushButton(self.widget_5)
        self.pushButton_14.setMinimumSize(QtCore.QSize(0, 40))
        self.pushButton_14.setObjectName("pushButton_14")
        self.horizontalLayout.addWidget(self.pushButton_14)
        self.pushButton_15 = QtWidgets.QPushButton(self.widget_5)
        self.pushButton_15.setMinimumSize(QtCore.QSize(0, 40))
        self.pushButton_15.setObjectName("pushButton_15")
        self.horizontalLayout.addWidget(self.pushButton_15)
        self.pushButton_16 = QtWidgets.QPushButton(self.widget_5)
        self.pushButton_16.setMinimumSize(QtCore.QSize(0, 40))
        self.pushButton_16.setObjectName("pushButton_16")
        self.horizontalLayout.addWidget(self.pushButton_16)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.pushButton_6 = QtWidgets.QPushButton(self.widget_5)
        self.pushButton_6.setMinimumSize(QtCore.QSize(0, 40))
        self.pushButton_6.setObjectName("pushButton_6")
        self.horizontalLayout.addWidget(self.pushButton_6)
        self.pushButton_5 = QtWidgets.QPushButton(self.widget_5)
        self.pushButton_5.setMinimumSize(QtCore.QSize(0, 40))
        self.pushButton_5.setObjectName("pushButton_5")
        self.horizontalLayout.addWidget(self.pushButton_5)
        self.pushButton_2 = QtWidgets.QPushButton(self.widget_5)
        self.pushButton_2.setMinimumSize(QtCore.QSize(0, 40))
        self.pushButton_2.setMaximumSize(QtCore.QSize(16777215, 40))
        self.pushButton_2.setObjectName("pushButton_2")
        self.horizontalLayout.addWidget(self.pushButton_2)
        self.verticalLayout_2.addLayout(self.horizontalLayout)
        self.horizontalLayout_5.addWidget(self.widget_5)
        self.widget_3 = QtWidgets.QWidget(Form)
        self.widget_3.setObjectName("widget_3")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.widget_3)
        self.verticalLayout.setContentsMargins(-1, 11, -1, 11)
        self.verticalLayout.setObjectName("verticalLayout")
        # self.horizontalLayout_13 = QtWidgets.QHBoxLayout()
        # self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        # self.pushButton_11 = QtWidgets.QPushButton(self.widget_3)
        # self.pushButton_11.setMinimumSize(QtCore.QSize(0, 35))
        # self.pushButton_11.setObjectName("pushButton_11")
        # self.horizontalLayout_13.addWidget(self.pushButton_11)
        # self.pushButton_12 = QtWidgets.QPushButton(self.widget_3)
        # self.pushButton_12.setMinimumSize(QtCore.QSize(0, 35))
        # self.pushButton_12.setObjectName("pushButton_12")
        # self.horizontalLayout_13.addWidget(self.pushButton_12)
        # self.pushButton_13 = QtWidgets.QPushButton(self.widget_3)
        # self.pushButton_13.setMinimumSize(QtCore.QSize(0, 35))
        # self.pushButton_13.setObjectName("pushButton_13")
        # self.horizontalLayout_13.addWidget(self.pushButton_13)
        # self.verticalLayout.addLayout(self.horizontalLayout_13)
        self.formLayout = QtWidgets.QFormLayout()
        self.formLayout.setLabelAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.formLayout.setFormAlignment(QtCore.Qt.AlignCenter)
        self.formLayout.setContentsMargins(-1, 11, -1, 11)
        self.formLayout.setSpacing(7)
        self.formLayout.setObjectName("formLayout")
        self.label_5 = QtWidgets.QLabel(self.widget_3)
        self.label_5.setMinimumSize(QtCore.QSize(0, 35))
        self.label_5.setMaximumSize(QtCore.QSize(16777215, 50))
        self.label_5.setObjectName("label_5")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_5)
        self.comboBox = QtWidgets.QComboBox(self.widget_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.comboBox.sizePolicy().hasHeightForWidth())
        self.comboBox.setSizePolicy(sizePolicy)
        self.comboBox.setMinimumSize(QtCore.QSize(0, 35))
        self.comboBox.setMaximumSize(QtCore.QSize(16777215, 35))
        self.comboBox.setObjectName("comboBox")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.comboBox)
        self.verticalLayout.addLayout(self.formLayout)
        self.formLayout_2 = QtWidgets.QFormLayout()
        self.formLayout_2.setLabelAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.formLayout_2.setFormAlignment(QtCore.Qt.AlignCenter)
        self.formLayout_2.setContentsMargins(-1, 11, -1, 11)
        self.formLayout_2.setObjectName("formLayout_2")
        self.label_6 = QtWidgets.QLabel(self.widget_3)
        self.label_6.setMinimumSize(QtCore.QSize(0, 35))
        self.label_6.setMaximumSize(QtCore.QSize(16777215, 50))
        self.label_6.setObjectName("label_6")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_6)
        self.comboBox_2 = QtWidgets.QComboBox(self.widget_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.comboBox_2.sizePolicy().hasHeightForWidth())
        self.comboBox_2.setSizePolicy(sizePolicy)
        self.comboBox_2.setMinimumSize(QtCore.QSize(0, 35))
        self.comboBox_2.setMaximumSize(QtCore.QSize(16777215, 35))
        self.comboBox_2.setObjectName("comboBox_2")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.comboBox_2)
        self.verticalLayout.addLayout(self.formLayout_2)
        self.groupBox = QtWidgets.QGroupBox(self.widget_3)
        self.groupBox.setObjectName("groupBox")
        self.gridLayout_3 = QtWidgets.QGridLayout(self.groupBox)
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout()
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.label = QtWidgets.QLabel(self.groupBox)
        self.label.setAlignment(QtCore.Qt.AlignCenter)
        self.label.setObjectName("label")
        self.horizontalLayout_6.addWidget(self.label)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_6.addItem(spacerItem2)
        self.label_2 = QtWidgets.QLabel(self.groupBox)
        self.label_2.setAlignment(QtCore.Qt.AlignCenter)
        self.label_2.setObjectName("label_2")
        self.horizontalLayout_6.addWidget(self.label_2)
        self.verticalLayout_6.addLayout(self.horizontalLayout_6)
        self.horizontalSlider = QtWidgets.QSlider(self.groupBox)
        self.horizontalSlider.setMouseTracking(False)
        self.horizontalSlider.setTabletTracking(False)
        self.horizontalSlider.setAutoFillBackground(False)
        self.horizontalSlider.setMaximum(20)
        self.horizontalSlider.setSingleStep(1)
        self.horizontalSlider.setPageStep(10)
        self.horizontalSlider.setProperty("value", 10)
        self.horizontalSlider.setTracking(True)
        self.horizontalSlider.setOrientation(QtCore.Qt.Horizontal)
        self.horizontalSlider.setInvertedAppearance(False)
        self.horizontalSlider.setInvertedControls(False)
        self.horizontalSlider.setTickPosition(QtWidgets.QSlider.TicksAbove)
        self.horizontalSlider.setTickInterval(1)
        self.horizontalSlider.setObjectName("horizontalSlider")
        self.verticalLayout_6.addWidget(self.horizontalSlider)
        self.horizontalLayout_7.addLayout(self.verticalLayout_6)
        self.textBrowser_3 = QtWidgets.QTextBrowser(self.groupBox)
        self.textBrowser_3.setMaximumSize(QtCore.QSize(80, 40))
        self.textBrowser_3.setStyleSheet("css QTextBrowser { text-align: center; }")
        self.textBrowser_3.setObjectName("textBrowser_3")
        self.horizontalLayout_7.addWidget(self.textBrowser_3)
        self.horizontalLayout_7.setStretch(0, 8)
        self.horizontalLayout_7.setStretch(1, 1)
        self.gridLayout_3.addLayout(self.horizontalLayout_7, 0, 0, 1, 1)
        self.verticalLayout.addWidget(self.groupBox)
        self.groupBox_2 = QtWidgets.QGroupBox(self.widget_3)
        self.groupBox_2.setObjectName("groupBox_2")
        self.gridLayout_4 = QtWidgets.QGridLayout(self.groupBox_2)
        self.gridLayout_4.setObjectName("gridLayout_4")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.verticalLayout_7 = QtWidgets.QVBoxLayout()
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.label_3 = QtWidgets.QLabel(self.groupBox_2)
        self.label_3.setAlignment(QtCore.Qt.AlignCenter)
        self.label_3.setObjectName("label_3")
        self.horizontalLayout_8.addWidget(self.label_3)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_8.addItem(spacerItem3)
        self.label_4 = QtWidgets.QLabel(self.groupBox_2)
        self.label_4.setAlignment(QtCore.Qt.AlignCenter)
        self.label_4.setObjectName("label_4")
        self.horizontalLayout_8.addWidget(self.label_4)
        self.verticalLayout_7.addLayout(self.horizontalLayout_8)
        self.horizontalSlider_2 = QtWidgets.QSlider(self.groupBox_2)
        self.horizontalSlider_2.setMinimum(1)
        self.horizontalSlider_2.setMaximum(8192)
        self.horizontalSlider_2.setSingleStep(100)
        self.horizontalSlider_2.setPageStep(100)
        self.horizontalSlider_2.setProperty("value", 8192)
        self.horizontalSlider_2.setOrientation(QtCore.Qt.Horizontal)
        self.horizontalSlider_2.setTickPosition(QtWidgets.QSlider.TicksAbove)
        self.horizontalSlider_2.setObjectName("horizontalSlider_2")
        self.verticalLayout_7.addWidget(self.horizontalSlider_2)
        self.horizontalLayout_9.addLayout(self.verticalLayout_7)
        self.textBrowser_2 = QtWidgets.QTextBrowser(self.groupBox_2)
        self.textBrowser_2.setMaximumSize(QtCore.QSize(80, 40))
        self.textBrowser_2.setObjectName("textBrowser_2")
        self.horizontalLayout_9.addWidget(self.textBrowser_2)
        self.horizontalLayout_9.setStretch(0, 8)
        self.horizontalLayout_9.setStretch(1, 1)
        self.gridLayout_4.addLayout(self.horizontalLayout_9, 0, 0, 1, 1)
        self.verticalLayout.addWidget(self.groupBox_2)
        # self.groupBox_3 = QtWidgets.QGroupBox(self.widget_3)
        # self.groupBox_3.setMaximumSize(QtCore.QSize(16777215, 80))
        # self.groupBox_3.setObjectName("groupBox_3")
        # self.gridLayout = QtWidgets.QGridLayout(self.groupBox_3)
        # self.gridLayout.setObjectName("gridLayout")
        # self.comboBox_3 = QtWidgets.QComboBox(self.groupBox_3)
        # sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        # sizePolicy.setHorizontalStretch(0)
        # sizePolicy.setVerticalStretch(0)
        # sizePolicy.setHeightForWidth(self.comboBox_3.sizePolicy().hasHeightForWidth())
        # self.comboBox_3.setSizePolicy(sizePolicy)
        # self.comboBox_3.setMinimumSize(QtCore.QSize(0, 35))
        # self.comboBox_3.setMaximumSize(QtCore.QSize(16777215, 35))
        # self.comboBox_3.setObjectName("comboBox_3")
        # self.gridLayout.addWidget(self.comboBox_3, 0, 0, 1, 1)
        # self.verticalLayout.addWidget(self.groupBox_3)
        self.groupBox_4 = QtWidgets.QGroupBox(self.widget_3)
        self.groupBox_4.setObjectName("groupBox_4")
        self.gridLayout_5 = QtWidgets.QGridLayout(self.groupBox_4)
        self.gridLayout_5.setObjectName("gridLayout_5")
        self.pushButton_17 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_17.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_17.setObjectName("pushButton_17")
        self.gridLayout_5.addWidget(self.pushButton_17, 0, 0, 1, 1)
        self.pushButton_18 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_18.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_18.setObjectName("pushButton_18")
        self.gridLayout_5.addWidget(self.pushButton_18, 0, 1, 1, 1)
        self.pushButton_19 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_19.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_19.setObjectName("pushButton_19")
        self.gridLayout_5.addWidget(self.pushButton_19, 0, 2, 1, 1)
        self.verticalLayout.addWidget(self.groupBox_4)
        self.label_9 = QtWidgets.QLabel(self.widget_3)
        self.label_9.setText("")
        self.label_9.setOpenExternalLinks(True)
        self.label_9.setObjectName("label_9")
        self.verticalLayout.addWidget(self.label_9)
        spacerItem4 = QtWidgets.QSpacerItem(20, 100, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem4)
        self.horizontalLayout_5.addWidget(self.widget_3)
        self.horizontalLayout_5.setStretch(0, 6)
        self.horizontalLayout_5.setStretch(1, 3)
        self.verticalLayout_5.addLayout(self.horizontalLayout_5)
        self.verticalLayout_5.setStretch(0, 3)
        self.verticalLayout_5.setStretch(1, 7)
        self.gridLayout_2.addLayout(self.verticalLayout_5, 0, 0, 1, 1)

        # 应用样式表
        Form.setStyleSheet(UI_Style)
        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "AI for Gemini"))
        self.pushButton_4.setText(_translate("Form", "Insert Media"))
        self.lineEdit_2.setPlaceholderText(_translate("Form", "The maximum file size cannot exceed 7M"))
        self.textEdit.setHtml(_translate("Form", "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"</style></head><body style=\" font-family:\'MS Shell Dlg 2\'; font-size:7.8pt; font-weight:400; font-style:normal;\">\n"
"<p style=\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><br /></p></body></html>"))
        self.textEdit.setPlaceholderText(_translate("Form", "Enter a prompt to submit your question to the AI.\n1.General prompts:- Tell me a joke.- What is the meaning of life?- Write a poem about love.- Summarize the plot of [movie/book].- Translate this sentence into [language].\n2.Creative prompts:- Write a short story about a talking cat.- Create a new superhero with unique powers.- Design a logo for a company that sells organic food.\n3.Informative prompts:- Explain the theory of relativity in simple terms.- What are the different types of clouds?- What are the symptoms of [disease]?- How do I [task]?"))
        self.pushButton_7.setText(_translate("Form", "Send Training_Dataset"))
        self.pushButton_9.setText(_translate("Form", "Send Batch Reqs"))
        self.pushButton_10.setText(_translate("Form", "Send Single Req"))
        # self.pushButton_8.setToolTip(_translate("Form", "Save the last reseponse to Google Sheet"))
        self.pushButton_8.setText(_translate("Form", "Archive TS"))
        self.pushButton.setText(_translate("Form", "Submit"))
        self.textEdit_2.setPlaceholderText(_translate("Form", "The system instructions tell the model that it should answer in the manner of a wedding. Using system instructions can provide context for the model to understand the task, provide more customized answers, and adhere to specific guidelines. Directives are applied every time you send a request to the model\nYou can use system instructions in a variety of ways, including:\n1. Define person settings or roles (for example, for chatbots)\n2. Define output formats (Markdown, YAML, etc.)\n3. Define the output style and tone (e.g., level of verbosity, formality, and degree target reading level)\n4. Define the goals or rules for the task (e.g., return a snippet without further explanation)\n5. Provide prompts Contextual others (e.g. knowledge thresholds)"))
        self.pushButton_3.setText(_translate("Form", "Set System Prompt"))
        self.textBrowser.setHtml(_translate("Form", "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"</style></head><body style=\" font-family:\'MS Shell Dlg 2\'; font-size:7.8pt; font-weight:400; font-style:normal;\">\n"
"<p style=\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><br /></p></body></html>"))
        self.textBrowser.setPlaceholderText(_translate("Form", "After clicking Submit, the model generates a response"))
        self.pushButton_14.setText(_translate("Form", "Send TS\n"
"Training_Dataset"))
        self.pushButton_15.setText(_translate("Form", "Send Batch TCs"))
        self.pushButton_16.setText(_translate("Form", "Send Single TC"))
        self.pushButton_6.setText(_translate("Form", "Load Log"))
        self.pushButton_5.setText(_translate("Form", "Save Log"))
        self.pushButton_2.setText(_translate("Form", "Clear"))
        # self.pushButton_11.setText(_translate("Form", "Create_cached"))
        # self.pushButton_12.setText(_translate("Form", "Update_cached"))
        # self.pushButton_13.setText(_translate("Form", "Delete_cached"))
        self.label_5.setText(_translate("Form", "Model "))
        self.label_6.setText(_translate("Form", "Region"))
        self.groupBox.setTitle(_translate("Form", "Temperature"))
        self.label.setText(_translate("Form", "0"))
        self.label_2.setText(_translate("Form", "2"))
        self.textBrowser_3.setHtml(_translate("Form", "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"</style></head><body style=\" font-family:\'MS Shell Dlg 2\'; font-size:7.8pt; font-weight:400; font-style:normal;\">\n"
"<p style=\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><br /></p></body></html>"))
        self.groupBox_2.setToolTip(_translate("Form", "Output token limit determines the maximum amount of text output from one prompt. \n"
"A token is approximately four characters."))
        self.groupBox_2.setTitle(_translate("Form", "Max Output Tokens"))
        self.label_3.setText(_translate("Form", "1"))
        self.label_4.setText(_translate("Form", "8192"))
        # self.groupBox_3.setTitle(_translate("Form", "Prompt Mode"))
        self.groupBox_4.setTitle(_translate("Form", "Plugins"))
        self.pushButton_17.setText(_translate("Form", "AI translate PPT"))
        self.pushButton_18.setText(_translate("Form", "Send Mail"))
        self.pushButton_19.setText(_translate("Form", "Email back"))
import images_rc
