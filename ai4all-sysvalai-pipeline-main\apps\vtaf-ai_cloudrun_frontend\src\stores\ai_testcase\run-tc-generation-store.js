import { ref } from "vue";
import { defineStore } from "pinia";
import { Notify, Loading } from "quasar";
import { ai_testcaseApi } from "src/boot/axios";

export const useRunTcGenerationStore = defineStore("run_tc_geneation", () => {
  const user_email = ref("");
  const organization_inputs = ref([]);
  const selectedBG = ref("");
  const selectedPG = ref("");
  const selectedPL = ref("");
  const selectedOrgUUID = ref("");
  const selectedFunctionality = ref("");
  const selectedFunctionalityUuid = ref("");

  const functionality_inputs = ref([]);
  const functions_dropdown = ref([]);

  const algorithms_and_sensor_inputs = ref([]);
  const algorithm_dropdown = ref([]);
  const sensor_dropdown = ref([]);

  const selectedSensors = ref({});
  const selectedAlgorithms = ref({});

  const selectedFile = ref(null);
  const tableData = ref([]);
  const columns = ref([]);
  const selectedRows = ref([]);

  const current_job_execution_id = ref("");
  const current_job_execution_id_status = ref(null);

  const all_history = ref([]);
  const current_history = ref([]);
  const job_status_loading = ref(false);
  const current_history_table_loading = ref(false);
  const tc_history_table_loading = ref(false);

  const stepper_loading = ref(false);
  const initial_loading = ref(false);
  const upload_file_loading = ref(false);

  const errorDialogMessage = ref("");
  const errorDialogVisible = ref(false);

  async function get_user_email() {
    try {
      const response = await ai_testcaseApi.get(`/user/get_user`);
      if (response.status === 200) {
        // Success
        const data = response.data.data;
        user_email.value = data;
        console.log(data);
      } else {
        handleError(response);
      }
    } catch (err) {
      console.log(err);
    }
  }

  async function get_job_status() {
    try {
      job_status_loading.value = true;
      const response = await ai_testcaseApi.get(
        `/job/get_job_status/${current_job_execution_id.value}`
      );
      if (response.status == 200) {
        current_job_execution_id_status.value = response.data;
      } else {
        handleError(response);
      }
    } catch (err) {
      console.log(err);
    } finally {
      job_status_loading.value = false;
    }
  }

  async function get_all_job_history() {
    try {
      tc_history_table_loading.value = true;
      console.log("email:", user_email.value);

      const response = await ai_testcaseApi.get(
        `/job/get_all_job_history/${user_email.value}`
      );
      if (response.status == 200) {
        console.log(response.data);
        all_history.value = response.data;
      } else {
        handleError(response);
      }
    } catch (err) {
      console.log(err);
    } finally {
      tc_history_table_loading.value = false;
    }
  }

  async function get_current_jobs() {
    try {
      current_history_table_loading.value = true;
      console.log("email:", user_email.value);

      const response = await ai_testcaseApi.get(
        `/job/get_current_jobs/${user_email.value}`
      );
      if (response.status == 200) {
        console.log(response.data);
        current_history.value = response.data;
      } else {
        handleError(response);
      }
    } catch (err) {
      console.log(err);
    } finally {
      current_history_table_loading.value = false;
    }
  }

  async function get_org_inputs() {
    try {
      initial_loading.value = true;
      const response = await ai_testcaseApi.get(`/run/get_organization_inputs`);
      if (response.status == 200) {
        organization_inputs.value = response.data;
      } else {
        handleError(response);
      }
    } catch (err) {
      console.log(err);
    } finally {
      initial_loading.value = false;
    }
  }

  async function get_algorithms_and_sensor_input() {
    try {
      stepper_loading.value = true;
      const formData = new FormData();
      formData.append("org_uuid", selectedOrgUUID.value);
      const response = await ai_testcaseApi.post(
        `/run/get_algorithms_and_sensors`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      if (response.status == 200) {
        algorithms_and_sensor_inputs.value = response.data;
        algorithm_dropdown.value = response.data.algorithms;
        sensor_dropdown.value = response.data.sensors;
        stepper_loading.value = false;
        console.log(algorithm_dropdown.value);
      } else {
        handleError(response);
      }
    } catch (error) {
      Notify.create({
        color: "negative",
        position: "bottom",
        message: error.message,
        icon: "report_problem",
      });
    } finally {
      stepper_loading.value = false;
    }
  }

  async function get_functionality_input() {
    try {
      stepper_loading.value = true;
      const formData = new FormData();
      formData.append("org_uuid", selectedOrgUUID.value);
      const response = await ai_testcaseApi.post(
        `/run/get_functionality_inputs`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      if (response.status == 200) {
        functionality_inputs.value = response.data;
        functions_dropdown.value = functionality_inputs.value.flatMap((item) =>
          item.details.map((detail) => detail.value)
        );
        functions_dropdown.value.sort();
        stepper_loading.value = false;
      } else {
        handleError(response);
      }
    } catch (error) {
      Notify.create({
        color: "negative",
        position: "bottom",
        message: error.message,
        icon: "report_problem",
      });
    } finally {
      stepper_loading.value = false;
    }
  }

  async function uploadFile() {
    upload_file_loading.value = true;
    if (!selectedFile.value) {
      upload_file_loading.value = false;
      return;
    }
    const formData = new FormData();
    formData.append("user_email", user_email.value);
    formData.append("file", selectedFile.value);
    formData.append("selected_rows", JSON.stringify(selectedRows.value));
    formData.append("business_group", selectedBG.value);
    formData.append("product_group", selectedPG.value);
    formData.append("product_line", selectedPL.value);
    formData.append("features", selectedFunctionality.value);
    formData.append("algorithms", JSON.stringify(selectedAlgorithms.value));
    formData.append("sensors", JSON.stringify(selectedSensors.value));
    try {
      const response = await ai_testcaseApi.post("/run/upload_file", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      if (response.status == 200) {
        console.log("uploaded");
        current_job_execution_id.value = response.data.execution_id;
        console.log(current_job_execution_id.value);
        upload_file_loading.value = false;
      } else {
        handleError(response);
      }
    } catch (error) {
      Notify.create({
        color: "negative",
        position: "bottom",
        message: error.message,
        icon: "report_problem",
      });
    } finally {
      upload_file_loading.value = false;
    }
  }

  function handleError(error) {
    errorDialogMessage.value = `Error ${error.status}:\n${
      error.data.detail || error.statusText
    }`;
    errorDialogVisible.value = true;
  }

  return {
    user_email,
    organization_inputs,
    selectedBG,
    selectedPG,
    selectedPL,
    selectedOrgUUID,
    functionality_inputs,
    selectedFunctionality,
    selectedFunctionalityUuid,
    functions_dropdown,
    algorithms_and_sensor_inputs,
    algorithm_dropdown,
    sensor_dropdown,
    selectedSensors,
    selectedAlgorithms,
    selectedFile,
    tableData,
    columns,
    selectedRows,
    current_job_execution_id,
    current_job_execution_id_status,
    all_history,
    current_history,
    job_status_loading,
    current_history_table_loading,
    tc_history_table_loading,
    stepper_loading,
    initial_loading,
    upload_file_loading,
    errorDialogMessage,
    errorDialogVisible,
    get_user_email,
    get_current_jobs,
    get_all_job_history,
    get_job_status,
    get_org_inputs,
    get_functionality_input,
    get_algorithms_and_sensor_input,
    uploadFile,
  };
});
